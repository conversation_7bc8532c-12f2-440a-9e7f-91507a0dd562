﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api
{
    public class SiniestroCompleto
    {
        public InformacionSiniestro informacionSiniestro { get; set; }

        public Siniestro siniestro { get; set; }

        public SiniestroContacto siniestroContacto { get; set; }

        public SiniestroRespuestaAseguradora siniestroRespuestaAseguradora { get; set; }

        public EstatusSiniestro estatusSiniestro { get; set; }

        public TipoSiniestro tipoSiniestro { get; set; }

        public TipoCoberturaSiniestro tipoCoberturaSiniestro { get; set; }
    }
}
