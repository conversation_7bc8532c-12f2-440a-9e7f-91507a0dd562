<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<FeatureProviderRegistry>
  <FeatureProvider>
    <Name>OSGeo.SDF.4.0</Name>
    <DisplayName>OSGeo FDO Provider for SDF</DisplayName>
    <Description>Read/write access to Autodesk's spatial database format, a file-based personal geodatabase that supports multiple features/attributes, spatial indexing, and file-locking.</Description>
    <IsManaged>False</IsManaged>
    <Version>*******</Version>
    <FeatureDataObjectsVersion>*******</FeatureDataObjectsVersion>
    <LibraryPath>.\SDFProvider.dll</LibraryPath>
  </FeatureProvider>
  <FeatureProvider>
    <Name>OSGeo.SHP.4.0</Name>
    <DisplayName>OSGeo FDO Provider for SHP</DisplayName>
    <Description>Read/write access to spatial and attribute data in an ESRI SHP file.</Description>
    <IsManaged>False</IsManaged>
    <Version>*******</Version>
    <FeatureDataObjectsVersion>*******</FeatureDataObjectsVersion>
    <LibraryPath>.\SHPProvider.dll</LibraryPath>
  </FeatureProvider>
  <FeatureProvider>
    <Name>OSGeo.ArcSDE.4.0</Name>
    <DisplayName>OSGeo FDO Provider for ArcSDE</DisplayName>
    <Description>Read/write access to an ESRI ArcSDE-based data store, using Oracle and SQL Server.</Description>
    <IsManaged>False</IsManaged>
    <Version>*******</Version>
    <FeatureDataObjectsVersion>*******</FeatureDataObjectsVersion>
    <LibraryPath>.\ArcSDEProvider.dll</LibraryPath>
  </FeatureProvider>
  <FeatureProvider>
    <Name>OSGeo.WFS.4.0</Name>
    <DisplayName>OSGeo FDO Provider for WFS</DisplayName>
    <Description>Read access to OGC WFS-based data store.</Description>
    <IsManaged>False</IsManaged>
    <Version>*******</Version>
    <FeatureDataObjectsVersion>*******</FeatureDataObjectsVersion>
    <LibraryPath>.\WFSProvider.dll</LibraryPath>
  </FeatureProvider>
  <FeatureProvider>
    <Name>OSGeo.WMS.4.0</Name>
    <DisplayName>OSGeo FDO Provider for WMS</DisplayName>
    <Description>Read access to OGC WMS-based data store.</Description>
    <IsManaged>False</IsManaged>
    <Version>*******</Version>
    <FeatureDataObjectsVersion>*******</FeatureDataObjectsVersion>
    <LibraryPath>.\WMSProvider.dll</LibraryPath>
  </FeatureProvider>
  <FeatureProvider>
    <Name>OSGeo.ODBC.4.0</Name> 
    <DisplayName>OSGeo FDO Provider for ODBC</DisplayName> 
    <Description>FDO Provider for ODBC</Description> 
    <IsManaged>False</IsManaged> 
    <Version>*******</Version> 
    <FeatureDataObjectsVersion>*******</FeatureDataObjectsVersion>
    <LibraryPath>.\ODBCProvider.dll</LibraryPath>
  </FeatureProvider>
  <FeatureProvider>
    <Name>OSGeo.MySQL.4.0</Name> 
    <DisplayName>OSGeo FDO Provider for MySQL</DisplayName> 
    <Description>FDO Provider for MySql</Description>
    <IsManaged>False</IsManaged>
    <Version>*******</Version>
    <FeatureDataObjectsVersion>*******</FeatureDataObjectsVersion>
    <LibraryPath>.\MySQLProvider.dll</LibraryPath>
  </FeatureProvider>
  <FeatureProvider>
    <Name>OSGeo.Gdal.4.0</Name>
    <DisplayName>OSGeo FDO Provider for GDAL</DisplayName>
    <Description>FDO Provider for GDAL</Description>
    <IsManaged>False</IsManaged>
    <Version>*******</Version>
    <FeatureDataObjectsVersion>*******</FeatureDataObjectsVersion>
    <LibraryPath>.\GRFPProvider.dll</LibraryPath>
  </FeatureProvider>
  <FeatureProvider>
    <Name>OSGeo.OGR.4.0</Name> 
    <DisplayName>OSGeo FDO Provider for OGR</DisplayName> 
    <Description>FDO Access to OGR Data Sources</Description> 
    <IsManaged>False</IsManaged> 
    <Version>*******</Version> 
    <FeatureDataObjectsVersion>*******</FeatureDataObjectsVersion> 
    <LibraryPath>.\OGRProvider.dll</LibraryPath> 
  </FeatureProvider>
  <FeatureProvider>
    <Name>OSGeo.KingOracle.4.0</Name>
    <DisplayName>OSGeo FDO Provider for Oracle</DisplayName>
    <Description>Read/write access to spatial and attribute data in an Oracle Spatial.</Description>
    <IsManaged>False</IsManaged>
    <Version>*******</Version>
    <FeatureDataObjectsVersion>*******</FeatureDataObjectsVersion>
    <LibraryPath>.\KingOracleProvider.dll</LibraryPath>
  </FeatureProvider>
  <FeatureProvider>
    <Name>OSGeo.SQLServerSpatial.4.0</Name>
    <DisplayName>OSGeo FDO Provider for SQL Server Spatial</DisplayName>
    <Description>Read/Write access to feature data in a MS SQL Server spatially enabled data store. Supports geospatial and non-geospatial data and schema.</Description>
    <IsManaged>False</IsManaged>
    <Version>*******</Version>
    <FeatureDataObjectsVersion>*******</FeatureDataObjectsVersion>
    <LibraryPath>.\SQLServerSpatialProvider.dll</LibraryPath>
  </FeatureProvider>
  <FeatureProvider>
    <Name>OSGeo.SQLite.4.0</Name>
    <DisplayName>OSGeo FDO Provider for SQLite (spatial)</DisplayName>
    <Description>Read/write access to feature data in a SQLite file.</Description>
    <IsManaged>False</IsManaged>
    <Version>*******</Version>
    <FeatureDataObjectsVersion>*******</FeatureDataObjectsVersion>
    <LibraryPath>.\SQLiteProvider.dll</LibraryPath>
  </FeatureProvider>
  <FeatureProvider>
    <Name>OSGeo.PostgreSQL.4.0</Name>
    <DisplayName>OSGeo FDO Provider for PostgreSQL/PostGIS</DisplayName>
    <Description>Read/write access to PostgreSQL/PostGIS-based data store. Supports spatial data types and spatial query operations.</Description>
    <IsManaged>False</IsManaged>
    <Version>*******</Version>
    <FeatureDataObjectsVersion>*******</FeatureDataObjectsVersion>
    <LibraryPath>.\PostgreSQLProvider.dll</LibraryPath>
  </FeatureProvider>
</FeatureProviderRegistry>
