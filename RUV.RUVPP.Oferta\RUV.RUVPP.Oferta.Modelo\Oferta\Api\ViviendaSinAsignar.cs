﻿namespace RUV.RUVPP.Oferta.Modelo.Oferta.Api
{
    public class ViviendaSinAsignar
    {
        public int featId { get; set; }
        public int idVivienda { get; set; }
        public int idPrototipo { get; set; }
        public int idEstatusVivienda { get; set; }
        public string estatusVivienda { get; set; }
        public string cuv { get; set; }
        public string calle { get; set; }
        public string numExt { get; set; }
        public string numInt { get; set; }
        public string lote { get; set; }
        public string manzana { get; set; }
        public string colonia { get; set; }
        public string codigoPostal { get; set; }
        public bool esSeleccionada { get; set; }
        public bool disponible { get; set; }
        public int? idViviendaPlanoSIG { get; set; }
    }
}