﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api
{
    public class FichaPagoSeguroCalidad
    {
        public int idDetalleFichaPagoPoliza {get;set;}
        public int idTipoPagoSeguroCalidad {get;set;}
        public DateTime? fechaSolicitud {get;set;}
        public decimal? monto {get;set;}
        public string ordenVerificacion {get;set;}
        public int idEmpresa {get;set;}
        public int idCuvsSolicitadas {get;set;}
        public int idfichaPago {get;set;}
        public int idEstatusFichaPago {get;set;}
        public int idTarifa {get;set;}
        public string referencia {get;set;}
        public DateTime? fechaEmision {get;set;}
        public DateTime? fechaExpiracion {get;set;}
        public decimal? importe {get;set;}
        public string conceptoPago {get;set;}
        public string numeroDocumento {get;set;}
        public string numeroProvisionalDocumento {get;set;}
        public DateTime? fechaPago {get;set;}
        public DateTime? fechaRealDePago {get;set;}
        public int cantidad {get;set;}
        public bool activo {get;set;}
        public decimal? iva {get;set;}
        public string clave  {get;set;}
        public int idDocumento {get;set;}
        public string numeroDocumentoSAP {get;set;}
        public decimal? importeTotal { get; set; }
    }
}
