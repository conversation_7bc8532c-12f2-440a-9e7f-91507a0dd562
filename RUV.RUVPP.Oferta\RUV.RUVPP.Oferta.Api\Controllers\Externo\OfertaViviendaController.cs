﻿using RUV.Comun.Utilerias;
using RUV.Comun.Web.Http;
using RUV.RUVPP.Entidades.General.Seguridad;
using RUV.RUVPP.Negocio.General.Seguridad;
using RUV.RUVPP.Oferta.Dominio.Oferta;
using RUV.RUVPP.Oferta.Dominio.SeguroCalidad;
using RUV.RUVPP.Oferta.Modelo.Oferta.Api;
using RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;
using System.Web.Http.Description;

namespace RUV.RUVPP.Oferta.Api.Controllers.Externo
{
    [RoutePrefix("externo/api/oferta-vivienda")]
    public class OfertaViviendaController : ApiControllerBase
    {
        private readonly IServicioOferta _servicioOferta;
        private readonly IServicioSeguridad _servicioSeguridad;
        private readonly IServicioSeguroCalidad _servicioSeguroCalidad;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="servicioOferta"></param>
        /// <param name="servicioSeguridad"></param>
        /// <param name="servicioSeguroCalidad"></param>
        public OfertaViviendaController(IServicioOferta servicioOferta, IServicioSeguridad servicioSeguridad, IServicioSeguroCalidad servicioSeguroCalidad)
            : base()
        {
            this._servicioOferta = servicioOferta;
            this._servicioSeguridad = servicioSeguridad;
            this._servicioSeguroCalidad = servicioSeguroCalidad;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="envioEstatusCredito"></param>
        /// <returns></returns>
        [HttpPost, Route("estatus-credito")]
        [ResponseType(typeof(ResultadoPeticion))]
        public async Task<HttpResponseMessage> GuardarDatosCreditoViviendaAsyn(EstatusCreditoVivienda envioEstatusCredito)
        {
            Contrasena auxiliar = new Contrasena();
            string contrasenaHash = auxiliar.ObtenerHashContrasena(envioEstatusCredito.password);

            Login login = this._servicioSeguridad.Autenticarporidusuarioseguridadints(envioEstatusCredito.usuario, contrasenaHash, false, false, null, null);
            ResultadoPeticion resultadoPeticion;
            if (login.TipoMensajeSeguridad == TipoMensajeSeguridad.Autenticado || login.TipoMensajeSeguridad == TipoMensajeSeguridad.SesionAnteriorSinCerrar)
            {
                
            }
            else
            {
                resultadoPeticion = new ResultadoPeticion();
                resultadoPeticion.codigo = "0005";
                resultadoPeticion.resultado = "NOK";
                resultadoPeticion.descripcion = "Usuario/Contraseña inválidos";
            }

            return Request.CreateResponse(HttpStatusCode.OK, new ResultadoPeticion());
        }


    }
}
