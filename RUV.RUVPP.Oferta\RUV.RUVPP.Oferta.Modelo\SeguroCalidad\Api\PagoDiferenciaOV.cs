﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api
{
    public class PagoDiferenciaOV
    {              
        public int? idPagosParcialesSeguroCalidad { get; set; }
        public string idOrdenVerificacion { get; set; }
        public string noPago { get; set; }
        public string fechaPago { get; set; }
        public string fechaCreacion { get; set; }
        public string fechaSolicitudPago { get; set; }
        public int? idEstatus { get; set; }
        public string Estatus { get; set; }
        public decimal? monto { get; set; }
        public string numeroFactura { get; set; }
        public DateTime? fechaEntregaFactura { get; set; }
        public string rfcDesarrollador { get; set; }
        public string numeroRegistroD { get; set; }
        public string cuentaAcreedorD { get; set; }
        public string resultadoT { get; set; }
        public string fechaInicioServicio { get; set; }
        public string fechaFinServicio { get; set; }
        public string cuentaAcreedorV { get; set; }
        public string clabeBancariaV { get; set; }
        public string tipoBanco { get; set; }
        public string documentoSAP { get; set; }
        public string sociedad { get; set; }
        public string ejercicio { get; set; }
        public string numeroProvicionalDocumento { get; set; }
        public int? idCuvsSolicitadas { get; set; }

        public int? numeroCuvs { get; set; }

        public List<string> cuentasBancarias { get; set; }

        public List<string> listaCuvs { get; set; }

        public string referencia { get; set; }
        public int idDocumentoFactura { get; set; }
        public string ordenVerificacionAmpliada { get; set; }
    }
}
