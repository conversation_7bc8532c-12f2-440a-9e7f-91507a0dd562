﻿using RUV.Comun.Seguridad.JWT;
using RUV.Comun.Utilerias;
using RUV.Comun.Web.Http;
using RUV.RUVPP.Oferta.Dominio.SeguroCalidad;
using RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api;
using RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using System.Web.Http.Description;
using iTextSharp.text;
using iTextSharp.text.pdf;
using System.IO;
using RUV.RUVPP.Negocio.General.Documentos.Abstracta;
using RUV.RUVPP.Oferta.GeneralesInterop.Documentos;
using RUV.RUVPP.Generales.Modelo.Documentos;
using RUV.RUVPP.Negocio.General.Seguridad;
using RUV.RUVPP.Entidades.General.Seguridad;
using RUV.RUVPP.Oferta.Modelo.Comun;

namespace RUV.RUVPP.Oferta.Api.Controllers.Externo
{
    /// <summary>
    /// 
    /// </summary>
    [RoutePrefix("externo/api/seguro-calidad")]
    public class SeguroCalidadController : ApiControllerBase
    {

        private readonly IServicioSeguroCalidad _servicioSeguroCalidad;
        private readonly IServicioDocumento<DocumentoOferta> _servicioDocumento;
        private readonly IServicioSeguridad _servicioSeguridad;
        /// <summary>
        /// 
        /// </summary>
        /// <param name="servicioSeguroCalidad"></param>
        /// <param name="servicioDocumento"></param>
        /// <param name="servicioSeguridad"></param>
        public SeguroCalidadController(IServicioSeguroCalidad servicioSeguroCalidad, IServicioDocumento<DocumentoOferta> servicioDocumento, IServicioSeguridad servicioSeguridad)
            : base()
        {
            this._servicioDocumento = servicioDocumento;
            this._servicioSeguroCalidad = servicioSeguroCalidad;
            this._servicioSeguridad = servicioSeguridad;
        }

        #region proceso90dias
        [HttpPost, Route("revisar-parametros-90Dias")]
        [ResponseType(typeof(bool))]
        public async Task<HttpResponseMessage> RevisarParametros90Dias()
        {
            var result = await this._servicioSeguroCalidad.RevisarParametros90Dias();

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpPost, Route("actualizar-parametros-config-90Dias")]
        [ResponseType(typeof(bool))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ActualizarParametrosConfigs90DiasAsync(List<ParametrosConfigs> listaParametros)
        {
            var usuario = (CustomUserRuv)User;
            var result = await this._servicioSeguroCalidad.ActualizarParametrosConfigs90DiasAsync(listaParametros, usuario.Email, true);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpPost, Route("obtener-parametros-config-90Dias")]
        [ResponseType(typeof(List<ParametrosConfigs>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerParametrosConfigs90DiasAsync(List<string> listaClaves)
        {
            var result = await this._servicioSeguroCalidad.ObtenerParametrosConfigs90DiasAsync(listaClaves);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("estatus-parametro-config-90Dias")]
        [ResponseType(typeof(Estatus90Dias))]
        public async Task<HttpResponseMessage> ObtenerEstatusParametrosConfigs90DiasAsync()
        {
            var result = await this._servicioSeguroCalidad.ObtenerEstatusParametrosConfigs90DiasAsync();

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }
        #endregion

        #region Polizas
        [HttpGet, Route("aseguradoras")]
        [ResponseType(typeof(ResultadoPaginado<List<Aseguradora>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerAseguradoraConPaginadoAsync(int tamanioPagina, int pagina, string razonSocial = null, string noRegistroRUV = null)
        {
            var result = await this._servicioSeguroCalidad.ObtenerAseguradoraConPaginadoAsync(tamanioPagina, pagina, razonSocial, noRegistroRUV);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("obtener-cuvsinpolizas")]
        [ResponseType(typeof(ResultadoPaginado<List<CuvPoliza>>))]
        public async Task<HttpResponseMessage> ObtenerCuvSinPolizaConPaginadoAsync(int tamanioPagina, int pagina, int? idOferente = null, int? idAseguradora = null, string cuv = null, string ordenVerificacion = null, bool? cambioValorAvaluo = null)
        {
            var result = await this._servicioSeguroCalidad.ObtenerCuvSinPolizaConPaginadoAsync(tamanioPagina, pagina, cuv, ordenVerificacion, idOferente, idAseguradora, cambioValorAvaluo);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpPost, Route("obtener-informacion-siniestro")]
        [ResponseType(typeof(ResultadoPaginado<List<InformacionSiniestro>>))]
        public async Task<HttpResponseMessage> ObtenerInformacionParaSiniestro(InformacionSiniestro informacionSiniestro)
        {
            var result = await this._servicioSeguroCalidad.ObtenerInformacionParaSiniestro(informacionSiniestro);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpPost, Route("obtener-informacion-siniestro-excel")]
        [ResponseType(typeof(ResultadoPaginado<List<InformacionSiniestroExcel>>))]
        public async Task<HttpResponseMessage> ObtenerInformacionParaSiniestroExcel(InformacionSiniestroExcel informacionSiniestroExcel)
        {
            var result = await this._servicioSeguroCalidad.ObtenerInformacionParaSiniestroExcel(informacionSiniestroExcel);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpPost, Route("enviar-siniestros-abiertos")]
        [ResponseType(typeof(bool))]
        public async Task<HttpResponseMessage> EnviarSiniestrosAbiertos()
        {
            var result = await this._servicioSeguroCalidad.EnviarSiniestrosAbiertos();

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpPost, Route("enviar-siniestros-idSiniestro")]
        [ResponseType(typeof(bool))]
        public async Task<HttpResponseMessage> EnviarSiniestroXIdSiniestro(int idSiniestro)
        {
            var result = await this._servicioSeguroCalidad.EnviarSiniestroXIdSiniestro(idSiniestro);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("obtener-consulta-aseguradora")]
        [ResponseType(typeof(ResultadoPaginado<List<OrdenDeVerificacion>>))]
        public async Task<HttpResponseMessage> ObtenerConsultaAseguradoraConPaginadoAsync(int tamanioPagina, int pagina, bool? soloRelacionComercial = null, int? idOferenteExterno = null, int? idAseguradoraExterno = null, int? idTipoAsignacion = null, int? idEstatusPagoEvaluacionRiesgo = null, string noRegistroRUV = null, string razonSocial = null, string ordenVerificacion = null, string noContrato = null, string fechaInicial = null, string fechaFinal = null, string fechaAceptacionInicio = null, string fechaAceptacionFinal = null)
        {
            var result = await this._servicioSeguroCalidad.ObtenerConsultaAseguradoraConPaginadoAsync(tamanioPagina, pagina, noRegistroRUV, razonSocial, ordenVerificacion, noContrato, fechaInicial, fechaFinal, fechaAceptacionInicio, fechaAceptacionFinal, idTipoAsignacion, idOferenteExterno, idAseguradoraExterno, idEstatusPagoEvaluacionRiesgo, soloRelacionComercial);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("obtener-info-orden")]
        [ResponseType(typeof(OrdenDeVerificacion))]
        public async Task<HttpResponseMessage> ObtenerInfoOrdenVerificacionAsync(string ordenVerificacion = null)
        {
            var result = await this._servicioSeguroCalidad.ObtenerInfoOrdenVerificacionAsync(ordenVerificacion);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("obtener-cuvconpolizas")]
        [ResponseType(typeof(ResultadoPaginado<List<CuvPoliza>>))]
        public async Task<HttpResponseMessage> ObtenerCuvConPolizaConPaginadoAsync(int tamanioPagina, int pagina, int? idOferente = null, int? idAseguradora = null, string cuv = null, string ordenVerificacion = null)
        {
            var result = await this._servicioSeguroCalidad.ObtenerCuvConPolizaConPaginadoAsync(tamanioPagina, pagina, cuv, ordenVerificacion, idOferente, idAseguradora);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpPost, Route("agregar-poliza")]
        [ResponseType(typeof(List<int>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> AgregarPolizaAsync(List<CuvPoliza> listaCuvPoliza)
        {

            var usuario = (CustomUserRuv)User;
            var listaPolizas = await this._servicioSeguroCalidad.AgregarPolizaAsync(listaCuvPoliza, usuario.IdUsuario);

            return Request.CreateResponse(HttpStatusCode.OK, listaPolizas);
        }


        [HttpGet, Route("obtener-costo-poliza")]
        [ResponseType(typeof(ResultadoPaginado<List<CuvPoliza>>))]
        public async Task<HttpResponseMessage> ObtenerCostoPolizaAsync(string ordenVerificacion, string cuv = null)
        {
            //var result = await this._servicioSeguroCalidad.ObtenerCostoPolizaAsync(ordenVerificacion, cuv);

            return Request.CreateResponse(HttpStatusCode.OK, false);
        }

        #endregion

        #region avaluos
        [HttpGet, Route("obtener-lista-avaluos")]
        [ResponseType(typeof(ResultadoPaginado<List<Avaluo>>))]
        public async Task<HttpResponseMessage> ObtenerListaAvaluosAsync(int tamanioPagina, int pagina, string cuv = null)
        {
            var result = await this._servicioSeguroCalidad.ObtenerListaAvaluosAsync(tamanioPagina, pagina, cuv);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }
        #endregion

        #region ServiciosAseguradora

        [HttpPost, Route("documento-poliza")]
        [ResponseType(typeof(ResultadoPeticion))]
        public async Task<HttpResponseMessage> GuardarDocumentoPolizaCUV(RespuestaAseguradoraSolicitud respuestaSolicitud)
        {
            ResultadoPeticion resultadoPeticion = new ResultadoPeticion() { resultado = "OK", descripcion = "Resultado Exitoso" };

            return Request.CreateResponse(HttpStatusCode.OK, resultadoPeticion);
        }

        [HttpPost, Route("envio-informacion-siniestro")]
        [ResponseType(typeof(ResultadoPeticion))]
        public async Task<HttpResponseMessage> EnviarInformacionSiniestroCuv(SiniestroAseguradora siniestroAseguradora)
        {
            ResultadoPeticion resultadoPeticion = new ResultadoPeticion();

            var result = await this._servicioSeguroCalidad.EnviarInformacionSiniestroCuv(siniestroAseguradora);

            return Request.CreateResponse(HttpStatusCode.OK, resultadoPeticion);
        }

        [HttpPost, Route("envio-informacion-siniestro-automatico")]
        [ResponseType(typeof(ResultadoPeticion))]
        public async Task<HttpResponseMessage> EnviarInformacionSiniestroAutomatico()
        {
            ResultadoPeticion resultadoPeticion = new ResultadoPeticion();

            await _servicioSeguroCalidad.EnviarInformacionSiniestroAutomatico();

            return Request.CreateResponse(HttpStatusCode.OK, resultadoPeticion);
        }
        //Cambio Actualizar Respuesta Siniestro
        [HttpPost, Route("actualiza-respuesta-siniestro")]
        [ResponseType(typeof(ResultadoPeticionAseguradora))]
        public async Task<HttpResponseMessage> ActualizaRespuestaSiniestroCuv(RespuestaSiniestro respuestaSiniestro)
        {
            ResultadoPeticion resultadoPeticion = await _servicioSeguroCalidad.ActualizarDatosRespuestaSiniestro(respuestaSiniestro);

            return Request.CreateResponse(HttpStatusCode.OK, resultadoPeticion);
        }
        //Cambio Actualizar Respuesta Siniestro
        [HttpPost, Route("respuesta-siniestro")]
        [ResponseType(typeof(ResultadoPeticionAseguradora))]
        public async Task<HttpResponseMessage> RespuestaSiniestroCuv(RespuestaSiniestro respuestaSiniestro)
        {
            /*Verificar proceso*/
           
            Console.WriteLine(respuestaSiniestro.fechadecierredeSiniestro);
            Console.WriteLine(respuestaSiniestro.fechadedeterminacion);
            Console.WriteLine(respuestaSiniestro.fechaInspeccionAjustador);
            Console.WriteLine(respuestaSiniestro.fechaOcurrencia);
            Console.WriteLine(respuestaSiniestro.fechaReporte);
            Console.WriteLine(respuestaSiniestro.fechaRespuestaAseguradora);
            Console.WriteLine(respuestaSiniestro.pagosGastosFecha);
            Console.WriteLine(respuestaSiniestro.pagosHonorariosFecha);
            Console.WriteLine(respuestaSiniestro.pagosIndemnizacionFecha);
            Console.WriteLine(respuestaSiniestro.totalPagoFecha);
            Console.WriteLine(respuestaSiniestro.vigenciaFechaFinPoliza);
            Console.WriteLine(respuestaSiniestro.vigenciaFechaInicioPoliza);
            Console.WriteLine(respuestaSiniestro);

            ResultadoPeticion resultadoPeticion = await _servicioSeguroCalidad.GuardarDatosRespuestaSiniestro(respuestaSiniestro);


            return Request.CreateResponse(HttpStatusCode.OK, resultadoPeticion);
        }

        [HttpGet, Route("enviar-ordenverificacion-aseguradora")]
        [ResponseType(typeof(ResultadoPeticionOV))]
        public async Task<HttpResponseMessage> EnviarOrdenVerificacionAseguradora(string ordenVerificacon)
        {
            ResultadoPeticionOV resultadoPeticion = await _servicioSeguroCalidad.EnviarOrdenVerificacionAseguradora(ordenVerificacon);

            return Request.CreateResponse(HttpStatusCode.OK, resultadoPeticion);
        }


        [HttpGet, Route("notificar-aceptacion-ov")]
        [ResponseType(typeof(ResultadoPeticionOV))]
        public async Task<HttpResponseMessage> NotificarAceptacionOVPorVerificador(string ordenVerificacion)
        {
            var resultadoPeticion = await _servicioSeguroCalidad.NotificarAceptacionOVPorVerificadorAsync(ordenVerificacion);

            return Request.CreateResponse(HttpStatusCode.OK, resultadoPeticion);
        }

        #endregion

        #region ServiciosInfonavit

        /// <summary>
        /// 
        /// </summary>
        /// <param name="datosTitulacion"></param>
        /// <returns></returns>
        [HttpPost, Route("datos-titulacion")]
        [ResponseType(typeof(ResultadoPeticion))]
        public async Task<HttpResponseMessage> GuardarDatosTitulacionAsyn(DatosTitulacionINFONAVIT datosTitulacion)
        {
            var result = await _servicioSeguroCalidad.GuardarDatosTitulacionAsync(datosTitulacion);
            ResultadoPeticion resultadoPeticion = new ResultadoPeticion() { resultado = "OK", descripcion = "Resultado Exitoso" };

            return Request.CreateResponse(HttpStatusCode.OK, resultadoPeticion);
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="datosValorAvaluo"></param>
        /// <returns></returns>
        [HttpPost, Route("valor-avaluo")]
        [ResponseType(typeof(ResultadoPeticion))]
        public async Task<HttpResponseMessage> GuardarDatosValorAvaluoAsyn(DatosValorAvaluo datosValorAvaluo)
        {
            ResultadoPeticion resultadoPeticion = await _servicioSeguroCalidad.GuardarDatosValorAvaluo(datosValorAvaluo);
            return Request.CreateResponse(HttpStatusCode.OK, resultadoPeticion);
        }

        /// <summary>|
        /// 
        /// </summary>
        /// <param name="datosVivienda"></param>
        /// <returns></returns>
        [HttpPost, Route("datos-vivienda-asegurada")]
        [ResponseType(typeof(DatosViviendaAsegurada))]
        public async Task<HttpResponseMessage> ObtenerDatosViviendaAseguradaAsyn(DatosVivienda datosVivienda)
        {
            DatosViviendaAsegurada resultadoPeticion = new DatosViviendaAsegurada() { claveEntidadOrden = "01", costoPoliza = 1000.0, costoVivienda = 1000000.0, cuv = "1234567890123456", fechaPagoOrden = "08-02-2018", fechaRegistroOrden = "07-08-2018", folioPoliza = "FOL123456789", idOferente = "99999999", idVivienda = 1, marcaSeguro = true, nombreEntidadOrden = "Aguascalientes", ordenVerificacion = "50000000001", pagoEvaluacionRiesgo = true, razonSocialAseguradora = "Aseguradora SA de CV", rfcAseguradora = "ASE111111DER", estatus = 0 };
            return Request.CreateResponse(HttpStatusCode.OK, resultadoPeticion);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="datosVivienda"></param>
        /// <returns></returns>
        [HttpPost, Route("vivienda-asegurada")]
        [ResponseType(typeof(ViviendaAsegurada))]
        public async Task<HttpResponseMessage> ViviendaAseguradaAsyn(DatosVivienda datosVivienda)
        {
            Contrasena auxiliar = new Contrasena();
            string contrasenaHash = auxiliar.ObtenerHashContrasena(datosVivienda.password);

            Login login = this._servicioSeguridad.Autenticarporidusuarioseguridadints(datosVivienda.usuario, contrasenaHash, false, false, null, null);
            ViviendaAsegurada resultadoPeticion;
            if (login.TipoMensajeSeguridad == TipoMensajeSeguridad.Autenticado || login.TipoMensajeSeguridad == TipoMensajeSeguridad.SesionAnteriorSinCerrar)
            { 
                if (datosVivienda.cuv.Equals("1601001175100254"))//Monitoreo Infonavit
                {
                    resultadoPeticion = new ViviendaAsegurada();
                    resultadoPeticion.estatus = 0;
                    resultadoPeticion.resultado = "OK";
                    resultadoPeticion.descripcion = "La Cuv no cuenta con seguro de calidad.";
                }else if (datosVivienda.cuv.Equals("1917008860101172"))
                {//Monitoreo Infonavit  
                    resultadoPeticion = new ViviendaAsegurada();
                    resultadoPeticion.estatus = 2;
                    resultadoPeticion.resultado = "OK";
                    resultadoPeticion.descripcion = "La Cuv tiene seguro calidad.";
                }else
                {
                    resultadoPeticion = await _servicioSeguroCalidad.ValidarSeguroVivienda(datosVivienda);
                } 
            }
            else {
                resultadoPeticion = new ViviendaAsegurada();
                resultadoPeticion.estatus = 0;
                resultadoPeticion.resultado = "NOK";
                resultadoPeticion.descripcion = "Usuario/Password incorrectos."; 
            }

            await _servicioSeguroCalidad.AgregaraBitacora(resultadoPeticion, datosVivienda.cuv);

            return Request.CreateResponse(HttpStatusCode.OK, resultadoPeticion);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="datosValorAvaluo"></param>
        /// <returns></returns>
        [HttpPost, Route("documento-poliza-cuv")]
        [ResponseType(typeof(ResultadoPeticion))]
        public async Task<HttpResponseMessage> DocumentoPolizaCuvAsyn(DatosValorAvaluo datosValorAvaluo)
        {
            ResultadoPeticion resultadoPeticion = new ResultadoPeticion();
            Contrasena auxiliar = new Contrasena();
            string contrasenaHash = auxiliar.ObtenerHashContrasena(datosValorAvaluo.password);

            Login login = this._servicioSeguridad.Autenticarporidusuarioseguridadints(datosValorAvaluo.usuario, contrasenaHash, false, false, null, null);
            if (login.TipoMensajeSeguridad == TipoMensajeSeguridad.Autenticado || login.TipoMensajeSeguridad == TipoMensajeSeguridad.SesionAnteriorSinCerrar)
            {
                resultadoPeticion = await _servicioSeguroCalidad.DocumentoPolizaCuv(datosValorAvaluo);
            }
            else
            {
                resultadoPeticion = new ResultadoPeticion();
                resultadoPeticion.codigo = "01";
                resultadoPeticion.resultado = "NOK";
                resultadoPeticion.descripcion = "Usuario/Password incorrectos.";
            }
            
            return Request.CreateResponse(HttpStatusCode.OK, resultadoPeticion);
        }
        #endregion

        #region PadronAseguradoras

        [HttpGet, Route("aseguradoras-relacion-comercial")]
        [ResponseType(typeof(ResultadoPaginado<List<Aseguradora>>))]
        //[AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerAseguradoraRelacionComercialConPaginadoAsync(int tamanioPagina, int pagina, string razonSocial = null, string noRegistroRUV = null, int? idDesarrollador = null, int? idAseguradora = null)
        {
            //var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.ObtenerAseguradoraRelacionComercialConPaginadoAsync(tamanioPagina, pagina, razonSocial, noRegistroRUV, idDesarrollador, idAseguradora);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("desarrollador-relacion-comercial")]
        [ResponseType(typeof(ResultadoPaginado<List<Aseguradora>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerDesarrolladoresRelacionComercialConPaginadoAsync(int tamanioPagina, int pagina, string razonSocial = null, string noRegistroRUV = null, string ordenVerificacion = null, string estatusCosto = null, int? idDesarrollador = null, int? idAseguradora = null)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.ObtenerDesarrolladoresRelacionComercialConPaginadoAsync(tamanioPagina, pagina, razonSocial, noRegistroRUV, ordenVerificacion, estatusCosto, idDesarrollador, idAseguradora);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("aseguradoras-padron-sinoferente-relacion-comercial")]
        [ResponseType(typeof(ResultadoPaginado<List<Aseguradora>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerAseguradoraEnPadronSinOferenteEnRelacionComercialConPaginadoAsync(int tamanioPagina, int pagina, int? idDesarrollador = null, string razonSocial = null, string noRegistroRUV = null)
        {
            var result = await this._servicioSeguroCalidad.ObtenerAseguradoraEnPadronSinOferenteEnRelacionComercialConPaginadoAsync(tamanioPagina, pagina, razonSocial, noRegistroRUV, idDesarrollador);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("aseguradoras-padron-sinoferente-relacion-comercial-defecto")]
        [ResponseType(typeof(bool))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> AsignarOVAseguradoraPorDefectoAsync(int idDesarrollador, string OV)
        {
            AsignacionPorDefectoAseguradora asignacionPorDefectoAseguradora = new AsignacionPorDefectoAseguradora() { idDesarrollador = idDesarrollador, ordenVerificacion = OV};
            var result = await _servicioSeguroCalidad.AsignarOVAseguradoraPorDefectoAsync(asignacionPorDefectoAseguradora);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="asignacionPorDefectoAseguradora"></param>
        /// <returns></returns>
        [HttpPost, Route("aseguradoras-padron-asignacion-defecto")]
        [ResponseType(typeof(bool))]
        public async Task<HttpResponseMessage> AsignacionOvAseguradoraPorDefectoAsync(AsignacionPorDefectoAseguradora asignacionPorDefectoAseguradora)
        {
            var result = await _servicioSeguroCalidad.AsignarOVAseguradoraPorDefectoAsync(asignacionPorDefectoAseguradora);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }
        
        [HttpGet, Route("aseguradoras-padron")]
        [ResponseType(typeof(ResultadoPaginado<List<Aseguradora>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerAseguradoraEnPadronConPaginadoAsync(int tamanioPagina, int pagina, string razonSocial = null, string noRegistroRUV = null)
        {
            var result = await this._servicioSeguroCalidad.ObtenerAseguradoraEnPadronConPaginadoAsync(tamanioPagina, pagina, razonSocial, noRegistroRUV);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("relacion-comercial")]
        [ResponseType(typeof(RelacionComercial))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerRelacionComercialAsync(int? idDesarrollador = null, int? idAseguradora = null)
        {
            var result = await this._servicioSeguroCalidad.ObtenerRelacionComercialAsync(idDesarrollador, idAseguradora, 1, null);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("relaciones-comerciales-desarrolladores")]
        [ResponseType(typeof(List<RelacionesComercialesOferente>))]
        public async Task<HttpResponseMessage> ObtenerRelacionesComercialesXDesarrolladorAsync()
        {
            var result = await this._servicioSeguroCalidad.ObtenerRelacionesComercialesXDesarrolladorAsync();

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpPost, Route("pasar-aseguradora-padron/{enPadronAseguradora}")]
        [ResponseType(typeof(bool))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> PasarAseguradoraPadronAsync(List<int> listaAseguradoras, int enPadronAseguradora)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.PasarAseguradoraPadronAsync(listaAseguradoras, enPadronAseguradora, usuario.IdUsuario.Value);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpPost, Route("guardar-relacion")]
        [ResponseType(typeof(int))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> GuardarRelacionComercialAsync(List<Aseguradora> listaRelacion)
        {
            var usuario = (CustomUserRuv)User;

            var listaIdRelacion = await this._servicioSeguroCalidad.GuardarRelacionComercialAsync(listaRelacion, usuario.IdUsuario, usuario.IdEmpresaInst);

            return Request.CreateResponse(HttpStatusCode.OK, listaIdRelacion);
        }

        [HttpPost, Route("actualizar-desarrollador-relacion")]
        [ResponseType(typeof(int))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ActualizarDesarroladorRelacionComercialAsync(RelacionComercial listaRelacion)
        {
            var usuario = (CustomUserRuv)User;

            var listaIdRelacion = await this._servicioSeguroCalidad.ActualizarRelacionComercialAsync(listaRelacion, usuario.IdUsuario);

            return Request.CreateResponse(HttpStatusCode.OK, listaIdRelacion);
        }


        [HttpPost, Route("actualizar-relacion-comercial")]
        [ResponseType(typeof(int))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ActualizarRelacionComercialAsync(RelacionComercial relacion)
        {
            var usuario = (CustomUserRuv)User;

            var idSancionGuardada = await this._servicioSeguroCalidad.ActualizarRelacionComercialAsync(relacion, usuario.IdUsuario);

            return Request.CreateResponse(HttpStatusCode.OK, idSancionGuardada);
        }


        [HttpPost, Route("actualizar-aseguradora-orden")]
        [ResponseType(typeof(bool))]
        public async Task<HttpResponseMessage> ActualizarAseguradoraOrdenPadronAsync(List<Aseguradora> listaAseguradoras)
        {
            var result = await this._servicioSeguroCalidad.ActualizarAseguradoraOrdenPadronAsync(listaAseguradoras);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpPost, Route("actualizar-relacion-comercial-rechazo")]
        [ResponseType(typeof(int))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ActualizarRelacionComercialXRechazoAsync(RelacionComercial relacion)
        {
            var usuario = (CustomUserRuv)User;

            var idSancionGuardada = await this._servicioSeguroCalidad.ActualizarRelacionComercialXRechazoAsync(relacion, usuario.IdUsuario);

            return Request.CreateResponse(HttpStatusCode.OK, idSancionGuardada);
        }

        /// <summary>
        /// Método que se ejecuta cuando una aseguradora es dada de baja.
        /// </summary>
        /// <param name="bajaAseguradora"></param>
        /// <returns></returns>
        [HttpPost, Route("baja-aseguradora")]
        [ResponseType(typeof(bool))]
        public async Task<HttpResponseMessage> BajaAseguradoraPadronAsync(BajaAseguradora bajaAseguradora)
        {
            var result = await this._servicioSeguroCalidad.PasarAseguradoraPadronAsync(bajaAseguradora.listaAseguradoras, bajaAseguradora.enPadronAseguradora, bajaAseguradora.idUsuario);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }
        #endregion

        #region sanciones


        [HttpGet, Route("aseguradoras-sanciones")]
        [ResponseType(typeof(List<Sancion>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerSancionesAseguradoraAsync(int? idSancion = null, int? idAseguradora = null)
        {
            var result = await this._servicioSeguroCalidad.ObtenerSancionesAsync(idSancion, idAseguradora);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }


        [HttpPost, Route("guardar-sancion")]
        [ResponseType(typeof(int))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> GuardarSancionAsync(Sancion sancion)
        {
            var usuario = (CustomUserRuv)User;

            var idSancionGuardada = await this._servicioSeguroCalidad.GuardarSancionAsync(sancion, usuario.IdUsuario);

            return Request.CreateResponse(HttpStatusCode.OK, idSancionGuardada);
        }

        [HttpPost, Route("actualizar-sancion")]
        [ResponseType(typeof(int))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ActualizarSancionAsync(Sancion sancion)
        {
            var usuario = (CustomUserRuv)User;

            var idSancionGuardada = await this._servicioSeguroCalidad.ActualizarSancionAsync(sancion, usuario.IdUsuario.Value);

            return Request.CreateResponse(HttpStatusCode.OK, idSancionGuardada);
        }

        [HttpGet, Route("cerrar-sanciones")]
        [ResponseType(typeof(List<Sancion>))]
        public async Task<HttpResponseMessage> CerrarSancionesAbiertasAsync()
        {
            var result = await this._servicioSeguroCalidad.CerrarSancionesAbiertasAsync();

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        #endregion

        //[HttpPost, Route("actualizar-aseguradora-orden/{idAseguradora}/turnoAsignacion/{turnoAsignacion}/ordenAsignacion/{ordenAsignacion}")]
        //[ResponseType(typeof(int))]        
        //public async Task<HttpResponseMessage> ActualizarAseguradoraOrdenPadronAsync(int idAseguradora, int? turnoAsignacion = null, int? ordenAsignacion = null)
        //{            
        //    var result = await this._servicioSeguroCalidad.ActualizarAseguradoraOrdenPadronAsync(idAseguradora, turnoAsignacion, ordenAsignacion);

        //    return Request.CreateResponse(HttpStatusCode.OK, result);
        //}

        #region Catalogos

        [HttpGet, Route("catalogo/estatus-pago-evaluacion")]
        [ResponseType(typeof(List<OpcionCatalogoSeguroCalidad>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerEstatusPagoEvaluacionRiesgoAsync()
        {
            var result = await this._servicioSeguroCalidad.ObtenerEstatusPagoEvaluacionRiesgoAsync();

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("catalogo/estatus-pagos")]
        [ResponseType(typeof(List<OpcionCatalogoSeguroCalidad>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerEstatusPagosAsync()
        {
            var result = await this._servicioSeguroCalidad.ObtenerEstatusPagosAsync();

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }
        #endregion

        #region Gestion Incidencias


        [HttpGet, Route("catalogo/tipo-incidencias")]
        [ResponseType(typeof(List<OpcionCatalogoSeguroCalidad>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerTipoIncidencias()
        {
            var result = await this._servicioSeguroCalidad.ObtenerTipoIncidenciasAsync();

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("catalogo/estatus-incidencias")]
        [ResponseType(typeof(List<OpcionCatalogoSeguroCalidad>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerEstatusIncidencias()
        {
            var result = await this._servicioSeguroCalidad.ObtenerEstatusIncidenciasAsync();

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("catalogo/clasificacion-incidencias")]
        [ResponseType(typeof(List<OpcionCatalogoSeguroCalidad>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerClasificacionIncidencias()
        {
            var result = await this._servicioSeguroCalidad.ObtenerClasificacionIncidenciasAsync();

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("catalogo/cobertura-afectada")]
        [ResponseType(typeof(List<OpcionCatalogoSeguroCalidad>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerCatalogoCoberturaAfectada()
        {
            var result = await this._servicioSeguroCalidad.ObtenerCatalogoCoberturaAfectadaAsync();

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("catalogo/clasificacion-riesgo")]
        [ResponseType(typeof(List<OpcionCatalogoSeguroCalidad>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerCatalogoClasificacionRiesgo()
        {
            var result = await this._servicioSeguroCalidad.ObtenerCatalogoClasificacioRiesgoAsync();

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("incidencias")]
        [ResponseType(typeof(ResultadoPaginado<List<IncidenciaGestion>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerIncidencias(int tamanioPagina, int pagina, string ordenVerificacion = null, string cuv = null, string desarrollador = null, string claveIncidencia = null,
                                                                    string idTipoIncidencia = null, string idEstatusIncidencia = null, string idClasificacionIncidencia = null, bool? esFechaRegistro = null, bool? esFechaAtencion = null,
                                                                    DateTime? fechaInicial = null, DateTime? fechaFinal = null, string conRiesgo = null, string idGrupoIncidencia = null)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.ObtenerIncidenciasGestionAsync(tamanioPagina, pagina, ordenVerificacion, cuv, desarrollador, claveIncidencia,
                idTipoIncidencia, idEstatusIncidencia, idClasificacionIncidencia, esFechaRegistro, esFechaAtencion, fechaInicial, fechaFinal, usuario.IdEntidad.ToString(),
                usuario.IdEmpresa.ToString(), conRiesgo, idGrupoIncidencia);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("incidencias-sin-paginado")]
        [ResponseType(typeof(List<IncidenciaGestion>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerIncidenciasSinPaginado(string ordenVerificacion = null, string cuv = null, string desarrollador = null, string claveIncidencia = null,
                                                                    string idTipoIncidencia = null, string idEstatusIncidencia = null, string idClasificacionIncidencia = null, bool? esFechaRegistro = null, bool? esFechaAtencion = null,
                                                                    DateTime? fechaInicial = null, DateTime? fechaFinal = null, string conRiesgo = null, string idGrupoIncidencia = null)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.ObtenerIncidenciasGestionSinPaginadoAsync(ordenVerificacion, cuv, desarrollador, claveIncidencia,
                idTipoIncidencia, idEstatusIncidencia, idClasificacionIncidencia, esFechaRegistro, esFechaAtencion, fechaInicial, fechaFinal, usuario.IdEntidad.ToString(),
                usuario.IdEmpresa.ToString(), conRiesgo, idGrupoIncidencia);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("catalogo/ordenes-verificacion")]
        [ResponseType(typeof(List<OpcionCatalogoSeguroCalidad>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerOrdenesVerificacion(string idEntidad = null, string idEmpresa = null)
        {
            var result = await this._servicioSeguroCalidad.ObtenerOrdenesVerificacionAsync(idEntidad, idEmpresa);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("catalogo/ov-diferencias")]
        [ResponseType(typeof(List<OpcionCatalogoSeguroCalidad>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerCatalogoOVDiferencias(int idVerificador)
        {
            var result = await this._servicioSeguroCalidad.ObtenerCatalogoOVDiferencias(idVerificador);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("catalogo/ordenes-verificacion-consulta")]
        [ResponseType(typeof(List<OpcionCatalogoSeguroCalidad>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerOrdenesVerificacionConsultaAsync(string noRuv = null, string razonSocial = null, string rfc = null)
        {
            var result = await this._servicioSeguroCalidad.ObtenerOrdenesVerificacionConsultaAsync(noRuv, razonSocial, rfc);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("datos-ov-incidencia")]
        [ResponseType(typeof(InformacionOVNuevaincidencia))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerInformacionOVNuevaIncidencia(string ordenVerificacion = null, string cuv = null)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.ObtenerInformacionOVNuevaIncidenciaAsync(ordenVerificacion, cuv, usuario.IdEntidad.ToString(), usuario.IdEmpresa.ToString());

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("cuv/{claveCuv}/validar")]
        [ResponseType(typeof(CuvValida))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ValidarCuvValida(string claveCuv = null)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.ValidarCuvValidaAsync(claveCuv, usuario.IdEntidad, usuario.IdEmpresa);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("orden-verificacion/{ordenVerificacion}/cuvs")]
        [ResponseType(typeof(List<CuvSeleccionada>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerCuvsXOrdenVerificacion(string ordenVerificacion = null, string cuv = null)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.ObtenerCuvsXOVAsync(ordenVerificacion, cuv);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }


        [HttpGet, Route("orden-verificacion/{ordenVerificacion}/cuvs/paginado")]
        [ResponseType(typeof(ResultadoPaginado<List<CuvSeleccionada>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerCuvsXOrdenVerificacionPaginado(int tamanioPagina, int pagina, string ordenVerificacion = null, string cuv = null)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.ObtenerCuvsXOVPaginadoAsync(tamanioPagina, pagina, ordenVerificacion, cuv);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpPost, Route("incidencia")]
        [ResponseType(typeof(bool))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> GuardarIncidencia(Incidencia incidencia)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.GuardarIncidenciaNotificacionAsync(incidencia, usuario.IdEntidad.Value);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }


        [HttpGet, Route("notificacion/{idIncidencia}/informacion-previa")]
        [ResponseType(typeof(ResultadoPaginado<List<CuvSeleccionada>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerInformacionPreviaNotificacion(string idIncidencia = null, string idVvGeneral = null)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.ObtenerInformacionPreviaNotificacionAsync(Convert.ToInt32(idIncidencia), Convert.ToInt32(idVvGeneral));

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("orden-verificacion/{ordenVerificacion}/cuvs-evaluacion-riesgos")]
        [ResponseType(typeof(List<CuvSeleccionada>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerCuvsXOrdenVerificacionEvaluacionRiesgos(string ordenVerificacion = null, string cuv = null)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.ObtenerCuvsXOVEvaluacionRiesgosAsync(ordenVerificacion, cuv);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }


        [HttpGet, Route("orden-verificacion/{ordenVerificacion}/cuvs-evaluacion-riesgos/paginado")]
        [ResponseType(typeof(ResultadoPaginado<List<CuvSeleccionada>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerCuvsXOrdenVerificacionEvaluacionRiesgosPaginado(int tamanioPagina, int pagina, string ordenVerificacion = null, string cuv = null)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.ObtenerCuvsXOVEvaluacionRiesgosPaginadoAsync(tamanioPagina, pagina, ordenVerificacion, cuv);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("cuv/{claveCuv}/validar-evaluacion-riesgos")]
        [ResponseType(typeof(CuvValida))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ValidarCuvValidaEvaluacionRiesgos(string claveCuv = null)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.ValidarCuvValidaEvaluacionRiesgosAsync(claveCuv, usuario.IdEntidad, usuario.IdEmpresa);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpPost, Route("evaluacion-riesgo")]
        [ResponseType(typeof(bool))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> GuardarEvaluacionRiesgos(EvaluacionRiesgo evaluacion)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.GuardarEvaluacionRiesgoAsync(evaluacion);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("incidencia/{idIncidencia}/detalle")]
        [ResponseType(typeof(DetalleIncidencia))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerDetalleIncidencia(int? idIncidencia, int? idVvGeneral)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.ObtenerDetalleIncidenciaAsync(idIncidencia, idVvGeneral, usuario.IdEntidad, usuario.IdEmpresa);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("datos-registro-mitigacion")]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerDatosRegistroMitigacion(int? idIncidencia)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.ObtenerDatosMitigacionAsync(idIncidencia);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpPost, Route("mitigacion")]
        [ResponseType(typeof(bool))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> GuardarMitigacionIncidencia(Mitigacion mitigacion)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.GuardarMitigacionIncidencia(mitigacion, usuario.IdEmpresaInst);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpPost, Route("cerrar/incidencia")]
        [ResponseType(typeof(bool))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> CerrarIncidencia(List<IncidenciaGestion> listaIncidencias)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.CerrarIncidenciaAsync(listaIncidencias, usuario.IdUsuario.Value, usuario.IdEmpresaInst, usuario.IdEntidad.Value);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpPost, Route("rechazar/mitigacion-incidencia")]
        [ResponseType(typeof(bool))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> RechazarMitigacion(List<IncidenciaGestion> listaIncidencias)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.RechazarMitigacionAsync(listaIncidencias);

            return Request.CreateResponse(HttpStatusCode.OK, true);
        }

        /*( RIch ) Gestión de incidencias Interno 02/04/2018/*/

        [HttpGet, Route("incidencias-Interno")]
        [ResponseType(typeof(ResultadoPaginado<List<IncidenciaGestion>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerIncidenciasInterno(int tamanioPagina, int pagina, string ordenVerificacion = null, string cuv = null, string desarrollador = null, string claveIncidencia = null,
                                                                       string idTipoIncidencia = null, string idEstatusIncidencia = null, string idClasificacionIncidencia = null, bool? esFechaRegistro = null, bool? esFechaAtencion = null,
                                                                       DateTime? fechaInicial = null, DateTime? fechaFinal = null, string conRiesgo = null, string verificador = null, string aseguradora = null)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.ObtenerIncidenciasInternoGestionAsync(tamanioPagina, pagina, ordenVerificacion, cuv, desarrollador, claveIncidencia,
                idTipoIncidencia, idEstatusIncidencia, idClasificacionIncidencia, esFechaRegistro, esFechaAtencion, fechaInicial, fechaFinal, usuario.IdEntidad.ToString(),
                usuario.IdEmpresa.ToString(), conRiesgo, verificador, aseguradora);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }



        [HttpGet, Route("incidencias-interno-sin-paginado")]
        [ResponseType(typeof(List<IncidenciaGestion>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerIncidenciasInternoSinPaginado(string ordenVerificacion = null, string cuv = null, string desarrollador = null, string claveIncidencia = null,
                                                              string idTipoIncidencia = null, string idEstatusIncidencia = null, string idClasificacionIncidencia = null, bool? esFechaRegistro = null, bool? esFechaAtencion = null,
                                                              DateTime? fechaInicial = null, DateTime? fechaFinal = null, string conRiesgo = null, string verificador = null, string aseguradora = null)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.ObtenerIncidenciasInternoGestionSinPaginadoAsync(ordenVerificacion, cuv, desarrollador, claveIncidencia,
                idTipoIncidencia, idEstatusIncidencia, idClasificacionIncidencia, esFechaRegistro, esFechaAtencion, fechaInicial, fechaFinal, usuario.IdEntidad.ToString(),
                usuario.IdEmpresa.ToString(), conRiesgo, verificador, aseguradora);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        #endregion

        #region Generacion Ficha
        /// <summary>
        /// 
        /// </summary>
        /// <param name="fichaPagoRiesgo"></param>
        /// <returns></returns>
        [HttpPost, Route("generacion-ficha-riesgo")]
        [ResponseType(typeof(int))]
        public async Task<HttpResponseMessage> GeneracionFichaRiesgo(FichaPagoRiesgo fichaPagoRiesgo)
        {
            var resultado = await this._servicioSeguroCalidad.GenerarFichaPagoEvaluacion(fichaPagoRiesgo);

            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="fichaPagoRiesgo"></param>
        /// <returns></returns>
        [HttpPost, Route("recalcular-costo-poliza")]
        [ResponseType(typeof(int))]
        public async Task<HttpResponseMessage> RecalcularCostoPoliza()
        {
            var resultado = await this._servicioSeguroCalidad.RecalcularCostoPoliza();

            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="fichaPagoRiesgo"></param>
        /// <returns></returns>
        [HttpPost, Route("recalcular-iva")]
        [ResponseType(typeof(int))]
        public async Task<HttpResponseMessage> RecalcularIva()
        {
            var resultado = await this._servicioSeguroCalidad.RecalcularIva();

            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="fichaPagoRiesgo"></param>
        /// <returns></returns>
        [HttpPost, Route("recalcular-costos-coniva")]
        [ResponseType(typeof(int))]
        public async Task<HttpResponseMessage> RecalcularCostosConIva()
        {
            var resultado = await this._servicioSeguroCalidad.RecalcularCostosConIva();

            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }

        #endregion

        #region Seleccion Aseguradora        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="idRuvAsis"></param>
        /// <param name="ordenVerificacion"></param>
        /// <param name="noContrato"></param>
        /// <param name="idEmpresaInstAseguradora"></param>
        /// <param name="idTipoAsignacion"></param>
        /// <param name="razonSocialAseguradora"></param>
        /// <returns></returns>
        [HttpGet, Route("ordenes-verificacion")]
        [ResponseType(typeof(ResultadoPaginado<List<OrdenDeVerificacion>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerOrdenesDeVerificacionAsync(int tamanioPagina, int pagina, int idRuvAsis, string ordenVerificacion = null, string noContrato = null, string idEmpresaInstAseguradora = null, int? idTipoAsignacion = null, string razonSocialAseguradora = null)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.ObtenerOrdenesDeVerificacionAsync(tamanioPagina, pagina, idRuvAsis, ordenVerificacion, noContrato, idEmpresaInstAseguradora, idTipoAsignacion, razonSocialAseguradora);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="ordenVerificacion"></param>
        /// <returns></returns>
        [HttpGet, Route("obtener-costo-evaluacion")]
        [ResponseType(typeof(string))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerCostoEvaluacionAsync(string ordenVerificacion)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.ObtenerCostoEvaluacionRiesgo(ordenVerificacion);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="ordenVerificacion"></param>
        /// <param name="idRUVAsis"></param>
        /// <returns></returns>
        [HttpGet, Route("validar-saldo-costo-evaluacion")]
        [ResponseType(typeof(string))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ValidarSaldoEvaluacionRiesgoAsync(string ordenVerificacion, int idRUVAsis)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.ValidarSaldoEvaluacionRiesgo(ordenVerificacion, idRUVAsis);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("obtener-relaciones-comerciales")]
        [ResponseType(typeof(ResultadoPaginado<List<RelacionComercial>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerRelacionesComercialesAsync(int tamanioPagina, int pagina, int idRuvAsis, string idEmpresaInst = null, string nombreRazonSocial = null)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.ObtenerRelacionesComercialesAsync(tamanioPagina, pagina, idRuvAsis, idEmpresaInst, nombreRazonSocial);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="relacionComercialOrden"></param>
        /// <returns></returns>
        [HttpPost, Route("guardar-relacion-comercial-orden")]
        [ResponseType(typeof(ResultadoPaginado<List<RelacionComercial>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> GuardarRelacionesComercialOrdenAsync(RelacionComercialOrden relacionComercialOrden)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.GuardarRelacionesComercialOrdenAsync(relacionComercialOrden.idOrdenVerificacion, relacionComercialOrden.idAseguradora, relacionComercialOrden.idRelacionComercial);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="ordenVerificacion"></param>
        /// <param name="idRuvAsIs"></param>
        /// <returns></returns>
        [HttpGet, Route("obtener-lista-viviendas")]
        [ResponseType(typeof(ResultadoPaginado<FichaPagoRiesgo>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerListaViviendasAsync(string ordenVerificacion, int idRuvAsIs)
        {
            var usuario = (CustomUserRuv)User;

            FichaPagoRiesgo fichaPagoRiesgo = new FichaPagoRiesgo();
            fichaPagoRiesgo.idEmpresaAsIs = idRuvAsIs;
            fichaPagoRiesgo.idOrdenVerificacion = ordenVerificacion;

            var listaViviendas = await this._servicioSeguroCalidad.ObtenerViviendasOrdenAsync(ordenVerificacion);
            fichaPagoRiesgo.precioViviendas = listaViviendas;


            return Request.CreateResponse(HttpStatusCode.OK, fichaPagoRiesgo);
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="idOrdenVerificacion"></param>
        /// <returns></returns>
        [HttpGet, Route("obtener-relacion-comercial-orden")]
        [ResponseType(typeof(DetalleSeleccionAseguradora))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerRelacionesComercialOrdenAsync(string idOrdenVerificacion)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.ObtenerRelacionComercialOrdenAsync(idOrdenVerificacion);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// Metodo para obtener los datos de contacto y dirección de una empresa
        /// </summary>
        /// <param name="idRuvAsIs">Recibe el id Empresa de RUV AsIs</param>
        /// <returns></returns>
        [HttpGet, Route("obtener-datos-contacto-direccion")]
        [ResponseType(typeof(DatosGeneralesEmpresa))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerDatosContactoDireccionAsync(int idRuvAsIs)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.ConsultarDatosGeneralesEmpresa(idRuvAsIs);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }
        
        /// <summary>
        /// Metodo para enviar el correo de confirmación cuando se paga una OV.
        /// </summary>
        /// <param name="envioCorreoConfirmacion">Parametro que recibe la orden, el id del oferente y el id de la aseguradora.</param>
        /// <returns></returns>
        [HttpPost, Route("enviar-correo-confirmacion")]
        [ResponseType(typeof(bool))]
        public async Task<HttpResponseMessage> EnviarCorreoConfirmacion(EnvioCorreoConfirmacion envioCorreoConfirmacion)
        {
            var result = await this._servicioSeguroCalidad.EnvioCorreoAseguradoraAsync(envioCorreoConfirmacion);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }



        /// <summary>
        /// Metodo para enviar el correo a la aseguradora de cuvs boqueadas
        /// </summary>
        /// <param name="ordenVerificacion">Parametro que recibe la orden de verficación.</param>
        /// <returns></returns>
        [HttpGet, Route("enviar-correo-cuvs-bloqueo")]
        [ResponseType(typeof(bool))]
        public async Task<HttpResponseMessage> EnviarCorreoAseguradoraCuvsBloqueadas(string ordenVerificacion)
        {
            var result = await this._servicioSeguroCalidad.EnviarCorreoAseguradoraCuvsBloqueadasAsync(ordenVerificacion);
            
            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        #endregion

        #region Generar PDF

        /*Document document = new Document();

        PdfWriter.GetInstance(document,

                  new FileStream("devjoker.pdf",

                         FileMode.OpenOrCreate));


        document.Open();

        document.Add(new Paragraph("Este es mi primer PDF al vuelo"));

        document.Close();*/
        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet, Route("generarPDF")]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> GenerarPDF()
        {
            //MemoryStream memStream = new MemoryStream();
            //Document document = new Document();
            //PdfWriter.GetInstance(document, memStream).CloseStream = false;


            //document.Open();

            //document.Add(new Paragraph("Este es mi primer PDF al vuelo"));

            //document.Close();

            //byte[] arreglo = memStream.ToArray();
            //memStream.Close();

            //List<DocumentoRuv> documentosCargados = new List<DocumentoRuv>();

            //var documento = this._servicioDocumento.AgregarYObtener(new DocumentoOferta
            //{
            //    rfcEmpresa = "AAAAAAAAAA", //TODO: Tomar de la identidad del usuario
            //    idCatalogoDocumento = 1,
            //    carpeta = "ejemplo",
            //    nombreArchivo = "PDFPRIMER.pdf",
            //    archivoStream = new MemoryStream(arreglo),
            //});

            //documentosCargados.Add(new DocumentoRuv
            //{
            //    IdDocumento = documento.idDocumento.Value,
            //    NombreArchivo = documento.nombreArchivo,
            //    UrlArchivo = documento.rutaArchivo
            //});

            //await this._servicioSeguroCalidad.EnviarCorreoCuvs();

            return Request.CreateResponse(HttpStatusCode.OK, true);
        }
        #endregion

        #region PagoDiferenciasOV

        [HttpGet, Route("obtener-pago-diferencia-OV")]
        [ResponseType(typeof(ResultadoPaginado<List<PagoDiferenciaOV>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerPagoDiferenciaOV(int tamanioPagina, int pagina, string ordenVerificacion, int? idEstatusPago = null)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.ObtenerPagoDiferenciaOV(tamanioPagina, pagina, ordenVerificacion, usuario.IdEmpresa.Value, idEstatusPago);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("obtener-consulta-pago-diferencia-OV")]
        [ResponseType(typeof(ResultadoPaginado<List<PagoDiferenciaOV>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerConsultaPagoDiferenciaOV(int tamanioPagina, int pagina, string ordenVerificacion, int? idEstatusPago = null, string idEmpresaInst = null)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.ObtenerConsultaPagoDiferenciaOV(tamanioPagina, pagina, ordenVerificacion, idEstatusPago, idEmpresaInst);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("agregar-pago-diferencia-OV")]
        [ResponseType(typeof(PagoDiferenciaOV))]
        public async Task<HttpResponseMessage> AgregarPagoDiferenciaOVAsync(string ordenVerificacion, string numeroFactura, string fechaEntregaFactura)
        {
            PagoDiferenciaOV pagoDiferenciaOV = new PagoDiferenciaOV { idOrdenVerificacion = ordenVerificacion, numeroFactura = numeroFactura, fechaEntregaFactura = Convert.ToDateTime(fechaEntregaFactura) };

            var result = await this._servicioSeguroCalidad.AgregarPagoDiferenciaOVAsync(pagoDiferenciaOV);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpPost, Route("actualizar-pago-diferencia-OV")]
        [ResponseType(typeof(ResultadoPeticion))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ActualizarPagoDiferenciaOVAsync(List<PagoDiferenciaOV> seleccionadas)
        {
            var usuario = (CustomUserRuv)User;

            ResultadoPeticion resultadoPeticion = await this._servicioSeguroCalidad.ActualizarPagoDiferenciaOVAsync(seleccionadas, usuario.IdRUVAsis.Value);

            return Request.CreateResponse(HttpStatusCode.OK, resultadoPeticion);
        }

        #endregion

        #region Procesos Aseguradora
        /// <summary>
        /// Metodo para generar la póliza de una vivienda con una aseguradora.
        /// </summary>
        /// <param name="peticionSolicitudPoliza">Objeto que recibe la orden de verificación y la cuv que se va a generar la póliza.</param>
        /// <returns></returns>
        //[HttpPost, Route("generar-documento-poliza")]
        //[ResponseType(typeof(RespuestaPoliza))]
        //public async Task<HttpResponseMessage> GenerarDocumentoPoliza(PeticionSolicitudPoliza peticionSolicitudPoliza)
        //{
        //    var resultado = await this._servicioSeguroCalidad.GenerarDocumentoPoliza(peticionSolicitudPoliza);

        //    return Request.CreateResponse(HttpStatusCode.OK, resultado);
        //}

        
        /// <summary>
        /// Metodo para generar la póliza de una vivienda con una aseguradora Producción.
        /// </summary>
        /// <returns></returns>
        [HttpPost, Route("generar-poliza-vivienda")]
        [ResponseType(typeof(bool))]
        public async Task<HttpResponseMessage> GenerarPolizaViviendas()
        {
            var resultado = false;
            for (int x = 1; x <= 6; x++){
                resultado = await this._servicioSeguroCalidad.GenerarPolizaVivienda(x);
            }
            
            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }
        

        /// <summary>
        /// 
        /// </summary>
        /// <param name="x"></param>
        /// <returns></returns>
        [HttpPost, Route("generar-poliza-vivienda-gmx")]
        [ResponseType(typeof(bool))]
        public async Task<HttpResponseMessage> GenerarPolizaViviendasGMX()
        {
            var resultado = false;
            int x = 3;
            resultado = await this._servicioSeguroCalidad.GenerarPolizaVivienda(x);
            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="x"></param>
        /// <returns></returns>
        [HttpPost, Route("generar-poliza-vivienda-lat")]
        [ResponseType(typeof(bool))]
        public async Task<HttpResponseMessage> GenerarPolizaViviendasLAT()
        {
            var resultado = false;
            int x = 5;
            resultado = await this._servicioSeguroCalidad.GenerarPolizaVivienda(x);
            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="x"></param>
        /// <returns></returns>
        [HttpPost, Route("generar-poliza-vivienda-spp")]
        [ResponseType(typeof(bool))]
        public async Task<HttpResponseMessage> GenerarPolizaViviendasSPP()
        {
            var resultado = false;
            int x = 6;
            resultado = await this._servicioSeguroCalidad.GenerarPolizaVivienda(x);
            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }

        /// <summary>
        /// Metodo para enviar el correo de confirmación cuando se validan los documentos.
        /// </summary>
        /// <returns></returns>
        [HttpPost, Route("enviar-correo-validacion-doc-proceso")]
        [ResponseType(typeof(bool))]
        public async Task<HttpResponseMessage> EnviarCorreoValidacionDocProceso(string id)
        {
            string idstr = await this._servicioSeguroCalidad.EnvioCorreoAseguradoraAsync(id); 

            return Request.CreateResponse(HttpStatusCode.OK, idstr);
        }
        /// <summary>
        /// Metodo para enviar el correo de confirmación cuando se validan los documentos.
        /// </summary>
        /// <returns></returns>
        [HttpPost, Route("enviar-correo-validacion-doc")]
        [ResponseType(typeof(bool))]
        public async Task<HttpResponseMessage> EnviarCorreoValidacionDoc()
        {
            await this._servicioSeguroCalidad.EnvioCorreoAseguradoraDocMinAsync();
            await this._servicioSeguroCalidad.EnvioCorreoAseguradoraReporteInicialAsync();
            await this._servicioSeguroCalidad.EnvioCorreoAseguradoraReporteDocumentalAsync();
            var resultado = await this._servicioSeguroCalidad.EnvioCorreoAseguradoraReporteHabitabilidadAsync();

            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }

        #endregion

        #region Asignacion Aseguradora Automatico

        /// <summary>
        /// Asignación de aseguradora automatica duerante los 90 días.
        /// </summary>
        /// <returns></returns>
        [HttpPost, Route("asingacion-aseguradora-automatico")]
        [ResponseType(typeof(bool))]
        public async Task<HttpResponseMessage> AsginarAseguradoraAutomatico()
        {
            //bool resultado = await _servicioSeguroCalidad.AsignacionAutomaticaAseguradoraAsync();

            //return Request.CreateResponse(HttpStatusCode.OK, resultado);

            return Request.CreateResponse(HttpStatusCode.OK, true);
        }
        #endregion

        #region Siniestro

        [HttpGet, Route("obtener-Aseguradoras-Siniestro")]
        [ResponseType(typeof(List<Aseguradora>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerAseguradoraSiniestros()
        {
            var result = await this._servicioSeguroCalidad.ObtenerAseguradoraSiniestros();

            return Request.CreateResponse(HttpStatusCode.OK, result);

        }

        [HttpGet, Route("obtener-TipoSiniestro")]
        [ResponseType(typeof(List<TipoSiniestro>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerTipoSiniestro()
        {
            var result = await this._servicioSeguroCalidad.ObtenerTipoSiniestro(null);

            return Request.CreateResponse(HttpStatusCode.OK, result);

        }

        [HttpGet, Route("obtener-TipoCoberturaSiniestro")]
        [ResponseType(typeof(List<TipoCoberturaSiniestro>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerTipoCoberturaSiniestro()
        {
            var result = await this._servicioSeguroCalidad.ObtenerTipoCoberturaSiniestro(null);

            return Request.CreateResponse(HttpStatusCode.OK, result);

        }

        [HttpGet, Route("obtener-EstatusSiniestro")]
        [ResponseType(typeof(List<EstatusSiniestro>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerEstatusSiniestro()
        {

            var result = await this._servicioSeguroCalidad.ObtenerEstatusSiniestro(null);

            return Request.CreateResponse(HttpStatusCode.OK, result);           

        }

        [HttpPost, Route("obtener-SiniestroRespuesta")]
        [ResponseType(typeof(List<SiniestroRespuestaAseguradora>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerSiniestroRespuestaAseguradora(SiniestroRespuestaAseguradora entity)
        {

            var result = await this._servicioSeguroCalidad.ObtenerSiniestroRespuestaAseguradora(entity);

            return Request.CreateResponse(HttpStatusCode.OK, result);

        }

        [HttpPost, Route("guardar-reporte-siniestro")]
        [ResponseType(typeof(int))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<JsonResponse> GuardarReporteSiniestro(SiniestroCompleto entity)
        {
            JsonResponse response = new JsonResponse();

            try
            {
                var result = await this._servicioSeguroCalidad.GuardarReporteSiniestro(entity);

                response.code = (int)HttpStatusCode.OK;

                response.model = result; 
            }
            catch (Exception e)
            {
                response.code = 500;
                response.message = "Error: " + e.Message.ToString();
                response.codigo = "500";
            }
            
            return response;          
        }


        [HttpGet, Route("obtener-parametro")]
        [ResponseType(typeof(ParametrosConfigs))]
        public async Task<HttpResponseMessage> ObtenerParametro(string clave)
        {
            ParametrosConfigs result = await this._servicioSeguroCalidad.ObtenerParametroCSIOAsync(clave);
            
            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        #endregion


        #region Pago Aseguradora



        [HttpPost, Route("carga-datos-seguro-calidad")]
        [ResponseType(typeof(int))]
        public async Task<HttpResponseMessage> CargarDatos()
        {
            var result = await this._servicioSeguroCalidad.CargarDatosFichaPagoPolizaAsync();

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }


        /// <summary>
        /// Servicio para la confirmación de pago a la aseguradora
        /// </summary>
        /// <param name="confirmacionPagoAseguradora"></param>
        /// <returns></returns>
        [HttpPost, Route("confirmacion-pago-aseguradora")]
        [ResponseType(typeof(Respuesta))]
        public async Task<HttpResponseMessage> ConfirmacionPagoAseguradora(ConfirmacionPagoAseguradora confirmacionPagoAseguradora)
        {
            Respuesta result = await this._servicioSeguroCalidad.ConfirmacionPagosAseguradoraAsync(confirmacionPagoAseguradora);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// Envio de pagos para aseguradoras Póliza
        /// </summary>
        /// <returns></returns>
        [HttpPost, Route("envio-pago-aseguradora-poliza")]
        [ResponseType(typeof(int))]
        public async Task<HttpResponseMessage> EnvioPagoAseguradoraPoliza()
        {
            int result = await this._servicioSeguroCalidad.EnviarPagosAseguradoraPolizaAsync();
                
            return Request.CreateResponse(HttpStatusCode.OK, result);
        }
        /// <summary>
        ///  Envio de pagos para aseguradoras Evaluación
        /// </summary>
        /// <returns></returns>
        [HttpPost, Route("envio-pago-aseguradora-evaluacion")]
        [ResponseType(typeof(int))]
        public async Task<HttpResponseMessage> EnvioPagoAseguradoraEvaluacion()
        {
            int result = await this._servicioSeguroCalidad.EnviarPagosAseguradoraEvaluacionAsync();

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        #endregion|

        #region Prepago

        /// <summary>
        /// Servicio para la confirmación de pago a la aseguradora
        /// </summary>
        /// <param name="confirmacionPagoAseguradora"></param>
        /// <returns></returns>
        [HttpPost, Route("validar-prepapago")]
        [ResponseType(typeof(Respuesta))]
        public async Task<HttpResponseMessage> ValidarMontoPrepago(ConfirmarPrepago confirmarPrepago, bool esCargoPrepago = false)
        {
            Respuesta result = await this._servicioSeguroCalidad.ValidarMontoPrepago(confirmarPrepago, esCargoPrepago);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }


        /// <summary>
        /// Servicio para la confirmación de pago a la aseguradora
        /// </summary>
        /// <param name="confirmacionPagoAseguradora"></param>
        /// <returns></returns>
        [HttpPost, Route("confirmar-prepago")]
        [ResponseType(typeof(Respuesta))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ConfirmarPrepago(CargoPrepago cargoPrepago)
        {
            var usuario = (CustomUserRuv)User;

            Respuesta result = await this._servicioSeguroCalidad.ConfirmarPrepago(usuario.IdUsuario, cargoPrepago); 

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        //// AAS 19022024 Se agrega función que realiza la afectación de saldo de forma manual de la póliza.
        ///// <summary>
        ///// 
        ///// </summary>
        ///// <param name="afectacionSaldo"></param>
        ///// <returns></returns>
        //[HttpPost, Route("realizar-afectacion-poliza")]
        //[ResponseType(typeof(Respuesta))]
        //public async Task<HttpResponseMessage> AfectarSaldoPoliza(AfectacionSaldo afectacionSaldo)
        //{
        //    Respuesta result = await this._servicioSeguroCalidad.AfectarSaldoPoliza(afectacionSaldo);

        //    return Request.CreateResponse(HttpStatusCode.OK, result);
        //}

        //// AAS 19022024 Se agrega función que realiza la afectación de saldo de forma manual de la diferencia.
        ///// <summary>
        ///// 
        ///// </summary>
        ///// <param name="afectacionSaldo"></param>
        ///// <returns></returns>
        //[HttpPost, Route("realizar-afectacion-diferencia")]
        //[ResponseType(typeof(Respuesta))]
        //public async Task<HttpResponseMessage> AfectarSaldoDiferencia(AfectacionSaldo afectacionSaldo)
        //{
        //    Respuesta result = await this._servicioSeguroCalidad.AfectarSaldoDiferencia(afectacionSaldo);

        //    return Request.CreateResponse(HttpStatusCode.OK, result);
        //}
        

        #endregion


        [HttpPost, Route("guardar-factura-verificador")]
        [ResponseType(typeof(int))]
        //[AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> GuardarDocumentoFactura(GuardarDocumentoPDF pdf)
        {
            var result = await this._servicioSeguroCalidad.GuardarDocumentoFactura(pdf.ordenVerificacion, pdf.idDocumentoFactura);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="relacionComercialOrden"></param>
        /// <param name="idRUVAsIs"></param>
        /// <param name="idUsuario"></param>
        /// <param name="monto"></param>
        /// <returns></returns>
        [HttpPost, Route("guardar-prepago-relacion-comercial-orden")]
        [ResponseType(typeof(Respuesta))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ConfirmarPrepagoEvaluacion(RelacionComercialOrden relacionComercialOrden)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.GuardarRelacionesComercialPrepagoOrdenAsync(relacionComercialOrden.idOrdenVerificacion, relacionComercialOrden.idAseguradora, relacionComercialOrden.idRelacionComercial, relacionComercialOrden.idRuvAsIs, relacionComercialOrden.idUsuario);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }




        #region Solictra Pagos Seguro Calidad

        [HttpPost, Route("solicitar-pago-poliza-aseguradora")]
        [ResponseType(typeof(JsonResponse))]
        public async Task<HttpResponseMessage> solicitarPagoPolizaAseguradora(ObtenerPolizaSCxPagar entity)
        {

            RespuestaERP respuestaERP = await this._servicioSeguroCalidad.solicitarPagoRiesgoAseguradora(entity);

            return Request.CreateResponse(HttpStatusCode.OK, respuestaERP);

        }

        [HttpPost, Route("solicitar-pago-riesgo-aseguradora")]
        [ResponseType(typeof(JsonResponse))]
        public async Task<HttpResponseMessage> solicitarPagoRiesgoAseguradora(ObtenerPolizaSCxPagar entity)
        {
            RespuestaERP respuestaERP = await this._servicioSeguroCalidad.solicitarPagoRiesgoAseguradora(entity);

            return Request.CreateResponse(HttpStatusCode.OK, respuestaERP);
        }

        [HttpPost, Route("consultar-pago-aseguradora")]
        [ResponseType(typeof(JsonResponse))]
        public async Task<HttpResponseMessage> consultaPagoAsegfuradora(ObtenerPolizaSCxPagar entity)
        {
            List<ConsultaDatosPolizaAseguradoraDTO> respuestaERP = await this._servicioSeguroCalidad.consultaPagoAsegfuradora(entity);

            return Request.CreateResponse(HttpStatusCode.OK, respuestaERP);
        }


        #endregion

        [HttpGet, Route("obtener-aseguradoras")]
        [ResponseType(typeof(List<Aseguradora>))]
        public async Task<HttpResponseMessage> ObtenerAseguradorasAsync()
        {
            var result = await this._servicioSeguroCalidad.ObtenerAseguradorasAsync();

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpPost, Route("enviar-ordenes-aseguradora")]
        [ResponseType(typeof(ResultadoPeticionOV))]
        public async Task<HttpResponseMessage> EnviarOrdenesAseguradora()
        {
            ResultadoPeticionOV resultadoPeticion = await _servicioSeguroCalidad.EnviarOrdenesAseguradora();

            return Request.CreateResponse(HttpStatusCode.OK, resultadoPeticion);
        }


        /// <summary>
        /// Servicio para la confirmación de pago a la aseguradora
        /// </summary>
        /// <param name="confirmacionPagoAseguradora"></param>
        /// <returns></returns>
        [HttpPost, Route("confirmacion-pago-aseguradora-reenvio")]
        [ResponseType(typeof(bool))]
        public async Task<HttpResponseMessage> ConfirmacionPagoAseguradoraReenvio()
        {
            bool result = await this._servicioSeguroCalidad.ConfirmacionPagosAseguradoraReenvioAsync();

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        #region ConsultaPagosAseguradora
        /// <summary>
        /// Consultar pagos aseguradora
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="ordenVerificacion"></param>
        /// <param name="cuv"></param>
        /// <param name="idOferente"></param>
        /// <param name="idAseguradora"></param>
        /// <returns></returns>
        [HttpGet, Route("consultar-pagos-aseguradora")]
        [ResponseType(typeof(ResultadoPaginado<List<PagosAseguradora>>))]
        public async Task<HttpResponseMessage> ConsultarPagosAseguradoraPaginado(int tamanioPagina, int pagina, string ordenVerificacion = null, string cuv = null, string idOferente = null, string idAseguradora = null)
        {
            var result = await this._servicioSeguroCalidad.ObtenerPagosAseguradoraPaginado(tamanioPagina, pagina, ordenVerificacion, cuv, idOferente, idAseguradora);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }


        /// <summary>
        /// Descargar Pagos Aseguradora Excel
        /// </summary>
        /// <param name="idEmpresa"></param>
        /// <param name="ordenVerificacion"></param>
        /// <param name="cuv"></param>
        /// <param name="idOferente"></param>
        /// <param name="idAseguradora"></param>
        /// <returns></returns>
        [HttpGet, Route("descargar-pagos-aseguradora-excel")]
        [ResponseType(typeof(ResultadoPaginado<List<PagosAseguradora>>))]
        public async Task<HttpResponseMessage> DescargarPagosAseguradoraExcel(int idEmpresa, string ordenVerificacion = null, string cuv = null, string idOferente = null, string idAseguradora = null)
        {
            string urlArchivo = await this._servicioSeguroCalidad.ObtenerPagosAseguradoraExcel(idEmpresa, ordenVerificacion, cuv, idOferente, idAseguradora);

            return Request.CreateResponse(HttpStatusCode.OK, urlArchivo);
        }
        #endregion


        #region REPORTEASEGURADORA

        [HttpPost, Route("obtener-reporteurl-PER-PPS")]
        [ResponseType(typeof(Respuesta))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerReporteURLPERPPS(RequestGenExcelPERPPS req)
        {
            var resp = new ResponseGenExcelPERPPS();
            var usuario = (CustomUserRuv)User;
            resp.urlArchivoReporte = await this._servicioSeguroCalidad.ObtenerReporteAseguradoraExcelPPSPER(req, usuario.rfcEmpresa);

            return Request.CreateResponse(HttpStatusCode.OK, resp);
        }

        #endregion



        #region devolucionesYInterno

        /// <summary>
        /// Consulta Devoluciones Paginado
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet, Route("consulta-devoluciones-paginado")]
        [ResponseType(typeof(ResultadoPaginado<List<ResponseConsultaDevoluciones>>))]
        public async Task<HttpResponseMessage> ObtenerConsultadevolucionesPag(int tamanioPagina, int pagina, double idOferente, string ordenVerificacion = null, string cuv = null, string fechaInicio = null, string fechaFin = null)
        {
            //var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.ConsultaDevolucionesPaginados(tamanioPagina, pagina, ordenVerificacion, cuv, idOferente, fechaInicio, fechaFin);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// Consulta Devoluciones InternoPaginado
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpGet, Route("consulta-devoluciones-Interno-paginado")]
        [ResponseType(typeof(ResultadoPaginado<List<ResponseConsultaDevolucionesInterno>>))]
        public async Task<HttpResponseMessage> ObtenerConsultadevolucionesInternoPag(int tamanioPagina, int pagina, string idDesarrollador = null, string ordenVerificacion = null, string cuv = null, int? idEstatusDevolucion = null, string fechaInicio = null, string fechaFin = null)
        {
            //var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.ConsultaDevolucionesInternoPaginados(tamanioPagina, pagina, ordenVerificacion, cuv, idDesarrollador, idEstatusDevolucion, fechaInicio, fechaFin);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// Obtener Reporte Excel Consulta Devoluciones
        /// </summary>
        /// <param name="idOferente"></param>
        /// <param name="ordenVerificacion"></param>
        /// <param name="cuv"></param>
        /// <param name="fechaInicio"></param>
        /// <param name="fechaFin"></param>
        /// <returns></returns>
        [HttpGet, Route("obtener-reporteurl-consulta-devoluciones")]
        [ResponseType(typeof(string))]
        public async Task<HttpResponseMessage> ObtenerReporteURLconsultadevoluciones(int idEmpresa, double idOferente, string ordenVerificacion = null, string cuv = null, string fechaInicio = null, string fechaFin = null)
        {
            var urlArchivoReporte = await this._servicioSeguroCalidad.ObtenerReporteConsultaDevolucionesExcel(idEmpresa, ordenVerificacion, cuv, idOferente, fechaInicio, fechaFin);

            return Request.CreateResponse(HttpStatusCode.OK, urlArchivoReporte);
        }

        /// <summary>
        /// Obtener Reporte Excel Consulta Devoluciones Interno
        /// </summary>
        /// <param name="idDesarrollador"></param>
        /// <param name="ordenVerificacion"></param>
        /// <param name="cuv"></param>
        /// <param name="idEstatusDevolucion"></param>
        /// <param name="fechaInicio"></param>
        /// <param name="fechaFin"></param>
        /// <returns></returns>
        [HttpGet, Route("obtener-reporteurl-consulta-devoluciones-interno")]
        [ResponseType(typeof(string))]
        public async Task<HttpResponseMessage> ObtenerReporteURLconsultadevolucionesInterno(int idEmpresa, string idDesarrollador = null, string ordenVerificacion = null, string cuv = null, int? idEstatusDevolucion = null, string fechaInicio = null, string fechaFin = null)
        {
            var urlArchivoReporte = await this._servicioSeguroCalidad.ObtenerReporteConsultaDevolucionesInternoExcel(idEmpresa, ordenVerificacion, cuv, idDesarrollador, idEstatusDevolucion, fechaInicio, fechaFin);

            return Request.CreateResponse(HttpStatusCode.OK, urlArchivoReporte);
        }

        #endregion



        #region SolicitudDevoucion

        /// <summary>
        /// COnsulta Devolución Póliza Paginado
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="idOferente"></param>
        /// <param name="ordenVerificacion"></param>
        /// <param name="cuv"></param>
        /// <param name="fechaInicio"></param>
        /// <param name="fechaFin"></param>
        /// <returns></returns>
        [HttpGet, Route("consulta-devolucion-poliza-paginado")]
        [ResponseType(typeof(ResultadoPaginado<List<DevolucionPoliza>>))]
        public async Task<HttpResponseMessage> ConsultaDevolucionPolizaPaginado(int tamanioPagina, int pagina, double idOferente, string ordenVerificacion = null, string cuv = null, string fechaInicio = null, string fechaFin = null)
        {
            var result = await this._servicioSeguroCalidad.ConsultaDevolucionPolizaPaginado(tamanioPagina, pagina, ordenVerificacion, cuv, idOferente, fechaInicio, fechaFin);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// Solicitar Devolución Póliza
        /// </summary>
        /// <param name="listaDevolucion"></param>
        /// <returns></returns>
        [HttpPost, Route("solicitar-devolucion-poliza")]
        [ResponseType(typeof(List<int>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> SolicitarDevolucionPoliza(List<DevolucionPoliza> listaDevolucion)
        {
            var user = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.SolicitarDevolucionPoliza(listaDevolucion, user.IdEmpresa.Value);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// Consulta Devolución Evaluación Paginado
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="idOferente"></param>
        /// <param name="ordenVerificacion"></param>
        /// <param name="cuv"></param>
        /// <param name="fechaInicio"></param>
        /// <param name="fechaFin"></param>
        /// <returns></returns>
        [HttpGet, Route("consulta-devolucion-evaluacion-paginado")]
        [ResponseType(typeof(ResultadoPaginado<List<DevolucionEvaluacion>>))]
        public async Task<HttpResponseMessage> ConsultaDevolucionEvaluacionPaginado(int tamanioPagina, int pagina, double idOferente, string ordenVerificacion = null, string cuv = null, string fechaInicio = null, string fechaFin = null)
        {
            var result = await this._servicioSeguroCalidad.ConsultaDevolucionEvaluacionPaginado(tamanioPagina, pagina, ordenVerificacion, cuv, idOferente, fechaInicio, fechaFin);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// Solicitar Devolución Evaluación
        /// </summary>
        /// <param name="listaDevolucion"></param>
        /// <returns></returns>
        [HttpPost, Route("solicitar-devolucion-evaluacion")]
        [ResponseType(typeof(List<int>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> SolicitarDevolucionEvaluacion(List<DevolucionEvaluacion> listaDevolucion)
        {
            var user = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.SolicitarDevolucionEvaluacion(listaDevolucion, user.IdEmpresa.Value);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }
        #endregion


        [HttpPost, Route("proceso-confirmacion-pago-verificador")]
        [ResponseType(typeof(bool))]
        public async Task<HttpResponseMessage> ConfirmarPagoVerificador()
        {
            var result = await this._servicioSeguroCalidad.ObtenerDatosConfirmacionAsync();

            return Request.CreateResponse(HttpStatusCode.OK, true);
        }

        [HttpGet, Route("obtener-reporte-pago-plataforma")]
        [ResponseType(typeof(string))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerReportePagoPlataforma(string fecha)
        {
            var usuario = (CustomUserRuv)User;

            var url = await this._servicioSeguroCalidad.ObtenerCertificadosAseguradoras(fecha, usuario.rfcEmpresa);

            return Request.CreateResponse(HttpStatusCode.OK, url);
        }

        [HttpPost, Route("consultar-afiliado-canadevi")]
        [ResponseType(typeof(int))]
        //[AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> consultarAfiliadoCanadevi(string rfc)
        {

            //CallService<string> proxy = new CallService<string>();

            //var respuesta = await proxy.GetEntity("https://4hriirpxy4.execute-api.us-east-1.amazonaws.com/canadevi-prod/afiliado/", rfc);


            var respuesta = await this._servicioSeguroCalidad.consultarAfiliadoCanadevi(rfc);

            return Request.CreateResponse(HttpStatusCode.OK, respuesta);
        }


        /// <summary>
        /// Metodo para obtener los datos para la pantalla de estado de cuenta
        /// </summary>
        /// <param name="idEmpresa"></param>
        /// <returns></returns>
        [HttpGet, Route("obtener-datos-estado-cuenta")]
        [ResponseType(typeof(EstadoDeCuenta))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerDatosEmpresa(int idEmpresa)
        {
            EstadoDeCuenta estadoDeCuenta = await this._servicioSeguroCalidad.ObtenerDatosEmpresaAsync(idEmpresa, null);

            return Request.CreateResponse(HttpStatusCode.OK, estadoDeCuenta);
        }

        /// <summary>
        /// Metodo para obtener los saldos para la pantalla de estado de cuenta
        /// </summary>
        /// <param name="idEmpresa"></param>
        /// <returns></returns>
        [HttpGet, Route("obtener-saldos-estado-cuenta")]
        [ResponseType(typeof(SaldosEstadoDeCuenta))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerSaldosEmpresa(int idEmpresa)
        {
            SaldosEstadoDeCuenta estadoDeCuenta = await this._servicioSeguroCalidad.ObtenerSaldosEmpresaAsync(idEmpresa);

            return Request.CreateResponse(HttpStatusCode.OK, estadoDeCuenta);
        }


        /// <summary>
        /// Metodo para obtener los movimientos de estado de cuenta para la pantalla de estado de cuenta
        /// </summary>
        /// <param name="idEmpresa"></param>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <returns></returns>
        [HttpGet, Route("obtener-movimientos-estado-cuenta")]
        [ResponseType(typeof(ResultadoPaginado<List<MovimientosEstadoDeCuenta>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerMovimientosEstadoDeCuentaEmpresa(int tamanioPagina, int pagina, int idEmpresa)
        {
            var estadoDeCuenta = await this._servicioSeguroCalidad.ObtenerMovimientosEmpresaAsync(tamanioPagina, pagina, idEmpresa);

            return Request.CreateResponse(HttpStatusCode.OK, estadoDeCuenta);
        }

    }
}
 
