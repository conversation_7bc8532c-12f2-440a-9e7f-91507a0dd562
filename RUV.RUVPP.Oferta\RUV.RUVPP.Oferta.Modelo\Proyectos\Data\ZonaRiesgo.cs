﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.Proyectos.Data
{
    public class ZonaRiesgo
    {
        public byte? idRiesgoOferta { get; set; }
        public string Nombre { get; set; }
        public string Descripcion { get; set; }
        public DateTime? FechaRegistro { get; set; }
        public DateTime? FechaActualizacion { get; set; }
        public bool? activo { get; set; }
        public string solucionMitigarRiesgo { get; set; }
        public bool esSeleccionada { get; set; }
        public bool esActualizacion { get; set; }
    }
}
