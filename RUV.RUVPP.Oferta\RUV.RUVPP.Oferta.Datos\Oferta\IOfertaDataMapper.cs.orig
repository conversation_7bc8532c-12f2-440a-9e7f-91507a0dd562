﻿using RUV.RUVPP.Oferta.Comun.Modelo;
using RUV.RUVPP.Oferta.Modelo.Historico.Data;
using RUV.RUVPP.Oferta.Modelo.Oferta.Api;
using RUV.RUVPP.Oferta.Modelo.Oferta.Data;
using RUV.RUVPP.Oferta.Modelo.Proyectos;
using RUV.RUVPP.Oferta.Modelo.Proyectos.Data;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Datos.Oferta
{
    public interface IOfertaDataMapper : IDisposable
    {
        Task<int> GuardarOfertaViviendaAsync(int idProyecto, string nombre, string oferta);
        Task<int> GuardarDetalleOfertaViviendaAsync(OfertaVivienda ofertaVivienda);
        Task<int>  ActualizarOfertaViviendaAsync(OfertaVivienda ofertaVivienda);
        Task<int> ActualizarDetalleOfertaViviendaAsync(OfertaVivienda ofertaVivienda);
        Task EliminarOfertaParcialAsync(int idParcialOferta);
        Task GuardarOfertaViviendaAsync(int idVivienda, int idOferta);
        Task EliminarOfertaViviendaAsync(int idVivienda, int idOferta);
        Task GuardarLicenciaFactibilidadAsync(List<LicenciaFactibilidad> licenciasFactibilidades, int idOferta);
        Task ActualizarLicenciaFactibilidadAsync(List<LicenciaFactibilidad> licenciasFactibilidades, int idOferta);
        Task EliminarLicenciaFactibilidadAsync(List<LicenciaFactibilidad> licenciasFactibilidades, int idOferta);
        Task GuardarDocumentoxOfertaViviendaAsync(int idOfertaVivienda, int idDocumento);
        Task EliminarDocumentoxOfertaViviendaAsync(int idOfertaVivienda, int idDocumento);
        Task<int> GuardarPrototipoxOfertaViviendaAsync(int idOfertaVivienda, int idPrototipo);
        Task EliminarPrototipoxOfertaViviendaAsync(int idOfertaVivienda, int idPrototipo);
        Task<int> ObtenerPrototipoxOfertaViviendaAsync(int idOfertaVivienda, int idPrototipo);
        Task<int> GuardarDocumentoxPrototipoxOfertaViviendaAsync(int idPrototipoxOfertaVivienda, int idDocumento);
        Task<int> EliminarDocumentoxPrototipoxOfertaViviendaAsync(int idPrototipoxOfertaVivienda, int idDocumento);
        Task GuardarOfertaViviendaxRiesgoOfertaAsync(ViviendaRiesgoOferta viviendariesgoOferta);
        Task GuardarDirectorResponsableObraxOfertaViviendaAsync(int idDRO, int idOfertaVivienda);
        Task GuardarPromotorExternoxOfertaViviendaAsync(int idPromotorExterno, int idOfertaVivienda);
        Task EliminarPromotorExternoxOfertaViviendaAsync(int idPromotorExterno, int idOfertaVivienda);
        Task GuardarPropietarioTerrenoxOfertaViviendaAsync(int idPropertarioTerreno, int idOfertaVivienda);
        Task GuardarZonasRiesgoAsync(ZonaRiesgo[] viviendaRiesgoOferta, int idOferta);
        Task ActualizarZonasRiesgoAsync(ZonaRiesgo[] viviendaRiesgoOferta, int idOferta);
        Task EliminarZonaRiesgoAsync(ZonaRiesgo[] viviendaRiesgoOferta, int idOferta);
        Task ActualizarOfertaViviendaxRiesgoOfertaAsync(ViviendaRiesgoOferta viviendariesgoOferta);
        Task EliminarOfertaViviendaxRiesgoOfertaAsync(ViviendaRiesgoOferta viviendariesgoOferta);
        Task<Tuple<int, List<ViviendaSinAsignar>>> ObtenerVivendaSinAsignarRegistroAsync(int idProyecto, int tamanioPagina, int pagina, int? idOferta);
        Task<Tuple<int, List<ViviendaSinAsignar>>> ObtenerVivendaSinAsignarActualizacionAsync(int tamanioPagina, int pagina, int? idOferta);
        Task<List<ViviendaPrototipo>> ObtenerVivienasPrototipoAsync(int idProyecto, int? idOferta);

        /// <summary>
        ///
        /// </summary>
        /// <param name="idOferta"></param>
        /// <returns></returns>
        Task<Modelo.Oferta.Api.Oferta> ObtenerOfertaPorIdAsync(int idOferta);
        Task<OfertaVivienda> ObtenerOfertaAsync(int idOferta);
        Task<OfertaVivienda> ObtenerOfertaDetalleAsync(int idOferta);

        Task<List<Modelo.Oferta.Api.Oferta>> ObtenerOfertasxProyectoAsync(int idProyecto);

        /// <summary>
        ///
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="idOferta"></param>
        /// <param name="nombreProyecto"></param>
        /// <returns></returns>
        Task<Tuple<int, List<Modelo.Oferta.Api.Oferta>>> ObtenerOfertasFiltradoAsync(int tamanioPagina, int pagina, int? idOferta, string nombreProyecto, int? idEmpresa);
        Task<OfertaVivienda> ObtenerDatosGeneralesAsync(int idOferta);
        Task<List<int>> ObtenerPromotorVendedorxOfertaAsync(int idOferta);
        Task<List<int>> ObtenerPropietarioTerrenoxOfertaAsync(int idOferta);
        Task<List<int>> ObtenerDROxOfertaAsync(int idOferta);
        Task<List<ZonaRiesgo>> ObtenerZonasRiesgoAsync(int idOferta);
        Task<List<DocumentoRuv>> ObtenerDocumentoxOfertaAsync(int idOferta, TiposDocumento dictamenRiesgoProyecto);
        Task<List<LicenciaFactibilidad>> ObtenerLicenciasFactibilidadAsync(int idOferta);
        Task<List<DocumentoComplementario>> ObtenerDocumentosComplementariosAsync(int idOferta);
        Task<List<PrototipoVivienda>> ObtenerDocumentosPrototiposAsync(int idOferta);
        Task<List<PrototipoCatalogo>> ObtenerCatalogoPrototiposAsync();
        Task<List<Documento>> ObtenerCatalogoDocumentosComplementariosAsync();
        Task<Dictionary<int, string>> ObtenerProyectosOfertaAsync(int idEmpresa);
        Task<int> DesvincularViviendaAsync(int idOfertaVivienda);
        Task<bool> ActualizarEstatusOfertaViviendaAsync(int idOfertaVivienda, int idEstatusOfertaVivienda);
        Task<bool> ActualizarEstatusViviendaAsync(int idOfertaVivienda, int idEstatusVivienda);
        /// <summary>
        /// 
        /// </summary>
        /// <param name="idProyecto"></param>
        /// <returns></returns>
        Task<List<Vivienda>> ObtenerVivendasPorIdProyectoAsync(int idProyecto);

<<<<<<< HEAD
        Task<List<Vivienda>> ObtenerViviendasPorOfertaViviendaAsync(int idOfertaVivienda);
=======
        /// <summary>
        /// Obtiene el listado de ordenes de trabajo para una oferta por idOferta
        /// </summary>
        /// <param name="idOfertaVivienda">Identificador de la oferta</param>
        /// <param name="tamanioPagina">Tamaño de pagina</param>
        /// <param name="pagina">Pagina</param>
        /// <returns>Lista de ordenes de trabajo de la oferta</returns>
        Task<Tuple<int, List<OrdenTrabajoOferta>>> ObtenerOrdenesTrabajoPorOfertaPaginadoAsync(int idOfertaVivienda, int tamanioPagina, int pagina);
>>>>>>> b31489300476573d134fc4a8438001685ad8b685
    }
}