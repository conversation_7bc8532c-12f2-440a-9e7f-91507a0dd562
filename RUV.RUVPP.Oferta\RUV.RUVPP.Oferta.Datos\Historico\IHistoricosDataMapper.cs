﻿using RUV.RUVPP.Oferta.Modelo.Historico.Data;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Datos.Historico
{
    /// <summary>
    /// 
    /// </summary>
    public interface IHistoricosDataMapper : IDisposable
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="estatusVivienda"></param>
        /// <returns></returns>
        Task<EstatusViviendas> GuardarHistoricoVivienda(EstatusViviendas estatusVivienda);
        /// <summary>
        /// 
        /// </summary>
        /// <param name="estatusOfertaVivienda"></param>
        /// <returns></returns>
        Task<EstatusOfertaVivienda> GuardarHistoricoOfertaVivienda(EstatusOfertaVivienda estatusOfertaVivienda);
        /// <summary>
        /// 
        /// </summary>
        /// <param name="estatusProyecto"></param>
        /// <returns></returns>
        Task<EstatusProyectos> GuardarHistoricoProyecto(EstatusProyectos estatusProyecto);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="estatusOfertaVivienda"></param>
        /// <param name="listaDetalleEstatus"></param>
        /// <returns></returns>
        Task<int> GuardarDetalleHistoricoOfertaVivienda(EstatusOfertaVivienda estatusOfertaVivienda, List<CampoAdicional> listaDetalleEstatus);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="listaDetalleEstatus"></param>
        /// <returns></returns>
        Task<List<CampoAdicional>> GuardarAdicionalHistorico(List<CampoAdicional> listaDetalleEstatus);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="estatusProyecto"></param>
        /// <param name="listaDetalleEstatus"></param>
        /// <returns></returns>
        Task<int> GuardarDetalleHistoricoProyecto(EstatusProyectos estatusProyecto, List<CampoAdicional> listaDetalleEstatus);


        /// <summary>
        /// 
        /// </summary>
        /// <param name="estatusVivienda"></param>
        /// <param name="listaDetalleEstatus"></param>
        /// <returns></returns>
        Task<int> GuardarDetalleHistoricoVivienda(EstatusViviendas estatusVivienda, List<DetalleEstatusVivienda> listaDetalleEstatus);

        /// <summary>
        /// Obtiene el historico de CUVs por IdVivienda
        /// </summary>
        /// <param name="idVivienda">Identificador de la vivienda</param>
        Task<List<HistoricoCuv>> ObtenerHistoricoCuvPorIdViviendaAsync(int idVivienda);

        /// <summary>
        /// Inserta datos en la tabla de Documento Por Evento Vivienda en la base.
        /// </summary>
        /// <param name="idEventoVivienda">Identificador del Evento de Vivienda</param>
        /// <param name="idDocumento">Identificador del Documento</param>
        Task<bool> InsertarDocumentoPorEventoVivienda(int idEventoVivienda, int idDocumento);

        /// <summary>
        /// Obtiene los documentos asociados a un Evento de Vivienda.
        /// </summary>
        /// <param name="idEventoVivienda">Identificador del Evento de Vivienda</param>
        Task<List<int>> ObtenerDocumentosPorEventoVivienda(int idEventoVivienda);
    }
}