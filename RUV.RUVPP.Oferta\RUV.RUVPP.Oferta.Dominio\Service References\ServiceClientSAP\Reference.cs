﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace RUV.RUVPP.Oferta.Dominio.ServiceClientSAP {
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.ServiceContractAttribute(Namespace="http://localhost", ConfigurationName="ServiceClientSAP.ServiceSoap")]
    public interface ServiceSoap {
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/WSConsultaOrdenPago", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string WSConsultaOrdenPago(string Usuario, string Pass, string sAcreedor, string sDocumento, string sReferencia);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/WSConsultaOrdenPago", ReplyAction="*")]
        System.Threading.Tasks.Task<string> WSConsultaOrdenPagoAsync(string Usuario, string Pass, string sAcreedor, string sDocumento, string sReferencia);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/WSRegistraPago", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string WSRegistraPago(string sUsuario, string sPass, string sDesarrollador, string sAcreedor, string sReferencia, string sConcepto, string sMoneda, decimal dImporte, string sNoPago, string sEjercicio, string sClabe);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/WSRegistraPago", ReplyAction="*")]
        System.Threading.Tasks.Task<string> WSRegistraPagoAsync(string sUsuario, string sPass, string sDesarrollador, string sAcreedor, string sReferencia, string sConcepto, string sMoneda, decimal dImporte, string sNoPago, string sEjercicio, string sClabe);
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    public interface ServiceSoapChannel : RUV.RUVPP.Oferta.Dominio.ServiceClientSAP.ServiceSoap, System.ServiceModel.IClientChannel {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    public partial class ServiceSoapClient : System.ServiceModel.ClientBase<RUV.RUVPP.Oferta.Dominio.ServiceClientSAP.ServiceSoap>, RUV.RUVPP.Oferta.Dominio.ServiceClientSAP.ServiceSoap {
        
        public ServiceSoapClient() {
        }
        
        public ServiceSoapClient(string endpointConfigurationName) : 
                base(endpointConfigurationName) {
        }
        
        public ServiceSoapClient(string endpointConfigurationName, string remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public ServiceSoapClient(string endpointConfigurationName, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public ServiceSoapClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress) {
        }
        
        public string WSConsultaOrdenPago(string Usuario, string Pass, string sAcreedor, string sDocumento, string sReferencia) {
            return base.Channel.WSConsultaOrdenPago(Usuario, Pass, sAcreedor, sDocumento, sReferencia);
        }
        
        public System.Threading.Tasks.Task<string> WSConsultaOrdenPagoAsync(string Usuario, string Pass, string sAcreedor, string sDocumento, string sReferencia) {
            return base.Channel.WSConsultaOrdenPagoAsync(Usuario, Pass, sAcreedor, sDocumento, sReferencia);
        }
        
        public string WSRegistraPago(string sUsuario, string sPass, string sDesarrollador, string sAcreedor, string sReferencia, string sConcepto, string sMoneda, decimal dImporte, string sNoPago, string sEjercicio, string sClabe) {
            return base.Channel.WSRegistraPago(sUsuario, sPass, sDesarrollador, sAcreedor, sReferencia, sConcepto, sMoneda, dImporte, sNoPago, sEjercicio, sClabe);
        }
        
        public System.Threading.Tasks.Task<string> WSRegistraPagoAsync(string sUsuario, string sPass, string sDesarrollador, string sAcreedor, string sReferencia, string sConcepto, string sMoneda, decimal dImporte, string sNoPago, string sEjercicio, string sClabe) {
            return base.Channel.WSRegistraPagoAsync(sUsuario, sPass, sDesarrollador, sAcreedor, sReferencia, sConcepto, sMoneda, dImporte, sNoPago, sEjercicio, sClabe);
        }
    }
}
