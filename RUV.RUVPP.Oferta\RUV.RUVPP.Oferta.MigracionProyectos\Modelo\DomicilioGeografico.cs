//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace RUV.RUVPP.Oferta.MigracionProyectos.Modelo
{
    using System;
    using System.Collections.Generic;
    
    public partial class DomicilioGeografico
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public DomicilioGeografico()
        {
            this.Vivienda = new HashSet<Vivienda>();
        }
    
        public int idDomicilioGeografico { get; set; }
        public byte idTipoDomicilioINEGI { get; set; }
        public string idEstado { get; set; }
        public string idmunicipio { get; set; }
        public string idLocalidad { get; set; }
        public byte idPeriodo { get; set; }
        public Nullable<int> numeroExteriorNumerico { get; set; }
        public string numeroExteriorAlfanumerico { get; set; }
        public string numeroExteriorAnt { get; set; }
        public Nullable<int> numeroInteriorNumerico { get; set; }
        public string numeroInteriorAlfanumerico { get; set; }
        public string idAsentamiento { get; set; }
        public string cp { get; set; }
        public int idVialidad1 { get; set; }
        public int idVialidad2 { get; set; }
        public int idVialidad3 { get; set; }
        public string descripcion { get; set; }
        public string domicilioGeografico1 { get; set; }
        public int idVialidadPrincipal { get; set; }
        public Nullable<System.DateTime> fechaRegistro { get; set; }
        public byte idTipoDomicilioRUV { get; set; }
        public string referenciaPrevia { get; set; }
        public string nombreVialidadPrincipal { get; set; }
        public string nombreVialidad1 { get; set; }
        public string nombreVialidad2 { get; set; }
        public string nombreVialidad3 { get; set; }
        public Nullable<byte> tipoVialidadP { get; set; }
        public byte tipoVialidad1 { get; set; }
        public byte tipoVialidad2 { get; set; }
        public byte tipoVialidad3 { get; set; }
        public string latitud { get; set; }
        public string longitud { get; set; }
        public string nombreTipoVialidadPrincipal { get; set; }
        public string nombreTipoVialidad1 { get; set; }
        public string nombreTipoVialidad2 { get; set; }
        public string nombreTipoVialidad3 { get; set; }
        public string nombreAsentamiento { get; set; }
        public string xmlSIG { get; set; }
        public string superManzana { get; set; }
        public string manzana { get; set; }
        public string lote { get; set; }
        public string localidad { get; set; }
    
        public virtual DomicilioCamino DomicilioCamino { get; set; }
        public virtual DomicilioCarretera DomicilioCarretera { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Vivienda> Vivienda { get; set; }
    }
}
