﻿using Microsoft.ApplicationInsights;
using RUV.Comun.Seguridad.JWT;
using RUV.Comun.Utilerias;
using RUV.Comun.Web;
using RUV.Comun.Web.Http;
using RUV.RUVPP.Oferta.Dominio.Historico;
using RUV.RUVPP.Oferta.Dominio.Oferta;
using RUV.RUVPP.Oferta.Dominio.Proyectos;
using RUV.RUVPP.Oferta.Dominio.Viviendas;
using RUV.RUVPP.Oferta.Modelo.AgenteServicio;
using RUV.RUVPP.Oferta.Modelo.Historico.Data;
using RUV.RUVPP.Oferta.Modelo.Proyectos;
using RUV.RUVPP.Oferta.Modelo.Proyectos.Data;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;
using System.Web.Http.Description;
using System.Web.WebPages.Html;

namespace RUV.RUVPP.Oferta.Api.Controllers.Interno
{
    /// <summary>
    /// 
    /// </summary>
    [RoutePrefix("interno/api/viviendas")]
    public class ViviendasController : ApiControllerBase
    {
        private readonly IServicioProyectos _servicioProyectos;
        private readonly IServicioVivienda _servicioVivienda;
        private readonly IServicioHistoricos _servicioHistoricos;
        private readonly IServicioOferta _servicioOfertas;
        /// <summary>
        /// 
        /// </summary>
        /// <param name="clienteTelemetria"></param>
        /// <param name="servicioProyectos"></param>
        /// <param name="servicioVivienda"></param>
        public ViviendasController(TelemetryClient clienteTelemetria, IServicioProyectos servicioProyectos, IServicioVivienda servicioVivienda, IServicioHistoricos servicioHistoricos, IServicioOferta servicioOferta) : base(clienteTelemetria)
        {
            this._servicioProyectos = servicioProyectos;
            this._servicioVivienda = servicioVivienda;
            this._servicioHistoricos = servicioHistoricos;
            this._servicioOfertas = servicioOferta;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="idProyecto"></param>
        /// <returns></returns>
        [HttpGet, Route("{idProyecto}")]
        [ResponseType(typeof(bool))]
        public async Task<HttpResponseMessage> GenerarCuvsAsync(int idProyecto)
        {
            List<Vivienda> listaVivienda = await this._servicioProyectos.ObtenerVivendasPorIdProyectoAsync(idProyecto);
            int x = 0;
            foreach (var vivienda_ in listaVivienda) {
                vivienda_.identificadorVivienda = x;
                x++;
            }
            Vivienda vivienda = listaVivienda.FirstOrDefault();
            listaVivienda = await this._servicioVivienda.GenerarCuvs(idProyecto, listaVivienda, DateTime.Now, vivienda.idEstado, vivienda.idMunicipio);

            return Request.CreateResponse(System.Net.HttpStatusCode.OK, true);
        }

        [HttpGet, Route("estatus/vivienda")]
        [ResponseType(typeof(List<EstatusVivienda>))]
        public async Task<HttpResponseMessage> ObtenerEstatusVivienda()
        {
            var lista = Enum.GetValues(typeof(EstatusVivienda)).Cast<EstatusVivienda>().Select(v => new SelectListItem
            {
                Text = v.ToString(),
                Value = ((int)v).ToString()
            }).ToList();

            return Request.CreateResponse(HttpStatusCode.OK, lista);
        }        

        /// <summary>
        /// Obtiene el historico de una cuv por idVivienda
        /// </summary>
        /// <param name="idVivienda">Identificador de la vivienda</param>
        /// <returns></returns>
        [HttpGet, Route("{idVivienda}/historicos/cuvs")]
        [ResponseType(typeof(bool))]
        public async Task<HttpResponseMessage> ObtenerHistoricoCuvAsync(int idVivienda)
        {
            List<HistoricoCuv> historicoCuvs = await this._servicioHistoricos.ObtenerHistoricoCuvPorIdViviendaAsync(idVivienda);
            return Request.CreateResponse(System.Net.HttpStatusCode.OK, historicoCuvs);
        }

        /// <summary>
        /// Elimina las cuvs (viviendas) especificadas
        /// </summary>
        /// <param name="idsVivienda">Identificadores de vivienda</param>
        /// <returns></returns>
        [HttpDelete, Route("cuvs")]
        [ResponseType(typeof(bool))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> EliminarCuvsAsync([FromUri]string idsVivienda)
        {
            var usuario = (CustomUserRuv)this.User;
            int[] ids = idsVivienda.Split(',').Select(Int32.Parse).ToArray();
            var resultado = await this._servicioOfertas.EliminarCuvsPorIdsViviendaAsync(ids, usuario);
            return Request.CreateResponse(System.Net.HttpStatusCode.OK, resultado);
        }

        /// <summary>
        /// Actualiza el estatus de las cuvs a individualizadas
        /// </summary>
        /// <param name="idsVivienda">Identificadores de vivienda</param>
        /// <returns></returns>
        [HttpPut, Route("cuvs/estatus/individualizadas")]
        [ResponseType(typeof(bool))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ActualizarCuvsAIndividualizadasAsync([FromUri]string idsVivienda, string fecha, string onavi)
        {
            var usuario = (CustomUserRuv)this.User;
            int[] ids = idsVivienda.Split(',').Select(Int32.Parse).ToArray();
            var resultado = await this._servicioOfertas.ActualizaCuvsAIndividualizadaPorIdsViviendaAsync(ids, fecha, onavi, usuario);
            return Request.CreateResponse(System.Net.HttpStatusCode.OK, resultado);
        }

        /// <summary>
        /// Actualiza el estatus de las cuvs a individualizadas
        /// </summary>
        /// <param name="archivo">Archivo xls para obtener un listado de CUV
        /// para individualizar con sus respectivos datos.</param>
        /// <returns></returns>
        [HttpPost, Route("cuvs/estatus/individualizar/masivo")]
        [ResponseType(typeof(bool))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
<<<<<<< Updated upstream
        public async Task<HttpResponseMessage> IndividualizacionMasiva(FileStream archivo)
=======
        public async Task<HttpResponseMessage> IndividualizacionMasivaAsync()
>>>>>>> Stashed changes
        {
            var usuario = (CustomUserRuv)this.User;
            HttpResponseMessage respuesta = null;

            //Obtener CUV, Fecha y ONAVI.
            if (!Request.Content.IsMimeMultipartContent())
                respuesta = Request.CreateErrorResponse(HttpStatusCode.UnsupportedMediaType, "La solicitud no tiene contenido válido.");
            else
            {
                var multipartData = await Request.Content.ParseMultipartAsync();

                foreach (var archivoActual in multipartData.Files)
                {
                    var ms = new MemoryStream(archivoActual.Value.Data);

                    StreamReader reader = new StreamReader(ms, System.Text.Encoding.UTF8, true);

                    char[] separator = new[] { ',' };
                    string currentLine;

                    int contador = 0;

                    List<IndividualizarViviendaDTO> individualizarViviendaDtoLista = new List<IndividualizarViviendaDTO>();

                    while ((currentLine = reader.ReadLine()) != null)
                    {
                        IndividualizarViviendaDTO individualizarViviendaDto = new IndividualizarViviendaDTO();

                        if (contador > 0)
                        {
                            var renglon = currentLine.Split(separator, StringSplitOptions.None);



                            individualizarViviendaDto
                        }

                        contador++;
                        

                    }



                }

            }


                //var resultado = await this._servicioOfertas.ActualizaCuvsAIndividualizadaPorIdsViviendaAsync(ids, fecha, onavi, usuario);
                return Request.CreateResponse(System.Net.HttpStatusCode.OK, true);
        }

        [HttpGet, Route("{idCuv}/conceptos/tipo/{idTipoPuntaje}")]
        [ResponseType(typeof(bool))]
        public async Task<HttpResponseMessage> ObtenerConceptos(int idCuv, int idTipoPuntaje)
        {
            var resultado = await this._servicioOfertas.ObtenerConceptosViviendaAsync(idCuv, idTipoPuntaje);
            return Request.CreateResponse(System.Net.HttpStatusCode.OK, resultado);
        }

        [HttpPut, Route("{idCuv}/elementos")]
        [ResponseType(typeof(bool))]
        public async Task<HttpResponseMessage> ObtenerElementos(int idCuv)
        {
            var resultado = await this._servicioOfertas.ObtenerElementosViviendaAsync(idCuv);
            return Request.CreateResponse(System.Net.HttpStatusCode.OK, resultado);
        }

        [HttpGet, Route("puntajes")]
        [ResponseType(typeof(List<Vivienda>))]
        public async Task<HttpResponseMessage> ObtenerPuntajes(int tamanioPagina, int pagina, int? idProyecto = null, int? idOferta = null, int? idOrdenVerificacion = null, int? idCuv = null)
        {
            var resultado = await this._servicioOfertas.ObtenerPuntajesViviendasPaginadoAsync(tamanioPagina, pagina, idProyecto, idOferta, idOrdenVerificacion, idCuv);
            return Request.CreateResponse(System.Net.HttpStatusCode.OK, resultado);
        }

        #region Disposible

        protected override void Dispose(bool disposing)
        {
            base.Dispose(disposing);
            this._servicioProyectos.Dispose();
        }

        #endregion
    }
}