﻿using Microsoft.Practices.Unity;
using RUV.RUVPP.Oferta.MigracionProyectos.Configuracion;
using RUV.RUVPP.Oferta.MigracionProyectos.Funciones;

namespace RUV.RUVPP.Oferta.MigracionProyectos
{
    internal class Program
    {
        private  static  void Main()
        {
            var unityContainer = UnityConfig.ConfigureContainer();
                       
            var _migracion = unityContainer.Resolve<MigracionProyecto>();
              _migracion.MigrarProyectosSincronos().Wait();
        }
    }
}