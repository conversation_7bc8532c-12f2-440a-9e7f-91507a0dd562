﻿using RUV.RUVPP.Oferta.Comun.Modelo;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.Prototipos.Api
{
    /// <summary>
    /// 
    /// </summary>
    public class Prototipo
    {
        public int? idPrototipo { get; set; }
        public byte? idClasificacionVivienda { get; set; }
        public byte? idTipologiaVivienda { get; set; }
        public string claveTipologiaVivienda { get; set; }
        public byte? idTipoDistribucionVivienda { get; set; }
        public int? idEmpresa { get; set; }
        public string nombre { get; set; }
        public decimal? areaLote { get; set; }
        public byte? numeroNivelesVivienda { get; set; }
        public byte? numeroNivelesPrototipo { get; set; }
        public byte? numeroRecamaras { get; set; }
        public byte? numeroAlcobas { get; set; }
        public byte? numeroBaniosCompletos { get; set; }
        public byte? numeroBaniosMedios { get; set; }
        public decimal? precioVivienda { get; set; }
        public decimal? superficiePaniosInteriores { get; set; }
        public decimal? areaMuros { get; set; }
        public decimal? superficieVolados { get; set; }
        public decimal? superficieIndivisosaCubierto { get; set; }
        public decimal? alturaLotes { get; set; }
        public decimal? huellas { get; set; }
        public decimal? peraltes { get; set; }
        public decimal? anchoRampa { get; set; }
        public decimal? longitudEscaleras { get; set; }
        public DateTime? fechaRegistro { get; set; }
        public DateTime? fechaActualizacion { get; set; }
        public bool? activo { get; set; }
        public int? viviendasAsociadas { get; set; }

        public int? ofertaEnVyD { get; set; }

        public int? totalRegistros { get; set; }
        public List<DistribucionAreaVivienda> distribucionAreaVivienda { get; set; }
        public List<DocumentoRuv> imagenes { get; set; }
        public DocumentoRuv cargaPlano { get; set; }
        public string nombreTipologia { get; set; }
        public string nombreClasificacionVivienda { get; set; }
        public decimal? metrosFrenteLote { get; set; }
        public string oferente { get; set; }
        public string temporalJSON { get; set; }
        public int? identificadorPrototipo { get; set; }
    }
}
