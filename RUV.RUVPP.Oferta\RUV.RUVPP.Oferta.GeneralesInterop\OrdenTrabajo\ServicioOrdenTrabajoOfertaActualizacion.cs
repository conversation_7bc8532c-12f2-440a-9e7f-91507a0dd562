﻿using RUV.RUVPP.Negocio.General.OrdenTrabajo.Servicios;
using RUV.RUVPP.Entidades.Comun.Enums;


namespace RUV.RUVPP.Oferta.GeneralesInterop.OrdenTrabajo
{
    public class ServicioOrdenTrabajoOfertaActualizacion : ServicioOrdenTrabajo
    {
        public ServicioOrdenTrabajoOfertaActualizacion() 
            : base(Producto.Oferta, ServicioProducto.ActualizaciondeOferta, null)
        {
        }
    }
}
