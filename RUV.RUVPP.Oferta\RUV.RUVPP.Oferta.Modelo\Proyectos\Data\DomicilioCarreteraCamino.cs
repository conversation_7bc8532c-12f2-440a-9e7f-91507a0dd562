﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.Proyectos.Data
{
    public class DomicilioCarreteraCamino
    {
        public int idDomicilioGeografico { get; set; }
        public string nombreVialidad { get; set; }
        public string origen { get; set; }
        public string destino { get; set; }
        public string cadenamiento { get; set; }
        public string idTipoCamino { get; set; }
        public string idAdministracion { get; set; }
        public string idDerechoTransito { get; set; }
        public string codigo { get; set; }
        public string idMargen { get; set; }
    }
}
