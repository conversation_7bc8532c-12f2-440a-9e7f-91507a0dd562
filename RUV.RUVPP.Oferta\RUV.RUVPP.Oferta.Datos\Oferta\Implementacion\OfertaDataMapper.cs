﻿using Dapper;
using Microsoft.ApplicationInsights;
using RUV.Comun.Datos.Mapper;
using RUV.RUVPP.Oferta.Comun.Modelo;
using RUV.RUVPP.Oferta.Modelo.Historico.Data;
using RUV.RUVPP.Oferta.Modelo.Oferta.Api;
using RUV.RUVPP.Oferta.Modelo.Oferta.Data;
using RUV.RUVPP.Oferta.Modelo.Proyectos;
using RUV.RUVPP.Oferta.Modelo.Proyectos.Data;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Datos.Oferta.Implementacion
{
    /// <summary>
    /// Clase que contiene métodos para guardar, obtener y actualizar las tablas relacionadas a la administración de oferta.
    /// </summary>
    public class OfertaDataMapper : SqlDataMapperBase, IOfertaDataMapper
    {
        /// <summary>
        /// Constructor de la Clase.
        /// </summary>
        /// <param name="clienteTelemetria">Cliente de Telemetri para Application Insights.</param>
        /// <param name="gestorConexiones">Gestor de conexiones a utilizar.</param>
        /// <param name="nombreCadenaConexion">Cadena de Conexión a Base de Datos.</param>
        public OfertaDataMapper(string nombreCadenaConexion, GestorConexiones gestorConexiones = null) : base(nombreCadenaConexion, gestorConexiones)
        {
        }

        /// <summary>
        /// Método que ejecuta el sp "oferta.usp_InsertarDirectorResponsableObraxOfertaVivienda."
        /// </summary>
        /// <param name="idDRO">id del Director Responsable de Obra.</param>
        /// <param name="idOfertaVivienda">id de la Oferta.</param>
        /// <returns></returns>
        public async Task GuardarDirectorResponsableObraxOfertaViviendaAsync(int idDRO, int idOfertaVivienda)
        {
            var parametros = new DynamicParameters();

            parametros.Add("@idDRO", idDRO);
            parametros.Add("@idOfertaVivienda", idOfertaVivienda);

            await this._conexion.ExecuteScalarAsync("oferta.usp_InsertarDirectorResponsableObraxOfertaVivienda", parametros, commandType: CommandType.StoredProcedure);
        }

        public async Task<Modelo.Oferta.Api.Oferta> ObtenerOfertasFiltroAsync(int? idEmpresa, string noRuv, string nombreProyecto, int? idProyecto, int? idOferta, string claveOferta)
        {
            var parameters = new DynamicParameters();
            parameters.Add("@idEmpresa", idEmpresa);
            parameters.Add("@nombreProyecto", nombreProyecto);
            parameters.Add("@noRuv", noRuv);
            parameters.Add("@idProyecto", idProyecto);
            parameters.Add("@idOferta", idOferta);
            parameters.Add("@claveOferta", claveOferta);

            var reader = await this._conexion.QueryMultipleAsync("oferta.ObtenerOfertasFiltro", parameters, commandType: CommandType.StoredProcedure);
            var proyectoResultado = await reader.ReadAsync<Modelo.Oferta.Api.Oferta>();

            return proyectoResultado.FirstOrDefault();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="idEmpresa"></param>
        /// <param name="noRuv"></param>
        /// <param name="nombreProyecto"></param>
        /// <param name="idProyecto"></param>
        /// <param name="idOferta"></param>
        /// <param name="claveOferta"></param>
        /// <param name="idVivienda"></param>
        /// <param name="cuv"></param>
        /// <returns></returns>
        public async Task<List<Vivienda>> ObtenerViviendasFiltroAsync(int? idEmpresa, string noRuv, string nombreProyecto, int? idProyecto, int? idOferta, string claveOferta, int? idVivienda, string cuv)
        {
            var parameters = new DynamicParameters();
            parameters.Add("@idEmpresa", idEmpresa);
            parameters.Add("@nombreProyecto", nombreProyecto);
            parameters.Add("@noRuv", noRuv);
            parameters.Add("@idProyecto", idProyecto);
            parameters.Add("@idOferta", idOferta);
            parameters.Add("@idVivienda", idVivienda);
            parameters.Add("@claveOferta", claveOferta);
            parameters.Add("@cuv", cuv);

            var reader = await this._conexion.QueryMultipleAsync("oferta.ObtenerViviendasFiltro", parameters, commandType: CommandType.StoredProcedure);
            var viviendaResultado = await reader.ReadAsync<Vivienda>();

            return viviendaResultado.ToList();
        }

        public async Task<Tuple<int, List<Modelo.Oferta.Api.Oferta>>> ObtenerOfertasFiltroConPaginadoAsync(int tamanioPagina, int pagina, int? idEmpresa, string noRuv, string nombreProyecto, int? idProyecto, int? idOferta, int? idEstatus)
        {
            var parameters = new DynamicParameters();
            parameters.Add("@idEmpresa", idEmpresa);
            parameters.Add("@tamanioPagina", tamanioPagina);
            parameters.Add("@pagina", pagina);
            parameters.Add("@nombreProyecto", nombreProyecto);
            parameters.Add("@noRuv", noRuv);
            parameters.Add("@idProyecto", idProyecto);
            parameters.Add("@idOferta", idOferta);
            parameters.Add("@idEstatus", idEstatus);

            var reader = await this._conexion.QueryMultipleAsync("oferta.ObtenerOfertasFiltroConPaginado", parameters, commandType: CommandType.StoredProcedure);
            var conteo = await reader.ReadAsync<int>();
            var proyectos = await reader.ReadAsync<Modelo.Oferta.Api.Oferta>();

                return Tuple.Create(conteo.FirstOrDefault(), proyectos.ToList());
        }

        public async Task<Tuple<int, List<Vivienda>>> ObtenerViviendasFiltroConPaginadoAsync(int tamanioPagina, int pagina, int? idEmpresa, string noRuv, string nombreProyecto, int? idProyecto, int? idOferta, int? idVivienda, string cuv)
        {
            var parameters = new DynamicParameters();
            parameters.Add("@idEmpresa", idEmpresa);
            parameters.Add("@tamanioPagina", tamanioPagina);
            parameters.Add("@pagina", pagina);
            parameters.Add("@nombreProyecto", nombreProyecto);
            parameters.Add("@noRuv", noRuv);
            parameters.Add("@idProyecto", idProyecto);
            parameters.Add("@idOferta", idOferta);
            parameters.Add("@idVivienda", idVivienda);
            parameters.Add("@cuv", cuv);

            var reader = await this._conexion.QueryMultipleAsync("oferta.ObtenerViviendasFiltroConPaginado", parameters, commandType: CommandType.StoredProcedure);
            var conteo = await reader.ReadAsync<int>();
            var viviendas = await reader.ReadAsync<Vivienda>();

            return Tuple.Create(conteo.FirstOrDefault(), viviendas.ToList());
        }

        /// <summary>
        /// Método que ejecuta el sp oferta.usp_InsertarOfertaViviendaxRiesgoOferta.
        /// </summary>
        /// <param name="viviendaRiesgoOferta">Objeto ViviendaRiesgoOferta a guardar en base de datos.</param>
        /// <returns></returns>
        public async Task GuardarOfertaViviendaxRiesgoOfertaAsync(ViviendaRiesgoOferta viviendaRiesgoOferta)
        {
            var parametros = new DynamicParameters();

            parametros.Add("@idOfertaVivienda", viviendaRiesgoOferta.idOfertaVivienda);
            parametros.Add("@idRiesgoOferta", viviendaRiesgoOferta.idRiesgoOferta);
            parametros.Add("@solucionMitigarRiesgo", viviendaRiesgoOferta.solucionMitigarRiesgo);

            await this._conexion.ExecuteScalarAsync("oferta.usp_InsertarOfertaViviendaxRiesgoOferta", parametros, commandType: CommandType.StoredProcedure);
        }

        public async Task ActualizarOfertaViviendaxRiesgoOfertaAsync(ViviendaRiesgoOferta viviendaRiesgoOferta)
        {
            var parametros = new DynamicParameters();

            parametros.Add("@idOfertaVivienda", viviendaRiesgoOferta.idOfertaVivienda);
            parametros.Add("@idRiesgoOferta", viviendaRiesgoOferta.idRiesgoOferta);
            parametros.Add("@solucionMitigarRiesgo", viviendaRiesgoOferta.solucionMitigarRiesgo);

            await this._conexion.ExecuteScalarAsync("oferta.usp_ActualizarOfertaViviendaxRiesgoOferta", parametros, commandType: CommandType.StoredProcedure);
        }

        public async Task EliminarOfertaViviendaxRiesgoOfertaAsync(ViviendaRiesgoOferta viviendaRiesgoOferta)
        {
            var parametros = new DynamicParameters();

            parametros.Add("@idOfertaVivienda", viviendaRiesgoOferta.idOfertaVivienda);
            parametros.Add("@idRiesgoOferta", viviendaRiesgoOferta.idRiesgoOferta);

            await this._conexion.ExecuteScalarAsync("oferta.usp_EliminarOfertaViviendaxRiesgoOferta", parametros, commandType: CommandType.StoredProcedure);
        }

        /// <summary>
        /// Método que ejecute el sp oferta.usp_InsertarPromotorExternoxOfertaVivienda
        /// </summary>
        /// <param name="idPromotorExterno">id del Promotor Externo</param>
        /// <param name="idOfertaVivienda">id de la Oferta</param>
        /// <returns></returns>
        public async Task GuardarPromotorExternoxOfertaViviendaAsync(int idPromotorExterno, int idOfertaVivienda)
        {
            var parametros = new DynamicParameters();

            parametros.Add("@idPromotorExterno", idPromotorExterno);
            parametros.Add("@idOfertaVivienda", idOfertaVivienda);

            await this._conexion.ExecuteScalarAsync("oferta.usp_InsertarPromotorExternoxOfertaVivienda", parametros, commandType: CommandType.StoredProcedure);
        }

        public async Task EliminarPromotorExternoxOfertaViviendaAsync(int idPromotorExterno, int idOfertaVivienda)
        {
            var parametros = new DynamicParameters();

            parametros.Add("@idPromotorExterno", idPromotorExterno);
            parametros.Add("@idOfertaVivienda", idOfertaVivienda);

            await this._conexion.ExecuteScalarAsync("oferta.usp_EliminarPromotorExternoxOfertaVivienda", parametros, commandType: CommandType.StoredProcedure);
        }

        /// <summary>
        /// Método que ejecuta el oferta.usp_InsertarPropietarioTerrenoxOfertaVivienda.
        /// </summary>
        /// <param name="idPropertarioTerreno">id del Propietario del Terreno.</param>
        /// <param name="idOfertaVivienda">id de Oferta.</param>
        /// <returns></returns>
        public async Task GuardarPropietarioTerrenoxOfertaViviendaAsync(int idPropertarioTerreno, int idOfertaVivienda)
        {
            var parametros = new DynamicParameters();

            parametros.Add("@idPropietarioTerreno", idPropertarioTerreno);
            parametros.Add("@idOfertaVivienda", idOfertaVivienda);

            await this._conexion.ExecuteScalarAsync("oferta.usp_InsertarPropietarioTerrenoxOfertaVivienda", parametros, commandType: CommandType.StoredProcedure);
        }

        /// <summary>
        /// Método que ejecuta el oferta.usp_InsertarOfertaVivienda
        /// </summary>
        /// <param name="ofertaVivienda">Objeto OfertaVivienda a guardar en base de datos.</param>
        /// <returns></returns>
        public async Task<int> GuardarOfertaViviendaAsync(int idProyecto, string nombre, string oferta)
        {
            var parametros = new DynamicParameters();

            parametros.Add("@idProyecto", idProyecto);
            parametros.Add("@idEstatusOfertaVivienda", 1);
            parametros.Add("@activo", 1);
            parametros.Add("@nombreDeFrente", nombre);
            parametros.Add("@esMAI", false);
            parametros.Add("@claveOfertaVivienda", string.Empty);
            parametros.Add("@temporalJSON", oferta);

            var reader = await this._conexion.ExecuteScalarAsync("oferta.usp_InsertarOfertaVivienda", parametros, commandType: CommandType.StoredProcedure);
            var result = reader ?? 0;

            return Convert.ToInt32(result);
        }

        public async Task<int> GuardarDetalleOfertaViviendaAsync(OfertaVivienda ofertaVivienda)
        {
            var parametros = new DynamicParameters();

            parametros.Add("@idOfertaVivienda", ofertaVivienda.idOfertaVivienda);
            parametros.Add("@idConstructor", ofertaVivienda.idConstructor);
            parametros.Add("@idPromotor", ofertaVivienda.idPromotor);
            parametros.Add("@idVendedor", ofertaVivienda.idVendedor);
            parametros.Add("@idDomicilioGeografico", ofertaVivienda.idDomicilioGeografico);
            parametros.Add("@idTipoSociedadHipotecaria", 1);//ofertaVivienda.idTipoSociedadHipotecaria);
            parametros.Add("@cuentaConMinistraciones", ofertaVivienda.cuentaConMinistraciones);
            parametros.Add("@empresaOtorgaMinistraciones", ofertaVivienda.empresaOtorgaMinistraciones);
            parametros.Add("@cuentaConCreditoPuente", ofertaVivienda.cuentaConCreditoPuente);
            parametros.Add("@empresaOtorgaCreditoPuente", ofertaVivienda.empresaOtorgaCreditoPuente);
            parametros.Add("@areaTerreno", ofertaVivienda.areaTerreno);
            parametros.Add("@superficieConstruida", ofertaVivienda.superficieConstruida);
            parametros.Add("@metrosFrente", ofertaVivienda.metrosFrente);
            parametros.Add("@numeroCatastralLote", ofertaVivienda.numeroCatastralLote);
            parametros.Add("@descripcionActualInmueble", "pruebas"); //ofertaVivienda.descripcionActualInmueble);
            parametros.Add("@descripcionAccionVivienda", "pruebas"); //ofertaVivienda.descripcionAccionVivienda);
            parametros.Add("@aceptacionCartaResponsabilidad", ofertaVivienda.aceptacionCartaResponsabilidad);

            var reader = await this._conexion.ExecuteScalarAsync("oferta.usp_InsertarDetalleOfertaVivienda", parametros, commandType: CommandType.StoredProcedure);
            var result = reader ?? 0;

            return Convert.ToInt32(result);
        }

        public async Task<int> ActualizarOfertaViviendaAsync(OfertaVivienda ofertaVivienda)
        {
            var parametros = new DynamicParameters();

            parametros.Add("@idOfertaVivienda", ofertaVivienda.idOfertaVivienda);
            parametros.Add("@idProyecto", ofertaVivienda.idProyecto);
            parametros.Add("@idEstatusOfertaVivienda", ofertaVivienda.idEstatusOfertaVivienda); //ofertaVivienda.idEstatusOfertaVivienda);
            parametros.Add("@claveOfertaVivienda", ofertaVivienda.claveOfertaVivienda ?? "");
            parametros.Add("@esMAI", ofertaVivienda.esMAI);
            parametros.Add("@nombreDeFrente", ofertaVivienda.nombreFrente);
            parametros.Add("@temporalJSON", ofertaVivienda.temporalJSON);

            var reader = await this._conexion.ExecuteScalarAsync("oferta.usp_ActualizarOfertaVivienda", parametros, commandType: CommandType.StoredProcedure);
            var result = reader ?? 0;

            return Convert.ToInt32(result);
        }

        public async Task<int> ActualizarTemporalOfertaViviendaAsync(OfertaVivienda ofertaVivienda)
        {
            var parametros = new DynamicParameters();

            parametros.Add("@idOfertaVivienda", ofertaVivienda.idOfertaVivienda);
            parametros.Add("@idEstatusOfertaVivienda", ofertaVivienda.idEstatusOfertaVivienda); //ofertaVivienda.idEstatusOfertaVivienda);
            parametros.Add("@temporalJSON", ofertaVivienda.temporalJSON);

            var reader = await this._conexion.ExecuteScalarAsync("oferta.usp_ActualizarTemporalOfertaVivienda", parametros, commandType: CommandType.StoredProcedure);
            var result = reader ?? 0;

            return Convert.ToInt32(result);
        }

        public async Task<int> ActualizarDetalleOfertaViviendaAsync(OfertaVivienda ofertaVivienda)
        {
            var parametros = new DynamicParameters();

            parametros.Add("@idOfertaVivienda", ofertaVivienda.idOfertaVivienda);
            parametros.Add("@idConstructor", ofertaVivienda.idConstructor);
            parametros.Add("@idPromotor", ofertaVivienda.idPromotor);
            parametros.Add("@idVendedor", ofertaVivienda.idVendedor);
            parametros.Add("@idDomicilioGeografico", 1);//ofertaVivienda.idDomicilioGeografico);nda);
            parametros.Add("@idTipoSociedadHipotecaria", 1);//ofertaVivienda.idTipoSociedadHipotecaria);
            parametros.Add("@cuentaConMinistraciones", ofertaVivienda.cuentaConMinistraciones);
            parametros.Add("@empresaOtorgaMinistraciones", ofertaVivienda.empresaOtorgaMinistraciones);
            parametros.Add("@cuentaConCreditoPuente", ofertaVivienda.cuentaConCreditoPuente);
            parametros.Add("@empresaOtorgaCreditoPuente", ofertaVivienda.empresaOtorgaCreditoPuente);
            parametros.Add("@areaTerreno", ofertaVivienda.areaTerreno);
            parametros.Add("@superficieConstruida", ofertaVivienda.superficieConstruida);
            parametros.Add("@metrosFrente", ofertaVivienda.metrosFrente);
            parametros.Add("@numeroCatastralLote", ofertaVivienda.numeroCatastralLote);
            parametros.Add("@descripcionActualInmueble", "pruebas"); //ofertaVivienda.descripcionActualInmueble);
            parametros.Add("@descripcionAccionVivienda", "pruebas"); //ofertaVivienda.descripcionAccionVivienda);
            parametros.Add("@aceptacionCartaResponsabilidad", ofertaVivienda.aceptacionCartaResponsabilidad);

            var reader = await this._conexion.ExecuteScalarAsync("oferta.usp_ActualizarDetalleOfertaVivienda", parametros, commandType: CommandType.StoredProcedure);
            var result = reader ?? 0;

            return Convert.ToInt32(result);
        }

        public async Task EliminarOfertaParcialAsync(int idParcialOferta)
        {
            var parametros = new DynamicParameters();
            parametros.Add("@idOfertaVivienda", idParcialOferta);

            await this._conexion.ExecuteScalarAsync("oferta.usp_EliminarOfertaViviendaParcial", parametros, commandType: CommandType.StoredProcedure);
        }

        /// <summary>
        /// Método que ejecuta el sp oferta.usp_InsertarDocumentoxPrototipoxOfertaVivienda
        /// </summary>
        /// <param name="idOfertaVivienda">id de Oferta Vivienda</param>
        /// <param name="idDocumento">id Documento</param>
        /// <returns></returns>
        public async Task GuardarDocumentoxOfertaViviendaAsync(int idOfertaVivienda, int idDocumento)
        {
            var parametros = new DynamicParameters();
            parametros.Add("@idOfertaVivienda", idOfertaVivienda);
            parametros.Add("@idDocumento", idDocumento);

            await this._conexion.ExecuteScalarAsync("oferta.usp_InsertarDocumentoxOfertaVivienda", parametros, commandType: CommandType.StoredProcedure);
        }

        public async Task EliminarDocumentoxOfertaViviendaAsync(int idOfertaVivienda, int idDocumento)
        {
            var parametros = new DynamicParameters();
            parametros.Add("@idOfertaVivienda", idOfertaVivienda);
            parametros.Add("@idDocumento", idDocumento);

            await this._conexion.ExecuteScalarAsync("oferta.usp_EliminarDocumentoxOfertaVivienda", parametros, commandType: CommandType.StoredProcedure);
        }

        /// <summary>
        /// Método que ejecuta el sp oferta.usp_InsertarPrototipoxOfertaVivienda
        /// </summary>
        /// <param name="idOfertaVivienda"></param>
        /// <param name="idPrototipo"></param>
        /// <returns>id del registro insertado</returns>
        public async Task<int> GuardarPrototipoxOfertaViviendaAsync(int idOfertaVivienda, int idPrototipo)
        {
            var parametros = new DynamicParameters();

            parametros.Add("@idOfertaVivienda", idOfertaVivienda);
            parametros.Add("@idPrototipo", idPrototipo);

            var resultado = await this._conexion.ExecuteScalarAsync("oferta.usp_InsertarPrototipoxOfertaVivienda", parametros, commandType: CommandType.StoredProcedure);
            var PrototipoxOfertaVivienda = resultado ?? 0;

            return Convert.ToInt32(PrototipoxOfertaVivienda);
        }

        public async Task EliminarPrototipoxOfertaViviendaAsync(int idOfertaVivienda, int idPrototipo)
        {
            var parametros = new DynamicParameters();

            parametros.Add("@idOfertaVivienda", idOfertaVivienda);
            parametros.Add("@idPrototipo", idPrototipo);

            await this._conexion.ExecuteScalarAsync("oferta.usp_EliminarPrototipoxOfertaVivienda", parametros, commandType: CommandType.StoredProcedure);
        }

        public async Task<int> ObtenerPrototipoxOfertaViviendaAsync(int idOfertaVivienda, int idPrototipo)
        {
            var parametros = new DynamicParameters();

            parametros.Add("@idOfertaVivienda", idOfertaVivienda);
            parametros.Add("@idPrototipo", idPrototipo);

            var resultado = await this._conexion.ExecuteScalarAsync("oferta.usp_ObtenerPrototipoxOfertaVivienda", parametros, commandType: CommandType.StoredProcedure);
            var PrototipoxOfertaVivienda = resultado ?? 0;

            return Convert.ToInt32(PrototipoxOfertaVivienda);
        }

        public async Task GuardarZonasRiesgoAsync(ZonaRiesgo[] zonasRiesgo, int idOferta)
        {
            foreach (var zonaRiesgo in zonasRiesgo)
            {
                await this._conexion.ExecuteScalarAsync("oferta.usp_InsertarOfertaViviendaxRiesgoOferta",
                    new
                    {
                        idOfertaVivienda = idOferta,
                        idRiesgoOferta = zonaRiesgo.idRiesgoOferta,
                        solucionMitigarRiesgo = zonaRiesgo.solucionMitigarRiesgo,
                    }, commandType: CommandType.StoredProcedure);
            }
        }

        public async Task ActualizarZonasRiesgoAsync(ZonaRiesgo[] zonasRiesgo, int idOferta)
        {
            foreach (var zonaRiesgo in zonasRiesgo)
            {
                await this._conexion.ExecuteScalarAsync("oferta.usp_ActualizarOfertaViviendaxRiesgoOferta",
                    new
                    {
                        idOfertaVivienda = idOferta,
                        idRiesgoOferta = zonaRiesgo.idRiesgoOferta,
                        solucionMitigarRiesgo = zonaRiesgo.solucionMitigarRiesgo,
                    }, commandType: CommandType.StoredProcedure);
            }
        }
        public async Task<Tuple<int, List<ViviendaSinAsignar>>> ObtenerVivendaSinAsignarAsync(int? idProyecto, int tamanioPagina, int pagina, int? idOferta)
        {
            var parametros = new DynamicParameters();
            parametros.Add("@idProyecto", idProyecto);
            parametros.Add("@idOferta", idOferta);
            parametros.Add("@tamanioPagina", tamanioPagina);
            parametros.Add("@pagina", pagina);

            var reader = await this._conexion.QueryMultipleAsync("oferta.usp_ObtenerViviendasSinAsignarOferta", parametros, commandType: CommandType.StoredProcedure);
            var viviendasSinAsignarTotales = await reader.ReadAsync<int>();
            var viviendasSinAsignar = await reader.ReadAsync<ViviendaSinAsignar>();

            return Tuple.Create(viviendasSinAsignarTotales.FirstOrDefault(), viviendasSinAsignar.ToList());
        }

        public async Task<Tuple<int, List<ViviendaSinAsignar>>> ObtenerVivendaSinAsignarRegistroAsync(int idProyecto, int tamanioPagina, int pagina, int? idOferta)
        {
            var parametros = new DynamicParameters();
            parametros.Add("@idProyecto", idProyecto);
            parametros.Add("@idOferta", idOferta);
            parametros.Add("@tamanioPagina", tamanioPagina);
            parametros.Add("@pagina", pagina);

            var reader = await this._conexion.QueryMultipleAsync("oferta.usp_ObtenerViviendasSinAsignarOfertaRegistro", parametros, commandType: CommandType.StoredProcedure);
            var viviendasSinAsignarTotales = await reader.ReadAsync<int>();
            var viviendasSinAsignar = await reader.ReadAsync<ViviendaSinAsignar>();

            return Tuple.Create(viviendasSinAsignarTotales.FirstOrDefault(), viviendasSinAsignar.ToList());
        }

        public async Task<Tuple<int, List<ViviendaSinAsignar>>> ObtenerVivendaSinAsignarActualizacionAsync(int tamanioPagina, int pagina, int? idOferta)
        {
            var parametros = new DynamicParameters();
            parametros.Add("@idOferta", idOferta);
            parametros.Add("@tamanioPagina", tamanioPagina);
            parametros.Add("@pagina", pagina);

            var reader = await this._conexion.QueryMultipleAsync("oferta.usp_ObtenerViviendasSinAsignarOfertaActualizacion", parametros, commandType: CommandType.StoredProcedure);
            var viviendasSinAsignarTotales = await reader.ReadAsync<int>();
            var viviendasSinAsignar = await reader.ReadAsync<ViviendaSinAsignar>();

            return Tuple.Create(viviendasSinAsignarTotales.FirstOrDefault(), viviendasSinAsignar.ToList());
        }

        public async Task<List<ViviendaPrototipo>> ObtenerVivienasPrototipoAsync(int? idProyecto, int? idOferta)
        {
            var parametros = new DynamicParameters();
            parametros.Add("@idProyecto", idProyecto);
            parametros.Add("@idOferta", idOferta);

            var resultados = await this._conexion.QueryAsync<ViviendaPrototipo>("oferta.usp_ObtenerViviendasPrototiposProyecto", parametros, commandType: CommandType.StoredProcedure);

            return resultados.ToList();
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="idOferta"></param>
        /// <returns></returns>
        public async Task<Modelo.Oferta.Api.Oferta> ObtenerOfertaPorIdAsync(int idOferta)
        {
            var parameters = new DynamicParameters();
            parameters.Add("@idOferta", idOferta);

            var ofertaResultado = await this._conexion.QueryAsync<Modelo.Oferta.Api.Oferta>("oferta.Obteneroferta", parameters, commandType: CommandType.StoredProcedure);

            return ofertaResultado.FirstOrDefault();
        }

        public async Task<OfertaVivienda> ObtenerOfertaAsync(int idOferta)
        {
            var parameters = new DynamicParameters();
            parameters.Add("@idOferta", idOferta);

            var ofertaResultado = await this._conexion.QueryAsync<OfertaVivienda>("oferta.ObtenerOferta", parameters, commandType: CommandType.StoredProcedure);

            return ofertaResultado.FirstOrDefault();
        }

        public async Task<OfertaVivienda> ObtenerOfertaDetalleAsync(int idOferta)
        {
            var parameters = new DynamicParameters();
            parameters.Add("@idOfertaVivienda", idOferta);

            var reader = await this._conexion.QueryMultipleAsync("oferta.ObtenerDetalleOferta", parameters, commandType: CommandType.StoredProcedure);
            var proyecto = await reader.ReadAsync<OfertaVivienda>();

            return proyecto.FirstOrDefault();
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="claveOferta"></param>
        /// <param name="nombreProyecto"></param>
        /// <param name="idEmpresa"></param>
        /// <returns></returns>
        public async Task<Tuple<int, List<Modelo.Oferta.Api.Oferta>>> ObtenerOfertasFiltradoAsync(int tamanioPagina, int pagina, string claveOferta, string nombreProyecto, int? idEmpresa)
        {
            var parameters = new DynamicParameters();
            parameters.Add("@pagina", pagina);
            parameters.Add("@tamanoPagina", tamanioPagina);
            parameters.Add("@nombreProyecto", nombreProyecto);
            parameters.Add("@claveOferta", claveOferta);
            parameters.Add("@idEmpresa", idEmpresa);

            var reader = await this._conexion.QueryMultipleAsync("[oferta].[oferta_ConsultaOfertaPaginados]", parameters, commandType: CommandType.StoredProcedure);

            var conteo = await reader.ReadAsync<int>();

            var ofertas = await reader.ReadAsync<Modelo.Oferta.Api.Oferta>();

            return Tuple.Create(conteo.FirstOrDefault(), ofertas.ToList());
        }

        public async Task<List<Modelo.Oferta.Api.Oferta>> ObtenerOfertasxProyectoAsync(int idProyecto)
        {
            var parameters = new DynamicParameters();
            parameters.Add("@idProyecto", idProyecto);

            var reader = await this._conexion.QueryMultipleAsync("[oferta].[ObtenerOfertasxProyecto]", parameters, commandType: CommandType.StoredProcedure);
            var ofertas = await reader.ReadAsync<Modelo.Oferta.Api.Oferta>();

            return ofertas.ToList();
        }

        public async Task<OfertaVivienda> ObtenerDatosGeneralesAsync(int idOferta)
        {
            var parameters = new DynamicParameters();
            parameters.Add("@idOferta", idOferta);

            var reader = await this._conexion.QueryMultipleAsync("oferta.ObtenerOferta", parameters, commandType: CommandType.StoredProcedure);
            var proyecto = await reader.ReadAsync<OfertaVivienda>();

            return proyecto.FirstOrDefault();
        }

        public async Task<List<int>> ObtenerPromotorVendedorxOfertaAsync(int idOferta)
        {
            var parameters = new DynamicParameters();
            parameters.Add("@idOferta", idOferta);

            var reader = await this._conexion.QueryMultipleAsync("oferta.ObtenerPromotorVendedorxOferta", parameters, commandType: CommandType.StoredProcedure);
            var datoscpv = await reader.ReadAsync<int>();

            return datoscpv.ToList();
        }

        public async Task<List<int>> ObtenerPropietarioTerrenoxOfertaAsync(int idOferta)
        {
            var parameters = new DynamicParameters();
            parameters.Add("@idOferta", idOferta);

            var reader = await this._conexion.QueryMultipleAsync("oferta.ObtenerPropietarioTerrenoxOferta", parameters, commandType: CommandType.StoredProcedure);
            var propietarioTerreno = await reader.ReadAsync<int>();

            return propietarioTerreno.ToList();
        }

        public async Task<List<int>> ObtenerDROxOfertaAsync(int idOferta)
        {
            var parameters = new DynamicParameters();
            parameters.Add("@idOferta", idOferta);

            var reader = await this._conexion.QueryMultipleAsync("oferta.ObtenerDirectorResponsableObraxOferta", parameters, commandType: CommandType.StoredProcedure);
            var dro = await reader.ReadAsync<int>();

            return dro.ToList();
        }

        public async Task<List<ZonaRiesgo>> ObtenerZonasRiesgoAsync(int idOferta)
        {
            var parameters = new DynamicParameters();
            parameters.Add("@idOferta", idOferta);

            var reader = await this._conexion.QueryMultipleAsync("oferta.ObtenerOfertaxRiesgoOferta ", parameters, commandType: CommandType.StoredProcedure);
            var zonaRiesgo = await reader.ReadAsync<ZonaRiesgo>();

            return zonaRiesgo.ToList();
        }

        public async Task<List<DocumentoRuv>> ObtenerDocumentoxOfertaAsync(int idOferta, TiposDocumento tipoDocumento)
        {
            var parameters = new DynamicParameters();
            parameters.Add("@idOferta", idOferta);
            parameters.Add("@idCatalogoDocumento", tipoDocumento);

            var reader = await this._conexion.QueryMultipleAsync("oferta.ObtenerDocumentoxOferta", parameters, commandType: CommandType.StoredProcedure);
            var documentos = await reader.ReadAsync<DocumentoRuv>();

            return documentos.ToList();
        }

        public async Task<List<LicenciaFactibilidad>> ObtenerLicenciasFactibilidadAsync(int idOferta)
        {
            var parameters = new DynamicParameters();
            parameters.Add("@idOferta", idOferta);

            var reader = await this._conexion.QueryMultipleAsync("oferta.usp_ObtenerLicenciasFactibilidad", parameters, commandType: CommandType.StoredProcedure);
            var documentos = await reader.ReadAsync<LicenciaFactibilidadOferta>();

            return documentos.Select(d => new LicenciaFactibilidad
            {
                idLicenciaFactibilidad = d.idLicenciaFactibilidad,
                esLicencia = d.esLicencia,
                esFactibilidad = d.esFactibilidad,
                numeroOficio = d.numeroOficio,
                emitidoPor = d.emitidoPor,
                fechaEmision = d.fechaEmision,
                fechaVigencia = d.fechaVigencia,
                activo = d.activo,
                idOfertaVivienda = d.idOfertaVivienda,
                documentoCargado = new DocumentoRuv
                {
                    IdDocumento = d.IdDocumento,
                    NombreArchivo = d.NombreArchivo,
                    UrlArchivo = d.UrlArchivo,
                    IdCatalogoDocumento = d.IdCatalogoDocumento
                }
            }).ToList();
        }

        public async Task<List<DocumentoComplementario>> ObtenerDocumentosComplementariosAsync(int idOferta)
        {
            var parameters = new DynamicParameters();
            parameters.Add("@idOferta", idOferta);

            var reader = await this._conexion.QueryMultipleAsync("oferta.usp_ObtenerDocumentosComplementarios", parameters, commandType: CommandType.StoredProcedure);
            var documentos = await reader.ReadAsync<DocumentoComplementarioOferta>();

            return documentos.Select(dc => new DocumentoComplementario
            {
                tipoDocumento = new Documento
                {
                    idCatalogoDocumento = dc.idCatalogoDocumento,
                    nombre = dc.nombre
                },
                documento = new DocumentoRuv
                {
                    IdDocumento = dc.IdDocumento,
                    NombreArchivo = dc.NombreArchivo,
                    UrlArchivo = dc.UrlArchivo,
                    IdCatalogoDocumento = dc.idCatalogoDocumento
                }
            }).ToList();
        }

        public async Task<Tuple<int, List<DocumentoComplementario>>> ObtenerDocumentosComplementariosPaginadosAsync(int idOferta, int pagina, int tamPag)
        {
            var parameters = new DynamicParameters();
            parameters.Add("@idOferta", idOferta);
            parameters.Add("@pagina", pagina);
            parameters.Add("@tamPag", tamPag);

            var reader = await this._conexion.QueryMultipleAsync("oferta.usp_ObtenerDocumentosComplementariosPaginados", parameters, commandType: CommandType.StoredProcedure);
            var filas = await reader.ReadAsync<int>();
            var documentos = await reader.ReadAsync<DocumentoComplementarioOferta>();
            var resultado = documentos.Select(dc => new DocumentoComplementario
            {
                                    fechaCargaUTC = dc.fechaCarga,
                                    tipoDocumento = new Documento
                                    {
                                        idCatalogoDocumento = dc.idCatalogoDocumento,
                                        nombre = dc.nombre
                                    },
                                    documento = new DocumentoRuv
                                    {
                                        IdDocumento = dc.IdDocumento,
                                        NombreArchivo = dc.NombreArchivo,
                                        UrlArchivo = dc.UrlArchivo,
                                        IdCatalogoDocumento = dc.idCatalogoDocumento
                                    }
                                }).ToList();

            return Tuple.Create(filas.FirstOrDefault(), resultado);
        }

        //DocumentoPrototipoOferta
        public async Task<List<PrototipoVivienda>> ObtenerDocumentosPrototiposAsync(int idOferta)
        {
            var parameters = new DynamicParameters();
            parameters.Add("@idOferta", idOferta);

            var reader = await this._conexion.QueryMultipleAsync("oferta.usp_ObtenerDocumentosPrototipos", parameters, commandType: CommandType.StoredProcedure);
            var documentos = await reader.ReadAsync<DocumentoPrototipoOferta>();

            return documentos.Select(dc => new PrototipoVivienda
            {
                idPrototipo = dc.idPrototipo,
                nombrePrototipo = dc.nombrePrototipo,
                estaSeleccionado = false,
                documentoPrototipo = new DocumentoRuv
                {
                    IdDocumento = dc.IdDocumento,
                    NombreArchivo = dc.NombreArchivo,
                    UrlArchivo = dc.UrlArchivo,
                    IdCatalogoDocumento = dc.idCatalogoDocumento
                }
            }).ToList();
        }

        public async Task<List<PrototipoCatalogo>> ObtenerCatalogoPrototiposAsync()
        {
            var reader = await this._conexion.QueryMultipleAsync("oferta.ObtenerPrototipos", null, commandType: CommandType.StoredProcedure);
            var catalogo = await reader.ReadAsync<PrototipoCatalogo>();

            return catalogo.ToList();
        }

        /// <summary>
        ///
        /// </summary>
        /// <returns></returns>
        public async Task<List<Documento>> ObtenerCatalogoDocumentosComplementariosAsync()
        {
            var reader = await this._conexion.QueryMultipleAsync("catalogo.ObtenerDocumentosComplementarios", null, commandType: CommandType.StoredProcedure);
            var catalogo = await reader.ReadAsync<Documento>();

            return catalogo.ToList();
        }

        public async Task GuardarLicenciaFactibilidadAsync(List<LicenciaFactibilidad> licenciasFactibilidades, int idOferta)
        {
            foreach (var licencia in licenciasFactibilidades)
            {
                var parametros = new DynamicParameters();
                parametros.Add("@esLicencia", licencia.esLicencia);
                parametros.Add("@esFactibilidad", licencia.esFactibilidad);
                parametros.Add("@numeroOficio", licencia.numeroOficio);
                parametros.Add("@emitidoPor", licencia.emitidoPor);
                parametros.Add("@fechaEmision", licencia.fechaEmision);
                parametros.Add("@fechaVigencia", licencia.fechaVigencia);
                parametros.Add("@activo", licencia.activo);
                parametros.Add("@idDocumento", licencia.documentoCargado.IdDocumento);
                parametros.Add("@idOfertaVivienda", idOferta);

                await this._conexion.ExecuteScalarAsync("oferta.usp_LicenciaFactibilidad_Insert", parametros, commandType: CommandType.StoredProcedure);
            }
        }

        public async Task ActualizarLicenciaFactibilidadAsync(List<LicenciaFactibilidad> licenciasFactibilidades, int idOferta)
        {
            foreach (var licencia in licenciasFactibilidades)
            {
                var parametros = new DynamicParameters();
                parametros.Add("@esLicencia", licencia.esLicencia);
                parametros.Add("@esFactibilidad", licencia.esFactibilidad);
                parametros.Add("@numeroOficio", licencia.numeroOficio);
                parametros.Add("@emitidoPor", licencia.emitidoPor);
                parametros.Add("@fechaEmision", licencia.fechaEmision);
                parametros.Add("@fechaVigencia", licencia.fechaVigencia);
                parametros.Add("@activo", licencia.activo);
                parametros.Add("@idDocumento", licencia.documentoCargado.IdDocumento);
                parametros.Add("@idOfertaVivienda", idOferta);
                parametros.Add("@idLicenciaFactibilidad", licencia.idLicenciaFactibilidad);

                await this._conexion.ExecuteScalarAsync("oferta.usp_ActualizarLicenciaFactibilidad", parametros, commandType: CommandType.StoredProcedure);
            }
        }

        public async Task EliminarLicenciaFactibilidadAsync(List<LicenciaFactibilidad> licenciasFactibilidades, int idOferta)
        {
            foreach (var licencia in licenciasFactibilidades)
            {
                var parametros = new DynamicParameters();
                parametros.Add("@idOfertaVivienda", idOferta);
                parametros.Add("@idLicenciaFactibilidad", licencia.idLicenciaFactibilidad);

                await this._conexion.ExecuteScalarAsync("oferta.usp_EliminarLicenciaFactibilidad", parametros, commandType: CommandType.StoredProcedure);
            }
        }

        public async Task<Dictionary<int, string>> ObtenerProyectosOfertaAsync(int idEmpresa)
        {
            Dictionary<int, string> listaProyectos = new Dictionary<int, string>();
            var parameters = new DynamicParameters();
            parameters.Add("@idEmpresa", idEmpresa);
            var reader = await this._conexion.QueryAsync("oferta.usp_ObtenerProyectosEmpresa", parameters, commandType: CommandType.StoredProcedure);
            foreach (var proyecto in reader)
            {
                listaProyectos.Add(proyecto.idProyecto, proyecto.nombre);
            }

            return listaProyectos;
        }

        public async Task<FichaPago> ObtenerFichaPagoAsync(int idOferta)
        {
            var parameters = new DynamicParameters();
            parameters.Add("@idOfertaVivienda", idOferta);

            var reader = await this._conexion.QueryMultipleAsync("contabilidad.ObtenerFichaPago", parameters, commandType: CommandType.StoredProcedure);
            var fichaPago = await reader.ReadAsync<FichaPago>();

            return fichaPago.FirstOrDefault();
        }

        public async Task GuardarOfertaViviendaAsync(int idVivienda, int idOfertaVivienda)
        {
            var parametros = new DynamicParameters();
            parametros.Add("@idVivienda", idVivienda);
            parametros.Add("@idOfertaVivienda", idOfertaVivienda);

            var results = await this._conexion.ExecuteAsync("oferta.usp_GuardarAsignarViviendasAOferta", parametros, commandType: CommandType.StoredProcedure);
        }

        public async Task EliminarOfertaViviendaAsync(int idVivienda, int idOfertaVivienda)
        {
            var parametros = new DynamicParameters();
            parametros.Add("@idVivienda", idVivienda);
            parametros.Add("@idOfertaVivienda", idOfertaVivienda);

            var results = await this._conexion.ExecuteAsync("oferta.usp_EliminarAsignarViviendasAOferta", parametros, commandType: CommandType.StoredProcedure);
        }

        public async Task<bool> EliminarOfertaAsync(int idOfertaVivienda)
        {
            var parameters = new DynamicParameters();
            parameters.Add("@idOfertaVivienda", idOfertaVivienda);

            await this._conexion.ExecuteScalarAsync("oferta.EliminarOferta", parameters, commandType: CommandType.StoredProcedure);

            return true;
        }

        public async Task<bool> EliminarOfertaLogicaAsync(int idOfertaVivienda)
        {
            var parameters = new DynamicParameters();
            parameters.Add("@idOfertaVivienda", idOfertaVivienda);
            parameters.Add("@activo", 0);

            await this._conexion.ExecuteScalarAsync("oferta.EliminarOfertaLogica", parameters, commandType: CommandType.StoredProcedure);

            return true;
        }

        public async Task<int> GuardarDocumentoxPrototipoxOfertaViviendaAsync(int idPrototipoxOfertaVivienda, int idDocumento)
        {
            var parametros = new DynamicParameters();

            parametros.Add("@idPrototipoxOfertaVivienda", idPrototipoxOfertaVivienda);
            parametros.Add("@idDocumento", idDocumento);

            var resultado = await this._conexion.ExecuteScalarAsync("oferta.usp_InsertarDocumentoxPrototipoxOfertaVivienda", parametros, commandType: CommandType.StoredProcedure);
            var PrototipoxOfertaVivienda = resultado ?? 0;

            return Convert.ToInt32(PrototipoxOfertaVivienda);
        }

        public async Task<int> EliminarDocumentoxPrototipoxOfertaViviendaAsync(int idPrototipoxOfertaVivienda, int idDocumento)
        {
            var parametros = new DynamicParameters();

            parametros.Add("@idPrototipoxOfertaVivienda", idPrototipoxOfertaVivienda);
            parametros.Add("@idDocumento", idDocumento);

            var resultado = await this._conexion.ExecuteScalarAsync("oferta.usp_EliminarDocumentoxPrototipoxOfertaVivienda", parametros, commandType: CommandType.StoredProcedure);
            var PrototipoxOfertaVivienda = resultado ?? 0;

            return Convert.ToInt32(PrototipoxOfertaVivienda);
        }

        public async Task ActualizarPromotorExternoxOfertaViviendaAsync(int idPromotorExterno, int idOfertaVivienda)
        {
            throw new NotImplementedException();
        }

        public async Task EliminarZonaRiesgoAsync(ZonaRiesgo[] viviendaRiesgoOferta, int idOferta)
        {
            foreach (var zona in viviendaRiesgoOferta)
            {
                var parameters = new DynamicParameters();
                parameters.Add("@idOferta", idOferta);
                parameters.Add("@idRiesgoOferta", zona.idRiesgoOferta);

                await this._conexion.ExecuteScalarAsync("oferta.EliminarZonaRiesgoOferta", parameters, commandType: CommandType.StoredProcedure);
            }
        }

        public async Task<bool> ActualizarEstatusOfertaViviendaAsync(int idOfertaVivienda, int idEstatusOfertaVivienda, bool actualizarFechaAceptacion=false)
        {
            var parameters = new DynamicParameters();

            parameters.Add("@idOfertaVivienda", idOfertaVivienda);
            parameters.Add("@idEstatusOfertaVivienda", idEstatusOfertaVivienda);
            parameters.Add("@actualizarFechaAceptacion", actualizarFechaAceptacion);

            var reader = await this._conexion.ExecuteScalarAsync("oferta.ActualizarEstatusOfertaVivienda", parameters, commandType: CommandType.StoredProcedure);
            var renglonesAfectados = Convert.ToInt32(reader ?? 0);
            var result = renglonesAfectados > 0 ? true : false;

            return result;
        }

        public async Task<bool> ActualizarEstatusViviendaAsync(int idOfertaVivienda, int idEstatusVivienda)
        {
            var parameters = new DynamicParameters();

            parameters.Add("@idOfertaVivienda", idOfertaVivienda);
            parameters.Add("@idEstatusVivienda", idEstatusVivienda);

            var reader = await this._conexion.ExecuteScalarAsync("oferta.ActualizarEstatusVivienda", parameters, commandType: CommandType.StoredProcedure);
            var renglonesAfectados = Convert.ToInt32(reader ?? 0);
            var result = renglonesAfectados > 0 ? true : false;

            return result;
        }

        public async Task<bool> ActualizarEstatusViviendaPoProyectoAsync(int idProyecto, int idEstatusVivienda)
        {
            var parameters = new DynamicParameters();

            parameters.Add("@idProyecto", idProyecto);
            parameters.Add("@idEstatusVivienda", idEstatusVivienda);

            var reader = await this._conexion.ExecuteScalarAsync("oferta.ActualizarEstatusViviendaPorProyecto", parameters, commandType: CommandType.StoredProcedure);
            var renglonesAfectados = Convert.ToInt32(reader ?? 0);
            var result = renglonesAfectados > 0 ? true : false;

            return result;
        }

        public async Task<int> DesvincularViviendaAsync(int idOfertaVivienda)
        {
            var parameters = new DynamicParameters();

            parameters.Add("@idOfertaVivienda", idOfertaVivienda);

            var reader = await this._conexion.ExecuteScalarAsync("oferta.DesvinculaViviendas", parameters, commandType: CommandType.StoredProcedure);
            var result = Convert.ToInt32(reader ?? 0);

            return result;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="idProyecto"></param>
        /// <returns></returns>
        public async Task<List<Vivienda>> ObtenerVivendasPorIdProyectoAsync(int idProyecto, int? idOferta)
        {
            var parametros = new DynamicParameters();
            parametros.Add("@idProyecto", idProyecto);
            parametros.Add("@idOfertaVivienda", idOferta);

            var reader = await this._conexion.QueryMultipleAsync("[oferta].[usp_ObtenerViviendasPorIdProyecto]", parametros, commandType: CommandType.StoredProcedure);
            var viviendas = await reader.ReadAsync<Vivienda>();

            return viviendas.ToList();
        }

        /// <summary>
        /// Obtiene el listado de ordenes de trabajo para una oferta por idOferta
        /// </summary>
        /// <param name="idOfertaVivienda">Identificador de la oferta</param>
        /// <param name="tamanioPagina">Tamaño de pagina</param>
        /// <param name="pagina">Pagina</param>
        /// <returns>Lista de ordenes de trabajo de la oferta</returns>
        public async Task<Tuple<int, List<OrdenTrabajoHistoricoValidacion>>> ObtenerOrdenesTrabajoPorOfertaPaginadoAsync(int idOfertaVivienda, int tamanioPagina, int pagina)
        {
            var parameters = new DynamicParameters();
            parameters.Add("@idOfertaVivienda", idOfertaVivienda);
            parameters.Add("@tamanioPagina", tamanioPagina);
            parameters.Add("@pagina", pagina);


            var reader = await this._conexion.QueryMultipleAsync("oferta.ObtenerOrdenesTrabajoXOfertaPaginado", parameters, commandType: CommandType.StoredProcedure);
            var conteo = await reader.ReadAsync<int>();
            var ordenes = await reader.ReadAsync<OrdenTrabajoHistoricoValidacion>();

            return Tuple.Create(conteo.FirstOrDefault(), ordenes.ToList());
        }

        public async Task<List<Vivienda>> ObtenerViviendasPorOfertaViviendaAsync(int idOfertaVivienda)
        {
            var parameters = new DynamicParameters();
            parameters.Add("@idOfertaVivienda", idOfertaVivienda);

            var viviendas = await this._conexion.QueryAsync<Vivienda, DomicilioGeografico, DomicilioCarreteraCamino, DomicilioCarreteraCamino, Vivienda>("oferta.usp_ObtenerViviendasPorIdOfertaVivienda", (vivienda, domicilioGeografico, domicilioCarretera, domicilioCamino) =>
            {
                vivienda.domicilioGeografico = domicilioGeografico ?? new DomicilioGeografico();
                vivienda.domicilioCarreteraCamino = domicilioCarretera ?? domicilioCamino ?? new DomicilioCarreteraCamino();
                return vivienda;
            },

            splitOn: "idDomicilioGeografico",
            commandType: CommandType.StoredProcedure,
            param: parameters
            );

            return viviendas.ToList();
        }

        public async Task<Tuple<int, List<Vivienda>>> ObtenerVivienasPorOfertaPaginadoAsync(int idOferta, int tamanioPagina, int pagina)
        {
            var parameters = new DynamicParameters();
            parameters.Add("@idOfertaVivienda", idOferta);
            parameters.Add("@tamanioPagina", tamanioPagina);
            parameters.Add("@pagina", pagina);


            var reader = await this._conexion.QueryMultipleAsync("oferta.usp_ObtenerViviendasPorOfertaPaginado", parameters, commandType: CommandType.StoredProcedure);
            var conteo = await reader.ReadAsync<int>();
            var viviendas = await reader.ReadAsync<Vivienda>();

            return Tuple.Create(conteo.FirstOrDefault(), viviendas.ToList());
        }

        /// <summary>
        /// Obtiene el catalogo de estatus de vivienda
        /// </summary>        
        public async Task<List<Modelo.Oferta.Data.EstatusVivienda>> ObtenerCatalogoEstatusViviendaAsync()
        {
            var reader = await this._conexion.QueryMultipleAsync("[catalogo].[usp_EstatusVivienda_Select]", null, commandType: CommandType.StoredProcedure);
            var estatus = await reader.ReadAsync<Modelo.Oferta.Data.EstatusVivienda>();
            return estatus.ToList();
        }

        /// <summary>
        /// Obtiene los ids y clave de oferta con estatus determinado dado un idProyecto
        /// </summary>
        /// <param name="idProyecto">Identificador dle proyecto</param>        
        public async Task<List<ClaveOferta>> ObtenerIdsOfertaPorProyectoAsync(int idProyecto, int idEstatusOferta)
        {
            var parametros = new DynamicParameters();
            parametros.Add("@idProyecto", idProyecto);
            //parametros.Add("@idEstatusOferta", idEstatusOferta);

            var reader = await this._conexion.QueryMultipleAsync("[oferta].[ObtenerOfertasxIdProyectoYEstatusOferta]", parametros, commandType: CommandType.StoredProcedure);
            var ids = await reader.ReadAsync<ClaveOferta>();

            return ids.ToList();
        }

        public async Task<int> GuardaClaveAsync(string claveOferta, int idOfertaVivienda)
        {
            var parameters = new DynamicParameters();

            parameters.Add("@idOfertaVivienda", idOfertaVivienda);
            parameters.Add("@claveOferta", claveOferta);

            var reader = await this._conexion.ExecuteScalarAsync("oferta.GuardarClaveOfertaVivienda", parameters, commandType: CommandType.StoredProcedure);
            var result = Convert.ToInt32(reader ?? 0);

            return result;
        }
        public async Task<List<Tuple<int, int, int>>> ObtenerViviendasPorIdOfertaViviendaAsync(int idVivienda)
        {
            var parametros = new DynamicParameters();
            parametros.Add("@idOferta", idVivienda);

            var reader = await this._conexion.QueryMultipleAsync("[oferta].[usp_ObtenerViviendasPorIdVivienda]", parametros, commandType: CommandType.StoredProcedure);
            var resultado = await reader.ReadAsync();
            var tupla = resultado
                            .Select(c => new { c.idOfertaVivienda, c.idVivienda, c.idEstatusVivienda })
                            .AsEnumerable()
                            .Select(c => new Tuple<int, int, int>(c.idOfertaVivienda, c.idVivienda, c.idEstatusVivienda))
                            .ToList();

            return tupla;
        }

        public async Task<List<int>> ObtenerViviendasAsignadasPorListaAsync(int idOferta, string listaIdsViviendas)
        {
            var parametros = new DynamicParameters();
            parametros.Add("@idOferta", idOferta);
            parametros.Add("@listaIdsViviendas", listaIdsViviendas);

            var reader = await this._conexion.QueryMultipleAsync("[oferta].[ObtenerViviendasAsignadasPorLista]", parametros, commandType: CommandType.StoredProcedure);
            var idsVivivendas = await reader.ReadAsync<int>();

            return idsVivivendas.ToList();
        }

        public async Task<string> ObtenerCUVPorIdViviendaAsync(int idVivienda)
        {
            var parametros = new DynamicParameters();
            parametros.Add("@idVivienda", idVivienda);

            var reader = await this._conexion.ExecuteScalarAsync("[oferta].[usp_ObtenerCUVPorIdVivienda]", parametros, commandType: CommandType.StoredProcedure);
            string cuv = Convert.ToString(reader);

            return cuv;
        }

        public async Task<string> ObtenerCUVGeograficaPorIdViviendaAsync(int idVivienda)
        {
            var parametros = new DynamicParameters();
            parametros.Add("@idVivienda", idVivienda);

            var reader = await this._conexion.ExecuteScalarAsync("[oferta].[usp_ObtenerCUVGeograficaPorIdVivienda]", parametros, commandType: CommandType.StoredProcedure);
            string cuvGeografica = Convert.ToString(reader);

            return cuvGeografica;
        }

        public async Task<List<Tuple<int, int?>>> ObtenerIdOfertaPorIdViviendaAsync(string idVivienda)
        {
            var parametros = new DynamicParameters();
            parametros.Add("@idVivienda", idVivienda);

            var lector = await this._conexion.QueryMultipleAsync("[oferta].[usp_ObtenerIdOfertaPorIdVivienda]", parametros, commandType: CommandType.StoredProcedure);
            var resultado = await lector.ReadAsync();

            var tupla = resultado
                            .Select(c => new { c.idVivienda, c.idOfertaVivienda })
                            .AsEnumerable()
                            .Select(c => new Tuple<int, int?>(c.idVivienda, c.idOfertaVivienda))
                            .ToList();

            return tupla;
        }

        public async Task<Tuple<int, List<LicenciaFactibilidad>>> ObtenerLicenciasFactibilidadPaginadosAsync(int idOferta, int pagina, int tamPag)
        {
            var parametros = new DynamicParameters();
            parametros.Add("@idOferta", idOferta);
            parametros.Add("@pagina", pagina);
            parametros.Add("@tamPag", tamPag);

            var lector = await this._conexion.QueryMultipleAsync("oferta.usp_ObtenerLicenciasFactibilidadPaginado", parametros, commandType: CommandType.StoredProcedure);
            var total = await lector.ReadAsync<int>();
            var documentos = await lector.ReadAsync<LicenciaFactibilidadOferta>();
            var resultado = documentos.Select(d => new LicenciaFactibilidad
                                {
                                    idLicenciaFactibilidad = d.idLicenciaFactibilidad,
                                    esLicencia = d.esLicencia,
                                    esFactibilidad = d.esFactibilidad,
                                    numeroOficio = d.numeroOficio,
                                    emitidoPor = d.emitidoPor,
                                    fechaEmision = d.fechaEmision,
                                    fechaVigencia = d.fechaVigencia,
                                    fechaCargaUTC = d.fechaCarga,
                                    activo = d.activo,
                                    idOfertaVivienda = d.idOfertaVivienda,
                                    documentoCargado = new DocumentoRuv
                                    {
                                        IdDocumento = d.IdDocumento,
                                        NombreArchivo = d.NombreArchivo,
                                        UrlArchivo = d.UrlArchivo,
                                        IdCatalogoDocumento = d.IdCatalogoDocumento
                                    }
                                }).ToList();

            return Tuple.Create(total.FirstOrDefault(), resultado);
        }

        public async Task<Tuple<int, List<PrototipoVivienda>>> ObtenerDocumentosPrototiposPaginadosAsync(int idOferta, int pagina, int tamPag)
        {
            var parametros = new DynamicParameters();
            parametros.Add("@idOferta", idOferta);
            parametros.Add("@pagina", pagina);
            parametros.Add("@tamPag", tamPag);

            var lector = await this._conexion.QueryMultipleAsync("oferta.usp_ObtenerDocumentosPrototiposPaginado", parametros, commandType: CommandType.StoredProcedure);
            var total = await lector.ReadAsync<int>();
            var documentos = await lector.ReadAsync<DocumentoPrototipoOferta>();
            var resultado = documentos.Select(dc => new PrototipoVivienda
                                {
                                    idPrototipo = dc.idPrototipo,
                                    nombrePrototipo = dc.nombrePrototipo,
                                    estaSeleccionado = false,
                                    fechaCargaUTC = dc.fechaCarga,
                                    documentoPrototipo = new DocumentoRuv
                                    {
                                        IdDocumento = dc.IdDocumento,
                                        NombreArchivo = dc.NombreArchivo,
                                        UrlArchivo = dc.UrlArchivo,
                                        IdCatalogoDocumento = dc.idCatalogoDocumento
                                    }
                                }).ToList();

            return Tuple.Create(total.FirstOrDefault(), resultado);
        }

        public async Task<bool> ActualizarEstatusIdViviendaAsync(int idVivienda, int idEstatusVivienda)
        {
            var parametros = new DynamicParameters();

            parametros.Add("@idVivienda", idVivienda);
            parametros.Add("@idEstatusVivienda", idEstatusVivienda);

            var lector = await this._conexion.ExecuteScalarAsync("oferta.usp_ActualizarEstatusVivienda", parametros, commandType: CommandType.StoredProcedure);
            var renglonesAfectados = Convert.ToInt32(lector ?? 0);

            return renglonesAfectados > 0;
        }

        public async Task<List<PrototipoVivienda>> ObtenerPrototiposConsultaGeneral(int? idProyecto, int? idOferta, string cuv = null)
        {
            List<PrototipoVivienda> listaPrototipos = new List<PrototipoVivienda>();
            var parametros = new DynamicParameters();
            parametros.Add("@idProyecto", idProyecto);
            parametros.Add("@idOferta", idOferta);
            parametros.Add("@cuv", cuv);
           

            var lector = await this._conexion.QueryMultipleAsync("oferta.usp_ObtenerPrototiposConsulta", parametros, commandType: CommandType.StoredProcedure);
            var proyectos = await lector.ReadAsync<PrototipoVivienda>();
            listaPrototipos = proyectos.ToList();

            return listaPrototipos;
        }

        public async Task<int> ObtenerIdentificadorViviendaPorCUV(string cuv)
        {
            DynamicParameters parametros = new DynamicParameters();
            parametros.Add("@cuv", cuv);

            var lector = await this._conexion.QueryMultipleAsync("oferta.usp_ObtenerIdViviendaPorCUV", parametros, commandType: CommandType.StoredProcedure);
            var idVivienda = await lector.ReadAsync<int>();

            return idVivienda.FirstOrDefault();
        }

        public async Task<int> ObtenerPrototipoPorViviendaAsync(int idVivienda)
        {
            var parametros = new DynamicParameters();
            parametros.Add("@idVivienda", idVivienda);

            var lector = await this._conexion.QueryMultipleAsync("oferta.usp_ObtenerPrototipoPorVivienda", parametros, commandType: CommandType.StoredProcedure);
            var idPrototipo = await lector.ReadAsync<int>();

            return idPrototipo.FirstOrDefault();
        }

        public async Task<string> ObtenerClaveOfertaxCuvAsync(string cuv)
        {
            var parametros = new DynamicParameters();
            parametros.Add("@cuv", cuv);

            var lector = await this._conexion.QueryMultipleAsync("oferta.usp_ObtenerClaveOfertaxCuv", parametros, commandType: CommandType.StoredProcedure);
            var claveOferta = await lector.ReadAsync<string>();

            return claveOferta.FirstOrDefault();
        }

        public async Task<List<DocumentoEventoVivienda>> ObtenerDocumentosPorIdentificadores(string idDocumentos)
        {
            DynamicParameters parametros = new DynamicParameters();
            parametros.Add("@idDocumentos", idDocumentos);

            var lector = await this._conexion.QueryMultipleAsync("oferta.usp_ObtenerDocumentosPorIdentificadores", parametros, commandType: CommandType.StoredProcedure);
            var listaDocumentos = await lector.ReadAsync<DocumentoEventoVivienda>();

            return listaDocumentos.ToList();
        }

        public async Task<bool> InsertarValoresDocumentoPorEstatusVivienda(int idDocumento, int idVivienda, string descripcionArchivo)
        {
            DynamicParameters parametros = new DynamicParameters();
            parametros.Add("@idDocumento", idDocumento);
            parametros.Add("@idVivienda", idVivienda);
            parametros.Add("@descripcionArchivo", descripcionArchivo);

            object lector = await this._conexion.ExecuteScalarAsync("oferta.usp_InsertarValoresDocumentoPorEventoVivienda", parametros, commandType: CommandType.StoredProcedure);

            int resultado = Convert.ToInt32(lector ?? 0);

            return resultado > 0;
        }

        public async Task<bool> EliminarRegistrosDocumentoPorEventoVivienda(int idDocumento)
        {
            DynamicParameters parametros = new DynamicParameters();
            parametros.Add("@idDocumento", idDocumento);

            object lector = await this._conexion.ExecuteScalarAsync("oferta.usp_EliminarRegistroDocumentoEventoVivienda", parametros, commandType: CommandType.StoredProcedure);

            int resultado = Convert.ToInt32(lector ?? 0);

            return resultado > 0;
        }

        public async Task<List<Tuple<int,string>>> ObtenerMedioIndividualizacion()
        {
            var lector = await this._conexion.QueryMultipleAsync("[oferta].[usp_ObtenerMediosDeIndividualizacion]", null, commandType: CommandType.StoredProcedure);

            var resultado = await lector.ReadAsync();
            List<Tuple<int, string>> tupla = resultado
                            .Select(a => new { a.idInstitucionIndividualizacion, a.nombreInstitucionIndividualizacion })
                            .AsEnumerable()
                            .Select(a => new Tuple<int, string>(a.idInstitucionIndividualizacion, a.nombreInstitucionIndividualizacion))
                            .ToList();

            return tupla;
        }

        public async Task<List<Tuple<int, string>>> ObtenerBancosIndividualizacion()
        {
            var lector = await this._conexion.QueryMultipleAsync("[oferta].[usp_ObtenerBancosIndividualizacion]", null, commandType: CommandType.StoredProcedure);

            var resultado = await lector.ReadAsync();
            List<Tuple<int, string>> tupla = resultado
                            .Select(a => new { a.idInstitucionIndividualizacion, a.nombreBanco })
                            .AsEnumerable()
                            .Select(a => new Tuple<int, string>(a.idInstitucionIndividualizacion, a.nombreBanco))
                            .ToList();

            return tupla;
        }

        public async Task<List<Tuple<int, string>>> ObtenerTipoIndividualizacion(int idMedioIndividualizacion)
        {
            DynamicParameters parametros = new DynamicParameters();
            parametros.Add("@idMedioIndividualizacion", idMedioIndividualizacion);

            var lector = await this._conexion.QueryMultipleAsync("[oferta].[usp_ObtenerTiposIndividualizacion]", parametros, commandType: CommandType.StoredProcedure);

            var resultado = await lector.ReadAsync();
            List<Tuple<int, string>> tupla = resultado
                            .Select(a => new { a.idTipoVentaViviendaxInstitucion, a.nombre })
                            .AsEnumerable()
                            .Select(a => new Tuple<int, string>(a.idTipoVentaViviendaxInstitucion, a.nombre))
                            .ToList();

            return tupla;
        }

        public async Task<bool> InsertarIndividualizacion(int idVivienda, int idMedioIndividualizacion, int? idTipoIndividualizacion, DateTime fechaIndividualizacion)
        {
            DynamicParameters parametros = new DynamicParameters();
            parametros.Add("@idVivienda", idVivienda);
            parametros.Add("@idMedioIndividualizacion", idMedioIndividualizacion);
            parametros.Add("@idTipoIndividualizacion", idTipoIndividualizacion);
            parametros.Add("@fechaIndividualizacion", fechaIndividualizacion);

            object lector = await this._conexion.ExecuteScalarAsync("oferta.usp_InsertarIndividualizacionVivienda", parametros, commandType: CommandType.StoredProcedure);

            int resultado = Convert.ToInt32(lector ?? 0);

            return resultado > 0;
        }

        public async Task<bool> EliminarIndividualizacion(int idVivienda)
        {
            DynamicParameters parametros = new DynamicParameters();
            parametros.Add("@idVivienda", idVivienda);

            object lector = await this._conexion.ExecuteScalarAsync("oferta.usp_EliminarIndividualizacionVivienda", parametros, commandType: CommandType.StoredProcedure);

            int resultado = Convert.ToInt32(lector ?? 0);

            return resultado > 0;
        }
    }
}