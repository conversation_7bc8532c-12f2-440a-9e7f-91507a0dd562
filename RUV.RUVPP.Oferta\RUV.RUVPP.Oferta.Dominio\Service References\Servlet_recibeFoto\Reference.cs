﻿//------------------------------------------------------------------------------
// <auto-generated>
//     Este código fue generado por una herramienta.
//     Versión de runtime:4.0.30319.42000
//
//     Los cambios en este archivo podrían causar un comportamiento incorrecto y se perderán si
//     se vuelve a generar el código.
// </auto-generated>
//------------------------------------------------------------------------------

namespace RUV.RUVPP.Oferta.Dominio.Servlet_recibeFoto {
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "4.0.0.0")]
    [System.ServiceModel.ServiceContractAttribute(Namespace="http://infonavit.org.mx/Avaluo/sndrecibeFoto", ConfigurationName="Servlet_recibeFoto.SI_recibeFoto_SO")]
    public interface SI_recibeFoto_SO {
        
        // CODEGEN: Se está generando un contrato de mensaje, ya que la operación SI_recibeFoto_SO no es RPC ni está encapsulada en un documento.
        [System.ServiceModel.OperationContractAttribute(Action="http://sap.com/xi/WebService/soap1.1", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        RUV.RUVPP.Oferta.Dominio.Servlet_recibeFoto.SI_recibeFoto_SOResponse SI_recibeFoto_SO(RUV.RUVPP.Oferta.Dominio.Servlet_recibeFoto.SI_recibeFoto_SORequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://sap.com/xi/WebService/soap1.1", ReplyAction="*")]
        System.Threading.Tasks.Task<RUV.RUVPP.Oferta.Dominio.Servlet_recibeFoto.SI_recibeFoto_SOResponse> SI_recibeFoto_SOAsync(RUV.RUVPP.Oferta.Dominio.Servlet_recibeFoto.SI_recibeFoto_SORequest request);
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.3752.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://infonavit.org.mx/Avaluo/sndrecibeFoto")]
    public partial class DT_recibeFoto : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string cuvField;
        
        private string id_avaluoField;
        
        private string id_fotoField;
        
        private string fotoField;
        
        private string id_perito_shfField;
        
        private DT_recibeFotoUsuarioVO usuarioVOField;
        
        private DT_recibeFotoMetadatosVO metadatosVOField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string cuv {
            get {
                return this.cuvField;
            }
            set {
                this.cuvField = value;
                this.RaisePropertyChanged("cuv");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string id_avaluo {
            get {
                return this.id_avaluoField;
            }
            set {
                this.id_avaluoField = value;
                this.RaisePropertyChanged("id_avaluo");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string id_foto {
            get {
                return this.id_fotoField;
            }
            set {
                this.id_fotoField = value;
                this.RaisePropertyChanged("id_foto");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string foto {
            get {
                return this.fotoField;
            }
            set {
                this.fotoField = value;
                this.RaisePropertyChanged("foto");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string id_perito_shf {
            get {
                return this.id_perito_shfField;
            }
            set {
                this.id_perito_shfField = value;
                this.RaisePropertyChanged("id_perito_shf");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=5)]
        public DT_recibeFotoUsuarioVO usuarioVO {
            get {
                return this.usuarioVOField;
            }
            set {
                this.usuarioVOField = value;
                this.RaisePropertyChanged("usuarioVO");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=6)]
        public DT_recibeFotoMetadatosVO metadatosVO {
            get {
                return this.metadatosVOField;
            }
            set {
                this.metadatosVOField = value;
                this.RaisePropertyChanged("metadatosVO");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.3752.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://infonavit.org.mx/Avaluo/sndrecibeFoto")]
    public partial class DT_recibeFotoUsuarioVO : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string id_usuarioField;
        
        private string contraseniaField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string id_usuario {
            get {
                return this.id_usuarioField;
            }
            set {
                this.id_usuarioField = value;
                this.RaisePropertyChanged("id_usuario");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string contrasenia {
            get {
                return this.contraseniaField;
            }
            set {
                this.contraseniaField = value;
                this.RaisePropertyChanged("contrasenia");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.3752.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://infonavit.org.mx/Avaluo/sndrecibeFoto")]
    public partial class DT_recibeFoto_res : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string folioField;
        
        private string codigo_recepcionField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string folio {
            get {
                return this.folioField;
            }
            set {
                this.folioField = value;
                this.RaisePropertyChanged("folio");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string codigo_recepcion {
            get {
                return this.codigo_recepcionField;
            }
            set {
                this.codigo_recepcionField = value;
                this.RaisePropertyChanged("codigo_recepcion");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.3752.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://infonavit.org.mx/Avaluo/sndrecibeFoto")]
    public partial class DT_recibeFotoMetadatosVO : object, System.ComponentModel.INotifyPropertyChanged {
        
        private string latitudField;
        
        private string longitudField;
        
        private string fechaVisitaInmuebleField;
        
        private string modeloTelefonoField;
        
        private string versionSOField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string latitud {
            get {
                return this.latitudField;
            }
            set {
                this.latitudField = value;
                this.RaisePropertyChanged("latitud");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string longitud {
            get {
                return this.longitudField;
            }
            set {
                this.longitudField = value;
                this.RaisePropertyChanged("longitud");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string fechaVisitaInmueble {
            get {
                return this.fechaVisitaInmuebleField;
            }
            set {
                this.fechaVisitaInmuebleField = value;
                this.RaisePropertyChanged("fechaVisitaInmueble");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=3)]
        public string modeloTelefono {
            get {
                return this.modeloTelefonoField;
            }
            set {
                this.modeloTelefonoField = value;
                this.RaisePropertyChanged("modeloTelefono");
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=4)]
        public string versionSO {
            get {
                return this.versionSOField;
            }
            set {
                this.versionSOField = value;
                this.RaisePropertyChanged("versionSO");
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "4.0.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class SI_recibeFoto_SORequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://infonavit.org.mx/Avaluo/sndrecibeFoto", Order=0)]
        public RUV.RUVPP.Oferta.Dominio.Servlet_recibeFoto.DT_recibeFoto MT_recibeFoto_req;
        
        public SI_recibeFoto_SORequest() {
        }
        
        public SI_recibeFoto_SORequest(RUV.RUVPP.Oferta.Dominio.Servlet_recibeFoto.DT_recibeFoto MT_recibeFoto_req) {
            this.MT_recibeFoto_req = MT_recibeFoto_req;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "4.0.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class SI_recibeFoto_SOResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://infonavit.org.mx/Avaluo/sndrecibeFoto", Order=0)]
        public RUV.RUVPP.Oferta.Dominio.Servlet_recibeFoto.DT_recibeFoto_res MT_recibeFoto_res;
        
        public SI_recibeFoto_SOResponse() {
        }
        
        public SI_recibeFoto_SOResponse(RUV.RUVPP.Oferta.Dominio.Servlet_recibeFoto.DT_recibeFoto_res MT_recibeFoto_res) {
            this.MT_recibeFoto_res = MT_recibeFoto_res;
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "4.0.0.0")]
    public interface SI_recibeFoto_SOChannel : RUV.RUVPP.Oferta.Dominio.Servlet_recibeFoto.SI_recibeFoto_SO, System.ServiceModel.IClientChannel {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "4.0.0.0")]
    public partial class SI_recibeFoto_SOClient : System.ServiceModel.ClientBase<RUV.RUVPP.Oferta.Dominio.Servlet_recibeFoto.SI_recibeFoto_SO>, RUV.RUVPP.Oferta.Dominio.Servlet_recibeFoto.SI_recibeFoto_SO {
        
        public SI_recibeFoto_SOClient() {
        }
        
        public SI_recibeFoto_SOClient(string endpointConfigurationName) : 
                base(endpointConfigurationName) {
        }
        
        public SI_recibeFoto_SOClient(string endpointConfigurationName, string remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public SI_recibeFoto_SOClient(string endpointConfigurationName, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public SI_recibeFoto_SOClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress) {
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        RUV.RUVPP.Oferta.Dominio.Servlet_recibeFoto.SI_recibeFoto_SOResponse RUV.RUVPP.Oferta.Dominio.Servlet_recibeFoto.SI_recibeFoto_SO.SI_recibeFoto_SO(RUV.RUVPP.Oferta.Dominio.Servlet_recibeFoto.SI_recibeFoto_SORequest request) {
            return base.Channel.SI_recibeFoto_SO(request);
        }
        
        public RUV.RUVPP.Oferta.Dominio.Servlet_recibeFoto.DT_recibeFoto_res SI_recibeFoto_SO(RUV.RUVPP.Oferta.Dominio.Servlet_recibeFoto.DT_recibeFoto MT_recibeFoto_req) {
            RUV.RUVPP.Oferta.Dominio.Servlet_recibeFoto.SI_recibeFoto_SORequest inValue = new RUV.RUVPP.Oferta.Dominio.Servlet_recibeFoto.SI_recibeFoto_SORequest();
            inValue.MT_recibeFoto_req = MT_recibeFoto_req;
            RUV.RUVPP.Oferta.Dominio.Servlet_recibeFoto.SI_recibeFoto_SOResponse retVal = ((RUV.RUVPP.Oferta.Dominio.Servlet_recibeFoto.SI_recibeFoto_SO)(this)).SI_recibeFoto_SO(inValue);
            return retVal.MT_recibeFoto_res;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<RUV.RUVPP.Oferta.Dominio.Servlet_recibeFoto.SI_recibeFoto_SOResponse> RUV.RUVPP.Oferta.Dominio.Servlet_recibeFoto.SI_recibeFoto_SO.SI_recibeFoto_SOAsync(RUV.RUVPP.Oferta.Dominio.Servlet_recibeFoto.SI_recibeFoto_SORequest request) {
            return base.Channel.SI_recibeFoto_SOAsync(request);
        }
        
        public System.Threading.Tasks.Task<RUV.RUVPP.Oferta.Dominio.Servlet_recibeFoto.SI_recibeFoto_SOResponse> SI_recibeFoto_SOAsync(RUV.RUVPP.Oferta.Dominio.Servlet_recibeFoto.DT_recibeFoto MT_recibeFoto_req) {
            RUV.RUVPP.Oferta.Dominio.Servlet_recibeFoto.SI_recibeFoto_SORequest inValue = new RUV.RUVPP.Oferta.Dominio.Servlet_recibeFoto.SI_recibeFoto_SORequest();
            inValue.MT_recibeFoto_req = MT_recibeFoto_req;
            return ((RUV.RUVPP.Oferta.Dominio.Servlet_recibeFoto.SI_recibeFoto_SO)(this)).SI_recibeFoto_SOAsync(inValue);
        }
    }
}
