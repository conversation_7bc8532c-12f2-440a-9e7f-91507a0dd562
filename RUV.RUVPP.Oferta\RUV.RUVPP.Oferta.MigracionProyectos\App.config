﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
  </configSections>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.5.2" />
  </startup>
  <connectionStrings>
    <add name="OfertaConnection" connectionString="data source=ruvpp-sql-dev.eastus.cloudapp.azure.com,1433;initial catalog=ruvPlusDesarrollo;persist security info=True;user id=RUVd3S4RR0LLo;password=**********;multipleactiveresultsets=True" providerName="System.Data.SqlClient" />
    
  <add name="ModeloOferta" connectionString="metadata=res://*/Modelo.ModeloBDOferta.csdl|res://*/Modelo.ModeloBDOferta.ssdl|res://*/Modelo.ModeloBDOferta.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=ruvpp-sql-dev.eastus.cloudapp.azure.com,1433;initial catalog=ruvPlusDesarrollo;persist security info=True;user id=RUVd3S4RR0LLo;password=**********;multipleactiveresultsets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" /></connectionStrings>
  <appSettings>
    <!-- Configuracion del Web Job -->
    <add key="MessagesRetryCount" value="5" />
    <!-- Configuración de Application Insights -->
    <add key="InstrumentationKey" value="cf08ab50-29a4-4797-ac2f-382d13ca7442" />
    <!-- Configuracion de acceso a datos -->
    <add key="RUV.RUVPP.Command.TimeoutSeconds" value="1200" />
    <add key="RUV.RUVPP.Transaccion.Timeout" value="10:00:00" />
    <add key="RUV.RUVPP.RetryTimes" value="4" />
    <!-- Configuraciones del Framework -->
    <add key="LogBookInterceptor.Enable" value="true" />
    <add key="LogBook.Storage.ConnectionName" value="BitacoraStorageConnectionString" />
    <!-- Nombre de la cadena de conexión utilizada por ensamblados Generales. -->
    <add key="NombreVariableConexion" value="GeneralesConnection" />
    <add key="nameDataStorageConnectionString" value="GeneralesStorageConnection" />
    <!-- Configuraciones para la carga de documentos -->
    <add key="Documentos.numeroIntentos" value="1" />
    <add key="Documentos.blockDataStorage" value="10" />
    <add key="Documentos.tiempoEsperaSegundos" value="30" />
    <add key="Documentos.tamañomaximo" value="5" />
    <!--Parametros de Correo-->
    <add key="Notificaciones.ConnectionName" value="NotificacionesStorageConnectionString" />
    <add key="Notificaciones.QueueName" value="notificacionescorreo" />
    <add key="Notificaciones.TableName" value="plantillastipocorreo" />
    <add key="Notificaciones.BlobName" value="adjuntoscorreo" />
    <add key="Notificaciones.NumeroIntentos" value="1" />
    <add key="Notificaciones.TiempoEspera" value="1" />
    <add key="Notificaciones.TamanoBloque" value="1" />
    <!--Parametros de WorkerRole-->
    <add key="Notificaciones.TiempoEsperaWR" value="60" />
    <add key="RUV.RUVPP.UI.Usuarios.Suscripcion" value="http://ruvruppdevwebapi.cloudapp.net/" />
    <add key="Notificaciones.QueueAlerta" value="notificacionesalerta" />
    <add key="Notificaciones.TokenAlerta" value="0D472D28-C528-4D12-935D-34214395140E" />
    <!--Parametros de Conexion Oracle para FDO-->
    <add key="SDFschema" value="Schema1" />
    <add key="OracleUser" value="OFERTADEV" />
    <add key="OraclePassword" value="P1$$w0rdDEV" />
    <add key="OracleUrl" value="map-q2-sd.cloudapp.net:25450/DSIGRUV" />
    <add key="OracleSchema" value="OFERTADEV" />
    <!--Parametros de Validacion con SIG-->
    <add key="ActivarIntegracionSIG" value="1" />
    <!--Generacion CUV Geografica-->
    <add key="UrlSigCuvGeografica" value="http://map-q3-w2k8r2.cloudapp.net/ws/cuvdev/ServicioCUV.svc" />
    <add key="CuvGeografica.Esquema" value="OFERTADEV" />
    <add key="CuvGeografica.GeometriaNombreCampo" value="GEOM" />
    <add key="CuvGeografica.IdNombreCampo" value="FEATID" />
    <add key="CuvGeografica.IdServicio" value="2" />
    <add key="CuvGeografica.NivelDecimal" value="0" />
    <add key="CuvGeografica.NombreTabla" value="SMB_CONSTRUCCIONES" />
    <add key="CuvGeografica.TipoGeometria" value="1" />
    <!--Parámetros de servicio de convivencia ASIS-->
    <add key="ActivarConvivenciaAsIs" value="1" />
    <add key="UrlAsIsGenerarCuvs" value="OfertaVivienda/api/generarCuvs" />
    <add key="UrlActualizarOfertaVivienda" value="OfertaVivienda/api/actualizarrOfertaVivienda" />
    <add key="UrlActualizarPrototipo" value="OfertaVivienda/api/actualizarPrototipo" />
    <add key="UrlActualizarVivienda" value="OfertaVivienda/api/actualizarVivienda " />
    <add key="UrlBaseConvivencia" value="http://ruvintmigradeve.cloudapp.net/" />
    <add key="UrlCancelarViviendas" value="OfertaVivienda/api/cancelarCuvs" />
    <add key="UrlEliminarPrototipo" value="OfertaVivienda/api/borrarPrototipo" />
    <add key="UrlGuardarOfertaVivienda" value="OfertaVivienda/api/guardarOfertaVivienda" />
    <add key="UrlGuardarPrototipo" value="OfertaVivienda/api/guardarPrototipo" />
    <add key="UrlIndividualizarViviendas" value="OfertaVivienda/api/individualizarVivienda" />
    <add key="UrlObtenerPuntajesCUV" value="OfertaVivienda/api/consultarPuntaje " />
    <add key="UrlValidarCUV" value="OfertaVivienda/api/validarPaqueteCUV   " />
    <add key="UrlValidarPrototipo" value="OfertaVivienda/api/validarPrototipo   " />
    <add key="UserASIS" value="IN094146" />
    <add key="PasswordASIS" value="ANGEL079 " />
    <add key="PrefijoClaveOferta" value="60" />
    <add key="TamanioListaValidarCUV" value="20" />
  </appSettings>
  <entityFramework>
    <defaultConnectionFactory type="System.Data.Entity.Infrastructure.LocalDbConnectionFactory, EntityFramework">
      <parameters>
        <parameter value="mssqllocaldb" />
      </parameters>
    </defaultConnectionFactory>
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
    </providers>
  </entityFramework>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.ApplicationInsights" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-10.0.0.0" newVersion="10.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.AI.Agent.Intercept" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.0.7.0" newVersion="2.0.7.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Cors" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.2.3.0" newVersion="5.2.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.0.1.0" newVersion="3.0.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.0.1.0" newVersion="3.0.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security.OAuth" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.0.1.0" newVersion="3.0.1.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>