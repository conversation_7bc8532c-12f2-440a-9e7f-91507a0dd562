﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.Oferta.Api
{
    public class MensajeDictaminacionOferta
    {
        /// <summary>
        /// Id del usuario que envia el proyecto.
        /// </summary>
        public int IdUsuario { get; set; }

        /// <summary>
        /// Nombre del usuario que envia el proyecto.
        /// </summary>
        public string NombreUsuario { get; set; }

        /// <summary>
        /// Token de acceso del usuario que envia el proyecto.
        /// </summary>
        public string TokenGuid { get; set; }

        /// <summary>
        /// Id de la empresa que envia el proyectos.
        /// </summary>
        public int? IdEmpresa { get; set; }

        /// <summary>
        /// Id del proyecto enviado.
        /// </summary>
        public int IdOferta { get; set; }

        /// <summary>
        /// Id del estatus previo del proyecto
        /// </summary>
        public int IdEstatusPrevio { get; set; }

        /// <summary>
        /// Id de la orden de trabajo a dictaminar.
        /// </summary>
        public int IdOrdenTrabajo { get; set; }

        /// <summary>
        /// Id del servicio a dictaminar
        /// </summary>
        public short IdServicio { get; set; }

        /// <summary>
        /// Indica si la dictaminacion es aceptacion o rechazo
        /// </summary>
        public bool Aceptacion { get; set; }

        /// <summary>
        /// Indica si es una nueva dictaminacion o una actualizacion.
        /// </summary>
        public bool SeActualizaDictaminacion { get; set; }

        /// <summary>
        /// Id del proyecto a dictaminar
        /// </summary>
        public int IdRegistro { get; set; }
    }
}
