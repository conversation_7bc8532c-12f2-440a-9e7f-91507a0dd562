﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.Prototipos.Api
{
    /// <summary>
    /// 
    /// </summary>
    public class AreaVivienda
    {
        public byte? idAreaVivienda { get; set; }

        public byte? idTipoDistribucionVivienda { get; set; }        

        public string nombre { get; set; }

        public string descripcion { get; set; }

        public DateTime? fechaRegistro { get; set; }

        public DateTime? fechaActualizacion { get; set; }

        public bool? activo { get; set; }
    }
}
