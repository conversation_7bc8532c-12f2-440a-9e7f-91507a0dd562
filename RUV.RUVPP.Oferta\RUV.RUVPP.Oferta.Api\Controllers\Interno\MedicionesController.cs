﻿using Microsoft.ApplicationInsights;
using RUV.Comun.Seguridad.JWT;
using RUV.Comun.Utilerias;
using RUV.Comun.Web.Http;
using RUV.RUVPP.Oferta.Dominio.Mediciones;
using RUV.RUVPP.Oferta.Modelo.Mediciones;
using RUV.RUVPP.Oferta.Modelo.Proyectos.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using System.Web.Http.Description;

namespace RUV.RUVPP.Oferta.Api.Controllers.Interno
{
    [RoutePrefix("interno/api/mediciones")]
    public class MedicionesController : ApiControllerBase
    {
        #region Campos

        private readonly IServicioMediciones _servicioMediciones;

        #endregion

        #region Acciones

        /// <summary>
        /// 
        /// </summary>
        /// <param name="clienteTelemetria"></param>
        /// <param name="_servicioMediciones"></param>
        public MedicionesController(IServicioMediciones _servicioMediciones)
            : base()   
        {
            this._servicioMediciones = _servicioMediciones;
        }

        [HttpGet, Route("proyectos/{idProyecto}/ofertas/{clave}/ordenesVerificacion/{ordenVerificacion}/cuvs/{cuv}/estatus/{idEstatus}/entidadesFederativas/{idEntidadFederativa}/frente/{nombreFrente}/registros/{numeroRegistroRuv}/tamanioPagina/{tamanioPagina}/pagina/{pagina}")]
        [ResponseType(typeof(ResultadoPaginado<List<ConsultaCuv>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerCuvsFiltradasPaginadasAsync([FromUri]int tamanioPagina, [FromUri]int pagina, int? idProyecto, [FromUri] string clave, [FromUri] string ordenVerificacion, [FromUri] string cuv, int? idEstatus, int? idEntidadFederativa, [FromUri] string nombreFrente, [FromUri] string numeroRegistroRuv)
        {
            var usuario = (CustomUserRuv)this.User;
            FiltrosConsultaCuv filtros = new FiltrosConsultaCuv() { IdProyecto = idProyecto, ClaveOferta = clave == "null" ? null : clave, OrdenVerificacion = ordenVerificacion == "null" ? null : ordenVerificacion, Cuv = cuv == "null" ? null : cuv, IdEstatusJuridicoVivienda = idEstatus, IdEntidadFederativa = idEntidadFederativa, NumeroRegistroRuv = numeroRegistroRuv == "null" ? null : numeroRegistroRuv, NombreFrente = nombreFrente == "null" ? null : nombreFrente };
            ResultadoPaginado<List<ConsultaCuv>> cuvs = await this._servicioMediciones.ObtenerCuvsFiltradasPaginadasAsync(filtros, usuario, tamanioPagina, pagina);
            return Request.CreateResponse(HttpStatusCode.OK, cuvs);
        }

        [HttpGet, Route("ofertas/{clave}/ordenesVerificacion/{ordenVerificacion}/cuvs")]
        [ResponseType(typeof(List<ConsultaCuv>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerCuvsAsync(string clave, string ordenVerificacion)
        {
            List<ConsultaCuv> cuvs = await this._servicioMediciones.ObtenerCuvsAsync(clave, ordenVerificacion);
            return Request.CreateResponse(HttpStatusCode.OK, cuvs);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="cuv"></param>
        /// <returns></returns>
        [HttpGet, Route("cuv/{cuv}/ecotecnologias")]
        [ResponseType(typeof(ResultadoPaginado<List<List<Ecotecnologias>>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerEcotecnologiasXCUVConPaginadoAsync(int tamanioPagina, int pagina, string cuv)
        {
            var result = await this._servicioMediciones.ObtenerEcotecnologiasXCUVConPaginadoAsync(tamanioPagina, pagina, cuv);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("cuv/{cuv}/atributos")]
        [ResponseType(typeof(ResultadoPaginado<List<List<Ecotecnologias>>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerAtributosXCUVConPaginadoAsync(int tamanioPagina, int pagina, string cuv)
        {
            var result = await this._servicioMediciones.ObtenerAtributosXCUVConPaginadoAsync(tamanioPagina, pagina, cuv);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("equipamiento/{claveOferta}/oferta")]
        [ResponseType(typeof(ResultadoPaginado<List<List<Equipamiento>>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerEquipamientoPorOfertaAsync(int tamanioPagina, int pagina, string claveOferta)
        {
            var result = await this._servicioMediciones.ObtenerEquipamientoPorOferta(tamanioPagina, pagina, claveOferta);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("equipamiento/{idOrdenVerificacion}/orden")]
        [ResponseType(typeof(ResultadoPaginado<List<List<Equipamiento>>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerEquipamientoPorOrdenAsync(int tamanioPagina, int pagina, string idOrdenVerificacion)
        {
            var result = await this._servicioMediciones.ObtenerEquipamientoPorOrden(tamanioPagina, pagina, idOrdenVerificacion);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("vivienda/{claveOferta}/equipamiento")]
        [ResponseType(typeof(ResultadoPaginado<List<List<Vivienda>>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerViviendasPorEquipamientoAsync(int tamanioPagina, int pagina, string claveOferta, string atributo = null)
        {
            var result = await this._servicioMediciones.ObtenerViviendasPorEquipamiento(tamanioPagina, pagina, atributo, claveOferta);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="cuv"></param>
        /// <returns></returns>
        [HttpGet, Route("equipamiento/{cuv}/vivienda")]
        [ResponseType(typeof(ResultadoPaginado<List<List<Equipamiento>>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerEquipamientoPorVivienda(int tamanioPagina, int pagina, string cuv)
        {
            var result = await this._servicioMediciones.ObtenerEquipamientoPorVivienda(tamanioPagina, pagina, cuv);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// Obtiene una lista de viviendas para su consulta
        /// </summary>
        /// <param name="idProyecto"></param>
        /// <param name="idOferta"></param>
        /// <param name="idOrdenVerificacion"></param>
        /// <returns></returns>
        [HttpGet, Route("consulta/sembrado")]
        [ResponseType(typeof(List<Vivienda>))]
        public async Task<HttpResponseMessage> ObtenerViviendasConsultaSembrado(int? idProyecto = null, int? idOferta = null, string idOrdenVerificacion = null)
        {
            var result = await this._servicioMediciones.ObtenerViviendasConsultaSembradoAsync(idProyecto, idOferta, idOrdenVerificacion);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        #endregion

        #region Disposible

        protected override void Dispose(bool disposing)
        {
            base.Dispose(disposing);

            this._servicioMediciones.Dispose();
        }

        #endregion
    }
}