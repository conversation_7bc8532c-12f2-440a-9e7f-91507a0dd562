﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api
{
    public class OrdenVerificacionAseguradora
    {
        public string oferta { get; set; }
        public string nombreFrente { get; set; }
        public string orden { get; set; }
        public string estatusOrden { get; set; }
        public string estatusDocumental { get; set; }
        public string totalViviendasOrden { get; set; }
        public string totalViviendasOferta { get; set; }
        public string fechaAsignacionOrden { get; set; }
        public string entidadOferta { get; set; }
        public string municipioOferta { get; set; }
        public string coloniaOferta { get; set; }
        public string cpOferta { get; set; }
        public string ponderacionUrbanizacion { get; set; }
        public string ponderacionInfraestructura { get; set; }
        public string ponderacionEquipamiento { get; set; }
        public string oferente { get; set; }
        public string rfcOferente { get; set; }
        public string nombreOferente { get; set; }
        public string calleOferente { get; set; }
        public string numExtOferente { get; set; }
        public string coloniaOferente { get; set; }
        public string cpOferente { get; set; }
        public string telefonoOferente { get; set; }
        public string mailOferente { get; set; }
        public string verificador { get; set; }
        public string rfcVerificador { get; set; }
        public string nombreVerificador { get; set; }
        public string calleVerificador { get; set; }
        public string numExtVerificador { get; set; }
        public string coloniaVerificador { get; set; }
        public string cpVerificador { get; set; }
        public string telefonoVerificador { get; set; }
        public string mailVerificador { get; set; }
        public string totalViviendas { get; set; }
        public string fechaPrimeraVisita { get; set; }
        public string porcentajeObra { get; set; }
        public int pruebas { get; set; }
        public string pruebasObservaciones { get; set; }

        public string nivelIncumplimiento { get; set; }


    }
}
