﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;

using System.Web.Http;

using System.Net;
using System.Web.Http.Description;
using RUV.RUVPP.Negocio.General.Seguridad;
using RUV.RUVPP.Entidades.General.Seguridad;
using RUV.Comun.Web.Http;
using Microsoft.ApplicationInsights;
using RUV.Comun.Seguridad.JWT;
using RUV.RUVPP.Negocio.General.OrdenTrabajo.Servicios;
using RUV.RUVPP.Entidades.Comun.Enums;
using System.Configuration;
using RUV.RUVPP.Entidades.General.Notificaciones;
using RUV.RUVPP.Negocio.General.Notificaciones;

namespace RUV.RUVPP.Oferta.Api.Controllers
{
    /// <summary>
    /// Expone operaciones para manejo de la información del usuario loegueado.
    /// </summary>
    [RoutePrefix("interno/api/cuenta")]
    public class CuentaController : ApiControllerBase
    {
        #region Campos

        private readonly IServicioMenu _servicioMenu;
        private readonly IServicioSeguridad _servicioSeguridad;
        private readonly ServicioAlertas _servicioAlertas;

        #endregion

        #region Constructor

        public CuentaController(IServicioMenu serivicioMenu, IServicioSeguridad servicioSeguridad, ServicioAlertas alertas) : base()
        {
            this._servicioMenu = serivicioMenu;
            this._servicioSeguridad = servicioSeguridad;
            this._servicioAlertas = alertas;
        }

        #endregion

        #region Acciones

        /// <summary>
        /// Obtiene las opciones de menú para el usuario.
        /// </summary>
        /// <returns>Opciones de menú del usuario.</returns>
        [ResponseType(typeof(List<MenuDto>))]
        [HttpGet, Route("menu")]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerMenu()
        {
            var usuarioRuv = (CustomUserRuv)User;
            var usuario = new UsuarioSeguridad
            {
                idUsuario = usuarioRuv.IdUsuario,//administrador Oferente
                idPerfil = (short)usuarioRuv.IdPerfil.GetValueOrDefault(),
                idEstatusEmpresa = 5,//aceptada
                esInterno = usuarioRuv.EsInterno
            };
            var sitioSolicitud = ConfigurationManager.AppSettings["Seguridad.ClaveSitio"];

            var menuBase = this._servicioMenu.CrearMenuPorSitioSolicitud(usuario, sitioSolicitud);

            var link = "";

            foreach (var menu in menuBase.SubMenus.FirstOrDefault().SubMenus)
            {
                if (menu.Link != null)
                {

                    var contadorDiagonales = 0;

                    for (int i = 0; i < menu.Link.Length; i++)
                    {
                        link += menu.Link.Substring(i, 1);

                        if (menu.Link.Substring(i, 1) == "/")
                        {
                            contadorDiagonales++;

                            if (contadorDiagonales == 3)
                                break;
                        }
                    }

                    if (contadorDiagonales == 3)
                        break;
                }
            }

            menuBase.SubMenus.Insert(0, new MenuDto(0, true)
            {
                Encabezado = "Principal",
                ParametrosRuta = null,
                SubMenus = null,
                Link = link + "Home/Index",
                EsLinkNavegacion = true                                
            });

            return Request.CreateResponse(HttpStatusCode.OK, menuBase.SubMenus);
        }

        /// <summary>
        /// Cierra la sesión del usuario.
        /// </summary>
        /// <returns>URL de redirección.</returns>
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        [ResponseType(typeof(string))]
        [HttpGet, Route("sesion/cerrar")]
        public async Task<HttpResponseMessage> CerrarSesion()
        {
            var servicioOrdenTrabajo = new ServicioOrdenTrabajo(Producto.Oferta, ServicioProducto.Generico, null);//agreegar contextBitacora

            var usuario = (CustomUserRuv)User;
            UsuarioDto usuarioInfo = this._servicioSeguridad.ObtenerUsuarioDto(usuario.IdUsuario.GetValueOrDefault());


            if (usuarioInfo == null)
                usuarioInfo = this._servicioSeguridad.ObtenerUsuarioDtoSinCorreo(usuario.IdUsuario.GetValueOrDefault());

            if (usuarioInfo.token == null || usuarioInfo.token.Equals(usuario.TokenGuid))
            {
                this._servicioSeguridad.CerrarSesion(new UsuarioDto { idUsuario = usuario.IdUsuario }, DateTime.Now.AddMinutes(1), servicioOrdenTrabajo.SuspenderAlCerrarSesion);
            }

            object objRedirect;// HttpContext.Current.Session["UrlRedirectLogin"]?.ToString();
                               // actionContext.Request.Properties.Add("UrlRedirectLogin", urlRedirect);

            Request.Properties.TryGetValue("UrlRedirectLogin", out objRedirect);

            string urlRedirect = objRedirect?.ToString() ?? string.Empty;

            if (!string.IsNullOrEmpty(urlRedirect))
            {
                return Request.CreateResponse(HttpStatusCode.OK, urlRedirect);
            }
            else
            {
                return Request.CreateResponse(HttpStatusCode.OK, "/Home/App");
            }
        }

        /// <summary>
        /// Obtiene información de las empresas del usuario.
        /// </summary>
        /// <returns>Información de las empresas.</returns>
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        [ResponseType(typeof(IList<UsuarioDto>))]
        [HttpGet, Route("opciones-empresa")]
        public async Task<HttpResponseMessage> ObtenerEmpresaOpciones(int? idEstatusUsuario = null)
        {
            var usuario = (CustomUserRuv)this.User;
            IServicioSeguridad servicioSeguridad = new ServicioSeguridad();
            UsuarioDto usuarioInfo = servicioSeguridad.ObtenerUsuarioDto(usuario.IdUsuario.GetValueOrDefault());

            var idEstatusUsu = usuarioInfo.idEstatusUsuario.GetValueOrDefault();

            if (idEstatusUsuario != null)
                idEstatusUsu = (byte)idEstatusUsuario;

            List<UsuarioDto> usuariosEmpresa = servicioSeguridad.ObtenerPerfilesUsuario(usuarioInfo.rfc, idEstatusUsu);

            return Request.CreateResponse(HttpStatusCode.OK, usuariosEmpresa);
        }

        /// <summary>
        /// Obtiene las notificaciones del usuario.
        /// </summary>
        /// <returns>Notificaciones del usuario.</returns>
        [HttpGet, Route("notificaciones")]
        [ResponseType(typeof(IEnumerable<AlertaDto>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerNotificaciones()
        {
            var usuario = (CustomUserRuv)User;
            int maxMostradas = 20; //definicion de sitio de Empresa
            AlertaDto elemento = new AlertaDto
            {
                activo = true,
                idUsuario = usuario.IdUsuario,
                idTipoAlerta = 1
            };


            int totalAlertas = ObtenerTotalRegistros(elemento);

            IEnumerable<AlertaDto> result = this._servicioAlertas.Obtener(elemento);

            List<AlertaDto> ultimasAlertas = result.OrderByDescending(a => a.fechaRegistro)
                                             .Take(maxMostradas)
                                             .ToList();


            return Request.CreateResponse(HttpStatusCode.OK, ultimasAlertas);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="idEmpresaSeleccionada"></param>
        /// <returns></returns>
        [HttpGet, Route("vincular-empresa")]
        [ResponseType(typeof(bool))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> vincularEmpresa(int idEmpresaSeleccionada)
        {
            var usuario = (CustomUserRuv)this.User;
            bool resultado;
            using (ServicioVinculacion servicio = new ServicioVinculacion())
            {
                resultado = servicio.AceptarVinculacionCuenta(usuario.IdUsuario.GetValueOrDefault(), idEmpresaSeleccionada);
            }

            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }

        #endregion

        #region Metodos privados

        /// <summary>
        /// Obtiene el total de notificaciones de acuerdo a un filtro específico
        /// </summary>
        /// <returns></returns>
        private int ObtenerTotalRegistros(AlertaDto filtro)
        {
            IEnumerable<AlertaDto> result = _servicioAlertas.Obtener(filtro);

            return result.Count();
        }
        #endregion
    }
}
