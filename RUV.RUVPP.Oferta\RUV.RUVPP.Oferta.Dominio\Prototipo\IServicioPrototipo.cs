﻿using RUV.Comun.Utilerias;
using RUV.RUVPP.Oferta.Comun.Modelo;
using RUV.RUVPP.Oferta.Modelo.Proyectos.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Dominio.Prototipo
{
    /// <summary>
    /// 
    /// </summary>
    public interface IServicioPrototipo : IDisposable
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="nombre"></param>
        /// <param name="idEmpresa"></param>
        /// <param name="idPrototipo"></param>
        /// <returns></returns>
        Task<int> ExistePrototipoPorNombreAsync(string nombre, int idEmpresa, int? idPrototipo);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="prototipo"></param>
        /// <param name="guardadoTemporal"></param>
        /// <returns></returns>
        Task<Modelo.Prototipos.Api.Prototipo> GuardarPrototipoAsync(Modelo.Prototipos.Api.Prototipo prototipo, bool guardadoTemporal);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="prototipo"></param>
        /// <returns></returns>
        Task<Modelo.Prototipos.Api.Prototipo> GuardarDetallePrototipoAsync(Modelo.Prototipos.Api.Prototipo prototipo);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="idEmpresa"></param>
        /// <param name="idPrototipo"></param>
        /// <returns></returns>
        Task<bool> ValidarEstatusAsisAsync(int idEmpresa, int idPrototipo);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="idPrototipo"></param>
        /// <param name="nombrePrototipo"></param>
        /// <returns></returns>
        Task<Modelo.Prototipos.Api.Prototipo> DuplicarPrototipoAsync(int idPrototipo, string nombrePrototipo);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="idEmpresa"></param>
        /// <param name="idPrototipo"></param>
        /// <param name="precioDesde"></param>
        /// <param name="precioHasta"></param>
        /// <param name="idTipologiaVivienda"></param>
        /// <param name="nombre"></param>
        /// <param name="oferente"></param>
        /// <param name="recamaras"></param>
        /// <returns></returns>
        Task<ResultadoPaginado<List<Modelo.Prototipos.Api.Prototipo>>> ObtenerPrototiposFiltradoAsync(int tamanioPagina, int pagina, int? idEmpresa, string identificadorPrototipo, float? precioDesde, float? precioHasta, int? idTipologiaVivienda, string nombre, string oferente, int? recamaras);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="idPrototipo"></param>
        /// <param name="soloJSON"></param>
        /// <returns></returns>
        Task<bool> EliminarPrototipoAsync(int idPrototipo, bool soloJSON);   

        /// <summary>
        /// 
        /// </summary>
        /// <param name="idPrototipo"></param>
        /// <returns></returns>
        Task<Modelo.Prototipos.Api.Prototipo> ObtenerPrototipoPorIdAsync(int idPrototipo);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="idPrototipo"></param>
        /// <param name="idVivienda"></param>
        /// <returns></returns>
        Task<ResultadoPaginado<List<Vivienda>>> ObtenerViviendasPorPrototipoIdAsync(int tamanioPagina, int pagina, int idPrototipo, int? idVivienda);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="prototipo"></param>
        /// <param name="guardadoTemporal"></param>
        /// <returns></returns>
        Task<int> ActualizarPrototipoAsync(Modelo.Prototipos.Api.Prototipo prototipo, bool guardadoTemporal);
        Task<ResultadoPaginado<List<Modelo.Prototipos.Api.Prototipo>>> ObtenerPrototiposAsociados(int pagina, int tamPag, int? idProyecto, int? idOferta);

    }
}
