//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace RUV.RUVPP.Oferta.MigracionProyectos.Modelo
{
    using System;
    using System.Collections.Generic;
    
    public partial class PromotorExterno
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public PromotorExterno()
        {
            this.Proyecto = new HashSet<Proyecto>();
        }
    
        public int idPromotorExterno { get; set; }
        public string numeroRegistroRUV { get; set; }
        public string nombreRazonSocial { get; set; }
        public string representanteLegal { get; set; }
        public string lada { get; set; }
        public string numeroTelefono { get; set; }
        public string correoElectronico { get; set; }
        public string registroPatronal { get; set; }
        public System.DateTime fechaRegistro { get; set; }
        public Nullable<System.DateTime> fechaActualizacion { get; set; }
        public bool activo { get; set; }
        public bool esVendedor { get; set; }
        public string rfc { get; set; }
    
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Proyecto> Proyecto { get; set; }
    }
}
