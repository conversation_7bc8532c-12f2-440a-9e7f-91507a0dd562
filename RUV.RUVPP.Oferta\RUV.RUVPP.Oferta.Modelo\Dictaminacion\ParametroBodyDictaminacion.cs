﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.Dictaminacion
{
    public class ParametroBodyDictaminacion
    {
        /// <summary>
        /// JSON de dictaminacion
        /// </summary>
        public string DictaminacionJSON { get; set; }        
        /// <summary>
        /// Indica si se dictamina con aceptación o rechazo
        /// </summary>
        public bool aceptacion { get; set; }
        /// <summary>
        /// Identificador del usuario que dictamina
        /// </summary>
        public int idUsuario { get; set; }
        /// <summary>
        /// Indica si se crea o se actualizan las dictaminaciones en BD
        /// </summary>
        public bool seActualizaDictaminacion { get; set; }
    }
}
