﻿using Microsoft.ApplicationInsights.DataContracts;
using Newtonsoft.Json;
using RUV.Comun.Seguridad.JWT;
using RUV.Comun.Utilerias;
using RUV.Comun.Web.Http;
using RUV.RUVPP.Entidades.Comun;
using RUV.RUVPP.Entidades.Empresa;
using RUV.RUVPP.Entidades.General.Seguridad;
using RUV.RUVPP.Negocio.Empresa.EmpresaConsulta;
using RUV.RUVPP.Negocio.General.OrdenTrabajo.Servicios;
using RUV.RUVPP.Oferta.Dominio.Empresa;
using RUV.RUVPP.Oferta.Dominio.Excepciones;
using RUV.RUVPP.Oferta.Dominio.Proyectos;
using RUV.RUVPP.Oferta.Modelo.Dictaminacion;
using RUV.RUVPP.Oferta.Modelo.Historico.Data;
using RUV.RUVPP.Oferta.Modelo.Plano;
using RUV.RUVPP.Oferta.Modelo.Proyectos;
using RUV.RUVPP.Oferta.Modelo.Proyectos.Api;
using RUV.RUVPP.Oferta.Modelo.Proyectos.Data;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;
using System.Web.Http.Description;
using System.Web.WebPages.Html;

namespace RUV.RUVPP.Oferta.Api.Controllers.Interno
{
    [RoutePrefix("interno/api/proyectos")]
    public class ProyectosController : ApiControllerBase
    {
        private readonly IServicioProyectos _servicioProyectos;
        private readonly IServicioProyectosCatalogos _servicioProyectosCatalogos;
        private readonly IServicioOrdenTrabajo _servicioOrdenTrabajo;
        private readonly IServicioEmpresaConsulta _servicioEmpresaConsulta;
        private readonly IServicioEmpresa _servicioEmpresa;

        public ProyectosController(IServicioProyectos servicioProyectos, IServicioProyectosCatalogos servicioProyectosCatalogos,
                                    IServicioOrdenTrabajo servicioOrdenTrabajo, IServicioEmpresaConsulta servicioEmpresaConsulta, IServicioEmpresa servicioEmpresa)
            : base()
        {
            this._servicioProyectos = servicioProyectos;
            this._servicioProyectosCatalogos = servicioProyectosCatalogos;
            this._servicioOrdenTrabajo = servicioOrdenTrabajo;
            this._servicioEmpresaConsulta = servicioEmpresaConsulta;
            this._servicioEmpresa = servicioEmpresa;
        }

        [HttpPost, Route("")]
        [ResponseType(typeof(int))]
        [AuthorizeApi(FacultadTipo.RegistrarProyecto)]
        public async Task<HttpResponseMessage> GuardarProyectoAsync(Proyecto proyecto)
        {
            var usuario = (CustomUserRuv)User;
            int result = 0;
            var contadorIntentos = 1;

            try
            {
                result = proyecto.idProyecto > 0 ?
                await this._servicioProyectos.ActualizarProyectoAsync(proyecto.idProyecto, proyecto.nombre, JsonConvert.SerializeObject(proyecto), proyecto.idEstatusProyecto, proyecto.fueronModificadosDatosSensibles) :
                await this._servicioProyectos.GuardarProyectoAsync(proyecto.idEmpresa, proyecto.claveEmpresa, proyecto.nombre, JsonConvert.SerializeObject(proyecto), usuario);
            }
            catch (Exception e)
            {
                if (e.Message.Contains("ORA-1257"))
                {
                    if (contadorIntentos > 1)
                    {
                        throw e;
                    }
                    else
                    {
                        contadorIntentos++;
                        result = proyecto.idProyecto > 0 ?
                        await this._servicioProyectos.ActualizarProyectoAsync(proyecto.idProyecto, proyecto.nombre, JsonConvert.SerializeObject(proyecto), proyecto.idEstatusProyecto, proyecto.fueronModificadosDatosSensibles) :
                        await this._servicioProyectos.GuardarProyectoAsync(proyecto.idEmpresa, proyecto.claveEmpresa, proyecto.nombre, JsonConvert.SerializeObject(proyecto), usuario);
                    }
                }
                else
                {
                    throw e;
                }
            }


            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpPost, Route("detalle")]
        [ResponseType(typeof(RespuestaCargaPlano))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> GuardarProyectoDetalleAsync(Proyecto proyecto)
        {
            var result = "";
            RespuestaCargaPlano resultado = new RespuestaCargaPlano();
            var usuario = (CustomUserRuv)User;
            int contadorIntentos = 1;

            this._clienteTelemetria.Context.Properties.Add("IdProyecto", proyecto.idProyecto.ToString());

            try
            {
                bool exitoso = false;
                Exception excepcionALanzar = null;
                Stopwatch cronometro = new Stopwatch();
                bool existeProyectoDetalle = false;

                try
                {
                    this._clienteTelemetria.TrackTrace($"Iniciando procesamiento del proyecto {proyecto.idProyecto}.");
                    cronometro.Start();
                    resultado = await this._servicioProyectos.GuardarProyectoDetalleAsync(proyecto, usuario, false);
                    cronometro.Stop();
                    exitoso = true;

                    this._clienteTelemetria.TrackTrace($"Envio terminado para el proyecto {proyecto.idProyecto}.");
                }
                catch (ExcepcionSig e)
                {
                    if (e.Message.Contains("ORA-00001") || e.Message.Contains("ORA-1257"))
                    {
                        if (e.Message.Contains("ORA-1257"))
                        {
                            if (contadorIntentos > 1)
                            {
                                excepcionALanzar = e;
                            }
                            else
                            {
                                contadorIntentos++;
                                resultado = await this._servicioProyectos.GuardarProyectoDetalleAsync(proyecto, usuario, false);
                            }
                        }
                        else
                        {
                            excepcionALanzar = e;
                        }
                    }
                    else
                    {
                        this._clienteTelemetria.TrackTrace($"Error al enviar proyecto, detalle: \n{e.Message}");
                        this._clienteTelemetria.TrackException(e);

                        return Request.CreateResponse(HttpStatusCode.InternalServerError, e.Message);
                    }
                }
                finally
                {
                    cronometro.Stop();

                    var metrica = new MetricTelemetry("RUVPP.Oferta.EnvioProyecto", cronometro.Elapsed.TotalSeconds);
                    metrica.Properties.Add("IdEmpresa", usuario.IdEmpresa.ToString());
                    metrica.Properties.Add("NoViviendas", proyecto.sembrado.viviendas.Count.ToString());
                    metrica.Properties.Add("EsActualizacion", Convert.ToInt32(existeProyectoDetalle).ToString());
                    metrica.Properties.Add("Exitoso", Convert.ToInt32(exitoso).ToString());

                    this._clienteTelemetria.TrackMetric(metrica);
                }

                if (excepcionALanzar != null)
                    throw excepcionALanzar;
                else
                    return Request.CreateResponse(HttpStatusCode.OK, resultado);
            }
            catch (ExceptionProyectos e)
            {
                this._clienteTelemetria.TrackTrace($"Error al enviar proyecto, detalle: \n{e.Message}");

                return Request.CreateResponse(HttpStatusCode.InternalServerError, e.Message);
            }
            catch (Exception e)
            {
                this._clienteTelemetria.TrackTrace($"Excepción al enviar proyecto {proyecto.idProyecto}.");
                this._clienteTelemetria.TrackException(e);

                throw;
            }
        }

        [HttpDelete, Route("{idProyecto}/parcial")]
        [ResponseType(typeof(bool))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> EliminarProyectoParcialAsync(int idProyecto)
        {
            var result = await this._servicioProyectos.EliminarProyectoParcialAsync(idProyecto);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("")]
        [ResponseType(typeof(ResultadoPaginado<List<Proyecto>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerProyectosAsync(int idEmpresa, int tamanioPagina, int pagina, int? idProyecto = null, string nombreProyecto = null, string idEntidadFederativa = null, string idMunicipio = null, string idLocalidad = null)
        {
            var result = await this._servicioProyectos.ObtenerProyectosAsync(idEmpresa, tamanioPagina, pagina, idProyecto, nombreProyecto, idEntidadFederativa, idMunicipio, idLocalidad);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpDelete, Route("{idProyecto}")]
        [ResponseType(typeof(bool))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> EliminarProyectoAsync(int idProyecto)
        {
            var usuario = (CustomUserRuv)User;
            var proyecto = await this._servicioProyectos.ObtenerProyectoAsync(idProyecto);

            var result = await this._servicioProyectos.EliminarProyectoAsync(idProyecto, usuario);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("{idProyecto}")]
        [ResponseType(typeof(Proyecto))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerProyectoAsync(int idProyecto, bool? esValidacionASIS = null)
        {
            var usuario = (CustomUserRuv)User;
            var result = await this._servicioProyectos.ObtenerProyectoAsync(idProyecto, esValidacionASIS != null ? true : false);

            //if (result.idEstatusProyecto != (int)EstatusProyecto.Validación && result.idEstatusProyecto != (int)EstatusProyecto.ValidaciónPorActualización)
            //{
            if (!string.IsNullOrEmpty(result.temporalJSON))
            {
                var id = result.idProyecto;
                var idestatusProyecto = result.idEstatusProyecto;
                var estatusProyecto = result.estatusProyecto;
                result = JsonConvert.DeserializeObject<Proyecto>(result.temporalJSON);
                result.idProyecto = id;
                result.idEstatusProyecto = idestatusProyecto;
                result.estatusProyecto = estatusProyecto;
                result.tieneDetalle = true;
            }
            //}
            //else
            //{
            //    await this._servicioProyectos.BorrarTemporalAsync(result.idProyecto);
            //}

            result.iniciaRegistro = false;

            if (usuario.IdEmpresa == 1 || usuario.IdEmpresa == result.idEmpresa)
            {
                result.esAccesible = true;
            }
            else
            {
                result = new Proyecto();
                result.esAccesible = false;
            }

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        //TASK: Mover a controlador de OV
        [HttpGet, Route("orden/paginador")]
        [ResponseType(typeof(List<Modelo.OrdenVerificacion.Data.OrdenVerificacion>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerOrdenesVerificacionPorIdProyectoAsync(int tamanioPagina, int pagina, int idProyecto)
        {
            var result = await this._servicioProyectos.ObtenerOrdenesVerificacionPorIdProyectoAsync(tamanioPagina, pagina, idProyecto);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("nombre/{nombre}")]
        [ResponseType(typeof(int))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ExisteProyectoAsync(int idEmpresa, int? idProyecto = null, string nombre = null)
        {
            var result = 0;

            if (nombre != null)
                result = await this._servicioProyectos.ExisteProyectoPorNombreAsync(nombre, idEmpresa, idProyecto);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("empresa/{idEmpresa}")]
        [ResponseType(typeof(List<Proyecto>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerProyectosPorEmpresa(int idEmpresa)
        {
            var resultado = await this._servicioProyectosCatalogos.ObtenerCatalogoProyectosxEmpresa(idEmpresa);

            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }

        [HttpGet, Route("catalogos/zonas-riesgo")]
        [ResponseType(typeof(List<ZonaRiesgo>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerCatalogoZonasDeRiesgoAsync()
        {
            var resultado = await this._servicioProyectosCatalogos.ObtenerZonasDeRiesgoAsync();

            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }

        [HttpGet, Route("catalogos/entidades-federativas")]
        [ResponseType(typeof(List<OpcionCatalogo>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerCatalogoEntidadesFederativasAsync()
        {
            var resultado = await this._servicioProyectosCatalogos.ObtenerCatalogoAsync(CatalogosProyectos.EntidadesFederativas);

            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }

        [HttpGet, Route("catalogos/municipios/{idBusqueda}")]
        [ResponseType(typeof(List<OpcionCatalogo>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerCatalogoMunicipiosAsync(int? idBusqueda)
        {
            var resultado = await this._servicioProyectosCatalogos.ObtenerCatalogoAsync(CatalogosProyectos.Municipios, idBusqueda);

            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }

        //TASK: Unificar con ObtenerCatalogoLocalidadesFiltradoAsync
        [HttpGet, Route("catalogos/localidades/{idBusqueda}")]
        [ResponseType(typeof(List<OpcionCatalogo>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerCatalogoLocalidadesAsync(int idBusqueda)
        {
            var resultado = await this._servicioProyectosCatalogos.ObtenerCatalogoAsync(CatalogosProyectos.Localidades, idBusqueda);

            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }

        //TASK: Unificar con ObtenerCatalogoLocalidadesAsync
        [HttpGet, Route("catalogos/localidades")]
        [ResponseType(typeof(List<OpcionCatalogo>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerCatalogoLocalidadesFiltradoAsync(string idEstado, string idMunicipio)
        {
            var resultado = await this._servicioProyectosCatalogos.ObtenerCatalogoLocalidadesFiltradoAsync(idEstado, idMunicipio);

            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }

        [HttpGet, Route("catalogos/cp")]
        [ResponseType(typeof(List<OpcionCatalogo>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerCatalogoCPFiltradoAsync(string idEstado, string idMunicipio)
        {
            var resultado = await this._servicioProyectosCatalogos.ObtenerCatalogoCPFiltradoAsync(idEstado, idMunicipio);

            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }

        [HttpGet, Route("catalogos/viviendas")]
        [ResponseType(typeof(List<OpcionCatalogo>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerCatalogoTipoViviendaAsync()
        {
            var resultado = await this._servicioProyectosCatalogos.ObtenerCatalogoAsync(CatalogosProyectos.TipoVivienda);

            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }

        [HttpGet, Route("catalogos/tipo-asentamientos")]
        [ResponseType(typeof(List<OpcionCatalogo>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerCatalogoTipoAsentamientoAsync()
        {
            var resultado = await this._servicioProyectosCatalogos.ObtenerCatalogoAsync(CatalogosProyectos.TipoAsentamiento_Oferta);

            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }

        [HttpGet, Route("catalogos/asentamientos")]
        [ResponseType(typeof(List<OpcionCatalogo>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerCatalogoAsentamientoAsync(string idEstado, string idMunicipio)
        {
            var resultado = await this._servicioProyectosCatalogos.ObtenerCatalogoasentamientosFiltradoAsync(idEstado, idMunicipio);

            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }

        [HttpGet, Route("catalogos/zonas")]
        [ResponseType(typeof(List<OpcionCatalogo>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerCatalogoTipoZonasAsync()
        {
            var resultado = await this._servicioProyectosCatalogos.ObtenerCatalogoAsync(CatalogosProyectos.TipoZonas);

            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }

        [HttpGet, Route("catalogos/tipo-vialidades")]
        [ResponseType(typeof(List<OpcionCatalogo>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerCatalogoTipoVialidadesAsync()
        {
            var resultado = await this._servicioProyectosCatalogos.ObtenerCatalogoAsync(CatalogosProyectos.TipoVialidad_Oferta);

            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }

        [HttpGet, Route("catalogos/vialidades")]
        [ResponseType(typeof(List<OpcionCatalogo>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerCatalogoVialidadesAsync(string idEstado, string idMunicipio)
        {
            var resultado = await this._servicioProyectosCatalogos.ObtenerCatalogoVialidadesFiltradoAsync(idEstado, idMunicipio);

            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }

        [HttpGet, Route("catalogos/administraciones")]
        [ResponseType(typeof(List<OpcionCatalogo>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerCatalogoTipoAdministracionesAsync()
        {
            var resultado = await this._servicioProyectosCatalogos.ObtenerCatalogoAsync(CatalogosProyectos.TipoAdministracion);

            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }

        [HttpGet, Route("catalogos/derechosTransito")]
        [ResponseType(typeof(List<OpcionCatalogo>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerCatalogoTipoDerechosTransitoAsync()
        {
            var resultado = await this._servicioProyectosCatalogos.ObtenerCatalogoAsync(CatalogosProyectos.TipoDerechoTransito);

            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }

        [HttpGet, Route("catalogos/caminos")]
        [ResponseType(typeof(List<OpcionCatalogo>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerCatalogoTipoCaminosAsync()
        {
            var resultado = await this._servicioProyectosCatalogos.ObtenerCatalogoAsync(CatalogosProyectos.TipoCamino_Oferta);

            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }

        [HttpGet, Route("catalogos/margenes")]
        [ResponseType(typeof(List<OpcionCatalogo>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerCatalogoTipoMargenesAsync()
        {
            var resultado = await this._servicioProyectosCatalogos.ObtenerCatalogoAsync(CatalogosProyectos.Margenes);

            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }

        [HttpGet, Route("catalogos/orientaciones")]
        [ResponseType(typeof(List<OpcionCatalogo>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerCatalogoTipoOrientacionesAsync()
        {
            var resultado = await this._servicioProyectosCatalogos.ObtenerCatalogoAsync(CatalogosProyectos.Orientaciones);

            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }

        [HttpGet, Route("catalogos/niveles")]
        [ResponseType(typeof(List<OpcionCatalogo>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerCatalogoTipoNivelesAsync()
        {
            var resultado = await this._servicioProyectosCatalogos.ObtenerCatalogoAsync(CatalogosProyectos.Niveles);

            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }

        //TASK: Validar funcionamiento
        [HttpGet, Route("catalogos/prototipos/{idEmpresa}")]
        [ResponseType(typeof(List<OpcionCatalogo>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerCatalogoPrototiposxEmpresaAsync(int idEmpresa)
        {
            var resultado = await this._servicioProyectosCatalogos.ObtenerCatalogoPrototiposxEmpresa(idEmpresa);

            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }

        [HttpPost, Route("plano")]
        [ResponseType(typeof(RespuestaCargaPlano))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> CargarInformacionPlano(int idArchivoSDF, int idUsuario, int idProyecto)
        {
            var usuario = (CustomUserRuv)User;
            //int mensaje = -1;

            var respuesta = await this._servicioProyectos.CargarArchivoSDFAsync(idArchivoSDF, idUsuario, idProyecto, usuario);

            return Request.CreateResponse(HttpStatusCode.OK, respuesta);
        }

        [HttpGet, Route("sembrado")]
        [ResponseType(typeof(Sembrado))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerSembradoProyecto(int idProyectoTemporal)
        {
            Sembrado listaVivienda = new Sembrado();

            listaVivienda = await this._servicioProyectos.ObtenerSembradoTemporalAsync(idProyectoTemporal);

            return Request.CreateResponse(HttpStatusCode.OK, listaVivienda);
        }

        [HttpGet, Route("{idProyecto}/sembrado")]
        [ResponseType(typeof(Sembrado))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerSembradoOficialTemporalProyecto(int idProyecto)
        {
            Sembrado listaVivienda = new Sembrado();

            listaVivienda = await this._servicioProyectos.ObtenerSembradoOficialTemporalAsync(idProyecto, false);
            //listaVivienda.viviendas.AddRange(listaVivienda.viviendas);
            //listaVivienda.viviendas.AddRange(listaVivienda.viviendas);
            //listaVivienda.viviendas.AddRange(listaVivienda.viviendas);
            //listaVivienda.viviendas.AddRange(listaVivienda.viviendas);

            return Request.CreateResponse(HttpStatusCode.OK, listaVivienda);
        }

        //TASK: Unificar con ObtenerProyectosAsync
        [HttpGet, Route("especializada/filtro")]
        [ResponseType(typeof(Proyecto))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerProyectosFiltroAsync(int? idEmpresa = null, string noRuv = null, string nombreProyecto = null, int? idProyecto = null)
        {
            Proyecto result = null;

            if (idEmpresa != null || noRuv != null || nombreProyecto != null || idProyecto != null)
            {
                result = await this._servicioProyectos.ObtenerProyectosFiltroAsync(idEmpresa, noRuv, nombreProyecto, idProyecto);
            }            

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        //TASK: Unificar con ObtenerProyectosAsync
        [HttpGet, Route("especializada/filtroconpaginado")]
        [ResponseType(typeof(ResultadoPaginado<List<Proyecto>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerProyectosFiltroConPaginadoAsync(int tamanioPagina, int pagina, int? idEmpresa = null, string noRuv = null, string nombreProyecto = null, int? idProyecto = null, int? idEntidad = null, int? idEstatus = null)
        {
            var result = await this._servicioProyectos.ObtenerProyectosFiltroConPaginadoAsync(tamanioPagina, pagina, idEmpresa, noRuv, nombreProyecto, idProyecto, idEntidad, idEstatus);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("{idProyecto}/viviendas")]
        [ResponseType(typeof(List<Vivienda>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerViviendasPorProyectoAsync(int idProyecto)
        {
            var result = await this._servicioProyectos.ObtenerViveindasPorProyectoAsync(idProyecto);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("{idProyecto}/viviendas/reporte")]
        [ResponseType(typeof(List<Vivienda>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerViviendasReportePorProyectoAsync(int idProyecto)
        {
            var result = await this._servicioProyectos.ObtenerViveindasReportePorProyectoAsync(idProyecto);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpPost, Route("{idProyecto}/viviendas")]
        [ResponseType(typeof(int))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ActualizarViviendasAsync(Sembrado sembrado)
        {
            var result = new List<Vivienda>();
            int contadorIntentos = 1;
            try
            {
                result = await this._servicioProyectos.ActualizarViviendasAsync(sembrado, null, null, null);
            }
            catch (Exception e)
            {
                if (e.Message.Contains("ORA-1257"))
                {
                    if (contadorIntentos > 1)
                    {
                        throw e;
                    }
                    else
                    {
                        contadorIntentos++;
                        result = await this._servicioProyectos.ActualizarViviendasAsync(sembrado, null, null, null);
                    }
                }
                else
                {
                    throw e;
                }
            }
            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        //TASK: Mover a controlador de OV
        /// <summary>
        /// Obtiene el historico de validaciones(lista de ordenes de trabajo de un proyecto con paginacion)
        /// </summary>
        /// <param name="idProyecto">Identificador del proyecto</param>
        /// <param name="tamanioPagina">Tamaño de pagina</param>
        /// <param name="pagina">Pagina consultada</param>
        /// <returns></returns>
        [HttpGet, Route("{idProyecto}/ordenes-trabajo")]
        [ResponseType(typeof(ResultadoPaginado<List<OrdenTrabajoHistoricoValidacion>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerOrdenesTrabajoPorProyectoPaginadoAsync(int idProyecto, int tamanioPagina, int pagina)
        {
            var result = await this._servicioProyectos.ObtenerOrdenesTrabajoPorProyectoPaginadoAsync(idProyecto, tamanioPagina, pagina);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        //TAKS: Repetido con ObtenerProyectoAsync
        [HttpGet, Route("proyectos/{idEmpresa}/paginador")]
        [ResponseType(typeof(ResultadoPaginado<List<Proyecto>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerProyectosPorIdEmpresaConPaginadorAsync(int tamanioPagina, int pagina, int idEmpresa)
        {
            var result = await this._servicioProyectos.ObtenerProyectosPorIdEmpresaConPaginadorAsync(tamanioPagina, pagina, idEmpresa);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        //TASK: Unificar con ObtenerViviendasPorProyectoAsync
        /// <summary>
        /// Obtiene el listado de viviendas por proyecto paginado
        /// </summary>
        /// <param name="idProyecto">Identificador del proyecto</param>
        /// <param name="tamanioPagina">Tamaño de pagina</param>
        /// <param name="pagina">Pagina consultada</param>
        /// <returns></returns>
        [HttpGet, Route("{idProyecto}/viviendas-paginado")]
        [ResponseType(typeof(ResultadoPaginado<List<Vivienda>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerViviendasPorProyectoPaginadoAsync(int idProyecto, int tamanioPagina, int pagina)
        {
            var result = await this._servicioProyectos.ObtenerViviendasPorProyectoPaginadoAsync(idProyecto, tamanioPagina, pagina);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("{idProyecto}/equipamientos")]
        [ResponseType(typeof(EquipamientosProyecto))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerEquipamientosPorProyecto(int idProyecto, bool esTemporal = true)
        {
            var result = await this._servicioProyectos.ObtenerEquipamientosProyecto(idProyecto, esTemporal);
            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpDelete, Route("{idProyecto}/viviendaTemporal/{featId}")]
        [ResponseType(typeof(bool))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> EliminarViviendaTemporal(int featId)
        {
            var result = await this._servicioProyectos.EliminarViviendaTemporal(featId);
            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpDelete, Route("{idProyecto}/viviendaOficial/{featId}")]
        [ResponseType(typeof(bool))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> EliminarViviendaOficial(int featId)
        {
            var result = await this._servicioProyectos.EliminarViviendaOficial(featId);
            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpPost, Route("datos/ubicacion/sembrado")]
        [ResponseType(typeof(Tuple<string, string>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerEstadosMunicipiosViviendas(Tuple<List<string>, List<Tuple<string, string>>> ids)
        {
            var idsEstados = ids.Item1;
            var idsMunicipios = ids.Item2;
            var result = await this._servicioProyectos.ObtenerEstadosMunicipiosViviendasAsync(idsEstados, idsMunicipios);
            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpDelete, Route("eliminar/equipamiento/{capa}/{featId}")]
        [ResponseType(typeof(bool))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> EliminarEquipamientoTemporlAsync(string capa, int featId)
        {
            var result = await this._servicioProyectos.EliminarEquipamientoTemporlAsync(capa, featId);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }


        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpPost, Route("vivienda/actualizatoken")]
        [ResponseType(typeof(bool))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ActualizaToken()
        {
            return Request.CreateResponse(HttpStatusCode.OK, true);
        }


        #region DictaminarProyecto

        [HttpPost, Route("{idProyecto}/dictaminaciones")]
        [ResponseType(typeof(Tuple<bool, bool>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> EnviarDictaminacionProyecto(int idProyecto, int idOrdenTrabajo, short idServicio, [FromBody]ParametroBodyDictaminacion parametroBodyDictaminacion)
        {
            var usuario = (CustomUserRuv)User;
            int contadorIntentos = 1;
            var result = Tuple.Create(false, false);
            try
            {
                result = await this._servicioProyectos.EnviarDictaminacionProyecto(idOrdenTrabajo, idServicio, idProyecto, parametroBodyDictaminacion, usuario, false);
                return Request.CreateResponse(HttpStatusCode.OK, result);
            }
            catch (Exception e)
            {
                if (e.Message.Contains("ORA-1257"))
                {
                    if (contadorIntentos > 1)
                    {
                        throw e;
                    }
                    else
                    {
                        contadorIntentos++;
                        result = await this._servicioProyectos.EnviarDictaminacionProyecto(idOrdenTrabajo, idServicio, idProyecto, parametroBodyDictaminacion, usuario, false);
                        return Request.CreateResponse(HttpStatusCode.OK, result);
                    }
                }
                else
                {
                    throw e;
                }
            }

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("{idProyecto}/empresa")]
        [ResponseType(typeof(DatosCPV))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerDatosEmpresa(int idProyecto)
        {
            Proyecto proyecto = await this._servicioProyectos.ObtenerProyectoAsync(idProyecto);
            EmpresaDto empresa = this._servicioEmpresaConsulta.ObtenerEmpresaDto(proyecto.idEmpresa);

            DatosCPV datosEmpresa = await this._servicioEmpresa.ConsultaEmpresaPorNRRUV(empresa.idEmpresa, empresa.IdEmpresaInst);

            return Request.CreateResponse(HttpStatusCode.OK, datosEmpresa);
        }

        [HttpGet, Route("catalogos/estatus-proyecto")]
        [ResponseType(typeof(List<EstatusProyecto>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerEstatusProyectos()
        {
            var lista = Enum.GetValues(typeof(EstatusProyecto)).Cast<EstatusProyecto>().Select(v => new SelectListItem
            {
                Text = v.ToString(),
                Value = ((int)v).ToString()
            }).ToList();

            return Request.CreateResponse(HttpStatusCode.OK, lista);
        }

        [HttpGet, Route("empresas")]
        [ResponseType(typeof(List<DatosCPV>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerEmpresas()
        {
            List<DatosCPV> empresas = new List<DatosCPV>();

            PaginadorLista<ConsultaEmpresas, PaginadorDto> resultado = this._servicioEmpresaConsulta.ObtenerEmpresasConsulta(new ConsultaEmpresas { idestatusempresa = "5", identidad = "7" }, new PaginadorDto { pagina = 1, numeroRegistros = 9999 });

            foreach (ConsultaEmpresas empresa in resultado.ListaEntidad)
            {
                DatosCPV empresaDatosCPV = new DatosCPV
                {
                    idEmpresa = empresa.idEmpresa.Value,
                    nombreRazonSocial = empresa.RazonSocial,
                    rfc = empresa.RFC,
                    correoElectronico = empresa.Correo,
                    noRegistroRUV = empresa.NRRuv
                };

                empresas.Add(empresaDatosCPV);
            }

            empresas.Sort(delegate (DatosCPV x, DatosCPV y)
            {
                return x.noRegistroRUV.CompareTo(y.noRegistroRUV);
            });

            return Request.CreateResponse(HttpStatusCode.OK, empresas);
        }

        #endregion DictaminarProyecto

        #region Disposible

        protected override void Dispose(bool disposing)
        {
            base.Dispose(disposing);

            this._servicioEmpresa.Dispose();
            this._servicioProyectos.Dispose();
            this._servicioProyectosCatalogos.Dispose();
        }

        #endregion Disposible
    }
}