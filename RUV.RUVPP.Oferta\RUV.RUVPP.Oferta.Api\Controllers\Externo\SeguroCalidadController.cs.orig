﻿using RUV.Comun.Seguridad.JWT;
using RUV.Comun.Utilerias;
using RUV.Comun.Web.Http;
using RUV.RUVPP.Oferta.Dominio.SeguroCalidad;
using RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api;
using RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using System.Web.Http.Description;
using iTextSharp.text;
using iTextSharp.text.pdf;
using System.IO;
using RUV.RUVPP.Negocio.General.Documentos.Abstracta;
using RUV.RUVPP.Oferta.GeneralesInterop.Documentos;
using RUV.RUVPP.Generales.Modelo.Documentos;
using RUV.RUVPP.Negocio.General.Seguridad;
using RUV.RUVPP.Entidades.General.Seguridad;

namespace RUV.RUVPP.Oferta.Api.Controllers.Externo
{
    [RoutePrefix("externo/api/seguro-calidad")]
    public class SeguroCalidadController : ApiControllerBase
    {

        private readonly IServicioSeguroCalidad _servicioSeguroCalidad;
        private readonly IServicioDocumento<DocumentoOferta> _servicioDocumento;
        private readonly IServicioSeguridad _servicioSeguridad;
        /// <summary>
        /// 
        /// </summary>
        /// <param name="servicioSeguroCalidad"></param>
        public SeguroCalidadController(IServicioSeguroCalidad servicioSeguroCalidad, IServicioDocumento<DocumentoOferta> servicioDocumento, IServicioSeguridad servicioSeguridad)
            : base()
        {
            this._servicioDocumento = servicioDocumento;
            this._servicioSeguroCalidad = servicioSeguroCalidad;
            this._servicioSeguridad = servicioSeguridad;
        }

        #region proceso90dias
        [HttpPost, Route("revisar-parametros-90Dias")]
        [ResponseType(typeof(bool))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> RevisarParametros90Dias()
        {
            var result = await this._servicioSeguroCalidad.RevisarParametros90Dias();

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpPost, Route("actualizar-parametros-config-90Dias")]
        [ResponseType(typeof(bool))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ActualizarParametrosConfigs90DiasAsync(List<ParametrosConfigs> listaParametros)
        {
            var result = await this._servicioSeguroCalidad.ActualizarParametrosConfigs90DiasAsync(listaParametros);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpPost, Route("obtener-parametros-config-90Dias")]
        [ResponseType(typeof(List<ParametrosConfigs>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerParametrosConfigs90DiasAsync(List<string> listaClaves)
        {
            var result = await this._servicioSeguroCalidad.ObtenerParametrosConfigs90DiasAsync(listaClaves);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }
        #endregion

        #region polizas
        [HttpGet, Route("aseguradoras")]
        [ResponseType(typeof(ResultadoPaginado<List<Aseguradora>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerAseguradoraConPaginadoAsync(int tamanioPagina, int pagina, string razonSocial = null, string noRegistroRUV = null)
        {
            var result = await this._servicioSeguroCalidad.ObtenerAseguradoraConPaginadoAsync(tamanioPagina, pagina, razonSocial, noRegistroRUV);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("obtener-cuvsinpolizas")]
        [ResponseType(typeof(ResultadoPaginado<List<CuvPoliza>>))]
        public async Task<HttpResponseMessage> ObtenerCuvSinPolizaConPaginadoAsync(int tamanioPagina, int pagina, int? idOferente = null, string cuv = null, string ordenVerificacion = null, bool? cambioValorAvaluo = null)
        {
            var result = await this._servicioSeguroCalidad.ObtenerCuvSinPolizaConPaginadoAsync(tamanioPagina, pagina, cuv, ordenVerificacion, idOferente, cambioValorAvaluo);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("obtener-consulta-aseguradora")]
        [ResponseType(typeof(ResultadoPaginado<List<OrdenDeVerificacion>>))]
        public async Task<HttpResponseMessage> ObtenerConsultaAseguradoraConPaginadoAsync(int tamanioPagina, int pagina, int? idOferenteExterno = null, int? idAseguradoraExterno = null, int? idTipoAsignacion = null, int? idEstatusPagoEvaluacionRiesgo = null, string noRegistroRUV = null, string razonSocial = null, string ordenVerificacion = null, string noContrato = null, string fechaInicial = null, string fechaFinal = null, string fechaAceptacionInicio = null, string fechaAceptacionFinal = null)
        {
            var result = await this._servicioSeguroCalidad.ObtenerConsultaAseguradoraConPaginadoAsync(tamanioPagina, pagina, noRegistroRUV, razonSocial, ordenVerificacion, noContrato, fechaInicial, fechaFinal, fechaAceptacionInicio, fechaAceptacionFinal, idTipoAsignacion, idOferenteExterno, idAseguradoraExterno, idEstatusPagoEvaluacionRiesgo);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("obtener-info-orden")]
        [ResponseType(typeof(OrdenDeVerificacion))]
        public async Task<HttpResponseMessage> ObtenerInfoOrdenVerificacionAsync(string ordenVerificacion = null)
        {
            var result = await this._servicioSeguroCalidad.ObtenerInfoOrdenVerificacionAsync(ordenVerificacion);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("obtener-cuvconpolizas")]
        [ResponseType(typeof(ResultadoPaginado<List<CuvPoliza>>))]
        public async Task<HttpResponseMessage> ObtenerCuvConPolizaConPaginadoAsync(int tamanioPagina, int pagina, int? idOferente = null, string cuv = null, string ordenVerificacion = null)
        {
            var result = await this._servicioSeguroCalidad.ObtenerCuvConPolizaConPaginadoAsync(tamanioPagina, pagina, cuv, ordenVerificacion, idOferente);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        #endregion

        #region avaluos
        [HttpGet, Route("obtener-lista-avaluos")]
        [ResponseType(typeof(ResultadoPaginado<List<Avaluo>>))]
        public async Task<HttpResponseMessage> ObtenerListaAvaluosAsync(int tamanioPagina, int pagina, string cuv = null)
        {
            var result = await this._servicioSeguroCalidad.ObtenerListaAvaluosAsync(tamanioPagina, pagina, cuv);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }
        #endregion

        #region ServiciosAseguradora

        [HttpPost, Route("documento-poliza")]
        [ResponseType(typeof(ResultadoPeticion))]
        public async Task<HttpResponseMessage> GuardarDocumentoPolizaCUV(RespuestaAseguradoraSolicitud respuestaSolicitud)
        {
            ResultadoPeticion resultadoPeticion = new ResultadoPeticion() { resultado = "OK", descripcion = "Resultado Exitoso" };

            return Request.CreateResponse(HttpStatusCode.OK, resultadoPeticion);
        }

        #endregion

        #region ServiciosInfonavit

        /// <summary>
        /// 
        /// </summary>
        /// <param name="datosTitulacion"></param>
        /// <returns></returns>
        [HttpPost, Route("datos-titulacion")]
        [ResponseType(typeof(ResultadoPeticion))]
        public async Task<HttpResponseMessage> GuardarDatosTitulacionAsyn(DatosTitulacionINFONAVIT datosTitulacion)
        {
            var result = await _servicioSeguroCalidad.GuardarDatosTitulacionAsync(datosTitulacion);
            ResultadoPeticion resultadoPeticion = new ResultadoPeticion() { resultado = "OK", descripcion = "Resultado Exitoso" };

            return Request.CreateResponse(HttpStatusCode.OK, resultadoPeticion);
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="datosValorAvaluo"></param>
        /// <returns></returns>
        [HttpPost, Route("valor-avaluo")]
        [ResponseType(typeof(ResultadoPeticion))]
        public async Task<HttpResponseMessage> GuardarDatosValorAvaluoAsyn(DatosValorAvaluo datosValorAvaluo)
        {
            ResultadoPeticion resultadoPeticion = await _servicioSeguroCalidad.GuardarDatosValorAvaluo(datosValorAvaluo);
            return Request.CreateResponse(HttpStatusCode.OK, resultadoPeticion);
        }

        /// <summary>|
        /// 
        /// </summary>
        /// <param name="datosVivienda"></param>
        /// <returns></returns>
        [HttpPost, Route("datos-vivienda-asegurada")]
        [ResponseType(typeof(DatosViviendaAsegurada))]
        public async Task<HttpResponseMessage> ObtenerDatosViviendaAseguradaAsyn(DatosVivienda datosVivienda)
        {
            DatosViviendaAsegurada resultadoPeticion = new DatosViviendaAsegurada() { claveEntidadOrden = "01", costoPoliza = 1000.0, costoVivienda = 1000000.0, cuv = "1234567890123456", fechaPagoOrden = "08-02-2018", fechaRegistroOrden = "07-08-2018", folioPoliza = "FOL123456789", idOferente = "99999999", idVivienda = 1, marcaSeguro = true, nombreEntidadOrden = "Aguascalientes", ordenVerificacion = "50000000001", pagoEvaluacionRiesgo = true, razonSocialAseguradora = "Aseguradora SA de CV", rfcAseguradora = "ASE111111DER", estatus = 0 };
            return Request.CreateResponse(HttpStatusCode.OK, resultadoPeticion);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="datosVivienda"></param>
        /// <returns></returns>
        [HttpPost, Route("vivienda-asegurada")]
        [ResponseType(typeof(ViviendaAsegurada))]
        public async Task<HttpResponseMessage> ViviendaAseguradaAsyn(DatosVivienda datosVivienda)
        {
            Contrasena auxiliar = new Contrasena();
            string contrasenaHash = auxiliar.ObtenerHashContrasena(datosVivienda.password);

            Login login = this._servicioSeguridad.Autenticarporidusuarioseguridadints(datosVivienda.usuario, contrasenaHash, false, false, null, null);
            ViviendaAsegurada resultadoPeticion;
            if (login.TipoMensajeSeguridad == TipoMensajeSeguridad.Autenticado || login.TipoMensajeSeguridad == TipoMensajeSeguridad.SesionAnteriorSinCerrar)
            {
                resultadoPeticion = await _servicioSeguroCalidad.ValidarSeguroVivienda(datosVivienda);
            }
            else {
                resultadoPeticion = new ViviendaAsegurada();
                resultadoPeticion.estatus = 0;
                resultadoPeticion.resultado = "NOK";
                resultadoPeticion.descripcion = "Usuario/Password incorrectos.";
            }

            return Request.CreateResponse(HttpStatusCode.OK, resultadoPeticion);
        }

        #endregion

        #region PadronAseguradoras

        [HttpGet, Route("aseguradoras-relacion-comercial")]
        [ResponseType(typeof(ResultadoPaginado<List<Aseguradora>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerAseguradoraRelacionComercialConPaginadoAsync(int tamanioPagina, int pagina, string razonSocial = null, string noRegistroRUV = null, int? idDesarrollador = null, int? idAseguradora = null)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.ObtenerAseguradoraRelacionComercialConPaginadoAsync(tamanioPagina, pagina, razonSocial, noRegistroRUV, idDesarrollador, idAseguradora);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("desarrollador-relacion-comercial")]
        [ResponseType(typeof(ResultadoPaginado<List<Aseguradora>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerDesarrolladoresRelacionComercialConPaginadoAsync(int tamanioPagina, int pagina, string razonSocial = null, string noRegistroRUV = null, string ordenVerificacion = null, string estatusCosto = null, int? idDesarrollador = null, int? idAseguradora = null)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.ObtenerDesarrolladoresRelacionComercialConPaginadoAsync(tamanioPagina, pagina, razonSocial, noRegistroRUV, ordenVerificacion, estatusCosto, idDesarrollador, idAseguradora);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("aseguradoras-padron-sinoferente-relacion-comercial")]
        [ResponseType(typeof(ResultadoPaginado<List<Aseguradora>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerAseguradoraEnPadronSinOferenteEnRelacionComercialConPaginadoAsync(int tamanioPagina, int pagina, int? idDesarrollador = null, string razonSocial = null, string noRegistroRUV = null)
        {
            var result = await this._servicioSeguroCalidad.ObtenerAseguradoraEnPadronSinOferenteEnRelacionComercialConPaginadoAsync(tamanioPagina, pagina, razonSocial, noRegistroRUV, idDesarrollador);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("aseguradoras-padron-sinoferente-relacion-comercial-defecto")]
        [ResponseType(typeof(ResultadoPaginado<Aseguradora>))]
        public async Task<HttpResponseMessage> ObtenerAseguradoraEnPadronSinOferenteXDefectoAsync(int idDesarrollador)
        {
            var result = await this._servicioSeguroCalidad.ObtenerAseguradoraEnPadronSinOferenteXDefectoAsync(idDesarrollador);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }
        [HttpGet, Route("aseguradoras-padron")]
        [ResponseType(typeof(ResultadoPaginado<List<Aseguradora>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerAseguradoraEnPadronConPaginadoAsync(int tamanioPagina, int pagina, string razonSocial = null, string noRegistroRUV = null)
        {
            var result = await this._servicioSeguroCalidad.ObtenerAseguradoraEnPadronConPaginadoAsync(tamanioPagina, pagina, razonSocial, noRegistroRUV);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("relacion-comercial")]
        [ResponseType(typeof(RelacionComercial))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerRelacionComercialAsync(int? idDesarrollador = null, int? idAseguradora = null)
        {
            var result = await this._servicioSeguroCalidad.ObtenerRelacionComercialAsync(idDesarrollador, idAseguradora, 1, null);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("relaciones-comerciales-desarrolladores")]
        [ResponseType(typeof(List<RelacionesComercialesOferente>))]
        public async Task<HttpResponseMessage> ObtenerRelacionesComercialesXDesarrolladorAsync()
        {
            var result = await this._servicioSeguroCalidad.ObtenerRelacionesComercialesXDesarrolladorAsync();

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpPost, Route("pasar-aseguradora-padron/{enPadronAseguradora}")]
        [ResponseType(typeof(bool))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> PasarAseguradoraPadronAsync(List<int> listaAseguradoras, int enPadronAseguradora)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.PasarAseguradoraPadronAsync(listaAseguradoras, enPadronAseguradora, usuario.IdUsuario.Value);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpPost, Route("guardar-relacion")]
        [ResponseType(typeof(int))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> GuardarRelacionComercialAsync(List<Aseguradora> listaRelacion)
        {
            var usuario = (CustomUserRuv)User;

            var listaIdRelacion = await this._servicioSeguroCalidad.GuardarRelacionComercialAsync(listaRelacion, usuario.IdUsuario, usuario.IdEmpresaInst);

            return Request.CreateResponse(HttpStatusCode.OK, listaIdRelacion);
        }

        [HttpPost, Route("actualizar-desarrollador-relacion")]
        [ResponseType(typeof(int))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ActualizarDesarroladorRelacionComercialAsync(RelacionComercial listaRelacion)
        {
            var usuario = (CustomUserRuv)User;

            var listaIdRelacion = await this._servicioSeguroCalidad.ActualizarRelacionComercialAsync(listaRelacion, usuario.IdUsuario);

            return Request.CreateResponse(HttpStatusCode.OK, listaIdRelacion);
        }


        [HttpPost, Route("actualizar-relacion-comercial")]
        [ResponseType(typeof(int))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ActualizarRelacionComercialAsync(RelacionComercial relacion)
        {
            var usuario = (CustomUserRuv)User;

            var idSancionGuardada = await this._servicioSeguroCalidad.ActualizarRelacionComercialAsync(relacion, usuario.IdUsuario);

            return Request.CreateResponse(HttpStatusCode.OK, idSancionGuardada);
        }


        [HttpPost, Route("actualizar-aseguradora-orden")]
        [ResponseType(typeof(bool))]
        public async Task<HttpResponseMessage> ActualizarAseguradoraOrdenPadronAsync(List<Aseguradora> listaAseguradoras)
        {
            var result = await this._servicioSeguroCalidad.ActualizarAseguradoraOrdenPadronAsync(listaAseguradoras);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpPost, Route("actualizar-relacion-comercial-rechazo")]
        [ResponseType(typeof(int))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ActualizarRelacionComercialXRechazoAsync(RelacionComercial relacion)
        {
            var usuario = (CustomUserRuv)User;

            var idSancionGuardada = await this._servicioSeguroCalidad.ActualizarRelacionComercialXRechazoAsync(relacion, usuario.IdUsuario);

            return Request.CreateResponse(HttpStatusCode.OK, idSancionGuardada);
        }


        #endregion

        #region sanciones


        [HttpGet, Route("aseguradoras-sanciones")]
        [ResponseType(typeof(List<Sancion>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerSancionesAseguradoraAsync(int? idSancion = null, int? idAseguradora = null)
        {
            var result = await this._servicioSeguroCalidad.ObtenerSancionesAsync(idSancion, idAseguradora);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }


        [HttpPost, Route("guardar-sancion")]
        [ResponseType(typeof(int))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> GuardarSancionAsync(Sancion sancion)
        {
            var usuario = (CustomUserRuv)User;

            var idSancionGuardada = await this._servicioSeguroCalidad.GuardarSancionAsync(sancion, usuario.IdUsuario);

            return Request.CreateResponse(HttpStatusCode.OK, idSancionGuardada);
        }

        [HttpPost, Route("actualizar-sancion")]
        [ResponseType(typeof(int))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ActualizarSancionAsync(Sancion sancion)
        {
            var usuario = (CustomUserRuv)User;

            var idSancionGuardada = await this._servicioSeguroCalidad.ActualizarSancionAsync(sancion, usuario.IdUsuario.Value);

            return Request.CreateResponse(HttpStatusCode.OK, idSancionGuardada);
        }
        #endregion

        #region Poliza

        [HttpPost, Route("agregar-poliza")]
        [ResponseType(typeof(List<int>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> AgregarPolizaAsync(List<CuvPoliza> listaCuvPoliza)
        {
            var listaPolizas = await this._servicioSeguroCalidad.AgregarPolizaAsync(listaCuvPoliza);

            return Request.CreateResponse(HttpStatusCode.OK, listaPolizas);
        }


        [HttpGet, Route("obtener-costo-poliza")]
        [ResponseType(typeof(ResultadoPaginado<List<CuvPoliza>>))]
        public async Task<HttpResponseMessage> ObtenerCostoPolizaAsync(string ordenVerificacion, string cuv = null)
        {
            //var result = await this._servicioSeguroCalidad.ObtenerCostoPolizaAsync(ordenVerificacion, cuv);

            return Request.CreateResponse(HttpStatusCode.OK, false);
        }

        #endregion


        //[HttpPost, Route("actualizar-aseguradora-orden/{idAseguradora}/turnoAsignacion/{turnoAsignacion}/ordenAsignacion/{ordenAsignacion}")]
        //[ResponseType(typeof(int))]        
        //public async Task<HttpResponseMessage> ActualizarAseguradoraOrdenPadronAsync(int idAseguradora, int? turnoAsignacion = null, int? ordenAsignacion = null)
        //{            
        //    var result = await this._servicioSeguroCalidad.ActualizarAseguradoraOrdenPadronAsync(idAseguradora, turnoAsignacion, ordenAsignacion);

        //    return Request.CreateResponse(HttpStatusCode.OK, result);
        //}

        #region Catalogos

        [HttpGet, Route("catalogo/estatus-pago-evaluacion")]
        [ResponseType(typeof(List<OpcionCatalogoSeguroCalidad>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerEstatusPagoEvaluacionRiesgoAsync()
        {
            var result = await this._servicioSeguroCalidad.ObtenerEstatusPagoEvaluacionRiesgoAsync();

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }
        #endregion

        #region Gestion Incidencias


        [HttpGet, Route("catalogo/tipo-incidencias")]
        [ResponseType(typeof(List<OpcionCatalogoSeguroCalidad>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerTipoIncidencias()
        {
            var result = await this._servicioSeguroCalidad.ObtenerTipoIncidenciasAsync();

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("catalogo/estatus-incidencias")]
        [ResponseType(typeof(List<OpcionCatalogoSeguroCalidad>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerEstatusIncidencias()
        {
            var result = await this._servicioSeguroCalidad.ObtenerEstatusIncidenciasAsync();

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("catalogo/clasificacion-incidencias")]
        [ResponseType(typeof(List<OpcionCatalogoSeguroCalidad>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerClasificacionIncidencias()
        {
            var result = await this._servicioSeguroCalidad.ObtenerClasificacionIncidenciasAsync();

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("catalogo/cobertura-afectada")]
        [ResponseType(typeof(List<OpcionCatalogoSeguroCalidad>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerCatalogoCoberturaAfectada()
        {
            var result = await this._servicioSeguroCalidad.ObtenerCatalogoCoberturaAfectadaAsync();

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("catalogo/clasificacion-riesgo")]
        [ResponseType(typeof(List<OpcionCatalogoSeguroCalidad>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerCatalogoClasificacionRiesgo()
        {
            var result = await this._servicioSeguroCalidad.ObtenerCatalogoClasificacioRiesgoAsync();

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("incidencias")]
        [ResponseType(typeof(ResultadoPaginado<List<IncidenciaGestion>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerIncidencias(int tamanioPagina, int pagina, string ordenVerificacion = null, string cuv = null, string desarrollador = null, string claveIncidencia = null,
                                                                    string idTipoIncidencia = null, string idEstatusIncidencia = null, string idClasificacionIncidencia = null, bool? esFechaRegistro = null, bool? esFechaAtencion = null,
                                                                    DateTime? fechaInicial = null, DateTime? fechaFinal = null, string conRiesgo = null)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.ObtenerIncidenciasGestionAsync(tamanioPagina, pagina, ordenVerificacion, cuv, desarrollador, claveIncidencia,
                idTipoIncidencia, idEstatusIncidencia, idClasificacionIncidencia, esFechaRegistro, esFechaAtencion, fechaInicial, fechaFinal, usuario.IdEntidad.ToString(),
                usuario.IdEmpresa.ToString(), conRiesgo);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("incidencias-sin-paginado")]
        [ResponseType(typeof(List<IncidenciaGestion>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerIncidenciasSinPaginado(string ordenVerificacion = null, string cuv = null, string desarrollador = null, string claveIncidencia = null,
                                                                    string idTipoIncidencia = null, string idEstatusIncidencia = null, string idClasificacionIncidencia = null, bool? esFechaRegistro = null, bool? esFechaAtencion = null,
                                                                    DateTime? fechaInicial = null, DateTime? fechaFinal = null, string conRiesgo = null)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.ObtenerIncidenciasGestionSinPaginadoAsync(ordenVerificacion, cuv, desarrollador, claveIncidencia,
                idTipoIncidencia, idEstatusIncidencia, idClasificacionIncidencia, esFechaRegistro, esFechaAtencion, fechaInicial, fechaFinal, usuario.IdEntidad.ToString(),
                usuario.IdEmpresa.ToString(), conRiesgo);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("catalogo/ordenes-verificacion")]
        [ResponseType(typeof(List<OpcionCatalogoSeguroCalidad>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerOrdenesVerificacion(string idEntidad = null, string idEmpresa = null)
        {
            var result = await this._servicioSeguroCalidad.ObtenerOrdenesVerificacionAsync(idEntidad, idEmpresa);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("catalogo/ordenes-verificacion-consulta")]
        [ResponseType(typeof(List<OpcionCatalogoSeguroCalidad>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerOrdenesVerificacionConsultaAsync(string noRuv = null, string razonSocial = null, string rfc = null)
        {
            var result = await this._servicioSeguroCalidad.ObtenerOrdenesVerificacionConsultaAsync(noRuv, razonSocial, rfc);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("datos-ov-incidencia")]
        [ResponseType(typeof(InformacionOVNuevaincidencia))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerInformacionOVNuevaIncidencia(string ordenVerificacion = null, string cuv = null)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.ObtenerInformacionOVNuevaIncidenciaAsync(ordenVerificacion, cuv, usuario.IdEntidad.ToString(), usuario.IdEmpresa.ToString());

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("cuv/{claveCuv}/validar")]
        [ResponseType(typeof(CuvValida))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ValidarCuvValida(string claveCuv = null)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.ValidarCuvValidaAsync(claveCuv, usuario.IdEntidad, usuario.IdEmpresa);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("orden-verificacion/{ordenVerificacion}/cuvs")]
        [ResponseType(typeof(List<CuvSeleccionada>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerCuvsXOrdenVerificacion(string ordenVerificacion = null, string cuv = null)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.ObtenerCuvsXOVAsync(ordenVerificacion, cuv);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }


        [HttpGet, Route("orden-verificacion/{ordenVerificacion}/cuvs/paginado")]
        [ResponseType(typeof(ResultadoPaginado<List<CuvSeleccionada>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerCuvsXOrdenVerificacionPaginado(int tamanioPagina, int pagina, string ordenVerificacion = null, string cuv = null)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.ObtenerCuvsXOVPaginadoAsync(tamanioPagina, pagina, ordenVerificacion, cuv);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpPost, Route("incidencia")]
        [ResponseType(typeof(bool))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> GuardarIncidencia(Incidencia incidencia)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.GuardarIncidenciaNotificacionAsync(incidencia, usuario.IdEntidad.Value);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }


        [HttpGet, Route("notificacion/{idIncidencia}/informacion-previa")]
        [ResponseType(typeof(ResultadoPaginado<List<CuvSeleccionada>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerInformacionPreviaNotificacion(string idIncidencia = null, string idVvGeneral = null)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.ObtenerInformacionPreviaNotificacionAsync(Convert.ToInt32(idIncidencia), Convert.ToInt32(idVvGeneral));

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("orden-verificacion/{ordenVerificacion}/cuvs-evaluacion-riesgos")]
        [ResponseType(typeof(List<CuvSeleccionada>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerCuvsXOrdenVerificacionEvaluacionRiesgos(string ordenVerificacion = null, string cuv = null)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.ObtenerCuvsXOVEvaluacionRiesgosAsync(ordenVerificacion, cuv);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }


        [HttpGet, Route("orden-verificacion/{ordenVerificacion}/cuvs-evaluacion-riesgos/paginado")]
        [ResponseType(typeof(ResultadoPaginado<List<CuvSeleccionada>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerCuvsXOrdenVerificacionEvaluacionRiesgosPaginado(int tamanioPagina, int pagina, string ordenVerificacion = null, string cuv = null)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.ObtenerCuvsXOVEvaluacionRiesgosPaginadoAsync(tamanioPagina, pagina, ordenVerificacion, cuv);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("cuv/{claveCuv}/validar-evaluacion-riesgos")]
        [ResponseType(typeof(CuvValida))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ValidarCuvValidaEvaluacionRiesgos(string claveCuv = null)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.ValidarCuvValidaEvaluacionRiesgosAsync(claveCuv, usuario.IdEntidad, usuario.IdEmpresa);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpPost, Route("evaluacion-riesgo")]
        [ResponseType(typeof(bool))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> GuardarEvaluacionRiesgos(EvaluacionRiesgo evaluacion)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.GuardarEvaluacionRiesgoAsync(evaluacion);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("incidencia/{idIncidencia}/detalle")]
        [ResponseType(typeof(DetalleIncidencia))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerDetalleIncidencia(int? idIncidencia, int? idVvGeneral)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.ObtenerDetalleIncidenciaAsync(idIncidencia, idVvGeneral, usuario.IdEntidad, usuario.IdEmpresa);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("datos-registro-mitigacion")]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerDatosRegistroMitigacion(int? idIncidencia)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.ObtenerDatosMitigacionAsync(idIncidencia);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpPost, Route("mitigacion")]
        [ResponseType(typeof(bool))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> GuardarMitigacionIncidencia(Mitigacion mitigacion)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.GuardarMitigacionIncidencia(mitigacion, usuario.IdEmpresaInst);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpPost, Route("cerrar/incidencia/{idIncidencia}")]
        [ResponseType(typeof(bool))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> CerrarIncidencia(IncidenciaGestion incidencia)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.CerrarIncidenciaAsync(Convert.ToInt32(incidencia.ClaveIncidencia), incidencia.OrdenVerificacion, usuario.IdUsuario.Value, usuario.IdEmpresaInst, usuario.IdEntidad.Value);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpPost, Route("rechazar/mitigacion-incidencia/{idIncidencia}")]
        [ResponseType(typeof(bool))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> RechazarMitigacion(Incidencia incidencia)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.RechazarMitigacionAsync(incidencia);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /*( RIch ) Gestión de incidencias Interno 02/04/2018/*/

        [HttpGet, Route("incidencias-Interno")]
        [ResponseType(typeof(ResultadoPaginado<List<IncidenciaGestion>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerIncidenciasInterno(int tamanioPagina, int pagina, string ordenVerificacion = null, string cuv = null, string desarrollador = null, string claveIncidencia = null,
                                                                       string idTipoIncidencia = null, string idEstatusIncidencia = null, string idClasificacionIncidencia = null, bool? esFechaRegistro = null, bool? esFechaAtencion = null,
                                                                       DateTime? fechaInicial = null, DateTime? fechaFinal = null, string conRiesgo = null, string verificador = null, string aseguradora = null)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.ObtenerIncidenciasInternoGestionAsync(tamanioPagina, pagina, ordenVerificacion, cuv, desarrollador, claveIncidencia,
                idTipoIncidencia, idEstatusIncidencia, idClasificacionIncidencia, esFechaRegistro, esFechaAtencion, fechaInicial, fechaFinal, usuario.IdEntidad.ToString(),
                usuario.IdEmpresa.ToString(), conRiesgo, verificador, aseguradora);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }



        [HttpGet, Route("incidencias-interno-sin-paginado")]
        [ResponseType(typeof(List<IncidenciaGestion>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerIncidenciasInternoSinPaginado(string ordenVerificacion = null, string cuv = null, string desarrollador = null, string claveIncidencia = null,
                                                              string idTipoIncidencia = null, string idEstatusIncidencia = null, string idClasificacionIncidencia = null, bool? esFechaRegistro = null, bool? esFechaAtencion = null,
                                                              DateTime? fechaInicial = null, DateTime? fechaFinal = null, string conRiesgo = null, string verificador = null, string aseguradora = null)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.ObtenerIncidenciasInternoGestionSinPaginadoAsync(ordenVerificacion, cuv, desarrollador, claveIncidencia,
                idTipoIncidencia, idEstatusIncidencia, idClasificacionIncidencia, esFechaRegistro, esFechaAtencion, fechaInicial, fechaFinal, usuario.IdEntidad.ToString(),
                usuario.IdEmpresa.ToString(), conRiesgo, verificador, aseguradora);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        #endregion

        #region Generacion Ficha
        /// <summary>
        /// 
        /// </summary>
        /// <param name="fichaPagoRiesgo"></param>
        /// <returns></returns>
        [HttpPost, Route("generacion-ficha-riesgo")]
        [ResponseType(typeof(int))]
        public async Task<HttpResponseMessage> GeneracionFichaRiesgo(FichaPagoRiesgo fichaPagoRiesgo)
        {
            var resultado = await this._servicioSeguroCalidad.GenerarFichaPagoEvaluacion(fichaPagoRiesgo);

            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="fichaPagoRiesgo"></param>
        /// <returns></returns>
        [HttpPost, Route("recalcular-costo-poliza")]
        [ResponseType(typeof(int))]
        public async Task<HttpResponseMessage> RecalcularCostoPoliza()
        {
            var resultado = await this._servicioSeguroCalidad.RecalcularCostoPoliza();

            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }

        #endregion

        #region Seleccion Aseguradora
        /// <summary>
        /// 
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="idRuvAsis"></param>
        /// <param name="ordenVerificacion"></param>
        /// <param name="noContrato"></param>
        /// <param name="idEmpresaInstAseguradora"></param>
        /// <param name="idTipoAsignacion"></param>
        /// <param name="razonSocialAseguradora"></param>
        /// <returns></returns>
        [HttpGet, Route("ordenes-verificacion")]
        [ResponseType(typeof(ResultadoPaginado<List<OrdenDeVerificacion>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerOrdenesDeVerificacionAsync(int tamanioPagina, int pagina, int idRuvAsis, string ordenVerificacion = null, string noContrato = null, string idEmpresaInstAseguradora = null, int? idTipoAsignacion = null, string razonSocialAseguradora = null)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.ObtenerOrdenesDeVerificacionAsync(tamanioPagina, pagina, idRuvAsis, ordenVerificacion, noContrato, idEmpresaInstAseguradora, idTipoAsignacion, razonSocialAseguradora);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="ordenVerificacion"></param>
        /// <returns></returns>
        [HttpGet, Route("obtener-costo-evaluacion")]
        [ResponseType(typeof(string))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerCostoEvaluacionAsync(string ordenVerificacion)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.ObtenerCostoEvaluacionRiesgo(ordenVerificacion);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("obtener-relaciones-comerciales")]
        [ResponseType(typeof(ResultadoPaginado<List<RelacionComercial>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerRelacionesComercialesAsync(int tamanioPagina, int pagina, int idRuvAsis, string idEmpresaInst = null, string nombreRazonSocial = null)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.ObtenerRelacionesComercialesAsync(tamanioPagina, pagina, idRuvAsis, idEmpresaInst, nombreRazonSocial);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="relacionComercialOrden"></param>
        /// <returns></returns>
        [HttpPost, Route("guardar-relacion-comercial-orden")]
        [ResponseType(typeof(ResultadoPaginado<List<RelacionComercial>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> GuardarRelacionesComercialOrdenAsync(RelacionComercialOrden relacionComercialOrden)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.GuardarRelacionesComercialOrdenAsync(relacionComercialOrden.idOrdenVerificacion, relacionComercialOrden.idAseguradora, relacionComercialOrden.idRelacionComercial);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="ordenVerificacion"></param>
        /// <param name="idRuvAsIs"></param>
        /// <returns></returns>
        [HttpGet, Route("obtener-lista-viviendas")]
        [ResponseType(typeof(ResultadoPaginado<FichaPagoRiesgo>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerListaViviendasAsync(string ordenVerificacion, int idRuvAsIs)
        {
            var usuario = (CustomUserRuv)User;

            FichaPagoRiesgo fichaPagoRiesgo = new FichaPagoRiesgo();
            fichaPagoRiesgo.idEmpresaAsIs = idRuvAsIs;
            fichaPagoRiesgo.idOrdenVerificacion = ordenVerificacion;

            var listaViviendas = await this._servicioSeguroCalidad.ObtenerViviendasOrdenAsync(ordenVerificacion);
            fichaPagoRiesgo.precioViviendas = listaViviendas;


            return Request.CreateResponse(HttpStatusCode.OK, fichaPagoRiesgo);
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="idOrdenVerificacion"></param>
        /// <returns></returns>
        [HttpGet, Route("obtener-relacion-comercial-orden")]
        [ResponseType(typeof(DetalleSeleccionAseguradora))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerRelacionesComercialOrdenAsync(string idOrdenVerificacion)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.ObtenerRelacionComercialOrdenAsync(idOrdenVerificacion);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="idEmpresa"></param>
        /// <param name="idRuvAsIs"></param>
        /// <returns></returns>
        [HttpGet, Route("obtener-datos-contacto-direccion")]
        [ResponseType(typeof(DatosGeneralesEmpresa))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerDatosContactoDireccionAsync(int idRuvAsIs)
        {
            var usuario = (CustomUserRuv)User;

            var result = await this._servicioSeguroCalidad.ConsultarDatosGeneralesEmpresa(idRuvAsIs);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="envioCorreoConfirmacion"></param>
        /// <returns></returns>
        [HttpPost, Route("enviar-correo-confirmacion")]
        [ResponseType(typeof(bool))]
        public async Task<HttpResponseMessage> EnviarCorreoConfirmacion(EnvioCorreoConfirmacion envioCorreoConfirmacion)
        {
            var result = await this._servicioSeguroCalidad.EnvioCorreoAseguradoraAsync(envioCorreoConfirmacion);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        #endregion

        #region Generar PDF

        /*Document document = new Document();

        PdfWriter.GetInstance(document,

                  new FileStream("devjoker.pdf",

                         FileMode.OpenOrCreate));


        document.Open();

        document.Add(new Paragraph("Este es mi primer PDF al vuelo"));

        document.Close();*/
        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet, Route("generarPDF")]
        public async Task<HttpResponseMessage> GenerarPDF()
        {
            //MemoryStream memStream = new MemoryStream();
            //Document document = new Document();
            //PdfWriter.GetInstance(document, memStream).CloseStream = false;


            //document.Open();

            //document.Add(new Paragraph("Este es mi primer PDF al vuelo"));

            //document.Close();

            //byte[] arreglo = memStream.ToArray();
            //memStream.Close();

            //List<DocumentoRuv> documentosCargados = new List<DocumentoRuv>();

            //var documento = this._servicioDocumento.AgregarYObtener(new DocumentoOferta
            //{
            //    rfcEmpresa = "AAAAAAAAAA", //TODO: Tomar de la identidad del usuario
            //    idCatalogoDocumento = 1,
            //    carpeta = "ejemplo",
            //    nombreArchivo = "PDFPRIMER.pdf",
            //    archivoStream = new MemoryStream(arreglo),
            //});

            //documentosCargados.Add(new DocumentoRuv
            //{
            //    IdDocumento = documento.idDocumento.Value,
            //    NombreArchivo = documento.nombreArchivo,
            //    UrlArchivo = documento.rutaArchivo
            //});

            //await this._servicioSeguroCalidad.EnviarCorreoCuvs();

            return Request.CreateResponse(HttpStatusCode.OK, true);
        }
        #endregion

<<<<<<< Updated upstream
        #region Servicio Aseguradora

        /// <summary>
        /// 
        /// </summary>
        /// <param name="datosGeneracionPoliza"></param>
        /// <returns></returns>
        [HttpPost, Route("generar-documento-poliza")]
        [ResponseType(typeof(RespuestaPoliza))]
        public async Task<HttpResponseMessage> GenerarDocumentoPoliza(PeticionSolicitudPoliza peticionSolicitudPoliza)
        {
            var resultado = await this._servicioSeguroCalidad.GenerarDocumentoPoliza(peticionSolicitudPoliza);

            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }


=======
        #region PagoDiferenciasOV

        [HttpGet, Route("obtener-pago-diferencia-OV")]
        [ResponseType(typeof(ResultadoPaginado<List<PagoDiferenciaOV>>))]
        public async Task<HttpResponseMessage> ObtenerPagoDiferenciaOV(int tamanioPagina, int pagina, string ordenVerificacion)
        {
            var result = await this._servicioSeguroCalidad.ObtenerPagoDiferenciaOV(tamanioPagina, pagina, ordenVerificacion);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("agregar-pago-diferencia-OV")]
        [ResponseType(typeof(PagoDiferenciaOV))]
        public async Task<HttpResponseMessage> AgregarPagoDiferenciaOVAsync(string ordenVerificacion, string numeroFactura, string fechaEntregaFactura)
        {
            PagoDiferenciaOV pagoDiferenciaOV = new PagoDiferenciaOV { idOrdenVerificacion = ordenVerificacion, numeroFactura = numeroFactura, fechaEntregaFactura = Convert.ToDateTime(fechaEntregaFactura) };

            var result = await this._servicioSeguroCalidad.AgregarPagoDiferenciaOVAsync(pagoDiferenciaOV);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("actualizar-pago-diferencia-OV")]
        [ResponseType(typeof(int))]
        public async Task<HttpResponseMessage> ActualizarPagoDiferenciaOVAsync(int idCuvSolicitadas, string numeroFactura, string fechaEntregaFactura)
        {
            PagoDiferenciaOV pagoDiferenciaOV = new PagoDiferenciaOV { idCuvsSolicitadas = idCuvSolicitadas, numeroFactura = numeroFactura, fechaEntregaFactura = Convert.ToDateTime(fechaEntregaFactura) };

            var result = await this._servicioSeguroCalidad.ActualizarPagoDiferenciaOVAsync(pagoDiferenciaOV);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }
>>>>>>> Stashed changes

        #endregion

        #region PagoDiferenciasOV

        [HttpGet, Route("obtener-pago-diferencia-OV")]
        [ResponseType(typeof(ResultadoPaginado<List<PagoDiferenciaOV>>))]
        public async Task<HttpResponseMessage> ObtenerPagoDiferenciaOV(int tamanioPagina, int pagina, string ordenVerificacion)
        {
            var result = await this._servicioSeguroCalidad.ObtenerPagoDiferenciaOV(tamanioPagina, pagina, ordenVerificacion);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("agregar-pago-diferencia-OV")]
        [ResponseType(typeof(PagoDiferenciaOV))]
        public async Task<HttpResponseMessage> AgregarPagoDiferenciaOVAsync(string ordenVerificacion, string numeroFactura, string fechaEntregaFactura)
        {
            PagoDiferenciaOV pagoDiferenciaOV = new PagoDiferenciaOV { idOrdenVerificacion = ordenVerificacion, numeroFactura = numeroFactura, fechaEntregaFactura = Convert.ToDateTime(fechaEntregaFactura) };

            var result = await this._servicioSeguroCalidad.AgregarPagoDiferenciaOVAsync(pagoDiferenciaOV);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("actualizar-pago-diferencia-OV")]
        [ResponseType(typeof(int))]
        public async Task<HttpResponseMessage> ActualizarPagoDiferenciaOVAsync(int idCuvSolicitadas, string numeroFactura, string fechaEntregaFactura)
        {
            PagoDiferenciaOV pagoDiferenciaOV = new PagoDiferenciaOV { idCuvsSolicitadas = idCuvSolicitadas, numeroFactura = numeroFactura, fechaEntregaFactura = Convert.ToDateTime(fechaEntregaFactura) };

            var result = await this._servicioSeguroCalidad.ActualizarPagoDiferenciaOVAsync(pagoDiferenciaOV);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        #endregion

        #region Servicio Aseguradora

        /// <summary>
        /// 
        /// </summary>
        /// <param name="datosGeneracionPoliza"></param>
        /// <returns></returns>
        [HttpPost, Route("generar-documento-poliza")]
        [ResponseType(typeof(RespuestaPoliza))]
        public async Task<HttpResponseMessage> GenerarDocumentoPoliza(PeticionSolicitudPoliza peticionSolicitudPoliza)
        {
            var resultado = await this._servicioSeguroCalidad.GenerarDocumentoPoliza(peticionSolicitudPoliza);

            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }



        #endregion
    }
}
 