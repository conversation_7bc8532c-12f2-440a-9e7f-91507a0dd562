﻿using RUV.RUVPP.Oferta.Comun.Modelo;
using RUV.RUVPP.Oferta.Datos.Empresa;
using RUV.RUVPP.Oferta.Datos.Plano;
using RUV.RUVPP.Oferta.Datos.Proyectos;
using RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Dictaminacion;
using RUV.RUVPP.Oferta.MigracionProyectos.Modelo;
using RUV.RUVPP.Oferta.Modelo.Proyectos.Data;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using System.Transactions;

namespace RUV.RUVPP.Oferta.MigracionProyectos.Funciones
{
    public class MigracionProyecto
    {
        private readonly IProyectosDataMapper _proyectoDataMapper;
        private readonly IPlanoDataMapper _planoDataMapper;
        private readonly TransactionOptions _opcionesTransaccion;
        private readonly IEmpresaDataMapper _empresaDM;
        private readonly ICatalogosProyectosDataMapper _servicioProyectosCatalogos;
        private readonly string connectionString;

        public MigracionProyecto(IProyectosDataMapper _proyectoDataMapper, IPlanoDataMapper _planoDataMapper, IEmpresaDataMapper _empresaDM, ICatalogosProyectosDataMapper _servicioProyectosCatalogos) : base()
        {
            var timeoutTransaccion = ConfigurationManager.AppSettings["RUV.RUVPP.Transaccion.Timeout"];
            connectionString = ConfigurationManager.ConnectionStrings["OfertaConnection"].ConnectionString;
            _opcionesTransaccion = new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted, Timeout = TimeSpan.Parse(timeoutTransaccion) };
            this._proyectoDataMapper = _proyectoDataMapper;
            this._planoDataMapper = _planoDataMapper;
            this._empresaDM = _empresaDM;
            this._servicioProyectosCatalogos = _servicioProyectosCatalogos;
        }

        public async Task MigrarProyectosSincronos()
        {
            Console.WriteLine("Iniciando Proceso");
            List<int> listaProyectosMigrar = new List<int>();

            using (ModeloOferta modelo = new ModeloOferta())
            {
                Console.WriteLine("Obteniendo Proyectos a Migrar...");
                //listaProyectosMigrar = modelo.Proyecto.Where(s => s.idEstatusProyecto == 2 || s.idEstatusProyecto == 4 || s.idEstatusProyecto == 6 || s.idEstatusProyecto == 7).Select(s => s.idProyecto).ToList();
                //listaProyectosMigrar = modelo.Proyecto.Where(s => s.idProyecto == 15).Select(s => s.idProyecto).ToList();
                listaProyectosMigrar = modelo.Proyecto.Where(s => s.idEstatusProyecto == 4 && s.idProyecto != 2 && s.idProyecto != 29).Select(s => s.idProyecto).ToList();
            }
            try
            {
                foreach (var idProyecto in listaProyectosMigrar)
                {
                    Console.WriteLine("Migrando Proyecto: " + idProyecto);
                    Oferta.Modelo.Proyectos.Api.Proyecto proyectoMigrar = new Oferta.Modelo.Proyectos.Api.Proyecto();

                    var cabeceraProyecto = await this._proyectoDataMapper.ObtenerProyectoAsync(idProyecto);
                    var detalleProyecto = await this._proyectoDataMapper.ObtenerProyectoDetalleAsync(idProyecto);
                    Console.WriteLine("Generando JSON...");
                    proyectoMigrar = cabeceraProyecto;
                    proyectoMigrar.dro = new Oferta.Modelo.Proyectos.Data.DRO();
                    proyectoMigrar.dictamenRiesgo = new Comun.Modelo.DocumentoRuv();
                    proyectoMigrar.constructor = new Oferta.Modelo.Proyectos.Data.DatosCPV();
                    proyectoMigrar.vendedor = new DatosCPV();
                    proyectoMigrar.promotor = new DatosCPV();
                    proyectoMigrar.ubicacion = string.Empty;
                    proyectoMigrar.listaMotivosRechazo =  null;
                    proyectoMigrar.idConstructor = detalleProyecto.idConstructor;
                    proyectoMigrar.idPromotor = detalleProyecto.idPromotor;
                    proyectoMigrar.idVendedor = detalleProyecto.idVendedor;
                    proyectoMigrar.esZonaRiesgo = detalleProyecto.esZonaRiesgo;
                    proyectoMigrar.aceptacionCartaResponsabilidad = detalleProyecto.aceptacionCartaResponsabilidad;
                    proyectoMigrar.folioAyto = detalleProyecto.folioAyto;
                    proyectoMigrar.folioSEPLADE = detalleProyecto.folioSEPLADE;
                    proyectoMigrar.plano = await this._proyectoDataMapper.ObtenerDocumentoxProyecto(idProyecto, Oferta.Modelo.Proyectos.TiposDocumento.Plano);                    
                    proyectoMigrar.zonasRiesgo = await this._servicioProyectosCatalogos.ObtenerCatalogoDeZonasDeRiesgoAsync();
                    List<ZonaRiesgo> zonasGuardadas = await this._proyectoDataMapper.ObtenerZonasRiesgo(idProyecto);
                    proyectoMigrar.esZonaRiesgo = zonasGuardadas.Count > 0;

                    foreach (var zona in proyectoMigrar.zonasRiesgo)
                    {
                        var datosZona = zonasGuardadas.Where(z => z.idRiesgoOferta == zona.idRiesgoOferta);
                        if (datosZona.ToList().Count > 0)
                        {
                            zona.solucionMitigarRiesgo = datosZona.FirstOrDefault().solucionMitigarRiesgo;
                            zona.esSeleccionada = true;
                            zona.esActualizacion = true;
                        }
                    }
                    proyectoMigrar.sembrado = await this._proyectoDataMapper.ObtenerSembradoxProyecto(idProyecto);

                    var idDRO = await this._proyectoDataMapper.ObtenerDROxProyecto(idProyecto);
                    proyectoMigrar.dro = await this._proyectoDataMapper.ObtenerDRO(idDRO.FirstOrDefault());
                    proyectoMigrar.dro.licencia = await this._proyectoDataMapper.ObtenerDocumentosDRO(idDRO.FirstOrDefault(), Oferta.Modelo.Proyectos.TiposDocumento.LicenciaDRO);
                    proyectoMigrar.dro.identificacionOficial = await this._proyectoDataMapper.ObtenerDocumentosDRO(idDRO.FirstOrDefault(), Oferta.Modelo.Proyectos.TiposDocumento.IdentificacionOficialDRO);

                    var listaPropietarioTerreno = await this._proyectoDataMapper.ObtenerPropietarioTerrenoxProyecto(idProyecto);
                    proyectoMigrar.propietarioTerreno = await this._proyectoDataMapper.ObtenerPropietarioTerreno(listaPropietarioTerreno.FirstOrDefault());

                    proyectoMigrar.constructor = await this._empresaDM.ConsultaEmpresaPorNRRUV(proyectoMigrar.idConstructor, null);

                    var dictamenRiesgo = await (this._proyectoDataMapper.ObtenerDocumentoxProyecto(idProyecto, Oferta.Modelo.Proyectos.TiposDocumento.DictamenRiesgoProyecto));
                    proyectoMigrar.dictamenRiesgo = dictamenRiesgo ?? new DocumentoRuv();

                    var listaPromotorVendedorProyecto = await this._proyectoDataMapper.ObtenerPromotorVendedorxProyecto(idProyecto);
                    foreach (var i in listaPromotorVendedorProyecto)
                    {
                        var promotorVendedor = await this._proyectoDataMapper.ObtenerPromotorVendedor(i);
                        if (!promotorVendedor.esVendedor)
                        {
                            proyectoMigrar.idPromotor = i;
                            proyectoMigrar.promotor = promotorVendedor;
                        }
                        else
                        {
                            proyectoMigrar.idVendedor = i;
                            proyectoMigrar.vendedor = promotorVendedor;
                        }
                    }

                    proyectoMigrar.constructor.esElMismo = proyectoMigrar.idConstructor == proyectoMigrar.idEmpresa;
                    proyectoMigrar.vendedor.esElMismo = detalleProyecto.idVendedor == null ? false : true;
                    proyectoMigrar.promotor.esElMismo = detalleProyecto.idPromotor == null ? false : true;
                    Console.WriteLine("Guardando JSON...");

                    var actualizado = await this._proyectoDataMapper.ActualizarProyectoAsync(idProyecto, proyectoMigrar.nombre, Newtonsoft.Json.JsonConvert.SerializeObject(proyectoMigrar), proyectoMigrar.idEstatusProyecto);
                    if (actualizado > 0)
                    {
                        if (cabeceraProyecto.idEstatusProyecto == 2 || cabeceraProyecto.idEstatusProyecto == 4)
                        {
                            Console.WriteLine("Borrando Datos...");

                            using (var modelo = new ModeloOferta())
                            {
                                using (var transaction = modelo.Database.BeginTransaction(System.Data.IsolationLevel.ReadCommitted))
                                {
                                    try
                                    {
                                        Console.WriteLine("Borrando Documentos....");
                                        var documentos = modelo.DocumentoxProyecto.Where(d => d.idProyecto == proyectoMigrar.idProyecto);
                                        modelo.DocumentoxProyecto.RemoveRange(documentos);

                                        Console.WriteLine("Borrando Domicilios....");
                                        foreach (var vivienda in proyectoMigrar.sembrado.viviendas)
                                        {
                                            var domicilioCamino = modelo.DomicilioCamino
                                                .Where(s => s.idDomicilioGeografico == vivienda.idDomicilioGeografico)
                                                .SingleOrDefault();
                                            if (domicilioCamino != null)
                                            {
                                                modelo.DomicilioCamino.Remove(domicilioCamino);
                                            }

                                            var domicilioCarretera = modelo.DomicilioCarretera
                                                .Where(s => s.idDomicilioGeografico == vivienda.idDomicilioGeografico)
                                                .SingleOrDefault();
                                            if (domicilioCarretera != null)
                                            {
                                                modelo.DomicilioCarretera.Remove(domicilioCarretera);
                                            }

                                            var domicilioGeografico = modelo.DomicilioGeografico
                                                .Where(s => s.idDomicilioGeografico == vivienda.idDomicilioGeografico)
                                                .SingleOrDefault();
                                            if (domicilioGeografico != null)
                                            {
                                                modelo.DomicilioGeografico.Remove(domicilioGeografico);
                                            }
                                        }

                                        var proyecto = modelo.Proyecto.FirstOrDefault(s => s.idProyecto == idProyecto);

                                        if (proyecto.DirectorResponsableObra != null)
                                        {
                                            Console.WriteLine("Borrando DRO");
                                            modelo.DirectorResponsableObra.RemoveRange(proyecto.DirectorResponsableObra);
                                        }

                                        if (proyecto.PropietarioTerreno != null)
                                        {
                                            Console.WriteLine("Borrando Propietario Terreno");
                                            modelo.PropietarioTerreno.RemoveRange(proyecto.PropietarioTerreno);
                                        }

                                        if (proyecto.ProyectoxRiesgoOferta != null)
                                        {
                                            Console.WriteLine("Borrando Zona Riesgo");
                                            modelo.ProyectoxRiesgoOferta.RemoveRange(proyecto.ProyectoxRiesgoOferta);
                                        }

                                        if (proyecto.PromotorExterno != null)
                                        {
                                            Console.WriteLine("Borrando Promotor/Vendedor");
                                            List<PromotorExterno> listaPVEliminar = new List<PromotorExterno>();
                                            foreach (var promotorVendedor in proyecto.PromotorExterno)
                                            {
                                                if (promotorVendedor.esVendedor)
                                                {
                                                    if (proyecto.DetalleProyecto.idVendedor == null)
                                                    {
                                                        listaPVEliminar.Add(promotorVendedor);
                                                    }
                                                }
                                                else
                                                {
                                                    if (proyecto.DetalleProyecto.idPromotor == null)
                                                    {
                                                        listaPVEliminar.Add(promotorVendedor);
                                                    }
                                                }
                                            }
                                            modelo.PromotorExterno.RemoveRange(listaPVEliminar);
                                        }

                                        if (proyecto.Vivienda != null)
                                        {
                                            Console.WriteLine("Borrando Viviendas");
                                            modelo.Vivienda.RemoveRange(proyecto.Vivienda);
                                        }




                                        modelo.SaveChanges();
                                        transaction.Commit();
                                        Console.WriteLine("Borrado exitoso!...");
                                    }
                                    catch (Exception e)
                                    {
                                        transaction.Rollback();
                                        throw;
                                    }
                                }
                            }
                            using (SqlConnection conn = new SqlConnection(connectionString))
                            {
                                string queryDeleteDetalleProyecto = string.Format("DELETE FROM oferta.DetalleProyecto WHERE idProyecto = {0}", idProyecto);
                                using (SqlCommand command = new SqlCommand(queryDeleteDetalleProyecto, conn))
                                {
                                    conn.Open();
                                    Console.WriteLine("Borrando Detalle Proyecto...");
                                    var affectedRows = command.ExecuteNonQuery();
                                }
                            }

                            //await this._planoDataMapper.MigrarOficialTemporal(proyectoMigrar.idProyecto);
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e.ToString());
                
            }
        }
    }
}