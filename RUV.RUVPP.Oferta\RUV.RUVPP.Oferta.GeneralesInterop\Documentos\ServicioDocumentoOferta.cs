﻿using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Text.RegularExpressions;
using System.Transactions;
using RUV.Comun.Negocio.CustomExceptions;
using RUV.RUVPP.Datos.General.SqlAzure.Documentos;
using RUV.RUVPP.Negocio.General.Documentos;
using RUV.RUVPP.Negocio.General.Documentos.Abstracta;
using System.IO;

namespace RUV.RUVPP.Oferta.GeneralesInterop.Documentos
{
    public class ServicioDocumentoOferta : ServicioDocumentoBase<DocumentoOferta>
    {
        #region Constantes

        private const int CanNotDeleteBecauseForeignKeySqlErrorCode = 547;

        private const char SeparadorAchivo = '/';
        private const string Andpersand = "&";

        private const int ContenedorMinLength = 3;
        private const int ContenedorMaxLength = 63;
        private const int NombreDocumentoMinLength = 1;
        private const int NombreDocumentoMaxLength = 1024;

        #endregion Constantes

        #region Constructor

        public ServicioDocumentoOferta()
            : base(new DocumentoOfertaDA(), new HistoricoDocumentoEmpresaDA())
        { }

        #endregion Constructor

        #region Metodos Sobreescritos

        /// <inheritdoc/>
        public override DocumentoOferta AgregarYObtener(DocumentoOferta elemento)
        {
            try
            {
                //archivo  en el blob
                if (elemento.archivoStream != null)
                {
                    string nombreArchivo;
                    ServicioDocumentoStorage servicioDocumentoStorage = new ServicioDocumentoStorage();

                    string soloNombreArchivo = Path.GetFileNameWithoutExtension(elemento.nombreArchivo);
                    string soloExtension = Path.GetExtension(elemento.nombreArchivo);

                    string soloNombreArchivoSinCaracteres = RemoverCaracteresInvalidos(soloNombreArchivo);

                    elemento.nombreArchivo = soloNombreArchivoSinCaracteres + soloExtension;

                    string nombreContenedor = elemento.rfcEmpresa.ToLower().Replace(Andpersand, string.Empty);
                    ValidarDocumento(elemento.nombreArchivo, nombreContenedor);

                    nombreArchivo = elemento.carpeta + elemento.nombreArchivo;
                    elemento.rutaArchivo = servicioDocumentoStorage.CargarBlob(elemento.archivoStream, nombreArchivo, nombreContenedor, false, true);
                    elemento.nombreArchivo = elemento.rutaArchivo.Split(SeparadorAchivo).Last();
                }

                //datos en slq
                return base._documentosDa.AgregarYObtener(elemento);
            }
            finally
            {
                if (elemento.archivoStream != null)
                    elemento.archivoStream.Close();
            }
        }

        public static string RemoverCaracteresInvalidos(string nombre)
        {
            string patronGuiones = @"(^[\-]*)";
            string patronCaracteresInvalidos = @"([^a-zA-Z0-9])";
            string remplazo = string.Empty;
            Regex regex = null;

            regex = new Regex(patronCaracteresInvalidos);
            nombre = regex.Replace(nombre, remplazo);
            regex = new Regex(patronGuiones);
            nombre = regex.Replace(nombre, remplazo);


            return nombre;
        }

        /// <inheritdoc/>
        public override DocumentoOferta CopiarYObtener(DocumentoOferta elementoOrigen, DocumentoOferta elementoDestino)
        {
            string nombreArchivo;
            ServicioDocumentoStorage servicioDocumentoStorage = new ServicioDocumentoStorage();           

            string nombreContenedor = elementoOrigen.rfcEmpresa.ToLower().Replace(Andpersand, string.Empty);
            ValidarDocumento(elementoOrigen.nombreArchivo, nombreContenedor);

            nombreArchivo = elementoOrigen.carpeta + elementoOrigen.nombreArchivo;

            var archivoOrigen = servicioDocumentoStorage.DescargarBlob(nombreArchivo, nombreContenedor);

            elementoDestino.archivoStream = archivoOrigen.StreamData;

            return this.AgregarYObtener(elementoDestino);
        }

        /// <inheritdoc/>
        public override DocumentoOferta MoverYObtener(DocumentoOferta elementoOrigen, DocumentoOferta elementoDestino)
        {
            var documento = this.CopiarYObtener(elementoOrigen, elementoDestino);

            if (documento != null)
            {
                this.Eliminar(elementoOrigen);
            }

            return documento;
        }

        /// <summary>
        ///Verifica que el nombre del archivo y el nombre del contenedor de un documento contengan caracteres validos
        /// </summary>
        /// <param name="nombreArchivo">Nombre del documento</param>
        /// <param name="nombreContenedor">Nombre del contenedor</param>
        /// <returns></returns>
        public override void ValidarDocumento(string nombreArchivo, string nombreContenedor)
        {
            string mensaje = string.Empty;
            string regexNombreArchivo;

            if (!nombreContenedor.All(char.IsLetterOrDigit) || nombreContenedor.Any(char.IsUpper))
            {
                mensaje = Recursos.DocumentoNombreContenedorInvalido;
                throw new RuvBusinessException(null, string.Format(mensaje, nombreContenedor));
            }

            if (nombreContenedor.Length < ContenedorMinLength || nombreContenedor.Length > ContenedorMaxLength)
            {
                mensaje = Recursos.DocumentoNombreContenedorMinMaxLength;
                throw new RuvBusinessException(null, string.Format(mensaje, nombreContenedor));
            }

            regexNombreArchivo = Recursos.DocumentoNombrePatron;

            if (!Regex.Match(nombreArchivo, regexNombreArchivo, RegexOptions.IgnoreCase).Success)
            {
                mensaje = Recursos.DocumentoNombreInvalido;
                throw new RuvBusinessException(null, string.Format(mensaje, nombreArchivo));
            }

            if (nombreArchivo.Length < NombreDocumentoMinLength || nombreArchivo.Length > NombreDocumentoMaxLength)
            {
                mensaje = Recursos.DocumentoNombreMinMaxLength;
                throw new RuvBusinessException(null, string.Format(mensaje, nombreContenedor));
            }
        }

        /// <summary>
        /// Elimina el documento en EntornoDocumento, en Comun Documento y opcionalmente en el Blob
        /// </summary>
        /// <param name="elemento">documento a eliminar</param>
        /// <param name="esEliminadoElBlob">true si se elimina tambien del blob</param>
        /// <returns></returns>
        public override int Eliminar(DocumentoOferta elemento, bool esEliminadoElBlob)
        {
            int resultado = 0;

            using (var transaccion = new TransactionScope(TransactionScopeOption.RequiresNew))
            {
                var documento = this._documentosDa.ObtenerConReflexion(elemento).First();

                try
                {
                    resultado = base._documentosDa.Eliminar(elemento);

                    if (resultado == 1 && esEliminadoElBlob)
                    {
                        var contenedor = elemento.rfcEmpresa.ToLower().Replace(Andpersand, string.Empty);
                        var archivo = documento.rutaArchivo.Split(new string[] { contenedor + SeparadorAchivo }, StringSplitOptions.RemoveEmptyEntries).Last();

                        ServicioDocumentoStorage storage = new ServicioDocumentoStorage();
                        storage.BorrarBlob(archivo, contenedor);

                        transaccion.Complete();
                    }
                }
                catch (SqlException ex) when (ex.Number == CanNotDeleteBecauseForeignKeySqlErrorCode)
                { }
            }

            return resultado;
        }

        public override IEnumerable<DocumentoOferta> Obtener(DocumentoOferta elemento)
        {
            ServicioDocumentoStorage servicioDocumentoStorage = new ServicioDocumentoStorage();
            var documento = this._documentosDa.ObtenerConReflexion(elemento);
            return documento;
        }

        /// <summary>
        /// Elimina el documento
        /// </summary>
        /// <param name="elemento">Datos del documento</param>
        /// <returns></returns>
        public override int Eliminar(DocumentoOferta elemento)
        {
            return this.Eliminar(elemento, false);
        }

        #endregion Metodos Sobreescritos
    }
}