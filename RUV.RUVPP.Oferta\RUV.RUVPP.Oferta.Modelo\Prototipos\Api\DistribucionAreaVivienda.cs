﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.Prototipos.Api
{
    /// <summary>
    /// 
    /// </summary>
    public class DistribucionAreaVivienda
    {
        public int? idTipoAreaVivienda { get; set; }

        public string nombreAreaVivienda  {get; set;}               

        public decimal totalSuperficie { get; set; }

        public List<TipoDimensionAreaVivienda> tipoDimensionAreaVivienda { get; set; }




    }
}
