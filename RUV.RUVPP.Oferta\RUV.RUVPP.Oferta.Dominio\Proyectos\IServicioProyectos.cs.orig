﻿using RUV.RUVPP.Oferta.Modelo.Proyectos.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RUV.RUVPP.Oferta.Modelo.Proyectos.Api;
using RUV.RUVPP.Oferta.Comun.Modelo;
using RUV.RUVPP.Oferta.Modelo.Dictaminacion;
using RUV.Comun.Utilerias;
using RUV.RUVPP.Oferta.Modelo.Historico.Data;
using RUV.RUVPP.Oferta.Modelo.Mediciones;

namespace RUV.RUVPP.Oferta.Dominio.Proyectos
{
    public interface IServicioProyectos : IDisposable
    {
        Task<int> ExisteProyectoPorNombreAsync(string nombre, int idEmpresa, int? idProyecto);
        Task<int> GuardarProyectoAsync(int idEmpresa,string claveEmpresa, string nombre, string proyecto);
        Task<int> GuardarProyectoDetalleAsync(Proyecto proyecto);
        Task<int> ActualizarProyectoAsync(int   idProyecto, string nombre, string proyecto);
        Task<int> ActualizarProyectoDetalleAsync(Proyecto proyecto);
        /// <summary>
        /// Actualiza el estatus de un proyecto
        /// </summary>
        /// <param name="idProyecto">Identificador del proyecto</param>
        /// <param name="idEstatus">Identificador del estatus</param>
        /// <returns></returns>
        Task<bool> ActualizarEstatusProyectoAsync(int idProyecto, int idEstatus);
        Task<ResultadoPaginado<List<Proyecto>>> ObtenerProyectosAsync(int idEmpresa, int tamanioPagina, int pagina, int? idProyecto, string nombreProyecto, int? idEntidadFederativa, int? idMunicipio, int? idLocalidad);
        Task<Proyecto> ObtenerProyectoAsync(int idProyecto);
        Task<Proyecto> ObtenerProyectoDetalleAsync(int idProyecto);

        Task<ResultadoPaginado<List<Proyecto>>> ObtenerProyectosPorIdEmpresaConPaginadorAsync(int tamanioPagina, int pagina, int idEmpresa);

        Task<bool> EliminarDocumentoProyecto(int idEmpresa, int idDocumento);
        /// <summary>
        /// Guarda/Actualiza la dictaminación, actualiza el estatus del proyecto y atiende la ODT.
        /// </summary>
        /// <param name="idOrdenTrabajo">Identificador de la orden de trabajo</param>
        /// <param name="idServicio">Identificador del servicio</param>
        /// <param name="idRegistro">Identificador del proyecto</param>
        /// <param name="parametrosDictaminacion">Objeto que contiene los siguientes parametros:
        /// seActualizaDictaminacion - FALSE si se inserta la dictaminacion, TRUE si se actualiza
        /// DictaminacionJSON -  cadena que contiene el JSON de dictaminacion</param>
        /// aceptacion - TRUE indica que se acepta la oferta, FALSE que se rechaza la oferta<returns></returns>
        /// <returns></returns>
        Task<bool> EnviarDictaminacionProyecto(int idOrdenTrabajo, short idServicio, int idRegistro, ParametroBodyDictaminacion parametrosDictaminacion);
        Task<bool> EliminarProyectoAsync(int idProyecto);
        Task<bool> EliminarProyectoParcialAsync(int idProyecto);
        //Task<int> CargarArchivoSDFAsync(int rutaArchivo, int idEmpresa, string nombreProyecto, int idProyecto);
        Task<int> CargarArchivoSDFAsync(int idArchivoSDF, int idUsuario, int idProyecto);
        Task<Sembrado> ObtenerSembradoTemporalAsync(int idProyectoTemporal);

        Task<Sembrado> ObtenerSembradoOficialTemporalAsync(int idProyecto);

        Task<Proyecto> ObtenerProyectosFiltroAsync(int? idEmpresa, string noRuv, string nombreProyecto, int? idProyecto);

        Task<ResultadoPaginado<List<Proyecto>>> ObtenerProyectosFiltroConPaginadoAsync(int tamanioPagina, int pagina, int? idEmpresa, string noRuv, string nombreProyecto, int? idProyecto);

        
        Task<List<Vivienda>> ObtenerViveindasPorProyectoAsync(int idProyecto);
        Task<bool> ActualizarViviendasAsync(Sembrado sembrado);
        /// <summary>
        /// Obtiene el listado de ordenes de trabajo para un proyecto por idProyecto paginado
        /// </summary>
        /// <param name="idProyecto">Identificador del proyecto</param>
        /// <param name="tamanioPagina">Tamaño de la pagina</param>
        /// <param name="pagina">Pagina</param>
        /// <returns></returns>
        Task<ResultadoPaginado<List<OrdenTrabajoHistoricoValidacion>>> ObtenerOrdenesTrabajoPorProyectoPaginadoAsync(int idProyecto, int tamanioPagina, int pagina);

        /// <summary>
        /// Obtiene el listado de viviendas para un proyecto por idProyecto paginado
        /// </summary>
        /// <param name="idProyecto">Identificador del proyecto</param>
        /// <param name="tamanioPagina">Tamaño de la pagina</param>
        /// <param name="pagina">Pagina</param>
        /// <returns></returns>
        Task<ResultadoPaginado<List<Vivienda>>> ObtenerViviendasPorProyectoPaginadoAsync(int idProyecto, int tamanioPagina, int pagina);

        /// <summary>
        /// Obtiene el resultado de la consulta de CUVS filtrada desde la BD del RUV++
        /// </summary>
        /// <param name="filtros">Objeto que contiene los siguientes filtros: IdProyecto, IdOferta, Cuv, IdEstatusJuridicoVivienda, IdEntidadFederativa, NombreFrente, NumeroRegistroRuv</param>        
<<<<<<< Updated upstream
        Task<Dictionary<string, ConsultaCuv>> ObtenerCuvsFiltradasAsync(FiltrosConsultaCuv filtros);
        Task<List<Vivienda>> ObtenerVivendasPorIdProyectoAsync(int idProyecto);
        Task<List<Proyecto>> ObtenerProyectosPorIdEmpresaAsync(int idEmpresa);
=======
        Task<List<ConsultaCuv>> ObtenerCuvsFiltradasAsync(FiltrosConsultaCuv filtros);
>>>>>>> Stashed changes

    }
}
