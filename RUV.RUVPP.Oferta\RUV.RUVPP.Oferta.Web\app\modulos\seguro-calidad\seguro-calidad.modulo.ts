﻿/// <reference path="datos/index.ts" />
/// <reference path="seguro-calidad.servicio.ts" />
import { NgModule, ModuleWithProviders } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { HttpModule } from '@angular/http';

import { ModuloComun, ModuloControles, ModuloFormualrios } from './../../../libs/ruvpp-angular2/index';

import { ruteo } from './seguro-calidad.ruteo';
import { ComponentePadronAseguradoras } from './modulos/padron-aseguradoras/padron-aseguradoras.componente';
import { ComponenteGestionAseguradoras } from './modulos/gestion-aseguradoras/gestion-aseguradoras.componente';
import { ComponenteGestionIncidencias } from './modulos/gestion-incidencias/gestion-incidencias.componente';
import { ComponenteGestionIncidenciasInterno } from './modulos/gestion-incidencias-interno/gestion-incidencias-interno.componente';
import { ComponenteValidarRelacionComercial } from './modulos/validar-relacion-comercial/validar-relacion-comercial.componente';
import { ComponenteSeleccionAseguradora } from './modulos/seleccion-aseguradora/seleccion-aseguradora.componente';
import { ComponenteSolicitarPoliza } from './modulos/solicitar-poliza/solicitar-poliza.componente';
import { ComponenteNuevoEstadoCuenta } from './modulos/nuevo-estado-cuenta/nuevo-estado-cuenta.componente';
import { ComponenteConsultaPoliza } from './modulos/consulta-poliza/consulta-poliza.componente';
import { ComponenteConsultaAseguradoras } from './modulos/consulta-aseguradoras/consulta-aseguradoras.componente';
import { ComponenteConsultaAsignacionDesarrollador } from './modulos/consulta-asignacion-desarrollador/consulta-asignacion-desarrollador.componente';
import { ComponenteConsultaAsignacionAseguradora } from './modulos/consulta-asignacion-aseguradora/consulta-asignacion-aseguradora.componente';
import { ComponentePagoDiferenciaOV } from './modulos/pago-diferencia-ov/pago-diferencia-ov.componente';
import { ComponenteConsultaPagoDiferenciaOV } from './modulos/consulta-pago-diferencia-ov/consulta-pago-diferencia-ov.componente';
import { ComponenteRegistrarSiniestro } from './modulos/registrar-siniestro/registrar-siniestro.componente';
import { ComponenteConsultaSiniestro } from './modulos/consulta-siniestro/consulta-siniestro.componente';
import { ComponenteSolicitarDevolucionDesarrollador } from './modulos/solicitar-devolucion-desarrollador/solicitar-devolucion-desarrollador.componente';
import { ServicioDatosSeguroCalidad, ServicioProcesoPagos } from './datos/index';
import { ComponenteSelectorFechaMesAno, ComponenteDetalleAvaluo, ComponenteRegistrarSancion, ComponenteHistorialSanciones, ComponenteRegistrarMitigacion, ComponenteDetalleIncidencia, ComponenteRevocarSancion, ComponenteRegistrarEvaluacionRiesgos, ComponenteGenerarRelacion, ComponenteRegistrarIncidenciaNotificacion, ComponenteRegistrarIncidenciaDesdeNotificacion, ComponenteDetalleSeleccionAseguradora, ComponenteSeleccionarAseguradora, ComponenteCriteriosPoliza, GenerarFichaPagoDiferenciaCuvComponente } from './componentes/index';
import { ServicioSeguroCalidad } from './seguro-calidad.servicio';
import { ComponenteConsultaPagosAseguradora } from './modulos/consulta-pagos-aseguradora/consulta-pagos-aseguradora.componente';
import { ComponenteReportePagosAseguradora } from './modulos/reporte-pagos-aseguradora/reporte-pagos-aseguradora.componente';
import { ComponenteReportePagoPlataformaSeguro } from './modulos/reporte-pago-plataforma-seguro/reporte-pago-plataforma-seguro.componente';

import { ComponenteConsultaDevoluciones } from './modulos/consulta-devoluciones/consulta-devoluciones.componente';
import { ComponenteConsultaDevolucionesInterno } from './modulos/consulta-devoluciones-interno/consulta-devoluciones-interno.componente';
import { ComponenteSolicitudDevolucionEvaluacion } from './modulos/solicitud-devolucion-evaluacion/solicitud-devolucion-evaluacion.componente';
import { ComponenteSolicitudDevolucionPoliza } from './modulos/solicitud-devolucion-poliza/solicitud-devolucion-poliza.componente';

import { ComponenteSolicitarPagoAseguradora } from './modulos/solicitar-pagos-aseguradora/solicitar-pagos-aseguradora.componente';

import { WjGridModule } from 'wijmo/wijmo.angular2.grid';
import { WjGridFilterModule } from 'wijmo/wijmo.angular2.grid.filter';
import { WjInputModule } from 'wijmo/wijmo.angular2.input';


@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        HttpModule,
        ReactiveFormsModule,
        ModuloComun,
        ModuloControles,
        ModuloFormualrios,
        ruteo,
        WjInputModule,
        WjGridModule,
        WjGridFilterModule,
    ],
    declarations: [
        ComponentePadronAseguradoras,
        ComponenteRegistrarSancion,
        ComponenteHistorialSanciones,
        ComponenteRevocarSancion,
        ComponenteGestionIncidencias,
        ComponenteGestionIncidenciasInterno,
        ComponenteGestionAseguradoras,
        ComponenteGenerarRelacion,
        ComponenteValidarRelacionComercial,
        ComponenteRegistrarIncidenciaNotificacion,
        ComponenteRegistrarIncidenciaDesdeNotificacion,
        ComponenteRegistrarEvaluacionRiesgos,
        ComponenteDetalleIncidencia,
        ComponenteRegistrarMitigacion,
        ComponenteSeleccionAseguradora,
        ComponenteDetalleSeleccionAseguradora,
        ComponenteSeleccionarAseguradora,
        ComponenteSolicitarPoliza,
        ComponenteNuevoEstadoCuenta,
        ComponenteConsultaPoliza,
        ComponenteConsultaAseguradoras,
        ComponenteConsultaAsignacionDesarrollador,
        ComponenteConsultaAsignacionAseguradora,
        ComponentePagoDiferenciaOV,
        ComponenteCriteriosPoliza,
        ComponenteDetalleAvaluo,
        ComponenteSolicitarDevolucionDesarrollador,
        ComponenteConsultaPagoDiferenciaOV,
        ComponenteRegistrarSiniestro,
        ComponenteConsultaSiniestro,
        ComponenteConsultaPagosAseguradora,
        ComponenteReportePagosAseguradora,
        ComponenteConsultaDevoluciones,
        ComponenteConsultaDevolucionesInterno,
        ComponenteSolicitudDevolucionEvaluacion,
        ComponenteSolicitudDevolucionPoliza,
        ComponenteReportePagoPlataformaSeguro,
        ComponenteSelectorFechaMesAno,
        ComponenteSolicitarPagoAseguradora,
        GenerarFichaPagoDiferenciaCuvComponente
    ],
    providers: [
        ServicioDatosSeguroCalidad,
        ServicioSeguroCalidad,
        ServicioProcesoPagos,
        DatePipe
    ]
})
export default class ModuloSeguroCalidad { }