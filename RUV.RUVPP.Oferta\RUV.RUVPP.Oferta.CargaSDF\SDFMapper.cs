﻿using Microsoft.ApplicationInsights;
using OSGeo.FDO;
using OSGeo.FDO.ClientServices;
using OSGeo.FDO.Commands;
using OSGeo.FDO.Commands.Feature;
using OSGeo.FDO.Commands.Schema;
using OSGeo.FDO.Common;
using OSGeo.FDO.Connections;
using OSGeo.FDO.Expression;
using OSGeo.FDO.Schema;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace RUV.RUVPP.Oferta.CargaSDF
{
    public class SDFMapper
    {
        public TelemetryClient ClienteTelemetria { get; set; }

        public SDFMapper(TelemetryClient clienteTelemetria)
        {
            this.ClienteTelemetria = clienteTelemetria;
        }

        public string ValidateFile(string file, string schemaFile)
        {
            bool validateFile = false;
            StringBuilder mensajes = new StringBuilder();

            string mensajeValidacionEstructura = ValidarEstructuraSDF(file, schemaFile);
            mensajes.Append(mensajeValidacionEstructura);

            // Get the Connection Manager object
            var mgr = FeatureAccessManager.GetConnectionManager();

            // Create a connection to the OSGeo.SDF provider
            using (var connection = mgr.CreateConnection("OSGeo.SDF"))
            {
                // Set connection properties
                var props = connection.ConnectionInfo.ConnectionProperties;
                props.SetProperty("File", file);

                // Open the connection
                connection.Open();
                var describeSchema =
                    (IDescribeSchema)
                        connection.CreateCommand(OSGeo.FDO.Commands.CommandType.CommandType_DescribeSchema);
                var schemas = describeSchema.Execute();

                //Load xml
                XDocument xdoc = XDocument.Load(schemaFile);

                //Run query
                var lv1s = from lv1 in xdoc.Descendants("feature")
                           select new
                           {
                               Name = lv1.Attribute("name").Value,
                               Children = lv1.Descendants("property")
                           };

                //Loop through results

                foreach (var lv1 in lv1s)
                {
                    string currentFeature = lv1.Name;
                    if (schemas[0].Classes.IndexOf(currentFeature) == -1)
                    {
                        mensajes.AppendLine("No se encontró la capa: " + currentFeature);
                        continue;
                    }
                    int currentClass = schemas[0].Classes.IndexOf(currentFeature);
                    foreach (var lv2 in lv1.Children)
                    {
                        string propertyName = lv2.Attribute("name").Value;
                        int indexProperty = schemas[0].Classes[currentClass].Properties.IndexOf(propertyName);
                        int featId = schemas[0].Classes[currentClass].Properties.IndexOf("FeatId");
                        if (indexProperty == -1)
                        {
                            mensajes.AppendLine(string.Format("No se encontró ningún valor en la propiedad: {0} del FeatId: {2} de la capa: {1} ", propertyName, currentFeature, featId));
                        }
                        else
                        {
                            using (ISelect selectedData = connection.CreateCommand(OSGeo.FDO.Commands.CommandType.CommandType_Select) as ISelect)
                            {
                                selectedData.SetFeatureClassName(currentFeature);

                                using (IFeatureReader readerFDO = selectedData.Execute())
                                {
                                    ClassDefinition definicionCapa = readerFDO.GetClassDefinition();

                                    while (readerFDO.ReadNext())
                                    {
                                        foreach (PropertyDefinition propertyDefinition in definicionCapa.Properties)
                                        {
                                            if (propertyDefinition.Name == propertyName)
                                            {
                                                if (propertyDefinition.PropertyType == PropertyType.PropertyType_GeometricProperty)
                                                {
                                                    GeometryValue propiedadGeometry = GetGeometryValue(propertyDefinition, readerFDO);
                                                }
                                                else
                                                {
                                                    PropertyValue propiedadDatos = GetPropertyValue(propertyDefinition, readerFDO);
                                                    if (string.IsNullOrEmpty(propiedadDatos.Value.ToString().Replace("''", "")))
                                                    {
                                                        mensajes.AppendLine(string.Format("No se encontró ningún valor en la propiedad: {0} del FeatId: {2} de la capa: {1} ", propertyName, currentFeature, featId));
                                                        break;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            return mensajes.ToString();
        }

        private string ValidarEstructuraSDF(string file, string schemaFile)
        {
            StringBuilder mensajes = new StringBuilder();

            var mgr = FeatureAccessManager.GetConnectionManager();

            using (var conexionArchivo = mgr.CreateConnection("OSGeo.SDF"))
            {
                var propiedades = conexionArchivo.ConnectionInfo.ConnectionProperties;
                propiedades.SetProperty("File", file);

                conexionArchivo.Open();
                var describeSchema =
                    (IDescribeSchema)
                        conexionArchivo.CreateCommand(OSGeo.FDO.Commands.CommandType.CommandType_DescribeSchema);
                var schemas = describeSchema.Execute();

                XDocument xdoc = XDocument.Load(schemaFile);

                var capasXml = from lv1 in xdoc.Descendants("feature")
                               select new
                               {
                                   Name = lv1.Attribute("name").Value,
                                   Obligatoria = lv1.Attribute("obligatoria").Value,
                                   Children = lv1.Descendants("property")
                               };

                var capasObligatorias = capasXml.Where(s => bool.Parse(s.Obligatoria) == true).ToList();

                foreach (var item in capasObligatorias)
                {
                    string capaActual = item.Name;
                    int indiceCapa = schemas[0].Classes.IndexOf(capaActual);
                    if (indiceCapa == -1)
                    {
                        string mensaje = string.Format("No se encontró la capa obligatoria: {0}", item.Name);
                        mensajes.AppendLine(mensaje);
                    }
                    else
                    {
                        using (ISelect selectedData = conexionArchivo.CreateCommand(OSGeo.FDO.Commands.CommandType.CommandType_Select) as ISelect)
                        {
                            selectedData.SetFeatureClassName(capaActual);

                            using (IFeatureReader readerFDO = selectedData.Execute())
                            {
                                int contadorRegistros = 0;
                                while (readerFDO.ReadNext())
                                {
                                    contadorRegistros++;
                                    break;
                                }
                                if (contadorRegistros == 0)
                                {
                                    string mensaje = string.Format("No se encontró la capa obligatoria: {0}", item.Name);
                                    mensajes.AppendLine(mensaje);
                                }
                            }
                        }
                    }
                }
                conexionArchivo.Close();
                return mensajes.ToString();
            }
        }

        /// <summary>
        /// Refactor de la herencia de Justo.
        /// </summary>
        public void ApplyOnlyDataNuevo(string filename, int idEmpresa, int idProyecto)
        {
            using (IConnectionManager connectionManager = FeatureAccessManager.GetConnectionManager())
            {
                using (IConnection connectionSDF = connectionManager.CreateConnection("OSGeo.SDF"))
                {
                    IConnectionPropertyDictionary propertiesFileSDF = connectionSDF.ConnectionInfo.ConnectionProperties;
                    propertiesFileSDF.SetProperty("File", filename);
                    connectionSDF.Open();
                    List<string> classNames = new List<string>();
                    using (IGetClassNames classesNameSDF = connectionSDF.CreateCommand(OSGeo.FDO.Commands.CommandType.CommandType_GetClassNames) as IGetClassNames)
                    {
                        classesNameSDF.SchemaName = ConfigurationManager.AppSettings["schema"];
                        StringCollection classes = classesNameSDF.Execute();

                        foreach (StringElement classNameSDF in classes)
                        {
                            string className = classNameSDF.String.Replace(classesNameSDF.SchemaName + ":", "");
#if DEBUG
                            //Console.WriteLine(string.Format("Insertando datos en la tabla:\n{0}", className));

#endif
                            using (ISelect selectedData = connectionSDF.CreateCommand(OSGeo.FDO.Commands.CommandType.CommandType_Select) as ISelect)
                            {
                                selectedData.SetFeatureClassName(className);

                                using (IFeatureReader readerFDO = selectedData.Execute())
                                {
                                    ClassDefinition classDefinition = readerFDO.GetClassDefinition();

                                    #region conexión sql

                                    IConnection connectionSQL = connectionManager.CreateConnection("OSGeo.SQLServerSpatial");
                                    IConnectionPropertyDictionary connectionProperties = connectionSQL.ConnectionInfo.ConnectionProperties;
                                    connectionProperties.SetProperty("Service", ConfigurationManager.AppSettings["servicio"]);
                                    connectionProperties.SetProperty("Username", ConfigurationManager.AppSettings["usuario"]);
                                    connectionProperties.SetProperty("Password", ConfigurationManager.AppSettings["password"]);
                                    connectionProperties.SetProperty("DataStore", ConfigurationManager.AppSettings["datastore"]);
                                    connectionSQL.Open();

                                    #endregion conexión sql

                                    while (readerFDO.ReadNext())
                                    {
                                        IInsert insertar = (IInsert)connectionSQL.CreateCommand(OSGeo.FDO.Commands.CommandType.CommandType_Insert);
                                        insertar.SetFeatureClassName(className);
                                        foreach (PropertyDefinition propertyDefinition in classDefinition.Properties)
                                        {
                                            if (propertyDefinition.PropertyType == PropertyType.PropertyType_GeometricProperty)
                                            {
                                                GeometryValue propiedadGeometry = GetGeometryValue(propertyDefinition, readerFDO);
                                                insertar.PropertyValues.Add(new PropertyValue(propertyDefinition.Name, propiedadGeometry));
                                            }
                                            if (propertyDefinition.PropertyType == PropertyType.PropertyType_DataProperty)
                                            {
                                                PropertyValue propiedadDatos = GetPropertyValue(propertyDefinition, readerFDO);
                                                insertar.PropertyValues.Add(propiedadDatos);
                                            }
                                        }
                                        insertar.PropertyValues.Add(new PropertyValue("IdEmpresa", new StringValue(idEmpresa.ToString())));
                                        insertar.PropertyValues.Add(new PropertyValue("IdProyecto", new StringValue(idProyecto.ToString())));
                                        insertar.Execute();
                                    }
                                    connectionSQL.Close();
                                }
                            }
                        }
                    }
                    connectionSDF.Close();
                }
            }
        }

        public void ApplyOnlyDataNuevo_Oracle(ConexionOracle datosConexionOracle, string filename, int idUsuario, string idProyectoTemporal)
        {
            int nivel = 4;

            using (IConnectionManager connectionManager = FeatureAccessManager.GetConnectionManager())
            {
                using (IConnection conexionSDF = connectionManager.CreateConnection("OSGeo.SDF"))
                {
                    IConnectionPropertyDictionary propertiesFileSDF = conexionSDF.ConnectionInfo.ConnectionProperties;
                    propertiesFileSDF.SetProperty("File", filename);
                    conexionSDF.Open();
                    List<string> classNames = new List<string>();

                    using (IGetClassNames classesNameSDF = conexionSDF.CreateCommand(OSGeo.FDO.Commands.CommandType.CommandType_GetClassNames) as IGetClassNames)
                    {
                        string esquemaSDF = datosConexionOracle.esquemaSDF;
                        string esquemaOracle = datosConexionOracle.esquemaOracle;
                        StringCollection classes = classesNameSDF.Execute();

                        List<InformacionClase> datosTablas = new List<InformacionClase>();

                        foreach (StringElement classNameSDF in classes)
                        {
                            string className = classNameSDF.String.Replace(esquemaSDF + ":", string.Empty);
                            string classNameOracle = className + "_TMP";

                            if (className.Contains("NUM") || className.Contains("CUV"))
                            {
                                continue;
                            }

                            using (ISelect selectedData = conexionSDF.CreateCommand(OSGeo.FDO.Commands.CommandType.CommandType_Select) as ISelect)
                            {
                                selectedData.SetFeatureClassName(className);

                                using (IFeatureReader readerFDO = selectedData.Execute())
                                {
                                    ClassDefinition definicionCapa = readerFDO.GetClassDefinition();

                                    while (readerFDO.ReadNext())
                                    {
                                        InformacionClase tabla = new InformacionClase(classNameOracle, esquemaOracle + "~" + classNameOracle + "~GEOM");
                                        //IInsert comandoInsertar = (IInsert)conexionOracle.CreateCommand(OSGeo.FDO.Commands.CommandType.CommandType_Insert);
                                        //comandoInsertar.SetFeatureClassName(esquemaOracle + "~" + classNameOracle + "~GEOM");

                                        foreach (PropertyDefinition propertyDefinition in definicionCapa.Properties)
                                        {
                                            if (propertyDefinition.Name == "FeatId" || propertyDefinition.Name == "CUV")
                                            {
                                                continue;
                                            }

                                            if (propertyDefinition.PropertyType == PropertyType.PropertyType_GeometricProperty)
                                            {
                                                GeometryValue propiedadGeometry = GetGeometryValue(propertyDefinition, readerFDO);
                                                tabla.Propiedades.Add(new PropertyValue(propertyDefinition.Name, propiedadGeometry));
                                            }
                                            else
                                            {
                                                if (className.Contains("SMB_CONSTRUCCIONES"))
                                                {
                                                    if (propertyDefinition.Name == "LAYER")
                                                    {
                                                        PropertyValue propiedadDatos = GetPropertyValue(propertyDefinition, readerFDO);
                                                        if (propiedadDatos.Value.ToString().Replace("\'", "") != "SMB_CONSTRUCCIONES")
                                                        {
                                                            string piso = propiedadDatos.Value.ToString().Replace("\'", "").Split('_')[2];
                                                            int idNivel = 0;
                                                            if (piso != "PB")
                                                            {
                                                                idNivel = int.Parse(piso.Substring(1, piso.Length - 1)) + nivel;
                                                            }
                                                            else
                                                            {
                                                                idNivel = nivel;
                                                            }
                                                            tabla.Propiedades.Add(new PropertyValue("NIVEL_NUM", new StringValue(idNivel.ToString())));
                                                        }
                                                        else
                                                        {
                                                            tabla.Propiedades.Add(new PropertyValue("NIVEL_NUM", new StringValue(nivel.ToString())));
                                                        }
                                                    }
                                                    else
                                                    {
                                                        if (propertyDefinition.PropertyType == PropertyType.PropertyType_DataProperty)
                                                        {
                                                            PropertyValue propiedadDatos = GetPropertyValue(propertyDefinition, readerFDO);
                                                            tabla.Propiedades.Add(propiedadDatos);
                                                        }
                                                    }
                                                }
                                                else
                                                {
                                                    if (propertyDefinition.PropertyType == PropertyType.PropertyType_DataProperty)
                                                    {
                                                        PropertyValue propiedadDatos = GetPropertyValue(propertyDefinition, readerFDO);
                                                        tabla.Propiedades.Add(propiedadDatos);
                                                    }
                                                }
                                            }
                                        }

                                        tabla.Propiedades.Add(new PropertyValue("CLASSID", new Int32Value(565)));
                                        tabla.Propiedades.Add(new PropertyValue("REVISIONNUMBER", new Int32Value(0)));
                                        tabla.Propiedades.Add(new PropertyValue("ID_PROYECTO", new StringValue(idProyectoTemporal)));

                                        datosTablas.Add(tabla);
                                    }
                                }
                            }
                        }
                        using (IConnection conexion = CrearConexionOracle(datosConexionOracle, connectionManager))
                        {
                            BorrarTablasOracle(esquemaSDF, esquemaOracle, idProyectoTemporal, conexion, classes);

                            foreach(var t in datosTablas)
                            {
                                IInsert comandoInsertar = (IInsert)conexion.CreateCommand(OSGeo.FDO.Commands.CommandType.CommandType_Insert);
                                comandoInsertar.SetFeatureClassName(t.NombreCalificado);

                                foreach (var propiedadActual in t.Propiedades)
                                {
                                    comandoInsertar.PropertyValues.Add(propiedadActual);
                                }

                                this.ClienteTelemetria.MedirDependencia($"{conexion.ConnectionInfo.ConnectionProperties.GetProperty("Service")}", $"INSERT INTO {t.Nombre}",
                                    () =>
                                    {
                                        //Console.WriteLine($"INSERT INTO {t.Nombre}");
                                        comandoInsertar.Execute();
                                    }, nombreTipoDependencia: "Oracle"
                                );
                            }
                        }
                    }
                }
            }
        }

        private IConnection CrearConexionOracle(ConexionOracle datosConexionOracle, IConnectionManager connectionManager)
        {
            IConnection conexion = connectionManager.CreateConnection("OSGeo.KingOracle");
            IConnectionPropertyDictionary propiedades = conexion.ConnectionInfo.ConnectionProperties;
            propiedades.SetProperty("Username", datosConexionOracle.usuarioOracle);
            propiedades.SetProperty("Password", datosConexionOracle.passwordOracle);
            propiedades.SetProperty("Service", datosConexionOracle.urlServicioOracle);
            propiedades.SetProperty("OracleSchema", datosConexionOracle.esquemaOracle);

            conexion.Open();

            return conexion;
        }

        private void BorrarTablasOracle(string esquemaSDF, string esquemaOracle, string idProyectoTemporal, IConnection conexionOracle, StringCollection listaCapasSDF)
        {
            foreach (StringElement nombreCapaSDF in listaCapasSDF)
            {
                string nombreCapa = nombreCapaSDF.String.Replace(esquemaSDF + ":", string.Empty);
                string nombreTablaOracle = nombreCapa + "_TMP";

                if (nombreCapa.Contains("NUM") || nombreCapa.Contains("CUV"))
                {
                    continue;
                }

                OSGeo.FDO.Filter.Filter filtroBorrar = OSGeo.FDO.Filter.Filter.Parse("ID_PROYECTO = " + idProyectoTemporal);
                IDelete comandoBorrar = conexionOracle.CreateCommand(CommandType.CommandType_Delete) as IDelete;
                comandoBorrar.SetFeatureClassName(esquemaOracle + "~" + nombreTablaOracle + "~GEOM");
                comandoBorrar.Filter = filtroBorrar;

                int registrosEliminados =
                    this.ClienteTelemetria.MedirDependencia($"{conexionOracle.ConnectionInfo.ConnectionProperties.GetProperty("Service")}", $"DELETE FROM {nombreTablaOracle} WHERE ID_PROYECTO = {idProyectoTemporal}",
                    () =>
                    {
                        //Console.WriteLine($"DELETE FROM {nombreTablaOracle} WHERE ID_PROYECTO = {idProyectoTemporal}");

                        return comandoBorrar.Execute();
                    }, nombreTipoDependencia: "Oracle");
            }
        }

        private GeometryValue GetGeometryValue(PropertyDefinition propertyDefinition, IFeatureReader readerFDO)

        {
            byte[] geometriaTemporal = readerFDO.GetGeometry(propertyDefinition.Name);
            GeometryValue geometria = new GeometryValue(geometriaTemporal);
            return geometria;
        }

        private PropertyValue GetPropertyValue(PropertyDefinition propertyDefinition, IFeatureReader readerFDO)
        {
            PropertyValue propertyValue = null;
            DataPropertyDefinition dataPropertyDefinition = propertyDefinition as DataPropertyDefinition;

            switch (dataPropertyDefinition.DataType)
            {
                case OSGeo.FDO.Schema.DataType.DataType_String:
                    {
                        if (!readerFDO.IsNull(dataPropertyDefinition.Name))
                        {
                            propertyValue = new PropertyValue(dataPropertyDefinition.Name, new StringValue(readerFDO.GetString(dataPropertyDefinition.Name)));
                        }
                        break;
                    }
                case OSGeo.FDO.Schema.DataType.DataType_Int16:
                    {
                        if (!readerFDO.IsNull(dataPropertyDefinition.Name))
                        {
                            propertyValue = new PropertyValue(dataPropertyDefinition.Name, new Int16Value(readerFDO.GetInt16(dataPropertyDefinition.Name)));
                        }
                        break;
                    }
                case OSGeo.FDO.Schema.DataType.DataType_Int32:
                    {
                        if (!readerFDO.IsNull(dataPropertyDefinition.Name))
                        {
                            propertyValue = new PropertyValue(dataPropertyDefinition.Name, new Int32Value(readerFDO.GetInt32(dataPropertyDefinition.Name)));
                        }
                        break;
                    }

                case OSGeo.FDO.Schema.DataType.DataType_Int64:
                    {
                        if (!readerFDO.IsNull(dataPropertyDefinition.Name))
                        {
                            propertyValue = new PropertyValue(dataPropertyDefinition.Name, new Int64Value(readerFDO.GetInt64(dataPropertyDefinition.Name)));
                        }
                        break;
                    }
                case OSGeo.FDO.Schema.DataType.DataType_DateTime:
                    {
                        if (!readerFDO.IsNull(dataPropertyDefinition.Name))
                        {
                            propertyValue = new PropertyValue(dataPropertyDefinition.Name, new DateTimeValue(readerFDO.GetDateTime(dataPropertyDefinition.Name)));
                        }
                        break;
                    }
                case OSGeo.FDO.Schema.DataType.DataType_Single:
                    {
                        if (!readerFDO.IsNull(dataPropertyDefinition.Name))
                        {
                            propertyValue = new PropertyValue(dataPropertyDefinition.Name, new SingleValue(readerFDO.GetSingle(dataPropertyDefinition.Name)));
                        }
                        break;
                    }
                case OSGeo.FDO.Schema.DataType.DataType_Double:
                    {
                        if (!readerFDO.IsNull(dataPropertyDefinition.Name))
                        {
                            propertyValue = new PropertyValue(dataPropertyDefinition.Name, new DoubleValue(readerFDO.GetDouble(dataPropertyDefinition.Name)));
                        }
                        break;
                    }
                case OSGeo.FDO.Schema.DataType.DataType_Decimal:
                    {
                        if (!readerFDO.IsNull(dataPropertyDefinition.Name))
                        {
                            propertyValue = new PropertyValue(dataPropertyDefinition.Name, new DecimalValue(readerFDO.GetDouble(dataPropertyDefinition.Name)));
                        }
                        break;
                    }
                case OSGeo.FDO.Schema.DataType.DataType_Boolean:
                    {
                        if (!readerFDO.IsNull(dataPropertyDefinition.Name))
                        {
                            propertyValue = new PropertyValue(dataPropertyDefinition.Name, new BooleanValue(readerFDO.GetBoolean(dataPropertyDefinition.Name)));
                        }
                        break;
                    }
                default:
                    {
                        break;
                    }
            }

            return propertyValue;
        }
    }

    internal class InformacionClase
    {
        public string Nombre { get; set; }
        public string NombreCalificado { get; set; }

        public List<PropertyValue> Propiedades { get; set; }

        public InformacionClase(string nombre, string nombreCalificado)
        {
            this.Nombre = nombre;
            this.NombreCalificado = nombreCalificado;
            this.Propiedades = new List<PropertyValue>();
        }

    }
}