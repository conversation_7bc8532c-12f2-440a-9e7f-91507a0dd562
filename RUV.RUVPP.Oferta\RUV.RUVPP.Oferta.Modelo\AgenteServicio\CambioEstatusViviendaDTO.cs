﻿namespace RUV.RUVPP.Oferta.Modelo.AgenteServicio
{
    /// <summary>
    /// Objeto que contiene las propiedades necesarias para ejecutar el servicio de Cambio Estatus Vivienda.
    /// </summary>
    public class CambioEstatusViviendaDTO
    {
        /// <summary>
        /// Constructor de la clase CancelacionCuvDTO.
        /// </summary>
        /// <param name="cuv">Clave Única de Vivienda que se quiere cancelar.</param>
        /// <param name="idUsuario">Identificador del usuario que ejecuta el servicio.</param>
        /// <param name="idEstatusVivienda">Identificador del Estatus al que se desea cambiar.</param>
        /// <param name="urlDocumento">Dirección en el blob del documento asociado.</param>
        /// <param name="fechaRegistro">Fecha en que se ejecuta el acción.</param>
        public CambioEstatusViviendaDTO(string cuv, int idUsuario, int idEstatusVivienda, string urlDocumento, string fechaRegistro)
        {
            this.cuv = cuv;
            this.idUsuario = idUsuario;
            this.idEstatusVivienda = idEstatusVivienda;
            this.urlDocumento = urlDocumento;
            this.fechaRegistro = fechaRegistro;
        }

        /// <summary>
        /// Clave Única de Vivienda de 16 dígitos.
        /// </summary>
        public string cuv { get; set; }

        /// <summary>
        /// Usuario que ejecuta el servicio.
        /// </summary>
        public int idUsuario { get; set; }

        /// <summary>
        /// Identificador del Estatus al que se desea cambiar.
        /// </summary>
        public int idEstatusVivienda { get; set; }

        /// <summary>
        /// Dirección en el blob del documento asociado.
        /// </summary>
        public string urlDocumento { get; set; }

        /// <summary>
        /// Fecha en que se ejecuta el acción.
        /// </summary>
        public string fechaRegistro { get; set; }
    }
}
