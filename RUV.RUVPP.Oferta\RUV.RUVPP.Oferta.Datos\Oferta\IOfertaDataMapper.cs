﻿using RUV.RUVPP.Oferta.Comun.Modelo;
using RUV.RUVPP.Oferta.Modelo.Historico.Data;
using RUV.RUVPP.Oferta.Modelo.Oferta.Api;
using RUV.RUVPP.Oferta.Modelo.Oferta.Data;
using RUV.RUVPP.Oferta.Modelo.Proyectos;
using RUV.RUVPP.Oferta.Modelo.Proyectos.Data;
using RUV.Comun.Seguridad.JWT;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Datos.Oferta
{
    public interface IOfertaDataMapper : IDisposable
    {
        Task<int> GuardarOfertaViviendaAsync(int idProyecto, string nombre, string oferta);
        Task<int> GuardarDetalleOfertaViviendaAsync(OfertaVivienda ofertaVivienda);
        Task<int>  ActualizarOfertaViviendaAsync(OfertaVivienda ofertaVivienda);
        Task<int> ActualizarTemporalOfertaViviendaAsync(OfertaVivienda ofertaVivienda);
        Task<int> ActualizarDetalleOfertaViviendaAsync(OfertaVivienda ofertaVivienda);
        Task EliminarOfertaParcialAsync(int idParcialOferta);
        Task<bool> EliminarOfertaAsync(int idOfertaVivienda);
        Task<bool> EliminarOfertaLogicaAsync(int idOfertaVivienda);
        Task GuardarOfertaViviendaAsync(int idVivienda, int idOferta);
        Task EliminarOfertaViviendaAsync(int idVivienda, int idOferta);

        Task<Modelo.Oferta.Api.Oferta> ObtenerOfertasFiltroAsync(int? idEmpresa, string noRuv, string nombreProyecto, int? idProyecto, int? idOferta, string claveOferta);

        Task<Tuple<int, List<Modelo.Oferta.Api.Oferta>>> ObtenerOfertasFiltroConPaginadoAsync(int tamanioPagina, int pagina, int? idEmpresa, string noRuv, string nombreProyecto, int? idProyecto, int? idOferta, int? idEstatus);

        Task GuardarLicenciaFactibilidadAsync(List<LicenciaFactibilidad> licenciasFactibilidades, int idOferta);
        Task ActualizarLicenciaFactibilidadAsync(List<LicenciaFactibilidad> licenciasFactibilidades, int idOferta);
        Task EliminarLicenciaFactibilidadAsync(List<LicenciaFactibilidad> licenciasFactibilidades, int idOferta);
        Task GuardarDocumentoxOfertaViviendaAsync(int idOfertaVivienda, int idDocumento);
        Task EliminarDocumentoxOfertaViviendaAsync(int idOfertaVivienda, int idDocumento);
        Task<int> GuardarPrototipoxOfertaViviendaAsync(int idOfertaVivienda, int idPrototipo);
        Task EliminarPrototipoxOfertaViviendaAsync(int idOfertaVivienda, int idPrototipo);

        Task<Tuple<int, List<Vivienda>>> ObtenerViviendasFiltroConPaginadoAsync(int tamanioPagina, int pagina, int? idEmpresa, string noRuv, string nombreProyecto, int? idProyecto, int? idOferta, int? idVivienda, string cuv);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="idEmpresa"></param>
        /// <param name="noRuv"></param>
        /// <param name="nombreProyecto"></param>
        /// <param name="idProyecto"></param>
        /// <param name="idOferta"></param>
        /// <param name="claveOferta"></param>
        /// <param name="idVivienda"></param>
        /// <param name="cuv"></param>
        /// <returns></returns>
        Task<List<Vivienda>> ObtenerViviendasFiltroAsync(int? idEmpresa, string noRuv, string nombreProyecto, int? idProyecto, int? idOferta, string claveOferta, int? idVivienda, string cuv);

        Task<int> ObtenerPrototipoxOfertaViviendaAsync(int idOfertaVivienda, int idPrototipo);
        Task<int> GuardarDocumentoxPrototipoxOfertaViviendaAsync(int idPrototipoxOfertaVivienda, int idDocumento);
        Task<int> EliminarDocumentoxPrototipoxOfertaViviendaAsync(int idPrototipoxOfertaVivienda, int idDocumento);
        Task GuardarOfertaViviendaxRiesgoOfertaAsync(ViviendaRiesgoOferta viviendariesgoOferta);
        Task GuardarDirectorResponsableObraxOfertaViviendaAsync(int idDRO, int idOfertaVivienda);
        Task GuardarPromotorExternoxOfertaViviendaAsync(int idPromotorExterno, int idOfertaVivienda);
        Task EliminarPromotorExternoxOfertaViviendaAsync(int idPromotorExterno, int idOfertaVivienda);
        Task GuardarPropietarioTerrenoxOfertaViviendaAsync(int idPropertarioTerreno, int idOfertaVivienda);
        Task GuardarZonasRiesgoAsync(ZonaRiesgo[] viviendaRiesgoOferta, int idOferta);
        Task ActualizarZonasRiesgoAsync(ZonaRiesgo[] viviendaRiesgoOferta, int idOferta);
        Task EliminarZonaRiesgoAsync(ZonaRiesgo[] viviendaRiesgoOferta, int idOferta);
        Task ActualizarOfertaViviendaxRiesgoOfertaAsync(ViviendaRiesgoOferta viviendariesgoOferta);
        Task EliminarOfertaViviendaxRiesgoOfertaAsync(ViviendaRiesgoOferta viviendariesgoOferta);
        Task<Tuple<int, List<ViviendaSinAsignar>>> ObtenerVivendaSinAsignarAsync(int? idProyecto, int tamanioPagina, int pagina, int? idOferta);
        Task<Tuple<int, List<ViviendaSinAsignar>>> ObtenerVivendaSinAsignarRegistroAsync(int idProyecto, int tamanioPagina, int pagina, int? idOferta);
        Task<Tuple<int, List<ViviendaSinAsignar>>> ObtenerVivendaSinAsignarActualizacionAsync(int tamanioPagina, int pagina, int? idOferta);
        Task<List<ViviendaPrototipo>> ObtenerVivienasPrototipoAsync(int? idProyecto, int? idOferta);

        /// <summary>
        ///
        /// </summary>
        /// <param name="idOferta"></param>
        /// <returns></returns>
        Task<Modelo.Oferta.Api.Oferta> ObtenerOfertaPorIdAsync(int idOferta);
        Task<OfertaVivienda> ObtenerOfertaAsync(int idOferta);
        Task<OfertaVivienda> ObtenerOfertaDetalleAsync(int idOferta);

        Task<List<Modelo.Oferta.Api.Oferta>> ObtenerOfertasxProyectoAsync(int idProyecto);

        /// <summary>
        ///
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="idOferta"></param>
        /// <param name="nombreProyecto"></param>
        /// <returns></returns>
        Task<Tuple<int, List<Modelo.Oferta.Api.Oferta>>> ObtenerOfertasFiltradoAsync(int tamanioPagina, int pagina, string claveOferta, string nombreProyecto, int? idEmpresa);
        Task<OfertaVivienda> ObtenerDatosGeneralesAsync(int idOferta);
        Task<List<int>> ObtenerPromotorVendedorxOfertaAsync(int idOferta);
        Task<List<int>> ObtenerPropietarioTerrenoxOfertaAsync(int idOferta);
        Task<List<int>> ObtenerDROxOfertaAsync(int idOferta);
        Task<List<ZonaRiesgo>> ObtenerZonasRiesgoAsync(int idOferta);
        Task<List<DocumentoRuv>> ObtenerDocumentoxOfertaAsync(int idOferta, TiposDocumento dictamenRiesgoProyecto);
        Task<List<LicenciaFactibilidad>> ObtenerLicenciasFactibilidadAsync(int idOferta);
        Task<List<DocumentoComplementario>> ObtenerDocumentosComplementariosAsync(int idOferta);
        Task<List<PrototipoVivienda>> ObtenerDocumentosPrototiposAsync(int idOferta);
        Task<List<PrototipoCatalogo>> ObtenerCatalogoPrototiposAsync();
        Task<List<Documento>> ObtenerCatalogoDocumentosComplementariosAsync();
        Task<Dictionary<int, string>> ObtenerProyectosOfertaAsync(int idEmpresa);
        Task<FichaPago> ObtenerFichaPagoAsync(int idOferta);
        Task<int> DesvincularViviendaAsync(int idOfertaVivienda);
        Task<bool> ActualizarEstatusOfertaViviendaAsync(int idOfertaVivienda, int idEstatusOfertaVivienda, bool actualizarFechaAceptacion=false);
        Task<bool> ActualizarEstatusViviendaAsync(int idOfertaVivienda, int idEstatusVivienda);

        Task<bool> ActualizarEstatusViviendaPoProyectoAsync(int idProyecto, int idEstatusVivienda);
        /// <summary>
        /// 
        /// </summary>
        /// <param name="idProyecto"></param>
        /// <returns></returns>
        Task<List<Vivienda>> ObtenerVivendasPorIdProyectoAsync(int idProyecto, int? idOferta);

        /// <summary>
        /// Obtiene el listado de ordenes de trabajo para una oferta por idOferta
        /// </summary>
        /// <param name="idOfertaVivienda">Identificador de la oferta</param>
        /// <param name="tamanioPagina">Tamaño de pagina</param>
        /// <param name="pagina">Pagina</param>
        /// <returns>Lista de ordenes de trabajo de la oferta</returns>
        Task<Tuple<int, List<OrdenTrabajoHistoricoValidacion>>> ObtenerOrdenesTrabajoPorOfertaPaginadoAsync(int idOfertaVivienda, int tamanioPagina, int pagina);

        Task<List<Vivienda>> ObtenerViviendasPorOfertaViviendaAsync(int idOfertaVivienda);

        /// <summary>
        /// Obtiene el listado de viviendas para una oferta por idOferta paginado
        /// </summary>
        /// <param name="idProyecto">Identificador del proyecto</param>
        /// <param name="tamanioPagina">Tamaño de pagina</param>
        /// <param name="pagina">Pagina</param>
        /// <returns>Lista de viviendas de la oferta</returns>
        Task<Tuple<int, List<Vivienda>>> ObtenerVivienasPorOfertaPaginadoAsync(int idOferta, int tamanioPagina, int pagina);
        /// <summary>
        /// Obtiene el catalogo de estatus de vivienda
        /// </summary>        
        Task<List<Modelo.Oferta.Data.EstatusVivienda>> ObtenerCatalogoEstatusViviendaAsync();
        /// <summary>
        /// Obtiene los ids y clave de oferta con estatus determinado dado un idProyecto
        /// </summary>
        /// <param name="idProyecto">Identificador dle proyecto</param>
        Task<List<ClaveOferta>> ObtenerIdsOfertaPorProyectoAsync(int idProyecto, int idEstatusOferta);
        Task<int> GuardaClaveAsync(string claveOferta, int idOfertaVivienda);
        /// <summary>
        /// Obtiene una lista de tuplas que contienen el idVivienda e idEstatusVivienda a partir de una
        /// oferta que se adquiere previamente por medio del idVivienda que se manda.
        /// </summary>
        /// <param name="idOfertaVivienda">Identificador de la vivienda para obtener la oferta a la que pertenece.</param>        
        Task<List<Tuple<int,int, int>>> ObtenerViviendasPorIdOfertaViviendaAsync(int idOfertaVivienda);
        /// <summary>
        /// Obtiene una CUV a partir de un idVivienda.
        /// </summary>
        /// <param name="idVivienda">Identificador de la vivienda.</param>        
        Task<string> ObtenerCUVPorIdViviendaAsync(int idVivienda);
        /// <summary>
        /// Obtiene una CUV Geográfica a partir de un idVivienda.
        /// </summary>
        /// <param name="idVivienda">Identificador de la vivienda.</param>        
        Task<string> ObtenerCUVGeograficaPorIdViviendaAsync(int idVivienda);
        /// <summary>
        /// Obtiene una lista de tuplas que contienen el idVivienda y el idOfertaVivienda.
        /// </summary>
        /// <param name="idVivienda">Identificador de la vivienda para obtener la oferta a la que pertenece.</param>        
        Task<List<Tuple<int, int?>>> ObtenerIdOfertaPorIdViviendaAsync(string idVivienda);
        Task<Tuple<int, List<DocumentoComplementario>>> ObtenerDocumentosComplementariosPaginadosAsync(int idOferta, int pagina, int tamPag);
        Task<Tuple<int, List<LicenciaFactibilidad>>> ObtenerLicenciasFactibilidadPaginadosAsync(int idOferta, int pagina, int tamPag);
        Task<Tuple<int, List<PrototipoVivienda>>> ObtenerDocumentosPrototiposPaginadosAsync(int idOferta, int pagina, int tamPag);
        Task<bool> ActualizarEstatusIdViviendaAsync(int idOfertaVivienda, int idEstatusVivienda);
        Task<List<int>> ObtenerViviendasAsignadasPorListaAsync(int idOferta, string listaIdsViviendas);
        Task<int> ObtenerIdentificadorViviendaPorCUV(string cuv);
        Task<List<PrototipoVivienda>> ObtenerPrototiposConsultaGeneral(int? idProyecto, int? idOferta, string cuv = null);
        Task<int> ObtenerPrototipoPorViviendaAsync(int idVivienda);
        /// <summary>
        /// Obtiene la clave de la oferta de una cuv
        /// </summary>
        /// <param name="cuv">clave unica de vivienda</param>
        /// <returns></returns>
        Task<string> ObtenerClaveOfertaxCuvAsync(string cuv);

        /// <summary>
        /// Obtiene los documentos asociados a los Identificadores
        /// que se envíen en forma de cadena de caracteres separados por comas.
        /// </summary>
        /// <param name="idDocumentos">Identificadores de los Documentos en cadena de caracteres separados por una coma.</param>
        /// <returns></returns>
        Task<List<DocumentoEventoVivienda>> ObtenerDocumentosPorIdentificadores(string idDocumentos);

        /// <summary>
        /// Inserta valores en la tabla de Documentos por Cambio de Estatus de Vivienda.
        /// </summary>
        /// <param name="idDocumento">Identificador del Documento.</param>
        /// <param name="idVivienda">Identificador de la Vivienda.</param>
        /// <param name="descripcionArchivo">Descripción del Documento.</param>
        /// <returns></returns>
        Task<bool> InsertarValoresDocumentoPorEstatusVivienda(int idDocumento, int idVivienda, string descripcionArchivo);

        /// <summary>
        /// Procedimiento Almacenado para Eliminar un registro de la tabla de Descripciones de Documentos por Evento Vivienda.
        /// </summary>
        /// <param name="idDocumento">Identificador del Documento.</param>
        /// <returns></returns>
        Task<bool> EliminarRegistrosDocumentoPorEventoVivienda(int idDocumento);

        /// <summary>
        /// Método para obtener los medios de individualización junto con un campo extra de Banco.
        /// </summary>
        /// <returns></returns>
        Task<List<Tuple<int, string>>> ObtenerMedioIndividualizacion();

        /// <summary>
        /// Método para obtener los bancos como medio de individualización.
        /// </summary>
        /// <returns></returns>
        Task<List<Tuple<int, string>>> ObtenerBancosIndividualizacion();

        /// <summary>
        /// Método para obtener los tipos de individualización de acuerdo a un medio introducido.
        /// </summary>
        /// <param name="idMedioIndividualizacion">Identificador del Medio seleccionado.</param>
        /// <returns></returns>
        Task<List<Tuple<int, string>>> ObtenerTipoIndividualizacion(int idMedioIndividualizacion);

        /// <summary>
        /// Método para insertar valores en la tabla de Individualización de Vivienda.
        /// </summary>
        /// <param name="idVivienda">Identificador de la Vivienda.</param>
        /// <param name="idMedioIndividualizacion">Identificador del Medio seleccionado.</param>
        /// <param name="idTipoIndividualizacion">Identificador del Tipo de Individualización. El dato es opcional.</param>
        /// <param name="fechaIndividualizacion">Fecha seleccionada.</param>
        /// <returns></returns>
        Task<bool> InsertarIndividualizacion(int idVivienda, int idMedioIndividualizacion, int? idTipoIndividualizacion, DateTime fechaIndividualizacion);

        /// <summary>
        /// Método para eliminar valores en la tabla de Individualización de Vivienda.
        /// </summary>
        /// <param name="idVivienda">Identificador de la Vivienda.</param>
        /// <returns></returns>
        Task<bool> EliminarIndividualizacion(int idVivienda);
    }
}