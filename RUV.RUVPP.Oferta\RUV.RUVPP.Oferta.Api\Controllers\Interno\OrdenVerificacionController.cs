﻿using Microsoft.ApplicationInsights;
using RUV.Comun.Seguridad.JWT;
using RUV.Comun.Utilerias;
using RUV.Comun.Web.Http;
using RUV.RUVPP.Entidades.General.Documentos;
using RUV.RUVPP.Generales.Modelo.Documentos;
using RUV.RUVPP.Oferta.Dominio.OrdenVerificacion;
using RUV.RUVPP.Oferta.Modelo.Mediciones;
using RUV.RUVPP.Oferta.Modelo.OrdenVerificacion.Api;
using RUV.RUVPP.Oferta.Modelo.OrdenVerificacion.Data;
using RUV.RUVPP.Oferta.Modelo.Proyectos.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using System.Web.Http.Description;

namespace RUV.RUVPP.Oferta.Api.Controllers.Interno
{
    /// <summary>
    /// 
    /// </summary>
    [RoutePrefix("interno/api/ordenverificacion")]
    public class OrdenVerificacionController : ApiControllerBase
    {
        private readonly IServicioOrdenVerficacion _servicioOrdenVerificacion;

        /// <summary>
        /// Constructor del Controlador
        /// </summary>
        /// <param name="clienteTelemetria"></param>
        /// <param name="_servicioOrdenVerificacion"></param>
        public OrdenVerificacionController(IServicioOrdenVerficacion _servicioOrdenVerificacion) : base()
        {
            this._servicioOrdenVerificacion = _servicioOrdenVerificacion;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="claveOferta"></param>
        /// <param name="idOrdenVerificacion"></param>
        /// <param name="idRUVAsis"></param>
        /// <returns></returns>
        [HttpGet, Route("ordenes/filtro")]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerOrdenesVerificacionFiltroAsync(string claveOferta = null, string idOrdenVerificacion = null, string idRUVAsis = null)
        {
            var result = await this._servicioOrdenVerificacion.ObtenerOrdenesVerificacionFiltroAsync(claveOferta, idOrdenVerificacion, idRUVAsis);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="idOrdenVerificacion"></param>
        /// <param name="idRuvAsIs"></param>
        /// <returns></returns>
        [HttpGet, Route("obtener-orden")]
        public async Task<HttpResponseMessage> ObtenerOrdenVerificacionAsync(string idOrdenVerificacion, int idRuvAsIs)
        {
            var result = await this._servicioOrdenVerificacion.ObtenerOrdenesVerificacionFiltroAsync(idOrdenVerificacion, idRuvAsIs);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("pagos-orden-verificacion-paginado")]
        [ResponseType(typeof(ResultadoPaginado<List<PagosOrdenVerificacion>>))]
        //[AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerPagosOrdenVerificacionPaginado(int tamanioPagina, int pagina, string idOrdenVerificacion)
        {
            var result = await this._servicioOrdenVerificacion.ObtenerPagosOrdenVerificacionPaginado(tamanioPagina, pagina, idOrdenVerificacion);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("obtener-informacion-documento")]
        [ResponseType(typeof(DocumentoRuv))]
        public async Task<HttpResponseMessage> ObtenerInformacionDocumento(int idDocumento)
        {
            var result = await this._servicioOrdenVerificacion.ObtenerInformacionDocumento(idDocumento);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="idVivienda"></param>
        /// <param name="idOrdenVerificacion"></param>
        /// <returns></returns>
        [HttpGet, Route("viviendas/orden")]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerViviendasPorOrdenesVerificacionAsync(string idVivienda = null, string idOrdenVerificacion = null)
        {
            var result = await this._servicioOrdenVerificacion.ObtenerViviendasPorOrdenesVerificacionAsync(idVivienda, idOrdenVerificacion);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="idOrdenVerificacion"></param>
        /// <returns></returns>
        [HttpGet, Route("viviendas/orden/{idOrdenVerificacion}/reporte")]
        [ResponseType(typeof(List<ConsultaReporteCuv>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerReporteViviendasPorOrdenesVerificacionAsync(string idOrdenVerificacion)
        {
            var result = await this._servicioOrdenVerificacion.ObtenerViviendasReportePorOrdenesVerificacionAsync(idOrdenVerificacion);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// Obtiene las viviendas por OV y su estatus en ASIS
        /// </summary>
        /// <param name="idOrdenVerificacion"></param>
        /// <returns></returns>
        [HttpGet, Route("{idOrdenVerificacion}/viviendas")]
        [ResponseType(typeof(List<ConsultaReporteCuv>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerViviendasSembradoPorOrdenesVerificacionAsync(string idOrdenVerificacion)
        {
            var result = await this._servicioOrdenVerificacion.ObtenerViviendasReportePorOrdenesVerificacionAsync(idOrdenVerificacion);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="claveOferta"></param>
        /// <param name="idOrdenVerificacion"></param>
        /// <param name="idCuv"></param>
        /// <returns></returns>
        [HttpGet, Route("ordenespaginador/oferta/{claveOferta}")]
        [ResponseType(typeof(ResultadoPaginado<List<OrdenVerificacion>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerOrdenesVerificacionPorIdOfertaPaginadorAsync(int tamanioPagina, int pagina, string claveOferta = null, string idOrdenVerificacion = null, string idCuv = null)
        {
            if (claveOferta == "{0}")
                claveOferta = null;

            if (idCuv == "0")
                idCuv = null;

            var result = await this._servicioOrdenVerificacion.ObtenerOrdenesVerificacionPorIdOfertaPaginadorAsync(tamanioPagina, pagina, claveOferta, idOrdenVerificacion, idCuv);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="idVivienda"></param>
        /// <param name="idOrdenVerificacion"></param>
        /// <returns></returns>
        [HttpGet, Route("viviendas/orden/paginador")]
        [ResponseType(typeof(ResultadoPaginado<List<List<Vivienda>>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerViviendasPorOrdenesVerificacionConPaginadoFiltroAsync(int tamanioPagina, int pagina, string idVivienda = null, string idOrdenVerificacion = null )
        {
            var result = await this._servicioOrdenVerificacion.ObtenerViviendasPorOrdenesVerificacionConPaginadoFiltroAsync(tamanioPagina, pagina, idVivienda, idOrdenVerificacion);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="idOrdenVerificacion"></param>
        /// <returns></returns>
        [HttpGet, Route("orden/{idOrdenVerificacion}")]
        [ResponseType(typeof(ResultadoPaginado<List<Vivienda>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerDetalleOrdenVerificacionAsync(string idOrdenVerificacion)
        {
            var result = await this._servicioOrdenVerificacion.ObtenerDetalleOrdenVerificacionAsync(idOrdenVerificacion);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="claveOferta"></param>
        /// <returns></returns>
        [HttpGet, Route("orden/oferta/{claveOferta}")]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerUltimaOrdenVerificacionPorIdOfertaAsync(string claveOferta)
        {
            var result = await this._servicioOrdenVerificacion.ObtenerUltimaOrdenVerificacionPorIdOfertaAsync(claveOferta);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        #region Disposible

        protected override void Dispose(bool disposing)
        {
            base.Dispose(disposing);

            this._servicioOrdenVerificacion.Dispose();
        }

        #endregion


    }
}