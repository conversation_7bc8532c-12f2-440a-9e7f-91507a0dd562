﻿using System;
using System.Web;
using System.Web.Hosting;
using System.Web.Http;
using Microsoft.Owin;
using Microsoft.Owin.Security;
using Microsoft.Owin.Security.OAuth;
using Microsoft.Owin.Security.OpenIdConnect;
using Owin;
using RUV.Comun.Seguridad.JWT;
using RUV.Comun.Seguridad.JWT.Token;
using System.Threading.Tasks;
using RUV.RUVPP.Negocio.General.Seguridad;
using System.Web.Routing;
using System.Net.Http;
using System.Threading;

namespace RUV.RUVPP.Oferta.Api
{
    public static class StartupAuth
    {


        // For more information on configuring authentication, please visit http://go.microsoft.com/fwlink/?LinkId=301864
        public static void Configuration(IAppBuilder app)
        {
            var appConfiguration = AppConfiguration.CreateFromConfigFile(prefix: "JwtRuv");



            var jwtBearerOptions = new JwtBearerTokenAuthenticationOptions(appConfiguration.JwtOptions);

            jwtBearerOptions.JwtBearerOptions.Provider = new SimpleOAuthBearerAuthenticationProvider();

            app.UseJwtBearerAuthenticationWithTokenProvider(jwtBearerOptions);

            var customProviderOptions = SampleOptionsHelper.CreateOptions(appConfiguration.JwtOptions);


            app.UseOAuthAuthorizationServer(new OAuthAuthorizationServerOptions
            {
                AccessTokenFormat = jwtBearerOptions.JwtFormat,
                ApplicationCanDisplayErrors = true,
                //TODO:MZ usuario Virtual -descomentar verificar token
                Provider = new CustomOAuthProvider(customProviderOptions, verficarToken), //en seguridad apagada no se verifica token
                AccessTokenExpireTimeSpan = TimeSpan.FromHours(12),//la expiracion del token la valida el atributo de autorizacion 
                AuthorizeEndpointPath = new PathString("/home/<USER>"),
                AllowInsecureHttp = true,//appConfiguration.AllowInsecureHttp,
                AccessTokenProvider = new JwtBearerTokenProvider(appConfiguration.JwtOptions)
            });


        }

        /// <summary>
        /// Verificacion de Token con Valores en BD
        /// </summary>
        /// <param name="usuario"></param>
        private static void verficarToken(CustomUserRuv usuario)
        {
            IServicioSeguridad servicio = new ServicioSeguridad();

            var esTokenValido = servicio.ValidarToken(usuario.IdUsuario.GetValueOrDefault(), usuario.TokenGuid);
            //finalizar la sesion
            if (!esTokenValido)
            {//validacion de Expiracion se permite la carga de la ventana Inicio y se cierra Sesion por acciones de API.
                var usuarioDB = servicio.ObtenerUsuarioDto(usuario.IdUsuario.GetValueOrDefault());
                var validacionExpiracion = servicio.ValidarToken(usuario.IdUsuario.GetValueOrDefault(), usuarioDB.token);

                if (!validacionExpiracion)
                    return;
            }


            if (!esTokenValido || string.IsNullOrEmpty(usuario.TokenGuid))
            {
                usuario.TokenGuid = null;
                return;
                //throw new Exception("InvalidToken");
            }

            //se actualiza  hora de fin de sesion
            if(!string.IsNullOrEmpty(usuario.TokenGuid))
                servicio.ActualizarToken(usuario.IdPerfil.GetValueOrDefault(), usuario.IdUsuario.GetValueOrDefault(), usuario.TokenGuid);
        }
    }
}
