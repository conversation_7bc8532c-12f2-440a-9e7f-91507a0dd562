﻿
using Microsoft.ApplicationInsights;
using RUV.Comun.Negocio;
using RUV.RUVPP.Oferta.Datos.OrdenVerificacion;
using RUV.RUVPP.Oferta.Dominio.OrdenVerificacion;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RUV.RUVPP.Oferta.Modelo.OrdenVerificacion.Data;
using RUV.Comun.Utilerias;
using RUV.RUVPP.Oferta.Modelo.Proyectos.Data;
using RUV.RUVPP.Oferta.Modelo.Mediciones;
using RUV.RUVPP.Oferta.Datos.Proyectos;
using RUV.RUVPP.Oferta.Datos.Mediciones;
using RUV.RUVPP.Oferta.Datos.ConvivenciaAsIs;
using RUV.RUVPP.Oferta.Modelo.AgenteServicio;
using System.Configuration;
using RUV.RUVPP.Oferta.Datos.Oferta;
using RUV.RUVPP.Oferta.Modelo.Oferta.Api;
using RUV.RUVPP.Negocio.Empresa.EmpresaConsulta;
using RUV.RUVPP.Entidades.Empresa;
using RUV.RUVPP.Negocio.Empresa.Comun;
using RUV.RUVPP.Oferta.Modelo.OrdenVerificacion.Api;
using RUV.RUVPP.Datos.Comun.Contratos;
using RUV.RUVPP.Negocio.General.Documentos.Abstracta;
using RUV.RUVPP.Entidades.General.Documentos;
using RUV.RUVPP.Generales.Modelo.Documentos;
using System.Web.Configuration;
using ClosedXML.Excel;
using System.IO;
using System.IO.Compression;
using System.Web;
using System.Net.Http;
using Newtonsoft.Json;
using System.Net.Http.Headers;
using System.Net;

namespace RUV.RUVPP.Oferta.Dominio.OrdenVerificacion.Implementacion
{
    public class ServicioOrdenVerificacion : ServicioDominioBase, IServicioOrdenVerficacion
    {

        private readonly IOrdenVerificacionDataMapper _ordenVerificacionDataMapper;
        private readonly IProyectosDataMapper _proyectosDataMapper;
        private readonly IMedicionesDataMapper _medicionesDataMapper;
        private readonly IAgenteServicioConvivenciaASIS _agenteServicioOfertaASIS;
        private readonly IOfertaDataMapper _ofertaDataMapper;
        private readonly IServicioEmpresaComun _servicioEmpresaComun;
        private readonly IPersistible<DocumentoDto> _servicioDocumento;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="clienteTelemetria"></param>
        /// <param name="_ordenVerificacionDataMapper"></param>
        public ServicioOrdenVerificacion(IOrdenVerificacionDataMapper _ordenVerificacionDataMapper, IProyectosDataMapper proyectosDataMapper,
                                            IMedicionesDataMapper medicionesDataMapper, IAgenteServicioConvivenciaASIS agenteServicioOfertaASIS,
                                            IOfertaDataMapper ofertaDataMapper, IServicioEmpresaComun servicioEmpresaComun,
                                            IPersistible<DocumentoDto> _servicioDocumento) : base()
        {
            this._ordenVerificacionDataMapper = _ordenVerificacionDataMapper;
            this._proyectosDataMapper = proyectosDataMapper;
            this._medicionesDataMapper = medicionesDataMapper;
            this._agenteServicioOfertaASIS = agenteServicioOfertaASIS;
            this._ofertaDataMapper = ofertaDataMapper;
            this._servicioEmpresaComun = servicioEmpresaComun;
            this._servicioDocumento = _servicioDocumento;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="idOrdenVerificacion"></param>
        /// <returns></returns>
        public async Task<List<Vivienda>> ObtenerDetalleOrdenVerificacionAsync(string idOrdenVerificacion)
        {
            return await this._ordenVerificacionDataMapper.ObtenerDetalleOrdenVerificacionAsync(idOrdenVerificacion);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="idOrdenVerificacion"></param>
        /// <returns></returns>
        public async Task<Modelo.OrdenVerificacion.Data.OrdenVerificacion> ObtenerOrdenesVerificacionFiltroAsync(string idOrdenVerificacion, int idRuvAsIs)
        {

            var ordenes = await this._ordenVerificacionDataMapper.ObtenerOrdenesVerificacionFiltroAsync(null, idOrdenVerificacion, idRuvAsIs.ToString());

            return ordenes.FirstOrDefault();
        }

        public async Task<ResultadoPaginado<List<PagosOrdenVerificacion>>> ObtenerPagosOrdenVerificacionPaginado(int tamanioPagina, int pagina, string idOrdenVerificacion)
        {
            ResultadoPaginado<List<PagosOrdenVerificacion>> resultado = new ResultadoPaginado<List<PagosOrdenVerificacion>>();
            resultado.TamanioPagina = tamanioPagina;
            resultado.PaginaActual = pagina;

            var data = await this._ordenVerificacionDataMapper.ObtenerPagosOrdenVerificacionPaginado(tamanioPagina, pagina, idOrdenVerificacion);

            if (data.Item1 > 0)
            {
                resultado.TotalRegistros = data.Item1;
                resultado.TotalPaginas = (int)Math.Ceiling((double)(resultado.TotalRegistros / tamanioPagina));
            }

            var ordenes = data.Item2;

            resultado.Resultado = ordenes;

            return resultado;

        }

        public async Task<DocumentoRuv> ObtenerInformacionDocumento(int idDocumento)
        {
            DocumentoRuv docRuv = new DocumentoRuv();

            var documento = _servicioDocumento.Obtener(new DocumentoDto() { idDocumento = idDocumento }).FirstOrDefault();

            docRuv.IdDocumento = documento.idDocumento.Value;
            docRuv.NombreArchivo = documento.nombreArchivo;
            docRuv.UrlArchivo = documento.rutaArchivo;
            docRuv.IdCatalogoDocumento = documento.idCatalogoDocumento;

            return docRuv;
        }





        /// <summary>
        /// 
        /// </summary>
        /// <param name="claveOferta"></param>
        /// <param name="idOrdenVerificacion"></param>
        /// <param name="idRUVAsis"></param>
        /// <returns></returns>
        public async Task<List<Modelo.OrdenVerificacion.Data.OrdenVerificacion>> ObtenerOrdenesVerificacionFiltroAsync(string claveOferta, string idOrdenVerificacion, string idRUVAsis)
        {
            var ordenes = await this._ordenVerificacionDataMapper.ObtenerOrdenesVerificacionFiltroAsync(claveOferta, idOrdenVerificacion, idRUVAsis);

            //obtener el nombre del proyecto

            foreach (var orden in ordenes)
            {
                if (orden.claveOferta != null && orden.claveOferta.Trim() != "")
                {
                    var oferta = await _ofertaDataMapper.ObtenerOfertasFiltroAsync(null, null, null, null, null, orden.claveOferta);

                    if (oferta != null)
                    {
                        orden.nombreFrente = oferta.nombreFrente;

                        List<ViviendaPrototipo> listaViviendaPrototipo = new List<ViviendaPrototipo>();

                        if (oferta.idEstatusOfertaVivienda == (int)EstatusOferta.EnRegistro)
                            listaViviendaPrototipo = await this._ofertaDataMapper.ObtenerVivienasPrototipoAsync(oferta.idProyecto, null);
                        else if (oferta.idEstatusOfertaVivienda == (int)EstatusOferta.Aceptada
                            || oferta.idEstatusOfertaVivienda == (int)EstatusOferta.Rechazada
                            || oferta.idEstatusOfertaVivienda == (int)EstatusOferta.RechazadaPorActualizacion)
                            listaViviendaPrototipo = await this._ofertaDataMapper.ObtenerVivienasPrototipoAsync(null, oferta.idOferta);
                        else
                            listaViviendaPrototipo = await this._ofertaDataMapper.ObtenerVivienasPrototipoAsync(oferta.idProyecto, oferta.idOferta);

                        oferta.listaViviendasProyecto = listaViviendaPrototipo.Select(v => { v.esActualizacion = v.esSeleccionado; return v; }).ToList();

                        if (orden != null) {
                            orden.numeroPrototipos = orden.numeroPrototipos;
                            orden.numeroViviendas = orden.numeroViviendas;
                        }
                        else
                        {
                            orden.numeroPrototipos = oferta.listaViviendasProyecto.GroupBy(o => o.idPrototipo).Count();
                            orden.numeroViviendas = oferta.listaViviendasProyecto.Count();
                        }
                        

                        var vivienda = await this._ofertaDataMapper.ObtenerViviendasPorOfertaViviendaAsync(oferta.idOferta.Value);

                        if (vivienda.ToList().Count > 0)
                        {
                            if (vivienda.FirstOrDefault().domicilioCarreteraCamino.idTipoCamino != null && vivienda.FirstOrDefault().domicilioCarreteraCamino.idTipoCamino != "0")
                            {
                                oferta.direccion = vivienda.FirstOrDefault().domicilioCarreteraCamino.nombreVialidad;
                            }
                            else if (vivienda.FirstOrDefault().domicilioGeografico.idmunicipio != null)
                            {
                                var domicilio =
                                    vivienda.FirstOrDefault().domicilioGeografico.estado
                                    + ", " + vivienda.FirstOrDefault().domicilioGeografico.municipio;

                                oferta.direccion = domicilio;
                            }

                        }

                        orden.direccionEntidad = oferta.direccion;

                        if (orden.noRuvVerificador != null) {
                            Entidades.Empresa.EmpresaDto empresaDto = this._servicioEmpresaComun.ObtenerEmpresa(orden.noRuvVerificador);

                            if (empresaDto != null) {
                                orden.correos = this._servicioEmpresaComun.ObtenerCorreosContacto(empresaDto.idEmpresa.Value).ToList();

                                orden.telefonos = this._servicioEmpresaComun.ObtenerTelefonosEmpresa(empresaDto.idEmpresa.Value).ToList();
                            }
                        }
                        

                    }

                }

            }

            return ordenes;
        }



        /// <summary>
        /// 
        /// </summary>
        /// <param name="idVivienda"></param>
        /// <param name="idOrdenVerificacion"></param>
        /// <returns></returns>
        public async Task<List<Vivienda>> ObtenerViviendasPorOrdenesVerificacionAsync(string idVivienda, string idOrdenVerificacion)
        {
            List<Vivienda> listaVivienda = await this._ordenVerificacionDataMapper.ObtenerViviendasPorOrdenesVerificacionAsync(idVivienda, idOrdenVerificacion);

            foreach (var vivienda in listaVivienda)
            {
                var viv = await _ofertaDataMapper.ObtenerViviendasFiltroAsync(null, null, null, null, null, null, null, vivienda.cuv);

                if(viv.Any())
                    vivienda.idPrototipo = viv.FirstOrDefault().idPrototipo;
            }            

            return listaVivienda;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="claveOferta"></param>
        /// <returns></returns>
        public async Task<Modelo.OrdenVerificacion.Data.OrdenVerificacion> ObtenerUltimaOrdenVerificacionPorIdOfertaAsync(string claveOferta)
        {
            return await this._ordenVerificacionDataMapper.ObtenerUltimaOrdenVerificacionPorIdOfertaAsync(claveOferta);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="claveOferta"></param>
        /// <param name="idOrdenVerificacion"></param>
        /// <param name="idCuv"></param>
        /// <returns></returns>
        public async Task<ResultadoPaginado<List<Modelo.OrdenVerificacion.Data.OrdenVerificacion>>> ObtenerOrdenesVerificacionPorIdOfertaPaginadorAsync(int tamanioPagina, int pagina, string claveOferta, string idOrdenVerificacion, string idCuv)
        {
            ResultadoPaginado<List<Modelo.OrdenVerificacion.Data.OrdenVerificacion>> resultado = new ResultadoPaginado<List<Modelo.OrdenVerificacion.Data.OrdenVerificacion>>()
            {
                TamanioPagina = tamanioPagina,
                PaginaActual = pagina
            };

            //var data = await this._ordenVerificacionDataMapper.ObtenerOrdenesVerificacionPorIdOfertaPaginadorAsync(tamanioPagina, pagina, claveOferta, idOrdenVerificacion, idCuv);

            var data = await this._ordenVerificacionDataMapper.ObtenerOrdenesVerificacionPorClavesOfertaPaginadorAsync(tamanioPagina, pagina, claveOferta);

            if (data.Item1 > 0)
            {
                resultado.TotalRegistros = data.Item1;
                resultado.TotalPaginas = (int)Math.Ceiling((double)(resultado.TotalRegistros / tamanioPagina));
            }

            var ordenes = data.Item2;
            var porcentajeAvance = 0;
            string oV = "";
            var contador = 0;
            var ordenesFinales = new List<Modelo.OrdenVerificacion.Data.OrdenVerificacion>();

            //foreach (var orden in ordenes)
            //{
            //    if (contador == 0)
            //    {
            //        oV = orden.idOrdenVerificacion;
            //        porcentajeAvance = Convert.ToInt32(orden.porcentajeAvanceObra);
            //    }
            //    else
            //    {
            //        if (oV == orden.idOrdenVerificacion)
            //        {
            //            if (porcentajeAvance < Convert.ToInt32(orden.porcentajeAvanceObra))
            //            {
            //                porcentajeAvance = Convert.ToInt32(orden.porcentajeAvanceObra);
            //            }
            //        }
            //        else
            //        {
            //            ordenesFinales.Add(orden);
            //            oV = orden.idOrdenVerificacion;
            //            porcentajeAvance = Convert.ToInt32(orden.porcentajeAvanceObra);
            //        }
            //    }

            //    contador++;

            //    if(contador == ordenes.Count)
            //        ordenesFinales.Add(orden);
            //}
            
            resultado.Resultado = ordenes;

            //aqui ir por las viviendas de cada uno
            foreach (var orden in resultado.Resultado)
            {

                //var viviendas = await ObtenerViviendasPorOrdenesVerificacionAsync(null, orden.idOrdenVerificacion);

                /*foreach (var vivienda in viviendas)
                {
                    if (Convert.ToInt32(vivienda.porcentajeDeAvance) <= 20)
                    {
                        orden.ceroaveinte += 1;
                    }
                    else if (Convert.ToInt32(vivienda.porcentajeDeAvance) >= 21 && Convert.ToInt32(vivienda.porcentajeDeAvance) <= 40)
                    {
                        orden.veintiunoacuarenta += 1;
                    }
                    else if (Convert.ToInt32(vivienda.porcentajeDeAvance) >= 41 && Convert.ToInt32(vivienda.porcentajeDeAvance) <= 60)
                    {
                        orden.cuarentayunoysesenta += 1;
                    }
                    else if (Convert.ToInt32(vivienda.porcentajeDeAvance) >= 61 && Convert.ToInt32(vivienda.porcentajeDeAvance) <= 80)
                    {
                        orden.sesentayunoyochenta += 1;
                    }
                    else if (Convert.ToInt32(vivienda.porcentajeDeAvance) >= 81 && Convert.ToInt32(vivienda.porcentajeDeAvance) <= 99)
                    {
                        orden.ochentayunoynoventaynueve += 1;
                    }
                    else if (Convert.ToInt32(vivienda.porcentajeDeAvance) >= 100)
                    {
                        orden.alcien += 1;
                    }

                }*/

                if (orden.claveOferta != null && orden.claveOferta.Trim() != "")
                {
                    var oferta = await _ofertaDataMapper.ObtenerOfertasFiltroAsync(null, null, null, null, null, orden.claveOferta);

                    if (oferta != null)
                    {
                        orden.nombreFrente = oferta.nombreFrente;

                        List<ViviendaPrototipo> listaViviendaPrototipo = new List<ViviendaPrototipo>();

                        if (oferta.idEstatusOfertaVivienda == (int)EstatusOferta.EnRegistro)
                            listaViviendaPrototipo = await this._ofertaDataMapper.ObtenerVivienasPrototipoAsync(oferta.idProyecto, null);
                        else if (oferta.idEstatusOfertaVivienda == (int)EstatusOferta.Aceptada
                            || oferta.idEstatusOfertaVivienda == (int)EstatusOferta.Rechazada
                            || oferta.idEstatusOfertaVivienda == (int)EstatusOferta.RechazadaPorActualizacion)
                            listaViviendaPrototipo = await this._ofertaDataMapper.ObtenerVivienasPrototipoAsync(null, oferta.idOferta);
                        else
                            listaViviendaPrototipo = await this._ofertaDataMapper.ObtenerVivienasPrototipoAsync(oferta.idProyecto, oferta.idOferta);

                        oferta.listaViviendasProyecto = listaViviendaPrototipo.Select(v => { v.esActualizacion = v.esSeleccionado; return v; }).ToList();

                        orden.numeroPrototipos = oferta.listaViviendasProyecto.GroupBy(o => o.idPrototipo).Count();
                        orden.numeroViviendas = oferta.listaViviendasProyecto.Count();

                        //var proyecto = await _proyectosDataMapper.ObtenerProyectoAsync(oferta.idProyecto.Value);                        

                    }
                }
            }

            return resultado;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="idVivienda"></param>
        /// <param name="idOrdenVerificacion"></param>
        /// <returns></returns>
        public async Task<ResultadoPaginado<List<Vivienda>>> ObtenerViviendasPorOrdenesVerificacionConPaginadoFiltroAsync(int tamanioPagina, int pagina, string idVivienda, string idOrdenVerificacion)
        {
            ResultadoPaginado<List<Vivienda>> resultado = new ResultadoPaginado<List<Vivienda>>()
            {
                TamanioPagina = tamanioPagina,
                PaginaActual = pagina
            };

            var data = await this._ordenVerificacionDataMapper.ObtenerViviendasPorOrdenesVerificacionConPaginadoFiltroAsync(tamanioPagina, pagina, idVivienda, idOrdenVerificacion);

            if (data.Item1 > 0)
            {
                resultado.TotalRegistros = data.Item1;
                resultado.TotalPaginas = (int)Math.Ceiling((double)(resultado.TotalRegistros / tamanioPagina));
            }

            resultado.Resultado = data.Item2;

            return resultado;
        }

        /// <summary>
        /// Obtiene los datos del sembrado, habitabilidad, puntajes, numero de ecotecnologias, numero de atributos de las viviendas por OV
        /// </summary>
        /// <param name="idOrdenVerificacion"></param>
        /// <returns></returns>
        public async Task<List<Vivienda>> ObtenerViviendasReportePorOrdenesVerificacionAsync(string idOrdenVerificacion)
        {
            var viviendasReporte = new List<Vivienda>();
            var viviendaReporte = new Vivienda();
            var puntajes = new PuntajeSalida();
            var usuario = ConfigurationManager.AppSettings["UserASIS"];
            var contrasenia = ConfigurationManager.AppSettings["PasswordASIS"];
            var viviendas = await this._ordenVerificacionDataMapper.ObtenerViviendasPorOrdenesVerificacionAsync(null, idOrdenVerificacion);

            foreach (var item in viviendas)
            {
                viviendaReporte = (await this._proyectosDataMapper.ObtenerViviendasPorCUVAsync(item.cuv)).FirstOrDefault() ?? new Vivienda() { domicilioGeografico = new DomicilioGeografico(), domicilioCarreteraCamino = new DomicilioCarreteraCamino(), Puntajes = new PuntajeSalida() };
                viviendaReporte.numeroAtributos = (await this._medicionesDataMapper.ObtenerAtributosXCuvsAsync(1, 7, item.cuv)).Item1;
                viviendaReporte.numeroEcotecnologias = (await this._medicionesDataMapper.ObtenerEcotecnologiasXCuvsAsync(1, 7, item.cuv)).Item1;
                viviendaReporte.habitabilidad = item.habitabilidad;
                viviendaReporte.porcentajeDeAvance = item.porcentajeDeAvance;

                puntajes = await this._agenteServicioOfertaASIS.LlamarOperacion<PuntajesASIS, PuntajeSalida>
                     (new PuntajesASIS() { user = usuario, password = contrasenia, cuv = item.cuv }, ConfigurationManager.AppSettings["UrlObtenerPuntajesCUV"]);

                viviendaReporte.Puntajes = puntajes;

                viviendaReporte.cuv = String.Concat(viviendaReporte.cuv, " ");

                viviendasReporte.Add(viviendaReporte);
            }


            return viviendasReporte;
        }

        /// <summary>
        /// Obtiene los datos del sembrado, y su estatus ASIS de las viviendas por OV
        /// </summary>
        /// <param name="idOrdenVerificacion"></param>
        /// <returns></returns>
        public async Task<List<Vivienda>> ObtenerViviendasPorOrdenesVerificacionAsync(string idOrdenVerificacion)
        {
            var viviendasConsulta = new List<Vivienda>();
            var viviendaConsulta = new Vivienda();
            ValidarCUVASIS validarCUV = new ValidarCUVASIS();

            var usuario = ConfigurationManager.AppSettings["UserASIS"];
            var contrasenia = ConfigurationManager.AppSettings["PasswordASIS"];
            var viviendas = await this._ordenVerificacionDataMapper.ObtenerViviendasPorOrdenesVerificacionAsync(null, idOrdenVerificacion);

            foreach (var item in viviendas)
            {
                viviendaConsulta = (await this._proyectosDataMapper.ObtenerViviendasPorCUVAsync(item.cuv)).FirstOrDefault() ?? new Vivienda() { domicilioGeografico = new DomicilioGeografico(), domicilioCarreteraCamino = new DomicilioCarreteraCamino() };
                
                viviendasConsulta.Add(viviendaConsulta);
            }

            return viviendasConsulta;
        }

        #region Metodos sobreescritos

        protected override void DisposeManagedResources()
        {
            this._ordenVerificacionDataMapper.Dispose();
            this._medicionesDataMapper.Dispose();
            this._ofertaDataMapper.Dispose();
            this._proyectosDataMapper.Dispose();
        }

        #endregion Metodos sobreescritos


    }
}
