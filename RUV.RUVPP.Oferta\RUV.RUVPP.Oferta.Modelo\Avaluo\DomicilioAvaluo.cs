﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.Avaluo
{
   public  class DomicilioAvaluo
    {
        public string fechaAsignacion  {get; set; }
        public int idBitacoraAvaluo { get; set; }
        public  string latitud { get; set; }
        public string longitud { get; set; }
        public string cp { get; set; }
        public string nombreasentamiento { get; set; }
        public string calle { get; set; }
        public string numeroexterior { get; set; }
        public string cve_ent { get; set; }
        public string nombreEntidad { get; set; }
        public string cve_mun { get; set; }
        public string nombreMunicipio { get; set; }
        public string cve_loc { get; set; }
        public string localidad { get; set; }
        public string primeraVialidad { get; set; }
        public string segundaVialidad { get; set; }
        public string vialidadPosterior { get; set; }
        public string numextalf { get; set; }
        public string numintnum { get; set; }
        public string numintalf { get; set; }

    }
}
