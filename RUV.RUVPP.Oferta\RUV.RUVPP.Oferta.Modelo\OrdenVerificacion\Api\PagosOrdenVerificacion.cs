﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.OrdenVerificacion.Api
{
    public class PagosOrdenVerificacion
    {
        public int? noPago { get; set; }
        public DateTime? fechaEmision { get; set; }
        public string estatusPago { get; set; }
        public decimal? monto { get; set; }
        public DateTime? fechaSolicitudPago { get; set; }
        public DateTime? fechaPago { get; set; }
        public string tipoPago { get; set; }
        public string urlFactura { get; set; }
        public int? idDocumento { get; set; }
    }
}
