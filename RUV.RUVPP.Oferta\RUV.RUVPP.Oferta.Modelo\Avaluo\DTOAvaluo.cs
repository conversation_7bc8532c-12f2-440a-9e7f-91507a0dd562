﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.Avaluo
{
  public  class DTOAvaluo
    {
        
        public string idAvaluo { get; set; }
        public string cuv { get; set; }
        public string cuvB { get; set; }
        public string fechaasignacion { get; set; }
        public string latitud { get; set; }
        public string longitud { get; set; }
        public string cp { get; set; }
        public string nombreasentamiento { get; set; }
        public string calle { get; set; }
        public string numeroexterior { get; set; }
        public int? cve_ent { get; set; }
        public string entidad { get; set; }
        public int? cve_mun { get; set; }
        public string municipio { get; set; }
        public int? cve_loc { get; set; }
        public string localidad { get; set; }
        public int? idDomicilioINEGI { get; set; }
        public string primera_vialidad { get; set; }
        public string segunda_vialidad { get; set; }
        public string vialidad_posterior { get; set; }
        public string numextalf { get; set; }
        public int numintnum { get; set; }
        public string numintalf { get; set; }
        public int idVivienda { get; set; }
        public int idViviendaMAI { get; set; }
        public int idBitacoraAvaluo { get; set; }

        public string ordenVerificacion { get; set; }

        public string colonia { get; set; }

        public string numeroRecamaras { get; set; }

        public string numeroBanios { get; set; }

        public string superficieTotalHabitable { get; set; }

        public string superficeTotalConstruida { get; set; }

        public DomicilioAvaluo domicilioAvaluo { get; set; }

        public List<DocumentoFotoAvaluo> listaDocumentoFotoAvaluo { get; set; }

        public bool estaSeleccionado { get; set; }

    }
}
