﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.Oferta.Api
{
    public class FichaPagoDTO
    {
        public int? idFichaPago { get; set; }
        public byte? idEstatusFichaPago { get; set; }
        public short? idTarifa { get; set; }
        public string referencia { get; set; }
        public DateTime? fechaEmision { get; set; }
        public DateTime? fechaExpiracion { get; set; }
        public decimal? importe { get; set; }
        public string conceptoPago { get; set; }
        public string numeroDocumento { get; set; }
        public string numeroProvisionalDocumento { get; set; }
        public DateTime? fechaPago { get; set; }
        public DateTime? fechaRealDePago { get; set; }
        public int? cantidad { get; set; }
        public bool? activo { get; set; }
        public decimal? iva { get; set; }
        public string clave { get; set; }
        public int? idDocumento { get; set; }
        public string descripcion { get; set; }
        public string rutaArchivo { get; set; }
        public int? idOrdenServicio { get; set; }
        public decimal? importeTotal { get; set; }
        public int? idEmpresa { get; set; }
        public int? idOfertaVivienda { get; set; }
        public int? envioSAPPrepago { get; set; }
        public int? idDocumentoERP { get; set; }
    }
}
