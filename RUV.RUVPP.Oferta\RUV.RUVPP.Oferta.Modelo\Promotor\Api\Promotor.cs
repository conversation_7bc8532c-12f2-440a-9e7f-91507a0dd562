﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.Promotor.Api
{
    public class Promotor
    {
        public int? idPromotor { get; set; }
        public string nombre { get; set; }
        public string apellidoPaterno { get; set; }
        public string apellidoMaterno { get; set; }
        public string rfc { get; set; }
        public string curp { get; set; }
        public string telefono { get; set; }
        public string correoElectronico { get; set; }
        public string numeroPromotor { get; set; }
        public int? idDocumentoIne { get; set; }
        public int? idCertificadoPromotor { get; set; }
        public int? idCedulaFiscal { get; set; }
        public bool? activo { get; set; }
        public bool? emigrado { get; set; }
        public decimal? idOferente { get; set; }
        public DateTime? fechaRegistro { get; set; }
        public DateTime? fechaActualizacion { get; set; }
        public string numCertificado { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class PromotorDTO : Promotor
    {
        public List<string> idEntidades { get; set; }
    }
}
