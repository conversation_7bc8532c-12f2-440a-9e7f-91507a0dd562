﻿
using Microsoft.ApplicationInsights;
using System;
using System.Collections.Generic;
using System.Linq;

namespace RUV.RUVPP.Oferta.CargaSDF
{
    internal class Program
    {
        /// <summary>
        /// Main de la aplicación
        /// </summary>
        /// <param name="args">Argumentos para que se ejecute la aplicación</param>
        /// <returns>0: ejecutado exitoso, -1: error en ejecución</returns>
        /// <remarks>
        /// [0] : ruta del archivo .sdf
        /// [1] : ruta del archivo .xml para validar .sdf
        /// [2] : idUsuario
        /// [3] : idProyecto
        /// [4] : esquema de archivo .sdf
        /// [5] : usuario de bd oracle
        /// [6] : password de usuario oracle
        /// [7] : url del servicio de oracle
        /// [8] : esquema de bd oracle
        /// [9] : id de Application Insights
        /// [10]: id de operación App Insights
        /// [11]: nombre de operación App Insights
        /// </remarks>
        private static int Main(string[] args)
        {
            ConexionOracle conexionOracle = new ConexionOracle();

            string rutaArchivo = args[0];
            string rutaEsquema = args[1];
            string idProyectoTemporal = args[3];
            conexionOracle.esquemaSDF = args[4];
            conexionOracle.usuarioOracle = args[5];
            conexionOracle.passwordOracle = args[6];
            conexionOracle.urlServicioOracle = args[7];
            conexionOracle.esquemaOracle = args[8];
            TelemetryClient clienteTelemetria = new TelemetryClient();
            clienteTelemetria.InstrumentationKey = args[9];
            clienteTelemetria.Context.Operation.Id = args[10];
            clienteTelemetria.Context.Operation.Name = args[11];
            SDFMapper sdfMapper = new SDFMapper(clienteTelemetria);            

            string resultadoCadena = string.Empty;
            int resultado = -1;
            try
            {
                int idUsuario = 0;

                if (int.TryParse(args[2], out idUsuario) && !string.IsNullOrEmpty(idProyectoTemporal))
                {
                    resultadoCadena = sdfMapper.ValidateFile(rutaArchivo, rutaEsquema);
                    if (string.IsNullOrEmpty(resultadoCadena))
                    {
                        sdfMapper.ApplyOnlyDataNuevo_Oracle(conexionOracle, rutaArchivo, idUsuario, idProyectoTemporal);
                        resultado = 0;
                    }
                    else
                    {
                        resultado = -2;
                    }
                }
                else
                {
                    resultado = -1;
                }
            }            
            catch (Exception ex)
            {
                var mensajesExcepciontes = ex.Message.Split('\n');
                var codigosErroresOracle = new List<string>();
                foreach (var excepcion in mensajesExcepciontes)
                {
                    codigosErroresOracle.Add(excepcion.Split(':')[0]);
                }

                var errorArco = codigosErroresOracle.Any(s => s == "ORA-13035");

                if (errorArco)
                {
                    Console.WriteLine("Arcos en el plano");
                    resultado = -3;
                }
                else
                {
                    Console.WriteLine(ex);

                    clienteTelemetria.TrackException(ex);
                    resultado = -4;
                }
            }
            finally
            {
                Console.WriteLine(resultadoCadena);
                clienteTelemetria.Flush();
                
            }
            return resultado;
        }
    }
}