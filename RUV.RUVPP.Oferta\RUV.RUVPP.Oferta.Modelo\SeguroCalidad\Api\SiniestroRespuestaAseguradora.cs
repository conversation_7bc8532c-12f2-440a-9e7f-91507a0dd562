﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api
{
    public class SiniestroRespuestaAseguradora
    {
        public int? idSiniestroRespuestaAseguradora { get; set; }
        public string respuesta { get; set; }
        public string fechaRespuestaAseguradora { get; set; }
        public string ajustador { get; set; }
        public string estimacion { get; set; }
        public string ajusteReserva { get; set; }
        public decimal? montoPagoIndemnizacion { get; set; }
        public string fechaPagoIndemnizacion { get; set; }
        public decimal? montoPagoHonorarios { get; set; }
        public string fechaPagoHonorarios { get; set; }
        public decimal? montoPagoGastos { get; set; }
        public string fechaPagoGastos { get; set; }
        public decimal? montoPagoTotal { get; set; }
        public string fechaPagoTotal { get; set; }
        public decimal? reservaFinal { get; set; }
        public string estatusReporte { get; set; }
        public string TipoMitigacion { get; set; }
        public int? idSiniestro { get; set; }
        public string fechaRegistro { get; set; }
        public string fechaActualizacion { get; set; }
        public bool? activo { get; set; }

    }
}
