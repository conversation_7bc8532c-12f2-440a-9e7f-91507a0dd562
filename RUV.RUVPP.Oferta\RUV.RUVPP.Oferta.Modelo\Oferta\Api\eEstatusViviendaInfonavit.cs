﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.Oferta.Api
{
    [DataContract]
    public enum eEstatusViviendaInfonavit
    {
        [Description("Estatus de vivienda en solicitud de credito")]
        InscripcionCredito = 1,
        [Description("Cancelacion de credito que se interpreta como disponible")]
        CancelacionCredito = 2,
        [Description("Estatus de vivienda individualizada")]
        EjercicioCredito = 3,
    }
}
