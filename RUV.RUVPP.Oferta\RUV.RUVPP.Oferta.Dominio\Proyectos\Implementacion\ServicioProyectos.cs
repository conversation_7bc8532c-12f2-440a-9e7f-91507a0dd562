﻿using Microsoft.ApplicationInsights;
using Microsoft.WindowsAzure.Storage.Queue;
using Newtonsoft.Json;
using Oracle.ManagedDataAccess.Client;
using RUV.Comun.Negocio;
using RUV.Comun.Seguridad.JWT;
using RUV.Comun.Servicios.Storage.Blob;
using RUV.Comun.Servicios.Storage.Blob.Data;
using RUV.Comun.Servicios.Storage.Queue;
using RUV.Comun.Utilerias;
using RUV.RUVPP.Entidades.Comun.Enums;
using RUV.RUVPP.Entidades.Empresa;
using RUV.RUVPP.Entidades.General.Notificaciones;
using RUV.RUVPP.Entidades.General.Notificaciones.Enum;
using RUV.RUVPP.Negocio.Empresa.Comun;
using RUV.RUVPP.Negocio.Empresa.EmpresaConsulta;
using RUV.RUVPP.Negocio.General.Notificaciones.Interfaces;
using RUV.RUVPP.Negocio.General.OrdenTrabajo.Comun;
using RUV.RUVPP.Negocio.General.OrdenTrabajo.Servicios;
using RUV.RUVPP.Oferta.Comun.Modelo;
using RUV.RUVPP.Oferta.Datos.ConvivenciaAsIs;
using RUV.RUVPP.Oferta.Datos.Mediciones;
using RUV.RUVPP.Oferta.Datos.Oferta;
using RUV.RUVPP.Oferta.Datos.OrdenVerificacion;
using RUV.RUVPP.Oferta.Datos.Plano;
using RUV.RUVPP.Oferta.Datos.Proyectos;
using RUV.RUVPP.Oferta.Dominio.Dictaminacion;
using RUV.RUVPP.Oferta.Dominio.Excepciones;
using RUV.RUVPP.Oferta.Dominio.Historico;
using RUV.RUVPP.Oferta.Dominio.OrdenVerificacion;
using RUV.RUVPP.Oferta.Dominio.Viviendas;
using RUV.RUVPP.Oferta.GeneralesInterop.Documentos;
using RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Dictaminacion;
using RUV.RUVPP.Oferta.Modelo.AgenteServicio;
using RUV.RUVPP.Oferta.Modelo.Dictaminacion;
using RUV.RUVPP.Oferta.Modelo.Historico.Data;
using RUV.RUVPP.Oferta.Modelo.Mediciones;
using RUV.RUVPP.Oferta.Modelo.Plano;
using RUV.RUVPP.Oferta.Modelo.Proyectos;
using RUV.RUVPP.Oferta.Modelo.Proyectos.Api;
using RUV.RUVPP.Oferta.Modelo.Proyectos.Data;
using RUV.RUVPP.Oferta.Reportes;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Transactions;
using System.Web;
using ProyectoConfiguracion = RUV.RUVPP.Oferta.GeneralesInterop.Modelo.ConfiguracionSeccion;

//using RUV.RUVPP.Negocio.Empresa.EmpresaConsulta;

namespace RUV.RUVPP.Oferta.Dominio.Proyectos.Implementacion
{
    public class ServicioProyectos : ServicioDominioBase, IServicioProyectos
    {
        #region Constantes

        private const string AsuntoCorreoRegistroFinalizado = "Registro de proyecto \"{0}\"";
        private const string AsuntoCorreoProyectoAceptado = "Proyecto \"{0}\" aceptado";
        private const string AsuntoCorreoProyectoRechazado = "Rechazo de ubicación de proyecto \"{0}\"";
        private const string AsuntoCorreoGeneracionCuvs = "Aceptación de Proyecto";
        private const string FormatoNombre = "{0} {1} {2}";

        private const string FormatoFecha = "dd/MM/yyyy";
        private const string nombreQueueEnvioProyecto = "oferta-envio-proyecto";
        private const string nombreQueueValidacionProyecto = "oferta-validacion-proyecto";
        private const string nombreQueueDictaminacionProyecto = "oferta-dictaminacion-proyecto";
        private const string mensajeValidacionMismaTipologia = "No se puede realizar el cambio de prototipo para la vivienda con id: {0}";

        #endregion Constantes

        #region Campos

        private readonly IProyectosDataMapper _proyectoDM;
        private readonly IOfertaDataMapper _ofertaDataMapper;
        private readonly IServicioProyectosCatalogos _servicioProyectosCatalogos;
        private readonly Empresa.IServicioEmpresa _servicioEmpresa;
        private readonly IServicioOrdenTrabajo _servicioOrdenTrabajoAlta;
        private readonly IServicioOrdenTrabajo _servicioOrdenTrabajoActualizacion;
        private readonly INotificacion<MensajeDto> _servicioNotificaciones;
        private readonly IServicioEmpresaConsulta _servicioEmpresaConsulta;
        private readonly IServicioEmpresaComun _servicioEmpresaComun;
        private readonly IServicioDictaminacion _servicioDictaminacion;
        private readonly IServicioOrdenVerficacion _servicioOrdenVerificacion;
        private readonly IServicioHistoricos _servicioHistorico;
        private readonly IServicioVivienda _servicioVivienda;
        private readonly IAgenteServicioConvivenciaASIS _agenteServicioOfertaASIS;
        private readonly IPlanoDataMapper _planoDataMapper;
        private readonly IBlobStorageContext _blobStorage;
        private readonly IQueueStorageContext _queueStorage;
        private readonly IServicioReportes _servicioReportes;
        private readonly IMedicionesDataMapper _medicionesDataMapper;
        private readonly IOrdenVerificacionDataMapper _ordenVerificacionDataMapper;
        private int activarSig = Convert.ToInt32(ConfigurationManager.AppSettings["ActivarIntegracionSIG"]);
        private int activarConvivencia = Convert.ToInt32(ConfigurationManager.AppSettings["ActivarConvivenciaAsIs"]);

        private readonly TransactionOptions _opcionesTransaccion;

        #endregion Campos

        #region Constructor

        public ServicioProyectos(IOrdenVerificacionDataMapper _ordenVerificacionDataMapper,
                                IProyectosDataMapper proyectoDM,
                                Empresa.IServicioEmpresa servicioEmpresa,
                                IServicioProyectosCatalogos servicioProyectosCatalogos, IServicioOrdenTrabajo servicioOrdenTrabajoAlta,
                                IServicioOrdenTrabajo servicioOrdenTrabajoActualizacion, INotificacion<MensajeDto> servicioNotificaciones,
                                IServicioEmpresaConsulta servicioEmpresaConsulta, IServicioEmpresaComun servicioEmpresaComun,
                                IServicioDictaminacion servicioDictaminacion,
                                IServicioHistoricos servicioHistorico,
                                IServicioVivienda servicioVivienda,
                                IOfertaDataMapper _ofertaDataMapper,
                                IServicioOrdenVerficacion servicioOrdenVerificacion,
                                IAgenteServicioConvivenciaASIS agenteServicioOfertaASIS,
                                IPlanoDataMapper _planoDataMapper,
                                IBlobStorageContext _blobStorage,
                                IQueueStorageContext _queuStorage,
                                IServicioReportes servicioReportes,
                                IMedicionesDataMapper medicionesDataMapper)
           : base()
        {
            this._clienteTelemetria = new TelemetryClient();
            this._proyectoDM = proyectoDM;
            this._servicioProyectosCatalogos = servicioProyectosCatalogos;
            this._servicioEmpresa = servicioEmpresa;
            this._servicioOrdenTrabajoAlta = servicioOrdenTrabajoAlta;
            this._servicioOrdenTrabajoActualizacion = servicioOrdenTrabajoActualizacion;
            this._servicioNotificaciones = servicioNotificaciones;
            this._servicioEmpresaConsulta = servicioEmpresaConsulta;
            this._servicioEmpresaComun = servicioEmpresaComun;
            this._servicioDictaminacion = servicioDictaminacion;
            this._servicioHistorico = servicioHistorico;
            this._servicioVivienda = servicioVivienda;
            this._ofertaDataMapper = _ofertaDataMapper;
            this._servicioOrdenVerificacion = servicioOrdenVerificacion;
            this._agenteServicioOfertaASIS = agenteServicioOfertaASIS;
            this._planoDataMapper = _planoDataMapper;
            this._blobStorage = _blobStorage;
            this._queueStorage = _queuStorage;
            this._servicioReportes = servicioReportes;
            this._medicionesDataMapper = medicionesDataMapper;
            this._ordenVerificacionDataMapper = _ordenVerificacionDataMapper;

            var timeoutTransaccion = ConfigurationManager.AppSettings["RUV.RUVPP.Transaccion.Timeout"];
            this._opcionesTransaccion = new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted, Timeout = TimeSpan.Parse(timeoutTransaccion) };
        }

        #endregion Constructor

        #region Metodos

        #region implementacion IServicioProyecto

        public async Task<int> ExisteProyectoPorNombreAsync(string nombre, int idEmpresa, int? idProyecto)
        {
            return await this._proyectoDM.ExisteProyectoPorNombreAsync(nombre, idEmpresa, idProyecto);
        }

        public async Task<bool> EliminarDocumentoProyecto(int idEmpresa, int idDocumento)
        {
            return await this._proyectoDM.EliminarDocumentoxProyectoAsync(idEmpresa, idDocumento);
        }

        public async Task<bool> EliminarProyectoAsync(int idProyecto, CustomUserRuv usuario)
        {
            var result = false;

            using (var transaccion = new TransactionScope(TransactionScopeOption.RequiresNew, this._opcionesTransaccion, TransactionScopeAsyncFlowOption.Enabled))
            {
                var proyecto = await this._proyectoDM.ObtenerProyectoAsync(idProyecto);
                var sembrado = await this._proyectoDM.ObtenerSembradoxProyecto(idProyecto);
                if (proyecto.idEstatusProyecto == (int)EstatusProyecto.Registro)
                {
                    using (var transaccionOracle = new TransactionScope(TransactionScopeOption.RequiresNew, this._opcionesTransaccion, TransactionScopeAsyncFlowOption.Enabled))
                    {
                        await this._planoDataMapper.EliminarProyecto(idProyecto);
                        transaccionOracle.Complete();
                    }
                    result = await this._proyectoDM.EliminarProyectoAsync(idProyecto);
                }
                else //if (proyecto.idEstatusProyecto == (int)EstatusProyecto.Rechazada || proyecto.idEstatusProyecto == (int)EstatusProyecto.Validación)
                {
                    using (var transaccionOracle = new TransactionScope(TransactionScopeOption.RequiresNew, this._opcionesTransaccion, TransactionScopeAsyncFlowOption.Enabled))
                    {
                        if (proyecto.idEstatusProyecto != (int)EstatusProyecto.Validación && proyecto.idEstatusProyecto != (int)EstatusProyecto.Rechazada)
                        {
                            sembrado.viviendas.ForEach((Action<Vivienda>)(async s =>
                            {
                                if (!string.IsNullOrEmpty(s.cuvGeografica))
                                {
                                    await this._planoDataMapper.CancelarCUV(s.cuvGeografica);
                                }
                            }));
                        }
                        await this._planoDataMapper.EliminarProyecto(idProyecto);
                        transaccionOracle.Complete();
                    }
                    result = await this._proyectoDM.EliminarProyectoLogicoAsync(idProyecto);
                }

                if (result)
                {
                    if (proyecto.idEstatusProyecto != (int)EstatusProyecto.Validación && proyecto.idEstatusProyecto != (int)EstatusProyecto.Registro && proyecto.idEstatusProyecto != (int)EstatusProyecto.Rechazada)
                    {
                        sembrado.viviendas.ForEach(async vivienda =>
                        {
                            if (!string.IsNullOrEmpty(vivienda.cuv))
                            {
                                CancelacionCuvDTO objetoCancelar = new CancelacionCuvDTO(vivienda.cuv, usuario.IdUsuario.ToString());

                                //Actualización de estatus en ASIS.
                                RespuestaPrototipoASIS respuesta = await this._agenteServicioOfertaASIS.LlamarOperacion<CancelacionCuvDTO, RespuestaPrototipoASIS>(objetoCancelar, ConfigurationManager.AppSettings["UrlCancelarViviendas"]);
                            }
                        });
                    }
                    using (var transaccionHistoricos = new TransactionScope(TransactionScopeOption.RequiresNew, this._opcionesTransaccion, TransactionScopeAsyncFlowOption.Enabled))
                    {
                        //Guardado histórico Proyecto "Eliminación Proyecto"
                        await GuardarHistoricoProyectoAsync(EventoProyecto.EliminacionDeProyecto, idProyecto, "Eliminación de Proyecto", usuario);
                        transaccionHistoricos.Complete();
                    }
                }
                transaccion.Complete();
            }
            return result;
        }

        public async Task<bool> EliminarProyectoParcialAsync(int idProyecto)
        {
            var proyecto = await this.ObtenerProyectoAsync(idProyecto);

            if (proyecto.idEstatusProyecto == (int)EstatusProyecto.Actualización)
            {
                await this._proyectoDM.ActualizarProyectoAsync(proyecto.idProyecto, proyecto.nombre, string.Empty, (int)EstatusProyecto.Aceptada);
            }

            return await this.EliminarTemporalProyectoAsync(idProyecto);
        }

        public async Task<bool> EliminarTemporalProyectoAsync(int idProyecto)
        {
            await this._planoDataMapper.EliminarProyectoTemporal(idProyecto);

            return await this._proyectoDM.EliminarProyectoParcialAsync(idProyecto);
        }

        public async Task<int> GuardarProyectoAsync(int idEmpresa, string claveEmpresa, string nombre, string proyecto, CustomUserRuv usuario)
        {
            int idProyecto = 0;
            int contadorIntentos = 1;

            using (var transaccion = new TransactionScope(TransactionScopeOption.RequiresNew, this._opcionesTransaccion, TransactionScopeAsyncFlowOption.Enabled))
            {
                idProyecto = await this._proyectoDM.GuardarProyectoAsync(idEmpresa, nombre, proyecto);

                using (var transaccionHistoricos = new TransactionScope(TransactionScopeOption.RequiresNew, this._opcionesTransaccion, TransactionScopeAsyncFlowOption.Enabled))
                {
                    //Guardado de histórico Proyecto "Registro de Proyecto / Generación de Id"
                    await GuardarHistoricoProyectoAsync(EventoProyecto.RegistroDeProyecto, idProyecto, "RegistrodeProyecto", usuario);

                    transaccionHistoricos.Complete();
                }

                using (var transaccionOracle = new TransactionScope(TransactionScopeOption.RequiresNew, this._opcionesTransaccion, TransactionScopeAsyncFlowOption.Enabled))
                {
                    try
                    {
                        bool esCreadoOracle = await this._planoDataMapper.GuardarProyectoTemporal(idProyecto, idEmpresa, claveEmpresa, nombre, "Registro", "Se crea Proyecto");

                        //throw new Exception("ORA-12571");
                    }
                    catch (Exception e)
                    {
                        if (e.Message.Contains("ORA-12571"))
                        {
                            if (contadorIntentos > 1)
                            {
                                throw new ExcepcionSig("Error en la conexión al actualizar sembrado, favor de volver a intentar.", e);
                            }
                            else
                            {
                                contadorIntentos++;
                                bool esCreadoOracle = await this._planoDataMapper.GuardarProyectoTemporal(idProyecto, idEmpresa, claveEmpresa, nombre, "Registro", "Se crea Proyecto");
                                //throw new Exception("ORA-12571");
                            }
                        }
                        else
                        {
                            throw new ExcepcionSig("Error en la conexión al actualizar sembrado, favor de volver a intentar.", e);
                        }
                    }

                    
                    transaccionOracle.Complete();
                }

                transaccion.Complete();
                usuario.IdUsuario = idEmpresa;
                usuario.NombreUsuario = claveEmpresa;
            }

            return idProyecto;
        }

        public async Task<RespuestaCargaPlano> GuardarProyectoDetalleAsync(Proyecto proyecto, CustomUserRuv usuario, bool esGuardadoAsincrono = false)
        {
            RespuestaCargaPlano resultado = new RespuestaCargaPlano();

            bool proyectoValido = false;
            bool superaMaximoViviendas = false;

            var existeProyectoDetalle = await this.ObtenerProyectoDetalleAsync(proyecto.idProyecto) != null;

            superaMaximoViviendas = proyecto.sembrado.viviendas.Count() > Convert.ToInt32(ConfigurationManager.AppSettings["NumeroMaximoViviendas"]);

            using (var transaccion = new TransactionScope(TransactionScopeOption.RequiresNew, this._opcionesTransaccion, TransactionScopeAsyncFlowOption.Enabled))
            {
                await this.ValidarMismaTipologiaViviendaAsync(proyecto.sembrado.viviendas);
                this.ValidarMismoDomicilioViviendas(proyecto.sembrado.viviendas);

                if (superaMaximoViviendas && !esGuardadoAsincrono)
                {
                    this._clienteTelemetria.TrackTrace("Actualizando temporalJSON del proyecto");
                    await this._proyectoDM.ActualizarProyectoTemporalAsync(proyecto.idProyecto, JsonConvert.SerializeObject(proyecto), (int)EstatusProyecto.Enviando);

                    await ActualizarEstatusProyectoAsync(proyecto.idProyecto, (int)EstatusProyecto.Enviando);
                    //Se encola mensaje para validacion asincrona
                    MensajeValidacionProyecto mensajeValidacionProyecto = new MensajeValidacionProyecto()
                    {
                        IdProyecto = proyecto.idProyecto,
                        IdUsuario = usuario.IdUsuario.Value,
                        IdEmpresa = usuario.IdEmpresa,
                        NombreUsuario = usuario.NombreUsuario,
                        TokenGuid = usuario.TokenGuid,
                        IdEstatusPrevio = proyecto.idEstatusProyecto
                    };

                    var mensajeAgregar = JsonConvert.SerializeObject(mensajeValidacionProyecto);

                    this._clienteTelemetria.TrackTrace("Agregando mensaje a cola");
                    await this._queueStorage.QueueClient.GetQueueReference(nombreQueueValidacionProyecto).AddMessageAsync(new CloudQueueMessage(mensajeAgregar));
                }
                else
                {
                    var resultadoValidacion = new Tuple<bool, string>(true, "");

                    if (esGuardadoAsincrono)
                    {
                        resultadoValidacion = new Tuple<bool, string>(true, null);
                        proyectoValido = resultadoValidacion.Item1;
                        resultado.mensajeError = resultadoValidacion.Item2;
                    }
                    else
                    {
                        resultadoValidacion = await this.ValidarProyecto(proyecto, superaMaximoViviendas, existeProyectoDetalle);
                        proyectoValido = resultadoValidacion.Item1;
                        resultado.mensajeError = resultadoValidacion.Item2;
                    }

                    
                    if (!string.IsNullOrEmpty(resultado.mensajeError))
                    {
                        string nombre = DateTime.Now.ToString("yyyy-MM-ddTHH_mm_ss_ff") + ".txt";
                        MemoryStream stream = new MemoryStream();
                        StreamWriter writer = new StreamWriter(stream);
                        writer.Write(resultado.mensajeError);
                        writer.Flush();
                        stream.Position = 0;

                        var url = this._servicioNotificaciones.CargarBlob(stream, nombre, false);
                        resultado.urlArchivo = url;

                        if ((proyecto.idEstatusProyecto == (int)EstatusProyecto.Aceptada) || (proyecto.idEstatusProyecto == (int)EstatusProyecto.RechazadaPorActualización) || (proyecto.idEstatusProyecto == (int)EstatusProyecto.Actualización) || (proyecto.idEstatusProyecto == (int)EstatusProyecto.Rechazada))
                        {
                            await this.ActualizarEstatusProyectoAsync(proyecto.idProyecto, (int)EstatusProyecto.Actualización);
                        }
                        if (proyecto.idEstatusProyecto == (int)EstatusProyecto.Registro)
                        {
                            await this.ActualizarEstatusProyectoAsync(proyecto.idProyecto, (int)EstatusProyecto.Registro);
                        }
                    }
                    else
                    {
                        if (((proyecto.idEstatusProyecto == (int)EstatusProyecto.Aceptada) || (proyecto.idEstatusProyecto == (int)EstatusProyecto.RechazadaPorActualización) || (proyecto.idEstatusProyecto == (int)EstatusProyecto.Actualización)) && proyecto.fueronModificadosDatosSensibles)
                        {
                            await this.ActualizarEstatusProyectoAsync(proyecto.idProyecto, (int)EstatusProyecto.ValidaciónPorActualización);
                            this._clienteTelemetria.TrackTrace("Actualizando temporalJSON del proyecto");
                            await this._proyectoDM.ActualizarProyectoTemporalAsync(proyecto.idProyecto, JsonConvert.SerializeObject(proyecto), (int)EstatusProyecto.ValidaciónPorActualización);
                        }
                        else if(proyecto.idEstatusProyecto == (byte)EstatusProyecto.Registro || proyecto.idEstatusProyecto == (byte)EstatusProyecto.Rechazada)
                        {
                            await this.ActualizarEstatusProyectoAsync(proyecto.idProyecto, (int)EstatusProyecto.Validación);
                            this._clienteTelemetria.TrackTrace("Actualizando temporalJSON del proyecto");
                            await this._proyectoDM.ActualizarProyectoTemporalAsync(proyecto.idProyecto, JsonConvert.SerializeObject(proyecto), (int)EstatusProyecto.Validación);
                        }

                        using (var transaccionHistoricos = new TransactionScope(TransactionScopeOption.RequiresNew, this._opcionesTransaccion, TransactionScopeAsyncFlowOption.Enabled))
                        {
                            await GuardarHistoricoProyectoAsync(EventoProyecto.SolicitudAValidacion, proyecto.idProyecto, "Solicitud A Validación", usuario);

                            transaccionHistoricos.Complete();
                        }

                        //Actualizar los datos del proyecto sin enviar a VyD
                        if (!proyecto.fueronModificadosDatosSensibles && proyecto.idEstatusProyecto == (byte)EstatusProyecto.Aceptada)
                        {
                            // Obtener datos del proyecto
                            Proyecto proyectoNoModificado = await this.ObtenerProyectoAsync(proyecto.idProyecto);
                            Proyecto proyectoActualizado = proyecto;

                            _clienteTelemetria.TrackTrace("Actualizando datos del proyecto");
                            await this.GuardaDatosProyecto(proyectoActualizado, usuario, proyectoNoModificado);


                        }

                        if (proyectoValido && (proyecto.idEstatusProyecto == (int)EstatusProyecto.Registro || proyecto.idEstatusProyecto == (int)EstatusProyecto.Rechazada || proyecto.idEstatusProyecto == (int)EstatusProyecto.RechazadaPorActualización || proyecto.idEstatusProyecto == (int)EstatusProyecto.Aceptada) && proyecto.fueronModificadosDatosSensibles)
                        {
                            await this.GenerarOrdenDeTrabajo(proyecto, usuario);
                        }
                    }
                }
                transaccion.Complete();
            }
            resultado.resultadoExitoso = (superaMaximoViviendas && !esGuardadoAsincrono) ? "-1" : proyecto.idProyecto.ToString();

            return resultado;
        }

        private async Task GenerarOrdenDeTrabajo(Proyecto proyecto, CustomUserRuv usuario)
        {
            OrdenBase odtCreada;
            this._clienteTelemetria.TrackTrace("Creando ODT.");
            var estatusActualOdt = await this._proyectoDM.ObtenerEstatusUltimaOdtProyecto(proyecto.idProyecto);

            if (estatusActualOdt == 0 || estatusActualOdt == 5)
            {
                if (proyecto.idEstatusProyecto == (int)EstatusProyecto.RechazadaPorActualización || proyecto.idEstatusProyecto == (int)EstatusProyecto.Aceptada)
                {
                    odtCreada = this._servicioOrdenTrabajoActualizacion.Crear(proyecto.idProyecto, proyecto.nombre);
                }
                else
                {
                    odtCreada = this._servicioOrdenTrabajoAlta.Crear(proyecto.idProyecto, proyecto.nombre);
                }

                odtCreada.PorAsignar();

                this._clienteTelemetria.TrackTrace("Enviando notificacion al tablero.");
                await ClienteHttpRuv.Post(usuario.TokenGuid, "Tableros/AgregarOrden", odtCreada.ToOrdenServicioDto());

                this._clienteTelemetria.TrackTrace("Enviando correo de notificacion a la empresa.");
                await this.EnviarCorreoOdtGenerada(usuario.IdEmpresa.Value, proyecto, odtCreada, usuario);
            }
        }

        private async Task<string> ValidarTipologiaViviendas(int idProyecto, bool superaMaximoViviendas)
        {
            var frecuenciaPeticion = Convert.ToInt32(ConfigurationManager.AppSettings["FrecuenciaPeticion"]);
            ValidacionProyecto validacionTipologia = new ValidacionProyecto();
            string resultadoValidacionTipologia = string.Empty;
            string nombreValidacion = string.Empty;

            this._clienteTelemetria.TrackTrace("Validando tipología de las viviendas del proyecto.");
            if (superaMaximoViviendas)
            {
                await this._planoDataMapper.InsertarSolicitudValidacionProyecto(idProyecto,(int)CatalogoSPValidacion.SP_TMP_VS_TMP,(int)CatalogoEstatusValidacionSig.EnSolicitud);

                do
                {
                    Thread.Sleep(frecuenciaPeticion);
                    validacionTipologia = await this._planoDataMapper.ObtenerEstatusValidacionProyecto(idProyecto, (int)CatalogoSPValidacion.SP_TMP_VS_TMP);
                }
                while (validacionTipologia.Resultado == null);   
                if (validacionTipologia.Resultado == ((int)CatalogoSPResultadoSig.NOK).ToString())
                {
                    resultadoValidacionTipologia = await this._planoDataMapper.ObtenerResultadoValidacionProyecto(idProyecto, (int)CatalogoSPValidacion.SP_TMP_VS_TMP);
                }
            }
            else
            {
                resultadoValidacionTipologia = await this._planoDataMapper.ValidarTipologia(idProyecto);
            }

            return resultadoValidacionTipologia;
        }

        private async Task ActualizarViviendasSembradoTemporal(List<Vivienda> viviendas, int idProyecto, string folioSEPLADE, string folioAyto)
        {
            this._clienteTelemetria.TrackTrace("Actualizando sembrado en Oracle.");
            foreach (var vivienda in viviendas)
            {
                await this._planoDataMapper.ActualizarViviendasSembradoTemporal(vivienda, idProyecto, folioSEPLADE, folioAyto);
            }
        }

        private async Task EliminarViviendasTemporal(List<Vivienda> viviendasAEliminar)
        {
            this._clienteTelemetria.TrackTrace("Borrando viviendas eliminadas.");
            foreach (var item in viviendasAEliminar)
            {
                await this.EliminarViviendaTemporal(item.featID);
            }
        }

        public async Task<Tuple<bool, string>> ValidarProyecto(Proyecto proyecto, bool superaMaximoViviendas, bool existeProyectoDetalle = false)
        {
            int contadorIntentos = 1;
            bool proyectoValido = false;
            StringBuilder mensajeError = new StringBuilder();

            this._clienteTelemetria.TrackTrace("Validando proyecto.");

            // TODO: Validar datos sensibles

            if (activarSig == 1)
            {
                using (var transaccioneOracle = new TransactionScope(TransactionScopeOption.RequiresNew, this._opcionesTransaccion, TransactionScopeAsyncFlowOption.Enabled))
                {
                    try
                    {
                        await this.EliminarViviendasTemporal(proyecto.sembrado.viviendasEliminadas);
                    }
                    catch (Exception e)
                    {
                        throw new ExcepcionSig("Error en la conexión al eliminar viviendas del sembrado, favor de volver a intentar.", e);
                    }

                    try
                    {
                        await this.ActualizarViviendasSembradoTemporal(proyecto.sembrado.viviendas, proyecto.idProyecto, proyecto.folioSEPLADE, proyecto.folioAyto);

                        //throw new Exception("ORA-12571");
                    }
                    catch (Exception e)
                    {
                        if (e.Message.Contains("ORA-12571"))
                        {
                            if (contadorIntentos > 1)
                            {
                                throw new ExcepcionSig("Error en la conexión al actualizar sembrado, favor de volver a intentar.", e);
                            }
                            else
                            {
                                contadorIntentos++;
                                await this.ActualizarViviendasSembradoTemporal(proyecto.sembrado.viviendas, proyecto.idProyecto, proyecto.folioSEPLADE, proyecto.folioAyto);
                                //throw new Exception("ORA-12571");
                            }
                        }
                        else
                        {
                            throw new ExcepcionSig("Error en la conexión al actualizar sembrado, favor de volver a intentar.", e);
                        }
                    }

                    try
                    {
                        mensajeError.Append(await this.ValidarTipologiaViviendas(proyecto.idProyecto, superaMaximoViviendas));
                    }
                    catch (Exception e)
                    {
                        throw new ExcepcionSig("Error en la conexión al validar tipología, favor de volver a intentar.", e);
                    }

                    try
                    {
                        mensajeError.Append(await this.ValidacionEspacial(proyecto.idProyecto, superaMaximoViviendas));
                    }
                    catch (Exception)
                    {
                        throw new ExcepcionSig("Error en la conexión al validar espacio geográfico, favor de volver a intentar.");
                    }

                    transaccioneOracle.Complete();
                }
            }
            proyectoValido = string.IsNullOrEmpty(mensajeError.ToString()) ? true : false;
            return new Tuple<bool, string>(proyectoValido, mensajeError.ToString());
        }

        private async Task<string> ValidacionEspacial(int idProyecto, bool superaMaximoViviendas)
        {
            string detalleError = string.Empty;
            string resultadoValidacionTipologia = string.Empty;
            ValidacionProyecto validacionTipologia = new ValidacionProyecto();
            var frecuenciaPeticion = Convert.ToInt32(ConfigurationManager.AppSettings["FrecuenciaPeticion"]);
            this._clienteTelemetria.TrackTrace("Validando el espacio geográfico del proyecto.");

            if (superaMaximoViviendas)
            {
                await this._planoDataMapper.InsertarSolicitudValidacionProyecto(idProyecto, (int)CatalogoSPValidacion.SP_VALIDACION, (int)CatalogoEstatusValidacionSig.EnSolicitud);

                do
                {
                    Thread.Sleep(frecuenciaPeticion);
                    validacionTipologia = await this._planoDataMapper.ObtenerEstatusValidacionProyecto(idProyecto, (int)CatalogoSPValidacion.SP_VALIDACION);
                }
                while (validacionTipologia.Resultado == null);

                if (validacionTipologia.Resultado == ((int)CatalogoSPResultadoSig.NOK).ToString())
                {
                    resultadoValidacionTipologia = await this._planoDataMapper.ObtenerResultadoValidacionProyecto(idProyecto, (int)CatalogoSPValidacion.SP_VALIDACION);
                }
            }
            else
            {
                resultadoValidacionTipologia = await this._planoDataMapper.ValidacionEspacialProyecto(idProyecto);
            }

            if (!string.IsNullOrEmpty(resultadoValidacionTipologia))
            {
                detalleError = resultadoValidacionTipologia;
            }
            return detalleError;
        }

        public async Task<int> ActualizarProyectoAsync(int idProyecto, string nombre, string temporalJson, int? idEstatusProyecto, bool fueronModificadosDatosSensible)
        {
            int? idEstatus = null;

            if (idEstatusProyecto != null && idEstatusProyecto == (int)EstatusProyecto.Aceptada && fueronModificadosDatosSensible)
                idEstatus = (int)EstatusProyecto.Actualización;

            //await this._proyectoDM.ActualizarProyectoAsync(idProyecto, nombre, proyecto, idEstatus);
            await this._proyectoDM.ActualizarProyectoTemporalAsync(idProyecto, temporalJson, idEstatus);

            return idProyecto;
        }

        public async Task BorrarTemporalAsync(int idProyecto)
        {
            await this._proyectoDM.ActualizarProyectoTemporalAsync(idProyecto, string.Empty, null);
        }

        public async Task<string> ActualizarProyectoDetalleAsync(Proyecto proyecto, CustomUserRuv usuario, int? esAsincrono = null)
        {
            int result = 0;

            this._clienteTelemetria.TrackTrace("Obteniendo proyecto actual a SQL.");
            this._clienteTelemetria.TrackTrace("Obteniendo proyecto.");
            var proyectoActual = await ObtenerProyectoAsync(proyecto.idProyecto);

            //Actualizacion de viviendas sembrado
            var viviendasGuardadas = proyectoActual.sembrado.viviendas;
            var viviendasModificadas = proyecto.sembrado.viviendas;

            var viviendasNuevas = viviendasModificadas.Except(viviendasGuardadas, new ComparadorViviendas()).ToList();
            var viviendasActualizadas = viviendasModificadas.Intersect(viviendasGuardadas, new ComparadorViviendas()).ToList();

            var viviendasEliminadas = proyecto.sembrado.viviendasEliminadas;

            var viviendasTotales = new List<Vivienda>();
            viviendasTotales.AddRange(viviendasNuevas);
            viviendasTotales.AddRange(viviendasActualizadas);
            //viviendasTotales.AddRange(viviendasEliminadas);

            if (esAsincrono == null)
            {
                this._clienteTelemetria.TrackTrace("Validando domicio de las viviendas.");
                this.ValidarMismoDomicilioViviendas(viviendasModificadas);
            }

            if (activarSig == 1)
            {
                try
                {
                    this._clienteTelemetria.TrackTrace("Actualizando bloqueo de CUV en Oracle.");
                    await this.ValidaBloqueoCUVS(proyecto.idProyecto);
                }
                catch (Exception e)
                {
                    throw new ExcepcionSig("Error al bloquear CUV", e);
                }

                try
                {
                    this._clienteTelemetria.TrackTrace("Actualizando sembrado en Oracle.");
                    foreach (var vivienda in proyecto.sembrado.viviendas)
                    {
                        await this._planoDataMapper.ActualizarViviendasSembradoTemporal(vivienda, proyecto.idProyecto, proyecto.folioSEPLADE, proyecto.folioAyto);
                        await this._planoDataMapper.ActualizarViviendasSembradoOficial(vivienda, proyecto.idProyecto, proyecto.folioSEPLADE, proyecto.folioAyto);
                    }
                }
                catch (Exception e)
                {
                    throw new ExcepcionSig("Error al actualizar sembrado en Oracle", e);
                }

                try
                {
                    this._clienteTelemetria.TrackTrace("Validando tipología de las viviendas del proyecto.");
                    string resultadoValidacionTipologia = await this._planoDataMapper.ValidarTipologia(proyecto.idProyecto);
                    if (!string.IsNullOrEmpty(resultadoValidacionTipologia))
                    {
                        throw new ExcepcionSig(resultadoValidacionTipologia);
                    }
                }
                catch (Exception e)
                {
                    throw new ExcepcionSig("Error al validar tipologia en Oracle", e);
                }
            }

            this._clienteTelemetria.TrackTrace("Guardando detalle del proyecto en SQL.");
            using (var transaccion = new TransactionScope(TransactionScopeOption.RequiresNew, this._opcionesTransaccion, TransactionScopeAsyncFlowOption.Enabled))
            {
                proyecto.idPromotor = proyecto.idPromotor == 0 ? null : proyecto.idPromotor;
                proyecto.idVendedor = proyecto.idVendedor == 0 ? null : proyecto.idVendedor;

                await this._proyectoDM.ActualizarProyectoAsync(proyecto.idProyecto, proyecto.nombre, proyecto.temporalJSON, null);
                await this._proyectoDM.ActualizarProyectoDetalleAsync(proyecto);

                //Elimina el temporal
                await this.ActualizarProyectoAsync(proyecto.idProyecto, proyecto.nombre, proyecto.temporalJSON, null, proyecto.fueronModificadosDatosSensibles);

                if (proyecto.idPromotor == null)
                {
                    if (proyectoActual.idPromotor > 0)
                    {
                        var idCPV = await this._proyectoDM.GuardarPromotorVendedorAsync(proyecto.promotor);
                        await this._proyectoDM.GuardarPromotorVendedorxProyectoAsync(idCPV, proyecto.idProyecto);
                    }
                    else
                    {
                        proyecto.promotor.idCPVExterno = proyectoActual.promotor.idCPVExterno;
                        await this._proyectoDM.ActualizarPromotorVendedorAsync(proyecto.promotor);
                    }
                }
                if (proyecto.idPromotor > 0 && proyectoActual.idPromotor == null)
                {
                    await this._proyectoDM.EliminarPromotorExternoxProyecto(proyecto.idProyecto, proyectoActual.promotor.idCPVExterno);
                }

                if (proyecto.idVendedor == null)
                {
                    if (proyectoActual.idVendedor > 0)
                    {
                        var idCPV = await this._proyectoDM.GuardarPromotorVendedorAsync(proyecto.vendedor);
                        await this._proyectoDM.GuardarPromotorVendedorxProyectoAsync(idCPV, proyecto.idProyecto);
                    }
                    else
                    {
                        proyecto.vendedor.idCPVExterno = proyectoActual.vendedor.idCPVExterno;
                        await this._proyectoDM.ActualizarPromotorVendedorAsync(proyecto.vendedor);
                    }
                }
                if (proyecto.idVendedor > 0 && proyectoActual.idVendedor == null)
                {
                    await this._proyectoDM.EliminarPromotorExternoxProyecto(proyecto.idProyecto, proyectoActual.vendedor.idCPVExterno);
                }

                if (proyectoActual.plano.IdDocumento != proyecto.plano.IdDocumento)
                {
                    await this._proyectoDM.EliminarDocumentoxProyectoAsync(proyectoActual.plano.IdDocumento, proyecto.idProyecto);
                    await this._proyectoDM.GuardarDocumentoxProyectoAsync(proyecto.plano.IdDocumento, proyecto.idProyecto);
                }

                await this._proyectoDM.ActualizarPropietarioTerrenoAsync(proyecto.propietarioTerreno);

                await this._proyectoDM.ActualizarDROAsync(proyecto.dro);

                if (proyecto.esZonaRiesgo.Value)
                {
                    await this._proyectoDM.EliminarZonaRiesgoAsync(proyecto.zonasRiesgo.Where(z => !z.esSeleccionada && z.esActualizacion).ToArray(), proyecto.idProyecto);
                    await this._proyectoDM.GuardarZonasRiesgoAsync(proyecto.zonasRiesgo.Where(z => z.esSeleccionada && !z.esActualizacion).ToArray(), proyecto.idProyecto);
                    await this._proyectoDM.ActualizarZonasRiesgoAsync(proyecto.zonasRiesgo.Where(z => z.esSeleccionada && z.esActualizacion).ToArray(), proyecto.idProyecto);

                    if (proyectoActual.dictamenRiesgo != null && proyectoActual.dictamenRiesgo.IdDocumento != 0)
                    {
                        if (proyecto.dictamenRiesgo.IdDocumento != 0)
                        {
                            if (proyectoActual.dictamenRiesgo.IdDocumento != proyecto.dictamenRiesgo.IdDocumento)
                            {
                                await this._proyectoDM.EliminarDocumentoxProyectoAsync(proyectoActual.dictamenRiesgo.IdDocumento, proyecto.idProyecto);
                                await this._proyectoDM.GuardarDocumentoxProyectoAsync(proyecto.dictamenRiesgo.IdDocumento, proyecto.idProyecto);
                            }
                        }
                        else
                        {
                            await this._proyectoDM.EliminarDocumentoxProyectoAsync(proyectoActual.dictamenRiesgo.IdDocumento, proyecto.idProyecto);
                        }
                    }
                    else
                    {
                        if (proyecto.dictamenRiesgo.IdDocumento != 0)
                        {
                            await this._proyectoDM.GuardarDocumentoxProyectoAsync(proyecto.dictamenRiesgo.IdDocumento, proyecto.idProyecto);
                        }
                    }
                }
                else
                {
                    await this._proyectoDM.EliminarZonaRiesgoAsync(proyecto.zonasRiesgo.ToArray(), proyecto.idProyecto);
                    if (proyectoActual.dictamenRiesgo != null)
                        await this._proyectoDM.EliminarDocumentoxProyectoAsync(proyectoActual.dictamenRiesgo.IdDocumento, proyecto.idProyecto);
                }

                //Agregar nuevas viviendas
                if (viviendasNuevas != null && viviendasNuevas.Any())
                {
                    var viviendaCompleta = new Vivienda();
                    var listaViviendasCompletas = new List<Vivienda>();
                    var vialidades = await this._servicioProyectosCatalogos.ObtenerVialidadesFiltradoAsync(proyecto.sembrado.viviendas[0].domicilioGeografico.idEstado ?? "", proyecto.sembrado.viviendas[0].domicilioGeografico.idmunicipio ?? "");
                    var asentamientos = await this._servicioProyectosCatalogos.ObtenerAsentamientosFiltradoAsync(proyecto.sembrado.viviendas[0].domicilioGeografico.idEstado ?? "", proyecto.sembrado.viviendas[0].domicilioGeografico.idmunicipio ?? "");

                    this._clienteTelemetria.TrackTrace("Agregando viviendas nuevas en SQL.");
                    foreach (var viviendaNueva in viviendasNuevas)
                    {
                        viviendaNueva.domicilioGeografico.idPeriodo = 4;
                        viviendaNueva.domicilioGeografico.idTipoDomicilioRUV = 3;
                        viviendaNueva.domicilioGeografico.idTipoDomicilioINEGI = 3;
                        viviendaNueva.domicilioGeografico.xmlSIG = "";
                        viviendaNueva.idViviendaPlanoSIG = viviendaNueva.idVivienda;

                        var tmp = await this.ObtenerDatosComplementariosVivienda(vialidades, asentamientos, viviendaNueva);
                        viviendaCompleta = tmp.Item1;
                        vialidades = tmp.Item2;
                        asentamientos = tmp.Item3;

                        var idDomicilioGeografico = await this._proyectoDM.GuardarDomicilioGeograficoAsync(viviendaCompleta.domicilioGeografico);

                        viviendaCompleta.idDomicilioGeografico = idDomicilioGeografico;
                        viviendaCompleta.idEstatusVivienda = null;
                        viviendaCompleta.idProyecto = proyecto.idProyecto;

                        await this._proyectoDM.GuardarViviendaAsync(viviendaCompleta);

                        viviendaCompleta.domicilioCarreteraCamino.idDomicilioGeografico = idDomicilioGeografico;
                        viviendaCompleta.domicilioCarreteraCamino.nombreVialidad = viviendaCompleta.domicilioGeografico.nombreVialidadPrincipal;

                        if (viviendaCompleta.domicilioGeografico.tipoVialidadP == 22)
                        {
                            await this._proyectoDM.GuardarDomicilioCarretera(viviendaCompleta.domicilioCarreteraCamino);
                        }
                        else if (viviendaCompleta.domicilioGeografico.tipoVialidadP == 24)
                        {
                            await this._proyectoDM.GuardarDomicilioCamino(viviendaCompleta.domicilioCarreteraCamino);
                        }

                        listaViviendasCompletas.Add(viviendaCompleta);
                    }
                }

                this._clienteTelemetria.TrackTrace("Actualizando estado del proyectos.");
                if (!(proyecto.idEstatusProyecto == (int)EstatusProyecto.Aceptada && !proyecto.fueronModificadosDatosSensibles))
                {
                    var nuevoEstatus = proyecto.idEstatusProyecto == (int)EstatusProyecto.Rechazada ? EstatusProyecto.Validación : EstatusProyecto.ValidaciónPorActualización;
                    await ActualizarEstatusProyectoAsync(proyecto.idProyecto, (int)nuevoEstatus);
                }

                using (var transaccionOracle = new TransactionScope(TransactionScopeOption.RequiresNew, this._opcionesTransaccion, TransactionScopeAsyncFlowOption.Enabled))
                {
                    try
                    {
                        //Se hace para preveer la solicitud de viviendas temporales como oficiales. Att. Hector
                        this._clienteTelemetria.TrackTrace("Actualizando sembrado oficial en Oracle.");
                        foreach (var vivienda in viviendasActualizadas)
                        {
                            if (vivienda.esViviendaModificada == true)
                            {
                                vivienda.idProyecto = proyecto.idProyecto;
                                await this._planoDataMapper.ActualizarViviendasSembradoOficial(vivienda, proyectoActual.idProyecto, proyectoActual.folioSEPLADE, proyectoActual.folioAyto);
                            }
                        }

                        this._clienteTelemetria.TrackTrace("Eliminando viviendas en Oracle.");
                        foreach (var vivienda in viviendasEliminadas)
                        {
                            await this._planoDataMapper.EliminarViviendaTemporal(vivienda.idVivienda, proyecto.idProyecto);
                            await this._planoDataMapper.EliminarVivienda(vivienda.featID);
                        }

                        this._clienteTelemetria.TrackTrace("Actualizando sembrado temporal en Oracle.");
                        foreach (var vivienda in proyecto.sembrado.viviendas)
                        {
                            await this._planoDataMapper.ActualizarViviendasSembradoTemporal(vivienda, proyecto.idProyecto, proyecto.folioSEPLADE, proyecto.folioAyto);
                        }
                    }
                    catch (Exception e)
                    {
                        throw new ExcepcionSig("Error al actualizar sembrado en Oracle", e);
                    }

                    if (activarSig == 1)
                    {
                        string resultadoMigracion = string.Empty;

                        try
                        {
                            this._clienteTelemetria.TrackTrace("Migrando la informacion a la BD Oficial de Oracle.");
                            resultadoMigracion = await this._planoDataMapper.MigracionTemporalOficial(proyecto.idProyecto);
                            if (!string.IsNullOrEmpty(resultadoMigracion))
                            {
                                throw new ExcepcionSig(resultadoMigracion);
                            }
                        }
                        catch (Exception e)
                        {
                            throw new ExcepcionSig("Error al realizar el proceso de migración en Oracle", e);
                        }
                    }

                    using (var transaccionHistoricos = new TransactionScope(TransactionScopeOption.RequiresNew, this._opcionesTransaccion, TransactionScopeAsyncFlowOption.Enabled))
                    {
                        //Guardar histórico Proyecto "Actualización"
                        this._clienteTelemetria.TrackTrace("Guardando historico del proyecto.");
                        await GuardarHistoricoProyectoAsync(EventoProyecto.Actualizacion, proyecto.idProyecto, "Actualización", usuario);

                        if (ConfigurationManager.AppSettings["ActivarConvivenciaAsIs"] == "1")
                        {
                            foreach (var viviendaModificada in viviendasActualizadas)
                            {
                                if ((viviendaModificada.esViviendaModificada == true) && viviendaModificada.idEstatusVivienda == (int)EstatusVivienda.Disponible)
                                {
                                    this._clienteTelemetria.TrackTrace("Actualizando viviendas en el AS IS.");

                                    viviendaModificada.idProyecto = proyecto.idProyecto;
                                    //Actualizacion de CUVS ASIS
                                    var viviendaActualizacionRespuestaASIS = await this.ObtenerViviendaASIS(viviendaModificada);
                                    var jsonRespuesta = Newtonsoft.Json.JsonConvert.SerializeObject(viviendaActualizacionRespuestaASIS);
                                    var respuesta = await this._agenteServicioOfertaASIS.LlamarOperacion<ViviendaActualizacionASIS, ViviendaActualizacionRespuestaASIS>(viviendaActualizacionRespuestaASIS, ConfigurationManager.AppSettings["UrlActualizarVivienda"]);

                                    if (!respuesta.estatusTransaccion)
                                    {
                                        throw new Exception(respuesta.mensaje);
                                    }
                                }
                            }
                        }

                        transaccionHistoricos.Complete();
                    }

                    transaccionOracle.Complete();
                }

                transaccion.Complete();
            }

            result = proyecto.idProyecto;

            if ((proyecto.idEstatusProyecto == (int)EstatusProyecto.Aceptada || esAsincrono != null) && !proyecto.fueronModificadosDatosSensibles)
            {
                await ActualizarEstatusProyectoAsync(proyecto.idProyecto, esAsincrono.Value);
            }
            else if (result > 0)
            {
                this._clienteTelemetria.TrackTrace("Creando ODT.");

                OrdenBase odtCreada = null;

                if (proyecto.idEstatusProyecto == (int)EstatusProyecto.Rechazada)
                {
                    odtCreada = this._servicioOrdenTrabajoAlta.Crear(proyecto.idProyecto, proyecto.nombre);
                    //await ActualizarEstatusProyectoAsync(proyecto.idProyecto, (int)EstatusProyecto.Validación);
                }
                else
                {
                    odtCreada = this._servicioOrdenTrabajoActualizacion.Crear(proyecto.idProyecto, proyecto.nombre);
                    //await ActualizarEstatusProyectoAsync(proyecto.idProyecto, (int)EstatusProyecto.ValidaciónPorActualización);
                }

                odtCreada.PorAsignar();

                this._clienteTelemetria.TrackTrace("Enviando notificacion al tablero.");
                await ClienteHttpRuv.Post(usuario.TokenGuid, "Tableros/AgregarOrden", odtCreada.ToOrdenServicioDto());

                this._clienteTelemetria.TrackTrace("Enviando correo de notificacion a la empresa.");
                await this.EnviarCorreoOdtGenerada(usuario.IdEmpresa.Value, proyecto, odtCreada, usuario);

                using (var transaccionHistoricos = new TransactionScope(TransactionScopeOption.RequiresNew, this._opcionesTransaccion, TransactionScopeAsyncFlowOption.Enabled))
                {
                    this._clienteTelemetria.TrackTrace("Guardando históricos de las viviendas.");
                    for (var i = 0; i < viviendasNuevas.Count; i++)
                    {
                        //Guardado de histórico CUV "Actualización" Nuevas
                        EstatusViviendas estatusVivienda = new EstatusViviendas();
                        estatusVivienda.cuv = viviendasNuevas[i].cuv;
                        estatusVivienda.estatusCUV = (int)EstatusVivienda.EnProcesoVerficacion;
                        estatusVivienda.identificadorVivienda = viviendasNuevas[i].idVivienda;
                        estatusVivienda.idEvento = (int)EventoCUV.Actualizacion;
                        estatusVivienda.idUsuario = (int)usuario.IdUsuario;
                        estatusVivienda.nombreUsuario = usuario.NombreUsuario;
                        List<DetalleEstatusVivienda> listaCamposAdicionales = new List<DetalleEstatusVivienda>();
                        DetalleEstatusVivienda campoAdicional = new DetalleEstatusVivienda((int)EstatusVivienda.EnProcesoVerficacion, EnumCampoAdicional.UsuarioRealizaActualizacion, usuario.NombreUsuario);
                        listaCamposAdicionales.Add(campoAdicional);
                        await this._servicioHistorico.GuardarHistoricoVivienda(estatusVivienda, listaCamposAdicionales);
                    }

                    for (var i = 0; i < viviendasActualizadas.Count; i++)
                    {
                        //Guardado de histórico CUV "Actualización" Actualizadas
                        EstatusViviendas estatusVivienda = new EstatusViviendas();
                        estatusVivienda.cuv = viviendasActualizadas[i].cuv;
                        estatusVivienda.estatusCUV = (int)EstatusVivienda.EnProcesoVerficacion;
                        estatusVivienda.identificadorVivienda = viviendasActualizadas[i].idVivienda;
                        estatusVivienda.idEvento = (int)EventoCUV.Actualizacion;
                        estatusVivienda.idUsuario = (int)usuario.IdUsuario;
                        estatusVivienda.nombreUsuario = usuario.NombreUsuario;
                        List<DetalleEstatusVivienda> listaCamposAdicionales = new List<DetalleEstatusVivienda>();
                        DetalleEstatusVivienda campoAdicional = new DetalleEstatusVivienda((int)EstatusVivienda.EnProcesoVerficacion, EnumCampoAdicional.UsuarioRealizaActualizacion, usuario.NombreUsuario);
                        listaCamposAdicionales.Add(campoAdicional);
                        await this._servicioHistorico.GuardarHistoricoVivienda(estatusVivienda, listaCamposAdicionales);
                    }

                    for (var i = 0; i < viviendasEliminadas.Count; i++)
                    {
                        //Guardado de histórico CUV "Actualización" Eliminadas
                        EstatusViviendas estatusVivienda = new EstatusViviendas();
                        estatusVivienda.cuv = viviendasEliminadas[i].cuv;
                        estatusVivienda.estatusCUV = (int)EstatusVivienda.EnProcesoVerficacion;
                        estatusVivienda.identificadorVivienda = viviendasEliminadas[i].idVivienda;
                        estatusVivienda.idEvento = (int)EventoCUV.Actualizacion;
                        estatusVivienda.idUsuario = (int)usuario.IdUsuario;
                        estatusVivienda.nombreUsuario = usuario.NombreUsuario;
                        List<DetalleEstatusVivienda> listaCamposAdicionales = new List<DetalleEstatusVivienda>();
                        DetalleEstatusVivienda campoAdicional = new DetalleEstatusVivienda((int)EstatusVivienda.EnProcesoVerficacion, EnumCampoAdicional.UsuarioRealizaActualizacion, usuario.NombreUsuario);
                        listaCamposAdicionales.Add(campoAdicional);
                        await this._servicioHistorico.GuardarHistoricoVivienda(estatusVivienda, listaCamposAdicionales);
                    }

                    transaccionHistoricos.Complete();
                }
            }

            return result.ToString();
        }

        /// <summary>
        /// Actualiza el estatus de un proyecto y si no logra actualizar el estatus realiza el trace en appInsights y manda una excepción.
        /// </summary>
        /// <param name="idProyecto">Identificador del proyecto</param>
        /// <param name="idEstatus">Identificador del estatus</param>
        /// <returns></returns>
        public async Task<bool> ActualizarEstatusProyectoAsync(int idProyecto, int idEstatus, bool actualizarFechaAceptacion = false)
        {
            bool cambioEstatus = await this._proyectoDM.ActualizarEstatusProyectoAsync(idProyecto, idEstatus, actualizarFechaAceptacion);

            this._clienteTelemetria.TrackTrace("Actualizando estado del proyectos.");

            if (!cambioEstatus)
                throw new Exception("No se pudo cambiar el estatus del proyecto");

            return cambioEstatus;
        }

        public async Task<ResultadoPaginado<List<Proyecto>>> ObtenerProyectosAsync(int idEmpresa, int tamanioPagina, int pagina, int? idProyecto, string nombreProyecto, string idEntidadFederativa, string idMunicipio, string idLocalidad)
        {
            ResultadoPaginado<List<Proyecto>> resultado = new ResultadoPaginado<List<Proyecto>>()
            {
                TamanioPagina = tamanioPagina,
                PaginaActual = pagina
            };

            if (idEntidadFederativa != null)
                idEntidadFederativa = idEntidadFederativa.PadLeft(2, '0');

            var data = await this._proyectoDM.ObtenerProyectosAsync(idEmpresa, tamanioPagina, pagina, idProyecto, nombreProyecto, idEntidadFederativa, idMunicipio, idLocalidad);

            if (data.Item1 > 0)
            {
                resultado.TotalRegistros = data.Item1;
                resultado.TotalPaginas = (int)Math.Ceiling((double)(resultado.TotalRegistros / tamanioPagina));
            }

            foreach (var item in data.Item2)
            {
                item.fechaRegistroUTC = item.fechaRegistro.Value.ToString(FormatoFecha);
                if (item.fechaActualizacion != null)
                    item.fechaActualizacionUTC = item.fechaActualizacion.Value.ToString(FormatoFecha);
            }
            resultado.Resultado = data.Item2;

            return resultado;
        }

        public async Task<Proyecto> ObtenerProyectoAsync(int idProyecto, bool? esValidacionASIS = null)
        {
            List<int> idsTMP;

            Proyecto proyecto = await this._proyectoDM.ObtenerProyectoAsync(idProyecto);
            Proyecto proyectoDetalle = await this._proyectoDM.ObtenerProyectoDetalleAsync(idProyecto);
            Proyecto temporal = new Proyecto();

            if (proyectoDetalle == null)
            {
                proyectoDetalle = new Proyecto();
            }
            else
            {
                proyectoDetalle.tieneDetalle = true;
            }

            proyectoDetalle.idProyecto = proyecto.idProyecto;
            proyectoDetalle.idEmpresa = proyecto.idEmpresa;
            proyectoDetalle.idEstatusProyecto = proyecto.idEstatusProyecto;
            proyectoDetalle.estatusProyecto = proyecto.estatusProyecto;
            proyectoDetalle.nombre = proyecto.nombre;
            proyectoDetalle.fechaRegistro = proyecto.fechaRegistro;
            proyectoDetalle.temporalJSON = proyecto.temporalJSON;
            if (proyecto.temporalJSON != null)
            {
                temporal = JsonConvert.DeserializeObject<Proyecto>(proyecto.temporalJSON);
            }

            proyectoDetalle.plano = await this._proyectoDM.ObtenerDocumentoxProyecto(idProyecto, TiposDocumento.Plano) ?? temporal.plano;

            proyectoDetalle.constructor = new DatosCPV();
            proyectoDetalle.vendedor = new DatosCPV();
            proyectoDetalle.promotor = new DatosCPV();

            if (proyectoDetalle.idConstructor != proyectoDetalle.idEmpresa)
                proyectoDetalle.constructor = await this._servicioEmpresa.ConsultaEmpresaPorNRRUV(proyectoDetalle.idConstructor, null) ?? (temporal.constructor ?? new DatosCPV());

            idsTMP = await this._proyectoDM.ObtenerPromotorVendedorxProyecto(idProyecto);

            if (idsTMP.Count != 0)
            {
                if (proyectoDetalle.idPromotor == null || proyectoDetalle.idVendedor == null)
                {
                    foreach (var idPV in idsTMP)
                    {
                        var pvTMP = await this._proyectoDM.ObtenerPromotorVendedor(idPV);
                        pvTMP.mostrar = true;
                        pvTMP.esActualizacion = true;

                        if (pvTMP.esVendedor)
                            proyectoDetalle.vendedor = pvTMP;
                        else
                            proyectoDetalle.promotor = pvTMP;
                    }
                }
            }
            else
            {
                proyecto.vendedor = temporal != null ? temporal.vendedor ?? new DatosCPV() : new DatosCPV();
                proyectoDetalle.promotor = temporal != null ? temporal.promotor ?? new DatosCPV() : new DatosCPV();
            }

            proyectoDetalle.constructor.esElMismo = proyectoDetalle.idConstructor == proyectoDetalle.idEmpresa;
            proyectoDetalle.promotor.esElMismo = (proyectoDetalle.idPromotor ?? 0) == proyectoDetalle.idEmpresa;
            proyectoDetalle.vendedor.esElMismo = (proyectoDetalle.idVendedor ?? 0) == proyectoDetalle.idEmpresa;

            idsTMP = await this._proyectoDM.ObtenerPropietarioTerrenoxProyecto(idProyecto);
            proyectoDetalle.propietarioTerreno = await this._proyectoDM.ObtenerPropietarioTerreno(idsTMP.FirstOrDefault());

            idsTMP = await this._proyectoDM.ObtenerDROxProyecto(idProyecto);
            proyectoDetalle.dro = await this._proyectoDM.ObtenerDRO(idsTMP.FirstOrDefault());

            if (proyectoDetalle.dro != null)
            {
                proyectoDetalle.dro.identificacionOficial = await this._proyectoDM.ObtenerDocumentosDRO(proyectoDetalle.dro.idDRO, TiposDocumento.IdentificacionOficialDRO);
                proyectoDetalle.dro.licencia = await this._proyectoDM.ObtenerDocumentosDRO(proyectoDetalle.dro.idDRO, TiposDocumento.LicenciaDRO);
            }

            proyectoDetalle.zonasRiesgo = await this._servicioProyectosCatalogos.ObtenerZonasDeRiesgoAsync();
            List<ZonaRiesgo> zonasGuardadas = await this._proyectoDM.ObtenerZonasRiesgo(idProyecto);
            proyectoDetalle.esZonaRiesgo = zonasGuardadas.Count > 0;

            foreach (var zona in proyectoDetalle.zonasRiesgo)
            {
                var datosZona = zonasGuardadas.Where(z => z.idRiesgoOferta == zona.idRiesgoOferta);
                if (datosZona.ToList().Count > 0)
                {
                    zona.solucionMitigarRiesgo = datosZona.FirstOrDefault().solucionMitigarRiesgo;
                    zona.esSeleccionada = true;
                    zona.esActualizacion = true;
                }
            }

            var dictamenRiesgo = await (this._proyectoDM.ObtenerDocumentoxProyecto(idProyecto, TiposDocumento.DictamenRiesgoProyecto));
            proyectoDetalle.dictamenRiesgo = dictamenRiesgo ?? new DocumentoRuv();

            proyectoDetalle.sembrado = await this._proyectoDM.ObtenerSembradoxProyecto(idProyecto);

            //if (esValidacionASIS == true)
            //{
            //    if (ConfigurationManager.AppSettings["ActivarConvivenciaAsIs"] == "1")
            //    {
            //        proyectoDetalle.sembrado.viviendas = await this.ObtenerEstatusViviendaASISXProyecto(proyectoDetalle);
            //    }
            //}

            if (proyectoDetalle.temporalJSON != null)
            {
                Proyecto temporalJson = JsonConvert.DeserializeObject<Proyecto>(proyectoDetalle.temporalJSON);

                if (temporalJson.listaMotivosRechazo != null && temporalJson.listaMotivosRechazo.Count() > 0)
                {
                    List<MotivoRechazo> nuevosMotivosRechazo = temporalJson.listaMotivosRechazo.ToList();
                    MotivoRechazo ultimoMotivo = nuevosMotivosRechazo[0];
                    DictaminacionContenedor ultimaDictaminacion = await this._servicioDictaminacion.ObtenerDictaminacionAsync(35, proyecto.idProyecto);
                    //int dictaminaciones = await this._servicioDictaminacion.ObtenerCantidadDictaminaciones(35, proyecto.idProyecto);

                    if (!ultimoMotivo.mensajeRechazo.Equals(ultimaDictaminacion.secciones[0].mensajeRechazo))
                    {
                        ultimoMotivo.estActualizado = false;
                        ultimoMotivo.mensajeRechazo = ultimaDictaminacion.secciones[0].mensajeRechazo;

                        temporalJson.listaMotivosRechazo = nuevosMotivosRechazo.ToArray();
                        proyectoDetalle.temporalJSON = JsonConvert.SerializeObject(temporalJson);
                    }
                }
            }

            return proyectoDetalle;
        }

        public async Task<Proyecto> ObtenerProyectoDetalleAsync(int idProyecto)
        {
            var proyectoDetalle = await this._proyectoDM.ObtenerProyectoDetalleAsync(idProyecto);

            return proyectoDetalle;
        }

        public async Task<ResultadoPaginado<List<Proyecto>>> ObtenerProyectosFiltroConPaginadoAsync(int tamanioPagina, int pagina, int? idEmpresa, string noRuv, string nombreProyecto, int? idProyecto, int? idEntidad, int? idEstatus)
        {
            ResultadoPaginado<List<Proyecto>> resultado = new ResultadoPaginado<List<Proyecto>>()
            {
                TamanioPagina = tamanioPagina,
                PaginaActual = pagina
            };

            var data = await this._proyectoDM.ObtenerProyectosFiltroConPaginadoAsync(tamanioPagina, pagina, idEmpresa, noRuv, nombreProyecto, idProyecto, idEstatus);

            foreach (var proyecto in data.Item2)
            {
                var vivienda = await this._proyectoDM.ObtenerViviendasPorProyectoAsync(proyecto.idProyecto);

                if (vivienda.ToList().Count > 0)
                {
                    if (vivienda.FirstOrDefault().domicilioCarreteraCamino.idTipoCamino != null && vivienda.FirstOrDefault().domicilioCarreteraCamino.idTipoCamino != "0")
                    {
                        proyecto.direccion = vivienda.FirstOrDefault().domicilioCarreteraCamino.nombreVialidad;
                    }
                    else if (vivienda.FirstOrDefault().domicilioGeografico.idmunicipio != null)
                    {
                        var domicilio =
                            vivienda.FirstOrDefault().domicilioGeografico.estado
                            + ", " + vivienda.FirstOrDefault().domicilioGeografico.municipio;

                        proyecto.direccion = domicilio;
                    }
                }
            }

            if (data.Item1 > 0)
            {
                resultado.TotalRegistros = data.Item1;
                resultado.TotalPaginas = (int)Math.Ceiling((double)(resultado.TotalRegistros / tamanioPagina));
            }

            resultado.Resultado = data.Item2;

            //aqui si llega identidad, obtener los proyectos que solo tengan esa identidad
            if (idEntidad != null)
            {
                var proyectos = new List<Proyecto>();

                foreach (Proyecto proyecto in resultado.Resultado)
                {
                    var viviendas = await ObtenerViveindasPorProyectoAsync(proyecto.idProyecto);

                    if (viviendas.Where(v => v.domicilioGeografico.idEstado == idEntidad.ToString()).Count() > 0)
                    {
                        proyectos.Add(proyecto);
                    }
                }

                resultado.Resultado = proyectos;
            }

            return resultado;
        }

        public async Task<Proyecto> ObtenerProyectosFiltroAsync(int? idEmpresa, string noRuv, string nombreProyecto, int? idProyecto)
        {
            Proyecto proyecto = await this._proyectoDM.ObtenerProyectosFiltroAsync(idEmpresa, noRuv, nombreProyecto, idProyecto);

            if (proyecto != null)
            {
                var vivienda = await this._proyectoDM.ObtenerViviendasPorProyectoAsync(proyecto.idProyecto);

                if (vivienda.ToList().Count > 0)
                {
                    if (vivienda.FirstOrDefault().domicilioCarreteraCamino.idTipoCamino != null && vivienda.FirstOrDefault().domicilioCarreteraCamino.idTipoCamino != "0")
                    {
                        proyecto.direccion = vivienda.FirstOrDefault().domicilioCarreteraCamino.nombreVialidad;
                    }
                    else if (vivienda.FirstOrDefault().domicilioGeografico.idmunicipio != null)
                    {
                        var domicilio =
                            vivienda.FirstOrDefault().domicilioGeografico.estado
                            + ", " + vivienda.FirstOrDefault().domicilioGeografico.municipio;

                        proyecto.direccion = domicilio;
                    }
                }
            }

            return proyecto;
        }

        public async Task<List<Vivienda>> ObtenerViveindasPorProyectoAsync(int idProyecto)
        {
            var proyecto = await this.ObtenerProyectoAsync(idProyecto, null);

            // Se concatena un espacion en blanco con la cuv con el fin que la exportacion a excel no redondee el numero.
            foreach (var item in proyecto.sembrado.viviendas)
            {
                item.cuv = String.Concat(item.cuv, " ");
            }

            return proyecto.sembrado.viviendas;
        }

        public async Task<List<Vivienda>> ObtenerViveindasReportePorProyectoAsync(int idProyecto)
        {
            var puntajes = new PuntajeSalida();
            var viviendaReporte = new Vivienda();
            var tmpViviendaReporte = new List<Vivienda>();
            var viviendasReporte = new List<Vivienda>();
            var viviendas_ = new List<Vivienda>();
            var usuario = ConfigurationManager.AppSettings["UserASIS"];
            var contrasenia = ConfigurationManager.AppSettings["PasswordASIS"];
            var proyecto = await this.ObtenerProyectoAsync(idProyecto, null);

            string cuvs = "";
            foreach (var item in proyecto.sembrado.viviendas)
            {
                cuvs += "'" +item.cuv + "',";
            }

            viviendas_ = await this._medicionesDataMapper.ObtenerDatosCuvsAsync(cuvs != null ? cuvs.Substring(0, cuvs.Length-1) : null);

            // Se concatena un espacion en blanco con la cuv con el fin que la exportacion a excel no redondee el numero.
            foreach (var item in proyecto.sembrado.viviendas)
            {
                viviendaReporte = item;
                /*viviendaReporte.numeroAtributos = (await this._medicionesDataMapper.ObtenerAtributosXCuvsAsync(1, 7, item.cuv)).Item1;
                viviendaReporte.numeroEcotecnologias = (await this._medicionesDataMapper.ObtenerEcotecnologiasXCuvsAsync(1, 7, item.cuv)).Item1;
                tmpViviendaReporte = await this._ordenVerificacionDataMapper.ObtenerViviendasPorOrdenesVerificacionAsync(viviendaReporte.cuv, null);
                viviendaReporte.habitabilidad = (tmpViviendaReporte != null && tmpViviendaReporte.Any()) ? tmpViviendaReporte.FirstOrDefault().habitabilidad : "";
                viviendaReporte.porcentajeDeAvance = (tmpViviendaReporte != null && tmpViviendaReporte.Any()) ? tmpViviendaReporte.FirstOrDefault().porcentajeDeAvance : "";
                */
                Vivienda vivienda = viviendas_.FirstOrDefault(m => m.cuv == item.cuv);

                viviendaReporte.numeroAtributos = vivienda.numeroAtributos;
                viviendaReporte.numeroEcotecnologias = vivienda.numeroEcotecnologias;
                viviendaReporte.habitabilidad = vivienda.habitabilidad;
                viviendaReporte.porcentajeDeAvance = vivienda.porcentajeDeAvance;

                puntajes = await this._agenteServicioOfertaASIS.LlamarOperacion<PuntajesASIS, PuntajeSalida>
                     (new PuntajesASIS() { user = usuario, password = contrasenia, cuv = item.cuv }, ConfigurationManager.AppSettings["UrlObtenerPuntajesCUV"]);

                viviendaReporte.Puntajes = puntajes;

                viviendaReporte.cuv = String.Concat(viviendaReporte.cuv, " ");

                viviendasReporte.Add(viviendaReporte);
            }

            return viviendasReporte;
        }

        /// <summary>
        /// Actualiza las viviendas en ASIS, SIG y OFERTA
        /// </summary>
        /// <param name="sembrado"></param>
        /// <returns></returns>
        public async Task<List<Vivienda>> ActualizarViviendasAsync(Sembrado sembrado, int? idProyecto, string folioSEPLADE = null, string folioAyto = null)
        {
            var resultado = false;
            var viviendaActual = new Vivienda();
            var viviendaCompleta = new Vivienda();
            var listaViviendasCompletas = new List<Vivienda>();

            if (sembrado != null && sembrado.viviendas != null && sembrado.viviendas.Any())
            {
                if (idProyecto == null)
                {
                    idProyecto = sembrado.viviendas.FirstOrDefault().idProyecto;
                }

                var vialidades = await this._servicioProyectosCatalogos.ObtenerVialidadesFiltradoAsync(sembrado.viviendas[0].domicilioGeografico.idEstado ?? "", sembrado.viviendas[0].domicilioGeografico.idmunicipio ?? "");
                var asentamientos = await this._servicioProyectosCatalogos.ObtenerAsentamientosFiltradoAsync(sembrado.viviendas[0].domicilioGeografico.idEstado ?? "", sembrado.viviendas[0].domicilioGeografico.idmunicipio ?? "");

                var proyectoActual = await ObtenerProyectoAsync(idProyecto.Value, null);

                foreach (var vivienda in sembrado.viviendas)
                {
                    if (vivienda.esViviendaModificada == true)
                    {
                        viviendaActual = proyectoActual.sembrado.viviendas.Where(v => v.featID == vivienda.featID).FirstOrDefault();

                        vivienda.cuv = viviendaActual.cuv;
                        vivienda.idProyecto = idProyecto.Value;
                        vivienda.domicilioGeografico.xmlSIG = "";
                        vivienda.domicilioGeografico.idPeriodo = 4;
                        vivienda.domicilioGeografico.idTipoDomicilioRUV = 3;
                        vivienda.domicilioGeografico.idTipoDomicilioINEGI = 3;
                        vivienda.identificadorVivienda = viviendaActual.identificadorVivienda;
                        vivienda.idEstatusVivienda = viviendaActual.idEstatusVivienda;
                        vivienda.idOfertaVivienda = viviendaActual.idOfertaVivienda;
                        vivienda.idDomicilioGeografico = viviendaActual.idDomicilioGeografico;
                        vivienda.domicilioGeografico.idDomicilioGeografico = viviendaActual.idDomicilioGeografico;
                        vivienda.idDomicilioGeografico = viviendaActual.idDomicilioGeografico;
                        vivienda.idViviendaPlanoSIG = vivienda.idVivienda;

                        if (viviendaActual.domicilioGeografico.idEstado == vivienda.domicilioGeografico.idEstado
                            && viviendaActual.domicilioGeografico.idmunicipio == vivienda.domicilioGeografico.idmunicipio
                            && viviendaActual.domicilioGeografico.idLocalidad == vivienda.domicilioGeografico.idLocalidad
                            && viviendaActual.domicilioGeografico.nombreAsentamiento == vivienda.domicilioGeografico.nombreAsentamiento
                            && viviendaActual.idTipoAsentamiento == vivienda.idTipoAsentamiento)
                        {
                            var tmp = await this.ObtenerDatosComplementariosVivienda(vialidades, asentamientos, vivienda, false);
                            viviendaCompleta = tmp.Item1;
                            vialidades = tmp.Item2;
                            asentamientos = tmp.Item3;
                        }
                        else
                        {
                            var tmp = await this.ObtenerDatosComplementariosVivienda(vialidades, asentamientos, vivienda);
                            viviendaCompleta = tmp.Item1;
                            vialidades = tmp.Item2;
                            asentamientos = tmp.Item3;
                        }

                        await this._proyectoDM.ActualizarViviendaAsync(viviendaCompleta);

                        await this._proyectoDM.ActualizarDomiciioGeograficoAsync(viviendaCompleta.domicilioGeografico);

                        if (proyectoActual.sembrado.viviendas.Where(v => v.featID == viviendaCompleta.featID).FirstOrDefault().domicilioGeografico.tipoVialidadP == 22)
                        {
                            if (viviendaCompleta.domicilioGeografico.tipoVialidadP == 22)
                            {
                                await this._proyectoDM.ActualizarDomicilioCarretera(viviendaCompleta.domicilioCarreteraCamino);
                            }
                            else
                            {
                                await this._proyectoDM.EliminarDomicilioCarretera(viviendaCompleta.idDomicilioGeografico);
                            }

                            if (viviendaCompleta.domicilioGeografico.tipoVialidadP == 24)
                            {
                                viviendaCompleta.domicilioCarreteraCamino.idDomicilioGeografico = viviendaCompleta.idDomicilioGeografico;
                                viviendaCompleta.domicilioCarreteraCamino.nombreVialidad = viviendaCompleta.domicilioGeografico.nombreVialidadPrincipal;
                                await this._proyectoDM.GuardarDomicilioCamino(viviendaCompleta.domicilioCarreteraCamino);
                            }
                        }
                        else if (proyectoActual.sembrado.viviendas.Where(v => v.featID == viviendaCompleta.featID).FirstOrDefault().domicilioGeografico.tipoVialidadP == 24)
                        {
                            if (viviendaCompleta.domicilioGeografico.tipoVialidadP == 24)
                            {
                                await this._proyectoDM.ActualizarDomicilioCamino(viviendaCompleta.domicilioCarreteraCamino);
                            }
                            else
                            {
                                await this._proyectoDM.EliminarDomicilioCamino(viviendaCompleta.idDomicilioGeografico);
                            }

                            if (viviendaCompleta.domicilioGeografico.tipoVialidadP == 22)
                            {
                                viviendaCompleta.domicilioCarreteraCamino.idDomicilioGeografico = viviendaCompleta.idDomicilioGeografico;
                                viviendaCompleta.domicilioCarreteraCamino.nombreVialidad = viviendaCompleta.domicilioGeografico.nombreVialidadPrincipal;
                                await this._proyectoDM.GuardarDomicilioCarretera(viviendaCompleta.domicilioCarreteraCamino);
                            }
                        }
                        else
                        {
                            if (viviendaCompleta.domicilioGeografico.tipoVialidadP == 22)
                            {
                                viviendaCompleta.domicilioCarreteraCamino.idDomicilioGeografico = viviendaCompleta.idDomicilioGeografico;
                                viviendaCompleta.domicilioCarreteraCamino.nombreVialidad = viviendaCompleta.domicilioGeografico.nombreVialidadPrincipal;
                                await this._proyectoDM.GuardarDomicilioCarretera(viviendaCompleta.domicilioCarreteraCamino);
                            }
                            else if (viviendaCompleta.domicilioGeografico.tipoVialidadP == 24)
                            {
                                viviendaCompleta.domicilioCarreteraCamino.idDomicilioGeografico = viviendaCompleta.idDomicilioGeografico;
                                viviendaCompleta.domicilioCarreteraCamino.nombreVialidad = viviendaCompleta.domicilioGeografico.nombreVialidadPrincipal;
                                await this._proyectoDM.GuardarDomicilioCamino(viviendaCompleta.domicilioCarreteraCamino);
                            }
                        }
                        using (var transaccionOracle = new TransactionScope(TransactionScopeOption.RequiresNew, this._opcionesTransaccion, TransactionScopeAsyncFlowOption.Enabled))
                        {
                            //Actualizar en ORACLE
                            await this._planoDataMapper.ActualizarViviendasSembradoOficial
                            (viviendaCompleta, idProyecto.Value, folioSEPLADE ?? proyectoActual.folioSEPLADE, folioAyto ?? proyectoActual.folioAyto);
                            transaccionOracle.Complete();
                        }
                    }

                    listaViviendasCompletas.Add(viviendaCompleta);
                }
            }

            return listaViviendasCompletas;
        }

        public async Task<List<Proyecto>> ObtenerProyectosPorIdEmpresaAsync(int idEmpresa)
        {
            List<Proyecto> listaProyecto = await this._proyectoDM.ObtenerProyectosPorIdEmpresaAsync(idEmpresa);

            return listaProyecto;
        }

        public async Task<RespuestaCargaPlano> CargarArchivoSDFAsync(int idArchivoSDF, int idUsuario, int idProyecto, CustomUserRuv usuario)
        {
            RespuestaCargaPlano errores = new RespuestaCargaPlano();

            string esquemaSDF = ConfigurationManager.AppSettings["SDFschema"];
            string usuarioOracle = ConfigurationManager.AppSettings["OracleUser"];
            string passwordOracle = ConfigurationManager.AppSettings["OraclePassword"];
            string urlServicioOracle = ConfigurationManager.AppSettings["OracleUrl"];
            string esquemaOracle = ConfigurationManager.AppSettings["OracleSchema"];
            string claveAppInsights = ConfigurationManager.AppSettings["InstrumentationKey"];

            //Se hace esto para obtener el Operation Name y Operation Id
            var tele = new Microsoft.ApplicationInsights.DataContracts.TraceTelemetry("Telemetría");
            Microsoft.ApplicationInsights.Extensibility.TelemetryConfiguration.Active.TelemetryInitializers[8].Initialize(tele);
            Microsoft.ApplicationInsights.Extensibility.TelemetryConfiguration.Active.TelemetryInitializers[9].Initialize(tele);

            //this._clienteTelemetria.Context.Operation.Id = tele.Context.Operation.Id;
            //this._clienteTelemetria.Context.Operation.Name = tele.Context.Operation.Name;

            string mensaje = string.Empty;
            int codigoRespuestaEjecutable = -1;
            string rutaEsquema = HttpContext.Current.Server.MapPath("~/App_Data/Esquemas/RegistroProyectoSchema.xml");
            string rutaArchivo = await this.ObtenerRutaArchivoSDF(idArchivoSDF);
            idUsuario = usuario.IdUsuario.Value;

            Process procesoCargaSDF = new Process();
            string rutaExe = HttpContext.Current.Server.MapPath("~/App_Data/FDO/CargaPlano_SFDToOracle.exe");
            DirectoryInfo directory = new DirectoryInfo(Path.GetFullPath(Path.Combine(System.Web.HttpRuntime.AppDomainAppPath, rutaExe)));
            procesoCargaSDF.StartInfo.FileName = directory.ToString();
            procesoCargaSDF.StartInfo.Arguments = string.Format("\"{0}\" \"{1}\" \"{2}\" \"{3}\" \"{4}\" \"{5}\" \"{6}\" \"{7}\" \"{8}\" \"{9}\" \"{10}\" \"{11}\" ", rutaArchivo, rutaEsquema, idUsuario, idProyecto, esquemaSDF, usuarioOracle, passwordOracle, urlServicioOracle, esquemaOracle, claveAppInsights, tele.Context.Operation.Id, tele.Context.Operation.Name);
            procesoCargaSDF.StartInfo.WindowStyle = ProcessWindowStyle.Hidden;
            procesoCargaSDF.StartInfo.UseShellExecute = false;
            procesoCargaSDF.StartInfo.RedirectStandardOutput = true;

            procesoCargaSDF.Start();
            mensaje = await procesoCargaSDF.StandardOutput.ReadToEndAsync();
            procesoCargaSDF.WaitForExit();
            codigoRespuestaEjecutable = procesoCargaSDF.ExitCode;
            procesoCargaSDF.Close();

            if (!string.IsNullOrEmpty(mensaje.Replace(System.Environment.NewLine, string.Empty)) && codigoRespuestaEjecutable != 0)
            {
                errores.codigoError = codigoRespuestaEjecutable;
                errores.mensajeError = mensaje;
                this._clienteTelemetria?.TrackException(new Exception(mensaje));
            }

            #region Proceso Bloqueo CUVS

            await this.ValidaBloqueoCUVS(idProyecto, codigoRespuestaEjecutable);

            #endregion Proceso Bloqueo CUVS

            if (activarSig == 1 && codigoRespuestaEjecutable == 0)
            {
                #region Validación dentro del república mexicana

                try
                {
                    var viviendasSinMunicipioEstado = await this._planoDataMapper.ObtenerViviendasSinMunEdo(idProyecto);

                    if (viviendasSinMunicipioEstado.Count > 0)
                    {
                        StringBuilder mensajes = new StringBuilder();
                        viviendasSinMunicipioEstado.ForEach(i =>
                        {
                            mensajes.AppendLine(string.Format("El ID VIVIENDA: {0} se encuentra fuera de la Republica Mexicana", i.idVivienda));
                        });
                        this._clienteTelemetria.TrackException(new Exception(mensajes.ToString()));
                        codigoRespuestaEjecutable = -5;
                        errores.codigoError = codigoRespuestaEjecutable;
                        errores.mensajeError += mensajes.ToString();
                        mensaje += mensajes.ToString();
                    }
                }
                catch (OracleException e)
                {
                    this._clienteTelemetria.TrackException(e);
                    codigoRespuestaEjecutable = -4;
                }

                #endregion Validación dentro del república mexicana

                #region Validación espacial

                try
                {
                    string validacionEspacial = await this._planoDataMapper.ValidacionProyecto(idProyecto);
                    if (!string.IsNullOrEmpty(validacionEspacial))
                    {
                        this._clienteTelemetria?.TrackException(new Exception(validacionEspacial));
                        codigoRespuestaEjecutable = -6;
                        errores.codigoError = codigoRespuestaEjecutable;
                        errores.mensajeError += validacionEspacial;
                        mensaje += validacionEspacial;
                    }
                }
                catch (OracleException e)
                {
                    this._clienteTelemetria.TrackException(e);
                    codigoRespuestaEjecutable = -4;
                }

                #endregion Validación espacial
            }

            File.Delete(rutaArchivo);
            if (codigoRespuestaEjecutable != 0)
            {
                this._planoDataMapper.EliminarProyectoTemporal(idProyecto);
                string nombre = DateTime.Now.ToString("yyyy-MM-ddTHH_mm_ss_ff") + ".txt";
                MemoryStream stream = new MemoryStream();
                StreamWriter writer = new StreamWriter(stream);
                writer.Write(mensaje);
                writer.Flush();
                stream.Position = 0;
                string contenedor = usuario.rfcEmpresa.ToLower();
                var url = this._blobStorage.UploadBlob(contenedor, new BlobBase() { Name = "log_carga_plano/" + nombre, ContainerName = "logerrores", StreamData = stream }, true, Microsoft.WindowsAzure.Storage.Blob.BlobContainerPublicAccessType.Container, true);
                errores.urlArchivo = url;
            }

            using (var transaccionHistoricos = new TransactionScope(TransactionScopeOption.RequiresNew, this._opcionesTransaccion, TransactionScopeAsyncFlowOption.Enabled))
            {
                //Guardado de histórico Proyecto "Carga de Plano"
                await GuardarHistoricoProyectoAsync(EventoProyecto.CargaDePlano, idProyecto, "CargadePlano", usuario);

                transaccionHistoricos.Complete();
            }

            return errores;
        }

        public async Task<Sembrado> ObtenerSembradoTemporalAsync(int idProyectoTemporal)
        {
            Sembrado listaViviendasSembrado = new Sembrado();
            listaViviendasSembrado = await _planoDataMapper.ObtenerSembradoTemporal(idProyectoTemporal);
            return listaViviendasSembrado;
        }

        #endregion implementacion IServicioProyecto

        #region EnvioDictaminacion

        /// <summary>
        /// Guarda/Actualiza la dictaminación, actualiza el estatus del proyecto y atiende la ODT.
        /// </summary>
        /// <param name="idOrdenTrabajo">Identificador de la orden de trabajo</param>
        /// <param name="idServicio">Identificador del servicio</param>
        /// <param name="idRegistro">Identificador del proyecto</param>
        /// <param name="parametrosDictaminacion">Objeto que contiene los siguientes parametros:
        /// seActualizaDictaminacion - FALSE si se inserta la dictaminacion, TRUE si se actualiza
        /// DictaminacionJSON -  cadena que contiene el JSON de dictaminacion</param>
        /// aceptacion - TRUE indica que se acepta la oferta, FALSE que se rechaza la oferta<returns></returns>
        /// <returns></returns>
        public async Task<Tuple<bool, bool>> EnviarDictaminacionProyecto(int idOrdenTrabajo, short idServicio, int idRegistro, ParametroBodyDictaminacion parametrosDictaminacion, CustomUserRuv usuario, bool esGuardadoAsincrono = false)
        {
            bool result = false;
            bool esProcesoAsincrono = false;
            bool dictaminacionGuardada = false;
            // Obtener datos del proyecto
            Proyecto proyectoNoModificado = await this.ObtenerProyectoAsync(idRegistro);
            Proyecto proyectoActualizado = JsonConvert.DeserializeObject<Proyecto>(proyectoNoModificado.temporalJSON);

            if (!esGuardadoAsincrono)
            {
                MotivosRechazo dictaminacion = JsonConvert.DeserializeObject<MotivosRechazo>(parametrosDictaminacion.DictaminacionJSON);

                if (dictaminacion.secciones[0].mensajeRechazo.Equals(dictaminacion.secciones[0].mensajeRechazoAnterior))
                {
                    dictaminacion.secciones[0].mensajeRechazo = dictaminacion.secciones[0].mensajeRechazo + ' ';
                    dictaminacion.secciones[0]._mensajeRechazoTemporal = dictaminacion.secciones[0]._mensajeRechazoTemporal + ' ';

                    parametrosDictaminacion.DictaminacionJSON = JsonConvert.SerializeObject(dictaminacion);
                }

            }

            _clienteTelemetria.TrackTrace("Iniciando proceso de dictaminación");

            using (var transaccion = new TransactionScope(TransactionScopeOption.RequiresNew, this._opcionesTransaccion, TransactionScopeAsyncFlowOption.Enabled))
            {
                if (!esGuardadoAsincrono)
                {
                    if (parametrosDictaminacion.seActualizaDictaminacion)
                    {
                        _clienteTelemetria.TrackTrace("Actualizando dictaminación");
                        dictaminacionGuardada = await this._servicioDictaminacion.ActualizarDictaminacionAsync(idServicio, idOrdenTrabajo, idRegistro, parametrosDictaminacion.DictaminacionJSON);
                    }
                    else
                    {
                        _clienteTelemetria.TrackTrace("Guardando dictaminación");
                        dictaminacionGuardada = await this._servicioDictaminacion.GuardarDictaminacionAsync(idServicio, idOrdenTrabajo, idRegistro, parametrosDictaminacion.DictaminacionJSON);
                    }
                }
                else
                {
                    dictaminacionGuardada = true;
                }

                if (dictaminacionGuardada)
                {
                    if (parametrosDictaminacion.aceptacion)
                    {
                        var superaMaximoViviendas = proyectoActualizado.sembrado.viviendas.Count() > Convert.ToInt32(ConfigurationManager.AppSettings["NumeroMaximoViviendas"]);

                        if (superaMaximoViviendas && !esGuardadoAsincrono)
                        {
                            _clienteTelemetria.TrackTrace("Ejecutando proceso Asincrono");
                            esProcesoAsincrono = true;
                            //Actualizar estatus a terminando aceptación
                            _clienteTelemetria.TrackTrace("Actualizando Estatus Proyecto a Terminando Aceptación");
                            await this.ActualizarEstatusProyectoAsync(idRegistro, (int)EstatusProyecto.TerminandoAceptacion);

                            MensajeDictaminacionProyecto mensaje = new MensajeDictaminacionProyecto()
                            {
                                IdProyecto = proyectoNoModificado.idProyecto,
                                IdUsuario = usuario.IdUsuario.Value,
                                IdEmpresa = usuario.IdEmpresa,
                                NombreUsuario = usuario.NombreUsuario,
                                TokenGuid = usuario.TokenGuid,
                                IdEstatusPrevio = proyectoNoModificado.idEstatusProyecto,
                                IdOrdenTrabajo = idOrdenTrabajo,
                                IdServicio = idServicio,
                                IdRegistro = idRegistro,
                                Aceptacion = true,
                                SeActualizaDictaminacion = parametrosDictaminacion.seActualizaDictaminacion
                            };

                            var mensajeAgregar = JsonConvert.SerializeObject(mensaje);

                            this._clienteTelemetria.TrackTrace("Agregando mensaje a cola");
                            await this._queueStorage.QueueClient.GetQueueReference(nombreQueueDictaminacionProyecto).AddMessageAsync(new CloudQueueMessage(mensajeAgregar));

                            //Se atiende la ODT en proceso sincrono
                            if (parametrosDictaminacion.aceptacion)
                            {
                                this._servicioOrdenTrabajoAlta.Atender(idOrdenTrabajo, true);
                                _clienteTelemetria.TrackTrace("Enviando Correo de Aceptación");
                                await this.EnviarCorreoProyectoAceptado(proyectoNoModificado);
                            }
                            else
                            {
                                this._servicioOrdenTrabajoAlta.Atender(idOrdenTrabajo, false);
                                _clienteTelemetria.TrackTrace("Enviando Correo de Rechazo");
                                await this.EnviarCorreoProyectoRechazado(proyectoNoModificado, idOrdenTrabajo, usuario);
                            }
                        }
                        else
                        {
                            _clienteTelemetria.TrackTrace("Ejecutando Proceso Sincrono");

                            _clienteTelemetria.TrackTrace("Guardando datos del proyecto");
                            await this.GuardaDatosProyecto(proyectoActualizado, usuario, proyectoNoModificado);

                            _clienteTelemetria.TrackTrace("Actualizando estatus proyecto a Aceptado");
                            await this.ActualizarEstatusProyectoAsync(idRegistro, (int)EstatusProyecto.Aceptada);

                            _clienteTelemetria.TrackTrace("Actualizando estatus viviendas a Inactiva en SQL");
                            await this._ofertaDataMapper.ActualizarEstatusViviendaPoProyectoAsync(idRegistro, (int)EstatusVivienda.Inactiva);

                            using (var transaccionOracle = new TransactionScope(TransactionScopeOption.RequiresNew, this._opcionesTransaccion, TransactionScopeAsyncFlowOption.Enabled))
                            {
                                _clienteTelemetria.TrackTrace("Actualizando estatus viviendas a Inactiva en Oracle");
                                await this._planoDataMapper.ActualizarEstatusViviendaPorProyectoAsync(idRegistro, ((int)Modelo.Proyectos.EstatusVivienda.Inactiva).ToString());

                                _clienteTelemetria.TrackTrace("Obteniendo sembrado temporal-oficial de Oracle");
                                var sembrado = this._planoDataMapper.ObtenerSembradoOficialTemporal(idRegistro);

                                transaccionOracle.Complete();
                            }
                            _clienteTelemetria.TrackTrace("Iniciando procesos de generación de CUVS");
                            if (activarSig == 1 && activarConvivencia == 1)
                            {
                                List<Vivienda> listaVivienda = await ObtenerVivendasPorIdProyectoAsync(proyectoNoModificado.idProyecto);
                                List<Vivienda> listaViviendaFiltrada = listaVivienda.Where(v => v.idEstatusVivienda != 7 && v.idEstatusVivienda != 8).ToList();
                                int y = 1;
                                foreach (Vivienda vivienda_ in listaViviendaFiltrada)
                                {
                                    vivienda_.identificadorVivienda = y;
                                    y++;
                                }
                                Vivienda vivienda = listaViviendaFiltrada.FirstOrDefault();
                                listaViviendaFiltrada = await this._servicioVivienda.GenerarCuvs(proyectoNoModificado.idProyecto, listaViviendaFiltrada, proyectoNoModificado.fechaRegistro ?? DateTime.Now, vivienda.idEstado, vivienda.idMunicipio);
                                await EnviarCorreoCuvsGeneradas(listaViviendaFiltrada, proyectoNoModificado, usuario);
                                foreach (Vivienda viviendaCuv in listaViviendaFiltrada)
                                {
                                    int resultado = await this._proyectoDM.ActualizarCuvVivienda(viviendaCuv);
                                }

                                using (var transaccionOracle = new TransactionScope(TransactionScopeOption.RequiresNew, this._opcionesTransaccion, TransactionScopeAsyncFlowOption.Enabled))
                                {
                                    foreach (Vivienda viviendaCuv in listaViviendaFiltrada)
                                    {
                                        bool resultadoCuv = await this._planoDataMapper.AsignarCUVAsisVivienda(viviendaCuv.featID, viviendaCuv.cuv);
                                        if (!resultadoCuv)
                                        {
                                            throw new Exception("Error al actualizar la cuv  en SIG");
                                        }
                                    }

                                    transaccionOracle.Complete();
                                }
                            }

                            if (!esGuardadoAsincrono)
                            {
                                _clienteTelemetria.TrackTrace("Atendiendo ODT");
                                this._servicioOrdenTrabajoAlta.Atender(idOrdenTrabajo, true);
                                _clienteTelemetria.TrackTrace("Enviando Correo de Aceptación");
                                await this.EnviarCorreoProyectoAceptado(proyectoNoModificado);
                            }
                        }

                        using (var transaccionHistoricos = new TransactionScope(TransactionScopeOption.RequiresNew, this._opcionesTransaccion, TransactionScopeAsyncFlowOption.Enabled))
                        {
                            if (parametrosDictaminacion.aceptacion)
                            {
                                //Guardado de histórico Proyecto "Aceptación"
                                _clienteTelemetria.TrackTrace("Guardando Histórico de Proyecto Aceptado");
                                await GuardarHistoricoProyectoAsync(EventoProyecto.Aceptacion, proyectoNoModificado.idProyecto, "Aceptado", usuario);
                                transaccionHistoricos.Complete();
                            }
                        }
                    }
                    else
                    {
                        if (proyectoNoModificado.idEstatusProyecto == (int)EstatusProyecto.ValidaciónPorActualización)
                        {
                            _clienteTelemetria.TrackTrace("Cambiando Estatus Proyecto a Rechazado por Actualización");
                            await this.ActualizarEstatusProyectoAsync(idRegistro, (int)EstatusProyecto.RechazadaPorActualización, parametrosDictaminacion.aceptacion);
                        }
                        else
                        {
                            _clienteTelemetria.TrackTrace("Cambiando Estatus Proyecto a Rechazado");
                            await this.ActualizarEstatusProyectoAsync(idRegistro, (int)EstatusProyecto.Rechazada, parametrosDictaminacion.aceptacion);
                        }

                        var proyectoMotivosRechazo = JsonConvert.DeserializeObject<Proyecto>(proyectoNoModificado.temporalJSON);
                        proyectoMotivosRechazo.listaMotivosRechazo = (await this._servicioDictaminacion.ObtenerMotivosRechazo(idServicio, idRegistro, idOrdenTrabajo, false)).ToArray();

                        //Se actualizan los motivos de rechazo en el json Temporal, ya que aun no se acepta el proyecto.
                        await this._proyectoDM.ActualizarProyectoTemporalAsync(idRegistro, JsonConvert.SerializeObject(proyectoMotivosRechazo), null);

                        using (var transaccionHistoricos = new TransactionScope(TransactionScopeOption.RequiresNew, this._opcionesTransaccion, TransactionScopeAsyncFlowOption.Enabled))
                        {
                            //Guardado de histórico Proyecto "Rechazo"
                            _clienteTelemetria.TrackTrace("Guardando Histórico de Proyecto Rechazado");
                            await GuardarHistoricoProyectoAsync(EventoProyecto.Rechazo, proyectoNoModificado.idProyecto, "Rechazado", usuario);
                            transaccionHistoricos.Complete();
                        }

                        if (!esGuardadoAsincrono)
                        {
                            _clienteTelemetria.TrackTrace("Atendiendo ODT");
                            this._servicioOrdenTrabajoAlta.Atender(idOrdenTrabajo, false);
                            //Envio de correo de Rechazo
                            _clienteTelemetria.TrackTrace("Enviando Correo de Rechazo");
                            await this.EnviarCorreoProyectoRechazado(proyectoNoModificado, idOrdenTrabajo, usuario);
                        }
                    }
                }
                result = true;
                transaccion.Complete();
            }
            _clienteTelemetria.TrackTrace("Terminando proceso de dictaminación");
            return new Tuple<bool, bool>(result, esProcesoAsincrono);
        }

        private async Task GuardaDatosProyecto(Proyecto proyectoActualizado, CustomUserRuv usuario, Proyecto proyectoNoModificado)
        {
            bool esActualizacionProyecto = proyectoNoModificado.idEstatusProyecto == (int)EstatusProyecto.ValidaciónPorActualización 
                || proyectoNoModificado.idEstatusProyecto == (int)EstatusProyecto.Aceptada;

            _clienteTelemetria.TrackTrace("Guardando y actualizando viviendas en SQL");
            List<Vivienda> listaViviendasCompletas = await this.GuardarActualizarViviendasProyecto(proyectoNoModificado, proyectoActualizado, usuario, esActualizacionProyecto);

            proyectoActualizado.idPromotor = proyectoActualizado.idPromotor == 0 ? null : proyectoActualizado.idPromotor;
            proyectoActualizado.idVendedor = proyectoActualizado.idVendedor == 0 ? null : proyectoActualizado.idVendedor;

            _clienteTelemetria.TrackTrace("Actualizando Proyecto");
            await this._proyectoDM.ActualizarProyectoAsync(proyectoActualizado.idProyecto, proyectoActualizado.nombre, proyectoActualizado.temporalJSON, null);

            _clienteTelemetria.TrackTrace("Actualizando Proyecto");
            await this.ActualizarProyectoAsync(proyectoActualizado.idProyecto, proyectoActualizado.nombre, proyectoActualizado.temporalJSON, null, proyectoActualizado.fueronModificadosDatosSensibles);

            _clienteTelemetria.TrackTrace("Guardando y Actualizando Promotor y Vendedor");
            await this.GuardarActualizarPromotorVerndedorAsync(proyectoActualizado, proyectoNoModificado);

            if (esActualizacionProyecto)
            {
                if (proyectoNoModificado.plano.IdDocumento != proyectoActualizado.plano.IdDocumento)
                {
                    _clienteTelemetria.TrackTrace("Eliminando Documentos Proyecto - Plano");
                    await this._proyectoDM.EliminarDocumentoxProyectoAsync(proyectoNoModificado.plano.IdDocumento, proyectoActualizado.idProyecto);

                    _clienteTelemetria.TrackTrace("Guardando Documentos Proyecto - Plano");
                    await this._proyectoDM.GuardarDocumentoxProyectoAsync(proyectoActualizado.plano.IdDocumento, proyectoActualizado.idProyecto);
                }

                _clienteTelemetria.TrackTrace("Actualizando Propietario Terreno");
                await this._proyectoDM.ActualizarPropietarioTerrenoAsync(proyectoActualizado.propietarioTerreno);

                _clienteTelemetria.TrackTrace("Actualizando DRO");
                await this._proyectoDM.ActualizarDROAsync(proyectoActualizado.dro);

                if (proyectoActualizado.esZonaRiesgo.Value)
                {
                    _clienteTelemetria.TrackTrace("Actualizando zona de riesgo");
                    await this._proyectoDM.EliminarZonaRiesgoAsync(proyectoActualizado.zonasRiesgo.Where(z => !z.esSeleccionada && z.esActualizacion).ToArray(), proyectoActualizado.idProyecto);
                    await this._proyectoDM.GuardarZonasRiesgoAsync(proyectoActualizado.zonasRiesgo.Where(z => z.esSeleccionada && !z.esActualizacion).ToArray(), proyectoActualizado.idProyecto);
                    await this._proyectoDM.ActualizarZonasRiesgoAsync(proyectoActualizado.zonasRiesgo.Where(z => z.esSeleccionada && z.esActualizacion).ToArray(), proyectoActualizado.idProyecto);

                    if (proyectoNoModificado.dictamenRiesgo != null && proyectoNoModificado.dictamenRiesgo.IdDocumento != 0)
                    {
                        if (proyectoActualizado.dictamenRiesgo.IdDocumento != 0)
                        {
                            if (proyectoNoModificado.dictamenRiesgo.IdDocumento != proyectoActualizado.dictamenRiesgo.IdDocumento)
                            {
                                await this._proyectoDM.EliminarDocumentoxProyectoAsync(proyectoNoModificado.dictamenRiesgo.IdDocumento, proyectoActualizado.idProyecto);
                                await this._proyectoDM.GuardarDocumentoxProyectoAsync(proyectoActualizado.dictamenRiesgo.IdDocumento, proyectoActualizado.idProyecto);
                            }
                        }
                        else
                        {
                            await this._proyectoDM.EliminarDocumentoxProyectoAsync(proyectoNoModificado.dictamenRiesgo.IdDocumento, proyectoActualizado.idProyecto);
                        }
                    }
                    //else
                    //{
                    //    if (proyectoActualizado.dictamenRiesgo.IdDocumento != 0)
                    //    {
                    //        await this._proyectoDM.GuardarDocumentoxProyectoAsync(proyectoActualizado.dictamenRiesgo.IdDocumento, proyectoActualizado.idProyecto);
                    //    }
                    //}
                }
                else
                {
                    _clienteTelemetria.TrackTrace("Eliminando zona riesgo");
                    await this._proyectoDM.EliminarZonaRiesgoAsync(proyectoActualizado.zonasRiesgo.ToArray(), proyectoActualizado.idProyecto);
                    if (proyectoNoModificado.dictamenRiesgo != null)
                        await this._proyectoDM.EliminarDocumentoxProyectoAsync(proyectoNoModificado.dictamenRiesgo.IdDocumento, proyectoActualizado.idProyecto);
                }
                _clienteTelemetria.TrackTrace("Actualizando Proyecto Detalle");
                await this._proyectoDM.ActualizarProyectoDetalleAsync(proyectoActualizado);
            }
            else

            {
                _clienteTelemetria.TrackTrace("Guardando Documento Proyecto");
                await this._proyectoDM.GuardarDocumentoxProyectoAsync(proyectoActualizado.plano.IdDocumento, proyectoActualizado.idProyecto);

                _clienteTelemetria.TrackTrace("Guardando Propietario de Terreno");
                proyectoActualizado.propietarioTerreno.idPropietarioTerreno = await this._proyectoDM.GuardarPropietarioTerrenoAsync(proyectoActualizado.propietarioTerreno);
                await this._proyectoDM.GuardarPropietarioTerrenoxProyectoAsync(proyectoActualizado.propietarioTerreno.idPropietarioTerreno, proyectoActualizado.idProyecto);

                _clienteTelemetria.TrackTrace("Guardando DRO");
                proyectoActualizado.dro.idDRO = await this._proyectoDM.GuardarDROAsync(proyectoActualizado.dro);
                await this._proyectoDM.GuardarDROxProyectoAsync(proyectoActualizado.dro.idDRO, proyectoActualizado.idProyecto);

                if (proyectoActualizado.esZonaRiesgo.Value)
                {
                    _clienteTelemetria.TrackTrace("Guardando Zona Riesgo");
                    await this._proyectoDM.GuardarZonasRiesgoAsync(proyectoActualizado.zonasRiesgo.Where(z => z.esSeleccionada).ToArray(), proyectoActualizado.idProyecto);

                    if (proyectoActualizado.dictamenRiesgo.IdDocumento != 0)
                    {
                        await this._proyectoDM.GuardarDocumentoxProyectoAsync(proyectoActualizado.dictamenRiesgo.IdDocumento, proyectoActualizado.idProyecto);
                    }
                }
                _clienteTelemetria.TrackTrace("Guardando Proyecto Detalle");
                await this._proyectoDM.GuardarProyectoDetalleAsync(proyectoActualizado);
            }
            if (activarSig == 1)
            {
                using (var transaccioneOracle = new TransactionScope(TransactionScopeOption.RequiresNew, this._opcionesTransaccion, TransactionScopeAsyncFlowOption.Enabled))
                {
                    _clienteTelemetria.TrackTrace("Ejecutando Validaciones de Oracle");
                    await this.EjecutarValidacionesOracle(proyectoActualizado, listaViviendasCompletas);

                    _clienteTelemetria.TrackTrace("Ejecutando Migracion de TMP a Oficial");
                    await this.EjecutarMigracionTemporalOficialOracle(proyectoActualizado);

                    transaccioneOracle.Complete();
                }
            }
        }

        private async Task GuardarActualizarPromotorVerndedorAsync(Proyecto proyectoActualizado, Proyecto proyectoNoModificado)
        {
            if (proyectoActualizado.idPromotor == null)
            {
                if (proyectoNoModificado.idPromotor == 0 || proyectoNoModificado.idPromotor == null)
                {
                    var idCPV = await this._proyectoDM.GuardarPromotorVendedorAsync(proyectoActualizado.promotor);
                    await this._proyectoDM.GuardarPromotorVendedorxProyectoAsync(idCPV, proyectoActualizado.idProyecto);
                }
                else
                {
                    proyectoActualizado.promotor.idCPVExterno = proyectoNoModificado.promotor.idCPVExterno;
                    await this._proyectoDM.ActualizarPromotorVendedorAsync(proyectoActualizado.promotor);
                }
            }
            if (proyectoActualizado.idPromotor > 0 && proyectoNoModificado.idPromotor == null)
            {
                await this._proyectoDM.EliminarPromotorExternoxProyecto(proyectoActualizado.idProyecto, proyectoNoModificado.promotor.idCPVExterno);
            }

            if (proyectoActualizado.idVendedor == null)
            {
                if (proyectoNoModificado.idVendedor == 0 || proyectoNoModificado.idPromotor == null)
                {
                    var idCPV = await this._proyectoDM.GuardarPromotorVendedorAsync(proyectoActualizado.vendedor);
                    await this._proyectoDM.GuardarPromotorVendedorxProyectoAsync(idCPV, proyectoActualizado.idProyecto);
                }
                else
                {
                    proyectoActualizado.vendedor.idCPVExterno = proyectoNoModificado.vendedor.idCPVExterno;
                    await this._proyectoDM.ActualizarPromotorVendedorAsync(proyectoActualizado.vendedor);
                }
            }
            if (proyectoActualizado.idVendedor > 0 && proyectoNoModificado.idVendedor == null)
            {
                await this._proyectoDM.EliminarPromotorExternoxProyecto(proyectoActualizado.idProyecto, proyectoNoModificado.vendedor.idCPVExterno);
            }
        }

        private async Task EjecutarMigracionTemporalOficialOracle(Proyecto proyectoActualizado)
        {
            try
            {
                _clienteTelemetria.TrackTrace("Realizando migración de tmp a oficial");
                string resultadoMigracion = await this._planoDataMapper.MigracionTemporalOficial(proyectoActualizado.idProyecto);
                if (!string.IsNullOrEmpty(resultadoMigracion))
                {
                    throw new ExcepcionSig(resultadoMigracion);
                }
            }
            catch (Exception e)
            {
                throw new ExcepcionSig("Error al realizar migración:" + e.ToString(), e);
                _clienteTelemetria.TrackTrace("Error al realizar migración : " + e.ToString());
            }
            finally
            {
                _clienteTelemetria.TrackTrace("Finalizando proceso de migración de tmp a oficial");
            }
        }

        private async Task EjecutarValidacionesOracle(Proyecto proyectoActualizado, List<Vivienda> listaViviendasCompletas)
        {
            try
            {
                this._clienteTelemetria.TrackTrace("Actualizando bloqueo de CUV en Oracle.");
                await this.ValidaBloqueoCUVS(proyectoActualizado.idProyecto);
            }
            catch (Exception e)
            {
                throw new ExcepcionSig("Error al bloquear CUV", e);
            }

            try
            {
                foreach (var vivienda in listaViviendasCompletas)
                {
                    await this._planoDataMapper.ActualizarViviendasSembradoTemporal(vivienda, proyectoActualizado.idProyecto, proyectoActualizado.folioSEPLADE, proyectoActualizado.folioAyto);
                    await this._planoDataMapper.ActualizarViviendasSembradoOficial(vivienda, proyectoActualizado.idProyecto, proyectoActualizado.folioSEPLADE, proyectoActualizado.folioAyto);
                }
            }
            catch (Exception e)
            {
                throw new ExcepcionSig("Error al actualizar sembrado en Oracle", e);
            }

            try
            {
                this._clienteTelemetria.TrackTrace("Validando tipología de las viviendas del proyecto.");
                string resultadoValidacionTipologia = await this._planoDataMapper.ValidarTipologia(proyectoActualizado.idProyecto);
                if (!string.IsNullOrEmpty(resultadoValidacionTipologia))
                {
                    throw new ExcepcionSig(resultadoValidacionTipologia);
                }
            }
            catch (Exception e)
            {
                throw new ExcepcionSig("Error al validar tipologia en Oracle", e);
            }
        }

        private async Task<List<Vivienda>> GuardarActualizarViviendasProyecto(Proyecto proyectoNoModificado, Proyecto proyectoActualizado, CustomUserRuv usuario, bool esActualizacionProyecto = false)
        {
            List<Vivienda> viviendasNuevas = new List<Vivienda>();
            List<Vivienda> listaViviendasCompletas = new List<Vivienda>();
            List<int> listaIdViviendas = new List<int>();

            if (esActualizacionProyecto)
            {
                List<Vivienda> viviendasTotales = new List<Vivienda>();
                List<Vivienda> viviendasGuardadas = proyectoNoModificado.sembrado.viviendas;
                List<Vivienda> viviendasModificadas = proyectoActualizado.sembrado.viviendas;

                List<Vivienda> viviendasPorAgregar = viviendasModificadas.Except(viviendasGuardadas, new ComparadorViviendas()).ToList();
                List<Vivienda> viviendasPorActualizar = viviendasModificadas.Intersect(viviendasGuardadas, new ComparadorViviendas()).ToList();

                viviendasTotales.AddRange(viviendasPorAgregar);
                viviendasTotales.AddRange(viviendasPorActualizar);

                if (viviendasPorAgregar != null && viviendasPorAgregar.Any())
                {
                    var viviendaCompleta = new Vivienda();
                    var vialidades = await this._servicioProyectosCatalogos.ObtenerVialidadesFiltradoAsync(proyectoActualizado.sembrado.viviendas[0].domicilioGeografico.idEstado ?? "", proyectoActualizado.sembrado.viviendas[0].domicilioGeografico.idmunicipio ?? "");
                    var asentamientos = await this._servicioProyectosCatalogos.ObtenerAsentamientosFiltradoAsync(proyectoActualizado.sembrado.viviendas[0].domicilioGeografico.idEstado ?? "", proyectoActualizado.sembrado.viviendas[0].domicilioGeografico.idmunicipio ?? "");

                    this._clienteTelemetria.TrackTrace("Agregando viviendas nuevas en SQL.");
                    foreach (var viviendaNueva in viviendasPorAgregar)
                    {
                        viviendaNueva.domicilioGeografico.idPeriodo = 4;
                        viviendaNueva.domicilioGeografico.idTipoDomicilioRUV = 3;
                        viviendaNueva.domicilioGeografico.idTipoDomicilioINEGI = 3;
                        viviendaNueva.domicilioGeografico.xmlSIG = "";
                        viviendaNueva.idViviendaPlanoSIG = viviendaNueva.idVivienda;

                        var tmp = await this.ObtenerDatosComplementariosVivienda(vialidades, asentamientos, viviendaNueva);
                        viviendaCompleta = tmp.Item1;
                        vialidades = tmp.Item2;
                        asentamientos = tmp.Item3;

                        var idDomicilioGeografico = await this._proyectoDM.GuardarDomicilioGeograficoAsync(viviendaCompleta.domicilioGeografico);

                        viviendaCompleta.idDomicilioGeografico = idDomicilioGeografico;
                        viviendaCompleta.idEstatusVivienda = null;
                        viviendaCompleta.idProyecto = proyectoActualizado.idProyecto;

                        await this._proyectoDM.GuardarViviendaAsync(viviendaCompleta);

                        viviendaCompleta.domicilioCarreteraCamino.idDomicilioGeografico = idDomicilioGeografico;
                        viviendaCompleta.domicilioCarreteraCamino.nombreVialidad = viviendaCompleta.domicilioGeografico.nombreVialidadPrincipal;

                        if (viviendaCompleta.domicilioGeografico.tipoVialidadP == 22)
                        {
                            await this._proyectoDM.GuardarDomicilioCarretera(viviendaCompleta.domicilioCarreteraCamino);
                        }
                        else if (viviendaCompleta.domicilioGeografico.tipoVialidadP == 24)
                        {
                            await this._proyectoDM.GuardarDomicilioCamino(viviendaCompleta.domicilioCarreteraCamino);
                        }

                        listaViviendasCompletas.Add(viviendaCompleta);
                    }
                }
                this._clienteTelemetria.TrackTrace("Actualizando viviendas existentes en SQL.");
                //Actualizar viviendas existentes
                var listaViviendasActualizadas = await this.ActualizarViviendasAsync(new Sembrado() { viviendas = viviendasPorActualizar }, proyectoActualizado.idProyecto, proyectoActualizado.folioSEPLADE, proyectoActualizado.folioAyto);

                //Eliminar Viviendas
                this._clienteTelemetria.TrackTrace("Borrando viviendas eliminadas.");
                foreach (var vivienda in proyectoActualizado.sembrado.viviendasEliminadas)
                {
                    if (viviendasGuardadas.Where(v => v.featID == vivienda.featID).Count() > 0)
                    {
                        await this._proyectoDM.EliminarVivienda(vivienda.featID);
                    }
                }
                using (var transaccionHistoricos = new TransactionScope(TransactionScopeOption.RequiresNew, this._opcionesTransaccion, TransactionScopeAsyncFlowOption.Enabled))
                {
                    this._clienteTelemetria.TrackTrace("Guardando históricos de las viviendas.");
                    for (var i = 0; i < viviendasPorAgregar.Count; i++)
                    {
                        //Guardado de histórico CUV "Actualización" Nuevas
                        EstatusViviendas estatusVivienda = new EstatusViviendas();
                        estatusVivienda.cuv = viviendasPorAgregar[i].cuv;
                        estatusVivienda.estatusCUV = (int)EstatusVivienda.EnProcesoVerficacion;
                        estatusVivienda.identificadorVivienda = viviendasPorAgregar[i].idVivienda;
                        estatusVivienda.idEvento = (int)EventoCUV.Actualizacion;
                        estatusVivienda.idUsuario = (int)usuario.IdUsuario;
                        estatusVivienda.nombreUsuario = usuario.NombreUsuario;
                        List<DetalleEstatusVivienda> listaCamposAdicionales = new List<DetalleEstatusVivienda>();
                        DetalleEstatusVivienda campoAdicional = new DetalleEstatusVivienda((int)EstatusVivienda.EnProcesoVerficacion, EnumCampoAdicional.UsuarioRealizaActualizacion, usuario.NombreUsuario);
                        listaCamposAdicionales.Add(campoAdicional);
                        await this._servicioHistorico.GuardarHistoricoVivienda(estatusVivienda, listaCamposAdicionales);
                    }

                    for (var i = 0; i < viviendasPorActualizar.Count; i++)
                    {
                        //Guardado de histórico CUV "Actualización" Actualizadas
                        EstatusViviendas estatusVivienda = new EstatusViviendas();
                        estatusVivienda.cuv = viviendasPorActualizar[i].cuv;
                        estatusVivienda.estatusCUV = (int)EstatusVivienda.EnProcesoVerficacion;
                        estatusVivienda.identificadorVivienda = viviendasPorActualizar[i].idVivienda;
                        estatusVivienda.idEvento = (int)EventoCUV.Actualizacion;
                        estatusVivienda.idUsuario = (int)usuario.IdUsuario;
                        estatusVivienda.nombreUsuario = usuario.NombreUsuario;
                        List<DetalleEstatusVivienda> listaCamposAdicionales = new List<DetalleEstatusVivienda>();
                        DetalleEstatusVivienda campoAdicional = new DetalleEstatusVivienda((int)EstatusVivienda.EnProcesoVerficacion, EnumCampoAdicional.UsuarioRealizaActualizacion, usuario.NombreUsuario);
                        listaCamposAdicionales.Add(campoAdicional);
                        await this._servicioHistorico.GuardarHistoricoVivienda(estatusVivienda, listaCamposAdicionales);
                    }

                    for (var i = 0; i < proyectoActualizado.sembrado.viviendasEliminadas.Count; i++)
                    {
                        //Guardado de histórico CUV "Actualización" Eliminadas
                        EstatusViviendas estatusVivienda = new EstatusViviendas();
                        estatusVivienda.cuv = proyectoActualizado.sembrado.viviendasEliminadas[i].cuv;
                        estatusVivienda.estatusCUV = (int)EstatusVivienda.EnProcesoVerficacion;
                        estatusVivienda.identificadorVivienda = proyectoActualizado.sembrado.viviendasEliminadas[i].idVivienda;
                        estatusVivienda.idEvento = (int)EventoCUV.Actualizacion;
                        estatusVivienda.idUsuario = (int)usuario.IdUsuario;
                        estatusVivienda.nombreUsuario = usuario.NombreUsuario;
                        List<DetalleEstatusVivienda> listaCamposAdicionales = new List<DetalleEstatusVivienda>();
                        DetalleEstatusVivienda campoAdicional = new DetalleEstatusVivienda((int)EstatusVivienda.EnProcesoVerficacion, EnumCampoAdicional.UsuarioRealizaActualizacion, usuario.NombreUsuario);
                        listaCamposAdicionales.Add(campoAdicional);
                        await this._servicioHistorico.GuardarHistoricoVivienda(estatusVivienda, listaCamposAdicionales);
                    }

                    transaccionHistoricos.Complete();
                }
            }
            else
            {
                var viviendaCompleta = new Vivienda();

                var vialidades = await this._servicioProyectosCatalogos.ObtenerVialidadesFiltradoAsync(proyectoActualizado.sembrado.viviendas[0].domicilioGeografico.idEstado ?? "", proyectoActualizado.sembrado.viviendas[0].domicilioGeografico.idmunicipio ?? "");
                var asentamientos = await this._servicioProyectosCatalogos.ObtenerAsentamientosFiltradoAsync(proyectoActualizado.sembrado.viviendas[0].domicilioGeografico.idEstado ?? "", proyectoActualizado.sembrado.viviendas[0].domicilioGeografico.idmunicipio ?? "");

                foreach (var vivienda in proyectoActualizado.sembrado.viviendas)
                {
                    vivienda.domicilioGeografico.idPeriodo = 4;
                    vivienda.domicilioGeografico.idTipoDomicilioRUV = 3;
                    vivienda.domicilioGeografico.idTipoDomicilioINEGI = 3;
                    vivienda.domicilioGeografico.xmlSIG = "";
                    vivienda.idViviendaPlanoSIG = vivienda.idVivienda;

                    var tmp = await this.ObtenerDatosComplementariosVivienda(vialidades, asentamientos, vivienda);
                    viviendaCompleta = tmp.Item1;
                    vialidades = tmp.Item2;
                    asentamientos = tmp.Item3;

                    var idDomicilioGeografico = await this._proyectoDM.GuardarDomicilioGeograficoAsync(viviendaCompleta.domicilioGeografico);

                    viviendaCompleta.idDomicilioGeografico = idDomicilioGeografico;
                    viviendaCompleta.idEstatusVivienda = null;
                    viviendaCompleta.idProyecto = proyectoActualizado.idProyecto;

                    int idVivienda = await this._proyectoDM.GuardarViviendaAsync(viviendaCompleta);
                    listaIdViviendas.Add(idVivienda);

                    viviendaCompleta.domicilioCarreteraCamino.idDomicilioGeografico = idDomicilioGeografico;
                    viviendaCompleta.domicilioCarreteraCamino.nombreVialidad = viviendaCompleta.domicilioGeografico.nombreVialidadPrincipal;

                    if (viviendaCompleta.domicilioGeografico.tipoVialidadP == 22)
                    {
                        await this._proyectoDM.GuardarDomicilioCarretera(viviendaCompleta.domicilioCarreteraCamino);
                    }
                    else if (viviendaCompleta.domicilioGeografico.tipoVialidadP == 24)
                    {
                        await this._proyectoDM.GuardarDomicilioCamino(viviendaCompleta.domicilioCarreteraCamino);
                    }

                    listaViviendasCompletas.Add(viviendaCompleta);
                }

                using (var transaccionHistoricos = new TransactionScope(TransactionScopeOption.RequiresNew, this._opcionesTransaccion, TransactionScopeAsyncFlowOption.Enabled))
                {
                    for (var i = 0; i < proyectoActualizado.sembrado.viviendas.Count; i++)
                    {
                        //Guardado de histórico CUV "Registro de Proyecto"
                        EstatusViviendas estatusVivienda = new EstatusViviendas();
                        estatusVivienda.cuv = null;
                        estatusVivienda.estatusCUV = (int)EstatusVivienda.Inactiva;
                        estatusVivienda.identificadorVivienda = listaIdViviendas[i];
                        estatusVivienda.idEvento = (int)EventoCUV.RegistroDeProyecto;
                        estatusVivienda.idUsuario = (int)usuario.IdUsuario;
                        estatusVivienda.nombreUsuario = usuario.NombreUsuario;
                        List<DetalleEstatusVivienda> listaCamposAdicionales = new List<DetalleEstatusVivienda>();
                        DetalleEstatusVivienda campoAdicional = new DetalleEstatusVivienda((int)EstatusVivienda.Inactiva, EnumCampoAdicional.IdProyecto, proyectoActualizado.idProyecto.ToString());
                        listaCamposAdicionales.Add(campoAdicional);
                        await this._servicioHistorico.GuardarHistoricoVivienda(estatusVivienda, listaCamposAdicionales);
                    }

                    //Guardado histórico Proyecto "Solicitud a Validación"
                    await GuardarHistoricoProyectoAsync(EventoProyecto.SolicitudAValidacion, proyectoActualizado.idProyecto, "Solicitud A Validación", usuario);

                    transaccionHistoricos.Complete();
                }
            }
            return listaViviendasCompletas;
        }

        private async Task<bool> GuardarActualizarDictaminacionAsync(short idServicio, int idOrdenTrabajo, int idRegistro, ParametroBodyDictaminacion parametrosDictaminacion)
        {
            bool dictaminacionGuardada = false;

            if (parametrosDictaminacion.seActualizaDictaminacion)
            {
                dictaminacionGuardada = await this._servicioDictaminacion.ActualizarDictaminacionAsync(idServicio, idOrdenTrabajo, idRegistro, parametrosDictaminacion.DictaminacionJSON);
            }
            else
            {
                dictaminacionGuardada = await this._servicioDictaminacion.GuardarDictaminacionAsync(idServicio, idOrdenTrabajo, idRegistro, parametrosDictaminacion.DictaminacionJSON);
            }

            return dictaminacionGuardada;
        }

        /// <summary>
        /// Obtiene las viviendas por medio del id del proyecto
        /// </summary>
        /// <param name="idProyecto"> id del proyecto</param>
        /// <returns>regresa una lista de las viviendas del proyecto</returns>
        public async Task<List<Vivienda>> ObtenerVivendasPorIdProyectoAsync(int idProyecto)
        {
            List<Vivienda> viviendasOferta = await this._ofertaDataMapper.ObtenerVivendasPorIdProyectoAsync(idProyecto, null);
            return viviendasOferta;
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="idProyecto"></param>
        /// <returns></returns>
        public async Task<ResultadoPaginado<List<Modelo.OrdenVerificacion.Data.OrdenVerificacion>>> ObtenerOrdenesVerificacionPorIdProyectoAsync(int tamanioPagina, int pagina, int idProyecto)
        {
            var ofertas = await _ofertaDataMapper.ObtenerOfertasxProyectoAsync(idProyecto);

            var resultado = new ResultadoPaginado<List<Modelo.OrdenVerificacion.Data.OrdenVerificacion>>();

            if (ofertas != null && ofertas.Count() > 0)
            {
                string clavesOferta = "";
                foreach (var item in ofertas)
                {
                    clavesOferta += "'" + item.claveOfertaVivienda + "',";
                }

                var listaOrdenes = await this._servicioOrdenVerificacion.ObtenerOrdenesVerificacionPorIdOfertaPaginadorAsync(tamanioPagina, pagina, clavesOferta != null ? clavesOferta.Substring(0, clavesOferta.Length - 1) : null, null, null);                

                resultado = listaOrdenes;
            }

            return resultado;
        }

        #endregion EnvioDictaminacion

        #region Notificaciones

        /// <summary>
        /// Envia un correo electronico informando envio de datos a VyD
        /// </summary>
        /// <param name="idEmpresa">idEmpresa que registra el proyecto</param>
        /// <param name="proyecto">Proyecto a dictaminar</param>
        /// <param name="odt">Orden de trabajo generada</param>
        /// /// <returns></returns>
        public async Task EnviarCorreoOdtGenerada(int idEmpresa, Proyecto proyecto, OrdenBase odt, CustomUserRuv usuario)
        {
            MensajeDto mensaje = null;
            EmpresaDto empresa = null;
            List<string> correo = new List<string>();

            correo = this._servicioEmpresaComun.ObtenerCorreosContacto(idEmpresa).ToList();
            empresa = this._servicioEmpresaConsulta.ObtenerEmpresaDto(idEmpresa);

            Dictionary<string, string> parametrosCorreo = new Dictionary<string, string>
                {
                    {"0", String.Format("{0} {1} {2}", empresa.nombreRazonSocial, empresa.apellidoPaterno, empresa.apellidoMaterno)},
                    {"1", empresa.rfc},
                    {"2", empresa.IdEmpresaInst},
                    {"3", usuario.IdUsuario.ToString()},
                    {"4", usuario.NombreUsuario},
                    {"5", proyecto.nombre },
                    {"6", proyecto.idProyecto.ToString("D6") },
                    {"7", DateTime.Now.ToString("dd-MM-yyyy") }
                };

            mensaje = new MensajeDto(String.Format(AsuntoCorreoRegistroFinalizado, proyecto.nombre),
                correo,
                eTipoCorreo.ContenidoHtmlMailNuevaOdtProyectos,
                eTipoMensaje.InformativoComunicacion,
                null,
                parametrosCorreo);

            _servicioNotificaciones.AgregarNotificacion(mensaje);
        }

        /// <summary>
        /// Envia un correo electronico informando dictaminacion aceptada
        /// </summary>
        /// <param name="proyecto">Proyecto dictaminado</param>
        /// <returns></returns>
        public async Task EnviarCorreoProyectoAceptado(Proyecto proyecto)
        {
            MensajeDto mensaje = null;
            EmpresaDto empresa = null;
            List<string> correo = new List<string>();

            correo = this._servicioEmpresaComun.ObtenerCorreosContacto(proyecto.idEmpresa).ToList();
            empresa = this._servicioEmpresaConsulta.ObtenerEmpresaDto(proyecto.idEmpresa);

            Dictionary<string, string> parametrosCorreo = new Dictionary<string, string>
                {
                    {"0", proyecto.nombre },
                    {"1", DateTime.Now.ToString(FormatoFecha) }
                };

            mensaje = new MensajeDto(String.Format(AsuntoCorreoProyectoAceptado, proyecto.nombre),
                correo,
                eTipoCorreo.ContenidoHtmlMailAceptadoProyectos,
                eTipoMensaje.InformativoComunicacion,
                null,
                parametrosCorreo);

            _servicioNotificaciones.AgregarNotificacion(mensaje);
        }

        /// <summary>
        /// Envia un correo electronico informando dictaminacion rechazada
        /// </summary>
        /// <param name="proyecto">Proyecto dictaminado</param>
        /// <param name="idOrdenTrabajo">Orden de trabajo generada</param>
        /// <param name="usuario">Datos del usuario</param>
        /// <returns></returns>
        public async Task EnviarCorreoProyectoRechazado(Proyecto proyecto, int idOrdenTrabajo, CustomUserRuv usuario)
        {
            MensajeDto mensaje = null;
            EmpresaDto empresa = null;
            List<UsuarioCorreoDto> usuariosCorreo = new List<UsuarioCorreoDto>();
            string motivosRechazoCorreo = "";
            Dictionary<string, string> parametrosCorreo;

            motivosRechazoCorreo = await this._servicioDictaminacion.ObtenerMotivosRechazoCorreo((int)ServicioProducto.AltaUbicacion, proyecto.idProyecto, idOrdenTrabajo);

            usuariosCorreo = this._servicioEmpresaComun.ObtenerCorreosContactoConDetalle(proyecto.idEmpresa).ToList();

            empresa = this._servicioEmpresaConsulta.ObtenerEmpresaDto(proyecto.idEmpresa);

            foreach (var usuarioCorreo in usuariosCorreo)
            {
                parametrosCorreo = new Dictionary<string, string>
                {
                    //{"0", String.Format("{0} {1} {2}",usuarioCorreo.nombreRazonSocial, usuarioCorreo.apellidoPaterno, usuarioCorreo.apellidoMaterno) },
                    {"0", obtenerNombreUsuario(usuarioCorreo)  },
                    {"1", proyecto.nombre},
                    {"2", proyecto.idProyecto.ToString("D6") },
                    {"3", obtenerNombreEmpresa(empresa) },
                    {"4", empresa.IdEmpresaInst },
                    {"5", motivosRechazoCorreo }
                };

                mensaje = new MensajeDto(String.Format(AsuntoCorreoProyectoRechazado, proyecto.nombre),
                    new List<string>() { usuarioCorreo.correoElectronico },
                    eTipoCorreo.ContenidoHtmlMailRechazadoProyectos,
                    eTipoMensaje.InformativoComunicacion,
                    null,
                    parametrosCorreo);

                _servicioNotificaciones.AgregarNotificacion(mensaje);
            }
        }

        #endregion Notificaciones

        #region Metodos de ayuda

        /// <summary>
        /// Genera una lista de tipo MotivosRechazo con la informacion obtenida del json de dictaminaciones
        /// </summary>
        /// <param name="seccionesFinales"></param>
        /// <param name="elementos">Total de elementos contenidos en las secciones obtenidos del json de configuracion</param>
        /// <param name="seccionesDictaminacion">Total de secciones contenidos en el json de ocnfiguracion</param>
        /// <param name="listaMotivosRechazo"></param>
        /// <returns>Lista de motivos rechazo</returns>
        private async Task<List<MotivoRechazo>> ConstruirListaMotivosRechazo(List<ProyectoConfiguracion.Seccion> seccionesFinales, List<ProyectoConfiguracion.Elemento> elementos, List<DictaminacionSeccion> seccionesDictaminacion, List<MotivoRechazo> listaMotivosRechazo)
        {
            seccionesDictaminacion.ForEach(async s =>
            {
                if (s.mensajeRechazo != null && s.mensajeRechazo.Length > 0)
                {
                    listaMotivosRechazo.Add(new MotivoRechazo()
                    {
                        consecutivo = listaMotivosRechazo.Count + 1,
                        secciones = seccionesFinales.Where(sf => sf.idSeccion == s.idSeccion).FirstOrDefault().nombre.ToString(),
                        elemento = null,
                        nombreElemento = null,
                        indice = null,
                        mensajeRechazo = s.mensajeRechazo,
                        idSeccion = s.idSeccion,
                        estActualizado = s._actualizaEstatusPanel != null ? s._actualizaEstatusPanel : false,
                        estaActivo = false
                    });
                }
                if (s.elementos != null && s.elementos.Count > 0)
                {
                    s.elementos.ForEach(c =>
                    {
                        if (c.mensajeRechazo != null && c.mensajeRechazo.Length > 0)
                        {
                            listaMotivosRechazo.Add(new MotivoRechazo()
                            {
                                consecutivo = listaMotivosRechazo.Count + 1,
                                secciones = seccionesFinales.Where(sf => sf.idSeccion == s.idSeccion).FirstOrDefault().nombre.ToString(),
                                elemento = c.idElemento,
                                nombreElemento = elementos.Where(e => e.idElemento == c.idElemento).FirstOrDefault().nombre.ToString(),
                                indice = c.indice,
                                mensajeRechazo = c.mensajeRechazo,
                                idSeccion = s.idSeccion,
                                estActualizado = c._actualizaEstatusPanel != null ? c._actualizaEstatusPanel : false,
                                estaActivo = false
                            });
                        }
                    });
                }

                if (s.secciones != null && s.secciones.Count > 0)
                {
                    await ConstruirListaMotivosRechazo(seccionesFinales, elementos, s.secciones, listaMotivosRechazo);
                }
            });

            return listaMotivosRechazo;
        }

        /// <summary>
        /// Genera una cadena que contiene el codigo html para generar la tabla de motivos de rechazo en el correo
        /// </summary>
        /// <param name="motivosRechazo">Lista de motivos de rechazo a renderizar</param>
        /// <returns>Cadena con el codigo html generado</returns>
        private async Task<string> GenerarListaHtmlMotivosRechazo(List<MotivoRechazo> motivosRechazo)
        {
            StringBuilder html = new StringBuilder();

            if (motivosRechazo != null && motivosRechazo.Any())
            {
                html.Append(@"<table>
                            <thead>
                                <tr><th> No.</th><th> Sección </th><th> Campo </th><th> Motivo </th></tr>
                            </thead>
                            <tbody class='modal-body'>");

                foreach (MotivoRechazo motivo in motivosRechazo)
                {
                    html.Append(@"<tr><td>");
                    html.Append(motivo.consecutivo);
                    html.Append("</td><td>");
                    html.Append(motivo.secciones != null ? motivo.secciones : "");
                    html.Append("</td><td>");
                    html.Append(motivo.nombreElemento != null ? motivo.elemento : "");
                    html.Append("</td><td>");
                    html.Append(motivo.mensajeRechazo != null ? motivo.mensajeRechazo : "");
                    html.Append("</td><td>");
                }

                html.Append("</tbody></table>");
            }
            else
                html.Append("Sin motivos de rechazo");

            return html.ToString();
        }

        /// <summary>
        /// Obtiene el total de secciones cpntenidos ene le json de configuración
        /// </summary>
        /// <param name="seccion">Lista de secciones que contienen a su vez otras secciones</param>
        /// <param name="seccionesFinales">Lista de secciones finales</param>
        /// <returns>Lista con el total de secciones</returns>
        private List<ProyectoConfiguracion.Seccion> ObtenerSecciones(List<ProyectoConfiguracion.Seccion> seccion, List<ProyectoConfiguracion.Seccion> seccionesFinales)
        {
            seccion.ForEach(s =>
            {
                if (s.secciones.Any())
                {
                    ObtenerSecciones(s.secciones, seccionesFinales);
                }
                else
                {
                    seccionesFinales.Add(s);
                }
            });

            return seccionesFinales;
        }

        /// <summary>
        /// Obtiene el total de controles contenidos en el json de configuracion
        /// </summary>
        /// <param name="seccion">Lista de secciones que contienen los contoles</param>
        /// <param name="elementos">Lista de elementos a obtener</param>
        /// <returns>Lista con el total de controles de cada seccion</returns>
        private List<ProyectoConfiguracion.Elemento> ObtenerElementos(List<ProyectoConfiguracion.Seccion> seccion, List<ProyectoConfiguracion.Elemento> elementos)
        {
            seccion.ForEach(s =>
            {
                if (s.secciones.Any())
                {
                    ObtenerElementos(s.secciones, elementos);
                }
                else
                {
                    if (s.elementos != null)
                    {
                        s.elementos.ForEach(e =>
                        {
                            elementos.Add(e);
                        });
                    }
                }
            });

            return elementos;
        }

        private async Task<string> ObtenerRutaArchivoSDF(int idArchivoSDF)
        {
            string rutaArchivoBlob = string.Empty;
            string rutaArchivoTemporal = string.Empty;
            ServicioDocumentoOferta servicioDocumento = new ServicioDocumentoOferta();
            var archivos = servicioDocumento.Obtener(new DocumentoOferta() { idDocumento = idArchivoSDF });
            rutaArchivoBlob = archivos.First().rutaArchivo;
            rutaArchivoTemporal = HttpContext.Current.Server.MapPath("~/App_Data/SDF_Temporal/" + archivos.First().nombreArchivo);
            using (HttpClient cliente = new HttpClient())
            {
                using (Stream response = await cliente.GetStreamAsync(rutaArchivoBlob))
                {
                    using (FileStream file = new FileStream(rutaArchivoTemporal, FileMode.Create))
                    {
                        response.CopyTo(file);
                    }
                }
            }
            return rutaArchivoTemporal;
        }

        private async Task<bool> ValidaBloqueoCUVS(int idProyecto, int codigoRespuestaEjecutable = 0)
        {
            bool resultado = true;
            if (activarConvivencia == 1 && codigoRespuestaEjecutable == 0)
            {
                try
                {
                    List<string> listaCUVS = await this._planoDataMapper.ObtenerViviendasConCUVASIS(idProyecto);
                    Dictionary<string, string> listaEstatusCUVS = new Dictionary<string, string>();
                    List<ValidarCUVASIS> listaValidarCUV = new List<ValidarCUVASIS>();

                    if (listaCUVS.Count > 0)
                    {
                        var listaCUVSASIS = new List<ValidarCUVASIS>();
                        var listaRespuestaCUVS = new List<RespuestaValidarCUVASIS>();
                        var listaRespuestaCUVSTotales = new List<RespuestaValidarCUVASIS>();
                        var listaViviendasDivididas = await this.obtenerListaViviendasDivididas(listaCUVS);

                        foreach (var item in listaViviendasDivididas)
                        {
                            listaCUVSASIS = await this.obtenerListaCUVSASISPlano(item);

                            listaRespuestaCUVS = await this._agenteServicioOfertaASIS.LlamarOperacion<List<ValidarCUVASIS>, List<RespuestaValidarCUVASIS>>(listaCUVSASIS, ConfigurationManager.AppSettings["UrlValidarCUV"]);

                            listaRespuestaCUVSTotales.AddRange(listaRespuestaCUVS);
                        }

                        if (listaRespuestaCUVSTotales.Any() && listaRespuestaCUVSTotales.FirstOrDefault().parametro != null)
                        {
                            foreach (var cuv in listaRespuestaCUVSTotales)
                            {
                                if (cuv.clave == 1)
                                {
                                    listaEstatusCUVS.Add(cuv.parametro, "0");
                                }
                                else
                                {
                                    listaEstatusCUVS.Add(cuv.parametro, "1");
                                }
                            }
                        }

                        if (listaEstatusCUVS.Count > 0)
                        {
                            if (!await this._planoDataMapper.ActualizarEstatusCUV(listaEstatusCUVS))
                            {
                                resultado = false;
                                this._clienteTelemetria.TrackException(new Exception(string.Format("Error al actualizar el estatus de la CUV_ASIS en Oracle. ID Proyecto: {0}", idProyecto)));
                                System.Threading.Thread.Sleep(1000);

                                throw new Exception(string.Format("Error al actualizar el estatus de la CUV_ASIS en Oracle. ID Proyecto: {0}", idProyecto));
                            }
                        }
                    }
                }
                catch (Exception e)
                {
                    resultado = false;
                    this._clienteTelemetria.TrackException(e);
                    System.Threading.Thread.Sleep(1000);

                    codigoRespuestaEjecutable = -4;
                    throw;
                }
            }
            return resultado;
        }

        /// <summary>
        /// Obtiene una lista de lista de cuvs.
        /// </summary>
        /// <param name="viviendas"></param>
        /// <returns></returns>
        private async Task<List<ValidarCUVASIS>> obtenerListaCUVSASISPlano(List<string> viviendas)
        {
            var listaViviendas = new List<ValidarCUVASIS>();

            foreach (var item in viviendas)
            {
                listaViviendas.Add(new ValidarCUVASIS() { cuv = item });
            }

            return listaViviendas;
        }

        #endregion Metodos de ayuda

        #region CUVs

        /// <summary>
        /// Obtiene el resultado de la consulta de CUVS filtrada desde la BD del RUV++
        /// </summary>
        /// <param name="filtros">Objeto que contiene los siguientes filtros: IdProyecto, IdOferta, Cuv, IdEstatusJuridicoVivienda, IdEntidadFederativa, NombreFrente, NumeroRegistroRuv</param>
        /// <param name="tamanioPagina">Tamaño de la pagina</param>
        /// <param name="pagina">pagina consultada</param>
        public async Task<ResultadoPaginado<List<ConsultaCuv>>> ObtenerCuvsFiltradasPaginadasAsync(FiltrosConsultaCuv filtros, int pagina, int tamanioPagina)
        {
            ResultadoPaginado<List<ConsultaCuv>> resultado = new ResultadoPaginado<List<ConsultaCuv>>()
            {
                TamanioPagina = tamanioPagina,
                PaginaActual = pagina
            };

            var data = await this._proyectoDM.ObtenerCuvsFiltradasPaginadasAsync(filtros, tamanioPagina, pagina);

            if (data.Item1 > 0)
            {
                resultado.TotalRegistros = data.Item1;
                resultado.TotalPaginas = (int)Math.Ceiling((double)(resultado.TotalRegistros / tamanioPagina));
            }

            resultado.Resultado = data.Item2;

            return resultado;
        }

        #endregion CUVs

        /// <summary>
        /// Envía los parámetros introducidos al Procedimiento Almacenado
        /// correspondiente para insertarlos en la base de datos de histórico.
        /// </summary>
        /// <param name="idEvento">Enumerador del tipo de evento registrado.</param>
        /// <param name="idProyecto">Identificador del Proyecto.</param>
        /// <param name="estatusProyecto">Línea del tipo de evento registrado.</param>
        /// <param name="usuario">Clase Usuario con Identificador y Nombre.</param>
        /// <returns></returns>
        public async Task GuardarHistoricoProyectoAsync(EventoProyecto idEvento, int idProyecto, string estatusProyecto, CustomUserRuv usuario)
        {
            this._clienteTelemetria.TrackTrace("Guardando historico del proyecto.");
            EstatusProyectos estatusProyectos = new EstatusProyectos();
            estatusProyectos.idEvento = (int)idEvento;
            estatusProyectos.idProyecto = idProyecto;
            estatusProyectos.estatusProyecto = estatusProyecto;
            estatusProyectos.idUsuario = (int)usuario.IdUsuario;
            estatusProyectos.nombreUsuario = usuario.NombreUsuario;
            await this._servicioHistorico.GuardarHistoricoProyecto(estatusProyectos);
        }

        public async Task<EquipamientosProyecto> ObtenerEquipamientosProyecto(int idProyecto, bool esTemporal = false)
        {
            EquipamientosProyecto equipamientosProyecto = new EquipamientosProyecto();

            equipamientosProyecto = await this._planoDataMapper.ObtenerListaEquipamientosProyecto(idProyecto, esTemporal);

            return equipamientosProyecto;
        }

        /// <summary>
        /// Obtiene el resultado de la consulta de CUVS filtrada y paginada
        /// </summary>
        /// <param name="filtros">Objeto que contiene los siguientes filtros: IdProyecto, IdOferta, Cuv, IdEstatusJuridicoVivienda, IdEntidadFederativa, NombreFrente, NumeroRegistroRuv</param>
        public async Task<List<ConsultaCuv>> ObtenerCuvsFiltradasAsync(FiltrosConsultaCuv filtros)
        {
            var resultado = await this._proyectoDM.ObtenerCuvsFiltradasAsync(filtros);
            return resultado;
        }

        #endregion Metodos

        #region Metodos sobreescritos

        protected override void DisposeManagedResources()
        {
            this._planoDataMapper.Dispose();
            this._proyectoDM.Dispose();
            this._servicioDictaminacion.Dispose();
            this._servicioEmpresa.Dispose();
            this._servicioProyectosCatalogos.Dispose();
            this._servicioHistorico.Dispose();
        }

        #endregion Metodos sobreescritos

        #region Metodos Consulta

        /// <summary>
        /// Obtiene el listado de ordenes de trabajo para un proyecto por idProyecto paginado
        /// </summary>
        /// <param name="idProyecto">Identificador del proyecto</param>
        /// <param name="tamanioPagina">Tamaño de la pagina</param>
        /// <param name="pagina">Pagina</param>
        /// <returns></returns>
        public async Task<ResultadoPaginado<List<OrdenTrabajoHistoricoValidacion>>> ObtenerOrdenesTrabajoPorProyectoPaginadoAsync(int idProyecto, int tamanioPagina, int pagina)
        {
            ResultadoPaginado<List<OrdenTrabajoHistoricoValidacion>> resultado = new ResultadoPaginado<List<OrdenTrabajoHistoricoValidacion>>()
            {
                TamanioPagina = tamanioPagina,
                PaginaActual = pagina
            };

            var data = await this._proyectoDM.ObtenerOrdenesTrabajoPorProyectoPaginadoAsync(idProyecto, tamanioPagina, pagina);

            if (data.Item1 > 0)
            {
                resultado.TotalRegistros = data.Item1;
                resultado.TotalPaginas = (int)Math.Ceiling((double)(resultado.TotalRegistros / tamanioPagina));
            }

            resultado.Resultado = data.Item2;

            return resultado;
        }

        public async Task<ResultadoPaginado<List<Proyecto>>> ObtenerProyectosPorIdEmpresaConPaginadorAsync(int tamanioPagina, int pagina, int idEmpresa)
        {
            ResultadoPaginado<List<Proyecto>> resultado = new ResultadoPaginado<List<Proyecto>>()
            {
                TamanioPagina = tamanioPagina,
                PaginaActual = pagina
            };

            var data = await this._proyectoDM.ObtenerProyectosPorIdEmpresaConPaginadorAsync(tamanioPagina, pagina, idEmpresa);

            if (data.Item1 > 0)
            {
                resultado.TotalRegistros = data.Item1;
                resultado.TotalPaginas = (int)Math.Ceiling((double)(resultado.TotalRegistros / tamanioPagina));
            }

            resultado.Resultado = data.Item2;

            return resultado;
        }

        /// <summary>
        /// Obtiene el listado de viviendas para un proyecto por idProyecto paginado
        /// </summary>
        /// <param name="idProyecto">Identificador del proyecto</param>
        /// <param name="tamanioPagina">Tamaño de la pagina</param>
        /// <param name="pagina">Pagina</param>
        /// <returns></returns>
        public async Task<ResultadoPaginado<List<Vivienda>>> ObtenerViviendasPorProyectoPaginadoAsync(int idProyecto, int tamanioPagina, int pagina)
        {
            ResultadoPaginado<List<Vivienda>> resultado = new ResultadoPaginado<List<Vivienda>>()
            {
                TamanioPagina = tamanioPagina,
                PaginaActual = pagina
            };

            var data = await this._proyectoDM.ObtenerVivienasPorProyectoPaginadoAsync(idProyecto, tamanioPagina, pagina);

            if (data.Item1 > 0)
            {
                resultado.TotalRegistros = data.Item1;
                resultado.TotalPaginas = (int)Math.Ceiling((double)(resultado.TotalRegistros / tamanioPagina));
            }

            resultado.Resultado = data.Item2;

            return resultado;
        }

        public async Task<Sembrado> ObtenerSembradoOficialTemporalAsync(int idProyecto, bool esConsulta)
        {
            var sembrado = await this._planoDataMapper.ObtenerSembradoOficialTemporal(idProyecto);
            var proyecto = await this._proyectoDM.ObtenerProyectoAsync(idProyecto);

            proyecto.sembrado = sembrado;
            proyecto.sembrado.viviendasEliminadas = new List<Vivienda>();

            if (!esConsulta)
            {
                if (ConfigurationManager.AppSettings["ActivarConvivenciaAsIs"] == "1")
                {
                    proyecto.sembrado.viviendas = await this.ObtenerEstatusViviendaASISXProyecto(proyecto);
                }

                if (proyecto.sembrado.viviendas.FirstOrDefault().cuv != null)
                {
                    var tmp = await this._proyectoDM.obtenerListaEstatusOfertaViviendas(proyecto.sembrado.viviendas.Select(v => v.cuv).ToList());

                    foreach (var vivienda in proyecto.sembrado.viviendas)
                    {
                        if (vivienda.cuv != null)
                        {
                            vivienda.idEstatusOferta = tmp.Find(v => v.cuv == vivienda.cuv).idEstatusOferta;
                            vivienda.cuv = vivienda.cuv + " ";
                        }
                    }
                }
            }

            return proyecto.sembrado;
        }

        public async Task<List<Vivienda>> ObtenerViviendasReportePorCUVAsync(string cuv)
        {
            return await this._proyectoDM.ObtenerViviendasPorCUVAsync(cuv);
        }

        #endregion Metodos Consulta

        #region Metodos de Ayuda

        /// <summary>
        /// Elimina una vivienda de la tabla temporal de Oracle
        /// </summary>
        /// <param name="featId"></param>
        /// <returns></returns>
        public async Task<bool> EliminarViviendaTemporal(int featId)
        {
            var resultado = false;

            await this._planoDataMapper.EliminarViviendaTemporal(featId);

            resultado = true;

            return resultado;
        }

        /// <summary>
        /// Elimina una vivienda de la tabla temporal de Oracle
        /// </summary>
        /// <param name="featId"></param>
        /// <returns></returns>
        public async Task<bool> EliminarViviendaOficial(int featId)
        {
            var resultado = false;

            using (var transaccionOracle = new TransactionScope(TransactionScopeOption.RequiresNew, TransactionScopeAsyncFlowOption.Enabled))
            {
                await this._planoDataMapper.EliminarVivienda(featId);
                transaccionOracle.Complete();
                resultado = true;
            }

            return resultado;
        }

        /// <summary>
        /// Construye uno objeto ViviendaActualizacionASIS a partir de una vivienda, para enviarlo al ASIS
        /// </summary>
        /// <param name="vivienda"></param>
        /// <returns></returns>
        private async Task<ViviendaActualizacionASIS> ObtenerViviendaASIS(Vivienda vivienda)
        {
            var viviendaActualizacionASIS = new ViviendaActualizacionASIS
            {
                idTipoOperacion = true,
                identificadorVivienda = vivienda.identificadorVivienda,
                idEstatusVivienda = vivienda.idEstatusVivienda.ToString(),
                idPrototipo = vivienda.idPrototipo,
                idOrientacionVivienda = vivienda.idOrientacionVivienda.ToString(),
                idDomicilioGeografico = vivienda.idDomicilioGeografico,
                numeroCatastralLote = vivienda.numeroCatastralLote,
                costo = vivienda.costo,
                metros2Lote = vivienda.metros2Lote,
                metrosFrenteLote = vivienda.metrosFrenteLote,
                fechaActualizacion = DateTime.Now.ToString("dd-MM-yyyy hh:mm:ss"),
                nombreCondominio = vivienda.nombreCondominio,
                edificio = vivienda.edificio,
                idNivelVivienda = vivienda.idNivelVivienda,
                cuv = vivienda.cuv,
                idOfertaVivienda = vivienda.idOfertaVivienda.ToString()
            };

            return viviendaActualizacionASIS;
        }

        /// <summary>
        /// Obtiene una lista de lista de cuvs a partir de una lista de viviendas del tamanio especificado.
        /// </summary>
        /// <param name="viviendas"></param>
        /// <returns></returns>
        private async Task<List<ValidarCUVASIS>> obtenerListaCUVSASIS(List<Vivienda> viviendas)
        {
            var listaViviendas = new List<ValidarCUVASIS>();

            foreach (var item in viviendas)
            {
                if (item.cuv != null)
                {
                    listaViviendas.Add(new ValidarCUVASIS() { cuv = item.cuv });
                }
            }

            return listaViviendas;
        }

        /// <summary>
        /// Obtiene una lista de lista de viviendas del tamanio especificado.
        /// </summary>
        /// <param name="viviendas"></param>
        /// <returns></returns>
        private async Task<List<List<T>>> obtenerListaViviendasDivididas<T>(List<T> viviendas)
        {
            int tamanioLista = Convert.ToInt32(ConfigurationManager.AppSettings["TamanioListaValidarCUV"]);

            List<List<T>> listaViviendas = new List<List<T>>();

            for (int i = 0; i < viviendas.Count; i += tamanioLista)
            {
                listaViviendas.Add(viviendas.GetRange(i, Math.Min(tamanioLista, viviendas.Count - i)));
            }

            return listaViviendas;
        }

        /// <summary>
        /// Obtiene el estatus de cada vivienda determinado por el ASIS para saber si se puede o no editar x Proyecto
        /// </summary>
        /// <param name="proyecto"></param>
        /// <returns></returns>
        public async Task<List<Vivienda>> ObtenerEstatusViviendaASISXProyecto(Proyecto proyecto)
        {
            if (proyecto.idEstatusProyecto == (int)EstatusProyecto.Aceptada
                || proyecto.idEstatusProyecto == (int)EstatusProyecto.RechazadaPorActualización
                || proyecto.idEstatusProyecto == (int)EstatusProyecto.Actualización)
            {
                var listaCUVS = new List<ValidarCUVASIS>();
                var respuesta = new List<RespuestaValidarCUVASIS>();
                var totalRespuesta = new List<RespuestaValidarCUVASIS>();
                var listaViviendasDivididas = await this.obtenerListaViviendasDivididas(proyecto.sembrado.viviendas);

                foreach (var viviendas in listaViviendasDivididas)
                {
                    listaCUVS = await this.obtenerListaCUVSASIS(viviendas);
                    var tmp = Newtonsoft.Json.JsonConvert.SerializeObject(listaCUVS);

                    if (listaCUVS.Any())
                    {
                        respuesta = await this._agenteServicioOfertaASIS.LlamarOperacion<List<ValidarCUVASIS>, List<RespuestaValidarCUVASIS>>(listaCUVS, ConfigurationManager.AppSettings["UrlValidarCUV"]);

                        totalRespuesta.AddRange(respuesta);
                    }
                }

                var vivienda = new Vivienda();
                foreach (var item in totalRespuesta)
                {
                    vivienda = proyecto.sembrado.viviendas.Where(v => v.cuv == item.parametro).FirstOrDefault();
                    vivienda.estatusASIS = item.clave;
                }
            }

            return proyecto.sembrado.viviendas;
        }

        /// <summary>
        /// Obtiene los datos complementarios de las viviendas a partir de la infomracion proporcionada del sembrado.
        /// </summary>
        /// <param name="vialidades"></param>
        /// <param name="asentamientos"></param>
        /// <param name="vivienda"></param>
        /// <param name="esNuevo"></param>
        /// <returns></returns>
        public async Task<Tuple<Vivienda, List<Vialidad>, List<Asentamiento>>> ObtenerDatosComplementariosVivienda(List<Vialidad> vialidades, List<Asentamiento> asentamientos, Vivienda vivienda, bool esNuevo = true)
        {
            var vialidadAgregada = new Vialidad();

            //vivienda.domicilioGeografico.idAsentamiento = vivienda.domicilioGeografico.idAsentamiento.Substring(0, 4);
            vivienda.domicilioGeografico.idLocalidad = vivienda.domicilioGeografico.idLocalidad.Substring(0, 4);

            vivienda.domicilioGeografico.cp = vivienda.domicilioGeografico.cp.Substring(0, 5);

            if (esNuevo)
            {
                //Asentamiento
                var asentamiento = asentamientos.Where(a => a.idEstado == vivienda.domicilioGeografico.idEstado
                                        && a.idmunicipio == vivienda.domicilioGeografico.idmunicipio
                                        && a.idLocalidad == vivienda.domicilioGeografico.idLocalidad
                                        && a.nombreAsentamiento.ToUpper() == vivienda.domicilioGeografico.nombreAsentamiento.ToUpper()
                                        && a.idTipoAsentamiento == vivienda.idTipoAsentamiento).ToList();

                if (asentamiento.Count() > 0)
                {
                    vivienda.domicilioGeografico.idAsentamiento = asentamiento.FirstOrDefault().idAsentamiento;
                }
                else
                {
                    var nuevoAsentamiento = new Asentamiento()
                    {
                        idTipoAsentamiento = vivienda.idTipoAsentamiento,
                        idEstado = vivienda.domicilioGeografico.idEstado,
                        idmunicipio = vivienda.domicilioGeografico.idmunicipio,
                        idLocalidad = vivienda.domicilioGeografico.idLocalidad,
                        nombreAsentamiento = vivienda.domicilioGeografico.nombreAsentamiento,
                        agregado = false,
                        agregadoRUV = true,
                        cp = vivienda.domicilioGeografico.cp,
                        idPeriodo = 4
                    };
                    //Guardar Asentamiento
                    vivienda.domicilioGeografico.idAsentamiento = await this._proyectoDM.AgregarAsentamientoAsync(nuevoAsentamiento);
                    nuevoAsentamiento.idAsentamiento = vivienda.domicilioGeografico.idAsentamiento;
                    asentamientos.Add(nuevoAsentamiento);
                }
            }

            var ambito = 0;

            switch (vivienda.idTipoZonaVivienda)
            {
                case 1: ambito = 2; break;
                case 2: ambito = 1; break;
                default: ambito = 2; break;
            }

            //Vialidad Principal
            var vialidad = vialidades.Where(v => v.nombreVialidad.ToUpper() == vivienda.domicilioGeografico.nombreVialidadPrincipal.ToUpper()
                                   && Convert.ToInt32(v.idTipoVialidad) == vivienda.domicilioGeografico.tipoVialidadP);

            vivienda.domicilioGeografico.idVialidadPrincipal = vialidad.Count() <= 0 ? (vialidadAgregada = await this.AgregarVialidadCompleto
                    (vivienda, vivienda.domicilioGeografico.tipoVialidadP, vivienda.domicilioGeografico.nombreVialidadPrincipal, ambito)).idVialidad :
                    Convert.ToInt32(vialidad.FirstOrDefault().idVialidad);
            vivienda.domicilioGeografico.nombreTipoVialidadPrincipal = ((TipoVialidad)vivienda.domicilioGeografico.tipoVialidadP).ToString();

            if (vialidad.Count() <= 0)
            {
                vialidades.Add(vialidadAgregada);
            }

            //Vialidad 1°
            vialidad = vialidades.Where(v => v.nombreVialidad.ToUpper() == vivienda.domicilioGeografico.nombreVialidad1.ToUpper()
                                   && Convert.ToInt32(v.idTipoVialidad) == vivienda.domicilioGeografico.tipoVialidad1
                                   && v.idLocalidad == vivienda.domicilioGeografico.idLocalidad).ToList();

            vivienda.domicilioGeografico.idVialidad1 = vialidad.Count() <= 0 ? (vialidadAgregada = await this.AgregarVialidadCompleto
                    (vivienda, vivienda.domicilioGeografico.tipoVialidad1, vivienda.domicilioGeografico.nombreVialidad1, ambito)).idVialidad :
                    Convert.ToInt32(vialidad.FirstOrDefault().idVialidad);
            vivienda.domicilioGeografico.nombreTipoVialidad1 = ((TipoVialidad)vivienda.domicilioGeografico.tipoVialidad1).ToString();
            if (vialidad.Count() <= 0)
            {
                vialidades.Add(vialidadAgregada);
            }

            //Vialidad 2°
            vialidad = vialidades.Where(v => v.nombreVialidad.ToUpper() == vivienda.domicilioGeografico.nombreVialidad2.ToUpper()
                                   && Convert.ToInt32(v.idTipoVialidad) == vivienda.domicilioGeografico.tipoVialidad2);

            vivienda.domicilioGeografico.idVialidad2 = vialidad.Count() <= 0 ? (vialidadAgregada = await this.AgregarVialidadCompleto
                    (vivienda, vivienda.domicilioGeografico.tipoVialidad2, vivienda.domicilioGeografico.nombreVialidad2, ambito)).idVialidad :
                    Convert.ToInt32(vialidad.FirstOrDefault().idVialidad);
            vivienda.domicilioGeografico.nombreTipoVialidad2 = ((TipoVialidad)vivienda.domicilioGeografico.tipoVialidad2).ToString();
            if (vialidad.Count() <= 0)
            {
                vialidades.Add(vialidadAgregada);
            }

            //Vialidad 3°
            vialidad = vialidades.Where(v => v.nombreVialidad.ToUpper() == vivienda.domicilioGeografico.nombreVialidad3.ToUpper()
                                   && Convert.ToInt32(v.idTipoVialidad) == vivienda.domicilioGeografico.tipoVialidad3);

            vivienda.domicilioGeografico.idVialidad3 = vialidad.Count() <= 0 ? (vialidadAgregada = await this.AgregarVialidadCompleto
                    (vivienda, vivienda.domicilioGeografico.tipoVialidad3, vivienda.domicilioGeografico.nombreVialidad3, ambito)).idVialidad :
                    Convert.ToInt32(vialidad.FirstOrDefault().idVialidad);
            vivienda.domicilioGeografico.nombreTipoVialidad3 = ((TipoVialidad)vivienda.domicilioGeografico.tipoVialidad3).ToString();
            if (vialidad.Count() <= 0)
            {
                vialidades.Add(vialidadAgregada);
            }

            return new Tuple<Vivienda, List<Vialidad>, List<Asentamiento>>(vivienda, vialidades, asentamientos);
        }

        /// <summary>
        /// Agrega una vialidad deltipo especificado
        /// </summary>
        /// <param name="vivienda"></param>
        /// <param name="tipoVialidad"></param>
        /// <param name="nombreVialidad"></param>
        /// <returns></returns>
        public async Task<Vialidad> AgregarVialidadCompleto(Vivienda vivienda, int tipoVialidad, string nombreVialidad, int ambito)
        {
            var vialidad = new Vialidad()
            {
                idTipoVialidad = tipoVialidad,
                idEstado = vivienda.domicilioGeografico.idEstado,
                idmunicipio = vivienda.domicilioGeografico.idmunicipio,
                idLocalidad = vivienda.domicilioGeografico.idLocalidad,
                nombreVialidad = nombreVialidad,
                agregado = false,
                agregadoRUV = true,
                idPeriodo = 4,
                idVialidadOF = null,
                idAmbito = ambito
            };

            //Guardar tercera vialidad
            vialidad.idVialidad = await this._proyectoDM.AgregarVialidadAsync(vialidad);

            return vialidad;
        }

        /// <summary>
        /// Valida que las viviendas no tengan el mismo domicilio
        /// </summary>
        /// <param name="viviendas"></param>
        /// <returns>Valor que indica si al menos dos viviendas tiene el mismo domicilio.</returns>
        public void ValidarMismoDomicilioViviendas(List<Vivienda> viviendas)
        {
            this._clienteTelemetria.TrackTrace("Validando domicio de las viviendas.");
            var listaCoincidencias = new List<Vivienda>();

            foreach (var vivienda in viviendas)
            {
                listaCoincidencias = this.compararDomiciliosViviendas(viviendas, vivienda);

                if (listaCoincidencias.Count() > 1)
                {
                    throw new ExceptionProyectos($"La vivienda con identificador {listaCoincidencias[0].idVivienda} es la misma que el identificador {listaCoincidencias[1].idVivienda}");
                }
            }
        }

        /// <summary>
        /// Compara los domicilios de las viviendas
        /// </summary>
        /// <param name="viviendas"></param>
        /// <param name="vivienda"></param>
        /// <returns></returns>
        public List<Vivienda> compararDomiciliosViviendas(List<Vivienda> viviendas, Vivienda vivienda)
        {
            var listaCoincidencias = new List<Vivienda>();

            var tmp = Newtonsoft.Json.JsonConvert.SerializeObject(vivienda);

            listaCoincidencias = viviendas.Where(v =>
                                       v.domicilioGeografico.cp == vivienda.domicilioGeografico.cp &&
                                       v.domicilioGeografico.idEstado == vivienda.domicilioGeografico.idEstado &&
                                       v.domicilioGeografico.idmunicipio == vivienda.domicilioGeografico.idmunicipio &&
                                       v.domicilioGeografico.idLocalidad == vivienda.domicilioGeografico.idLocalidad &&
                                       v.domicilioGeografico.idAsentamiento == vivienda.domicilioGeografico.idAsentamiento &&
                                       v.idTipoZonaVivienda == vivienda.idTipoZonaVivienda &&
                                       v.domicilioGeografico.idVialidadPrincipal == vivienda.domicilioGeografico.idVialidadPrincipal &&
                                       (v.domicilioGeografico.superManzana ?? "") == (vivienda.domicilioGeografico.superManzana ?? "") &&
                                       (v.domicilioGeografico.manzana ?? "") == (vivienda.domicilioGeografico.manzana ?? "") &&
                                       (v.domicilioGeografico.lote ?? "") == (vivienda.domicilioGeografico.lote ?? "") &&
                                       v.domicilioGeografico.numeroExteriorNumerico == vivienda.domicilioGeografico.numeroExteriorNumerico &&
                                       (v.domicilioGeografico.numeroExteriorAlfanumerico ?? "") == (vivienda.domicilioGeografico.numeroExteriorAlfanumerico ?? "") &&
                                       (v.domicilioGeografico.numeroExteriorAnt ?? "") == (vivienda.domicilioGeografico.numeroExteriorAnt ?? "") &&
                                       v.domicilioGeografico.numeroInteriorNumerico == vivienda.domicilioGeografico.numeroInteriorNumerico &&
                                       (v.domicilioGeografico.numeroInteriorAlfanumerico ?? "") == (vivienda.domicilioGeografico.numeroInteriorAlfanumerico ?? "") &&
                                       v.numeroCatastralLote == vivienda.numeroCatastralLote &&
                                       v.idNivelVivienda == vivienda.idNivelVivienda &&
                                       v.noEstacionamientos == vivienda.noEstacionamientos &&
                                       v.domicilioGeografico.idVialidad1 == vivienda.domicilioGeografico.idVialidad1 &&
                                       v.domicilioGeografico.idVialidad2 == vivienda.domicilioGeografico.idVialidad2 &&
                                       v.domicilioGeografico.idVialidad3 == vivienda.domicilioGeografico.idVialidad3 &&
                                       v.costo == vivienda.costo &&
                                       v.metros2Lote == vivienda.metros2Lote &&
                                       v.metrosFrenteLote == vivienda.metrosFrenteLote &&
                                       v.idOrientacionVivienda == vivienda.idOrientacionVivienda &&
                                       (v.domicilioCarreteraCamino.origen ?? "") == (vivienda.domicilioCarreteraCamino.origen ?? "") &&
                                       (v.domicilioCarreteraCamino.destino ?? "") == (vivienda.domicilioCarreteraCamino.destino ?? "") &&
                                       v.domicilioCarreteraCamino.idMargen == vivienda.domicilioCarreteraCamino.idMargen &&
                                       (v.domicilioCarreteraCamino.cadenamiento ?? "") == (vivienda.domicilioCarreteraCamino.cadenamiento ?? "") &&
                                       v.domicilioCarreteraCamino.idAdministracion == vivienda.domicilioCarreteraCamino.idAdministracion &&
                                       v.domicilioCarreteraCamino.idDerechoTransito == vivienda.domicilioCarreteraCamino.idDerechoTransito &&
                                       (v.domicilioCarreteraCamino.codigo ?? "") == (vivienda.domicilioCarreteraCamino.codigo ?? "")
                                        ).ToList();

            return listaCoincidencias;
        }

        #endregion Metodos de Ayuda

        private async Task EnviarCorreoCuvsGeneradas(List<Vivienda> viviendas, Proyecto proyecto, CustomUserRuv usuario)
        {
            MensajeDto mensaje = null;
            List<UsuarioCorreoDto> correosDestinatarios = new List<UsuarioCorreoDto>();
            List<string> correosDesarrollador = new List<string>();
            Dictionary<string, string> parametrosCorreo;
            string urlAdjunto;

            var empresa = this._servicioEmpresaConsulta.ObtenerEmpresaDto(proyecto.idEmpresa);
            correosDesarrollador = this._servicioEmpresaComun.ObtenerCorreosContacto(proyecto.idEmpresa).ToList();
            correosDestinatarios = this._servicioEmpresaComun.ObtenerCorreosContactoConDetalle(proyecto.idEmpresa).ToList();
            //construccion de archivo adjunto
            var datosViviendas = await this._proyectoDM.ObtenerSembradoxProyecto(proyecto.idProyecto);
            MemoryStream adjuntoPorCorreo;
            MemoryStream adjunto = this._servicioReportes.GenerarReporteCuvsRegistradasPdf(new Modelo.Reportes.Proyectos.CuvsRegistradasProyecto()
            {
                DatosGenerales = new Modelo.Reportes.Proyectos.DatosGeneralesProyecto()
                {
                    AnioReporte = DateTime.Now.Year.ToString(),
                    DiaReporte = DateTime.Now.Day.ToString().PadLeft(2, '0'),
                    MesReporte = DateTime.Now.ToString("MMMM", CultureInfo.CreateSpecificCulture("es-Mx")).ToUpper(),
                    IdProyecto = proyecto.idProyecto.ToString().PadLeft(6, '0'),
                    NombreProyecto = proyecto.nombre.ToUpper(),
                    NombreDesarrollador = obtenerNombreEmpresa(empresa),
                    FechaAceptacionProyecto = DateTime.Now.ToString("dd/MM/yyyy"),
                    FechaRegistroProyecto = proyecto.fechaRegistro == null ? String.Empty : proyecto.fechaRegistro.Value.ToString("dd/MM/yyyy")
                },
                Correos = correosDesarrollador.Select(c => new Modelo.Reportes.Proyectos.CorreoReporte() { correo = c }).ToList(),
                CuvsRegistradas = viviendas.Select(v => new Modelo.Reportes.Proyectos.ViviendaReporte()
                {
                    IdVivienda = v.idViviendaPlanoSIG.HasValue ? v.idViviendaPlanoSIG.ToString() : string.Empty,
                    Cuv = v.cuv,
                    Direccion = obtenerDireccionVivienda(v.idVivienda, datosViviendas)
                }).ToList()
            });

            foreach (var correoDestinatario in correosDestinatarios)
            {
                parametrosCorreo = new Dictionary<string, string>
                {
                    {"0", obtenerNombreUsuario(correoDestinatario) },
                    {"1", proyecto.nombre },
                    {"2", proyecto.idProyecto.ToString() },
                    {"3", obtenerNombreEmpresa(empresa) },
                    {"4", viviendas.Count().ToString()},
                };

                mensaje = new MensajeDto(AsuntoCorreoGeneracionCuvs,
                    new List<string> { correoDestinatario.correoElectronico },
                    eTipoCorreo.ContenidoHtmlMailCuvsGeneradas,
                    eTipoMensaje.InformativoComunicacion,
                    null,
                    parametrosCorreo);

                adjunto.Position = 0;
                adjuntoPorCorreo = new MemoryStream();
                await adjunto.CopyToAsync(adjuntoPorCorreo);
                adjuntoPorCorreo.Position = 0;
                urlAdjunto = this._servicioNotificaciones.CargarBlob(adjuntoPorCorreo, "CuvsRegistradasProyecto.pdf", false);
                mensaje.AttachmentBlobName = "CuvsRegistradasProyecto.pdf";

                this._servicioNotificaciones.AgregarNotificacion(mensaje);
            }
        }

        private string obtenerDireccionVivienda(int idVivienda, Sembrado datosViviendas)
        {
            StringBuilder direccion = new StringBuilder();
            var vivienda = datosViviendas.viviendas.Find(v => v.idVivienda == idVivienda);
            if (vivienda != null)
            {
                if (!string.IsNullOrWhiteSpace(vivienda.domicilioGeografico.nombreVialidadPrincipal))
                    direccion.Append(" " + vivienda.domicilioGeografico.nombreVialidadPrincipal);
                if (!string.IsNullOrWhiteSpace(vivienda.domicilioGeografico.manzana))
                    direccion.Append(" M-" + vivienda.domicilioGeografico.manzana);
                if (!string.IsNullOrWhiteSpace(vivienda.domicilioGeografico.lote))
                    direccion.Append(" L-" + vivienda.domicilioGeografico.manzana);
                if (!string.IsNullOrWhiteSpace(vivienda.domicilioGeografico.numeroExteriorNumerico))
                    direccion.Append(" Num. Ext. " + vivienda.domicilioGeografico.numeroExteriorNumerico);
                if (!string.IsNullOrWhiteSpace(vivienda.domicilioGeografico.numeroExteriorAlfanumerico))
                    direccion.Append(" " + vivienda.domicilioGeografico.numeroExteriorAlfanumerico);
                if (!string.IsNullOrWhiteSpace(vivienda.domicilioGeografico.numeroInteriorNumerico))
                    direccion.Append(" Num. Int. " + vivienda.domicilioGeografico.numeroInteriorNumerico);
                if (!string.IsNullOrWhiteSpace(vivienda.domicilioGeografico.numeroExteriorAlfanumerico))
                    direccion.Append(" " + vivienda.domicilioGeografico.numeroExteriorAlfanumerico);
                if (!string.IsNullOrWhiteSpace(vivienda.domicilioGeografico.localidad))
                    direccion.Append(", " + vivienda.domicilioGeografico.localidad);
                if (!string.IsNullOrWhiteSpace(vivienda.domicilioGeografico.municipio))
                    direccion.Append(", " + vivienda.domicilioGeografico.municipio);
                if (!string.IsNullOrWhiteSpace(vivienda.domicilioGeografico.estado))
                    direccion.Append(", " + vivienda.domicilioGeografico.estado);
            }
            return direccion.ToString();
        }

        private string obtenerNombreEmpresa(EmpresaDto empresa)
        {
            StringBuilder nombreEmpresa = new StringBuilder();
            if (empresa != null)
            {
                if (!string.IsNullOrWhiteSpace(empresa.nombreRazonSocial))
                    nombreEmpresa.Append(empresa.nombreRazonSocial);
                if (!string.IsNullOrWhiteSpace(empresa.apellidoPaterno))
                    nombreEmpresa.Append(" " + empresa.apellidoPaterno);
                if (!string.IsNullOrWhiteSpace(empresa.apellidoMaterno))
                    nombreEmpresa.Append(" " + empresa.apellidoMaterno);
            }
            return nombreEmpresa.ToString();
        }

        private string obtenerNombreUsuario(UsuarioCorreoDto usuario)
        {
            StringBuilder nombreEmpresa = new StringBuilder();
            if (usuario != null)
            {
                if (!string.IsNullOrWhiteSpace(usuario.nombreRazonSocial))
                    nombreEmpresa.Append(usuario.nombreRazonSocial);
                if (!string.IsNullOrWhiteSpace(usuario.apellidoPaterno))
                    nombreEmpresa.Append(" " + usuario.apellidoPaterno);
                if (!string.IsNullOrWhiteSpace(usuario.apellidoMaterno))
                    nombreEmpresa.Append(" " + usuario.apellidoMaterno);
            }
            return nombreEmpresa.ToString();
        }

        public async Task<string> GuardarProyectoAsincronoAsync(Proyecto proyecto, CustomUserRuv usuario)
        {
            MensajeEnvioProyecto mensaje = new MensajeEnvioProyecto()
            {
                IdProyecto = proyecto.idProyecto,
                IdUsuario = usuario.IdUsuario.Value,
                IdEmpresa = usuario.IdEmpresa,
                NombreUsuario = usuario.NombreUsuario,
                TokenGuid = usuario.TokenGuid,
                IdEstatusPrevio = proyecto.idEstatusProyecto
            };

            var mensajeAgregar = JsonConvert.SerializeObject(mensaje);

            using (var transaccion = new TransactionScope(TransactionScopeOption.RequiresNew, this._opcionesTransaccion, TransactionScopeAsyncFlowOption.Enabled))
            {
                this._clienteTelemetria.TrackTrace("Actualizando temporalJSON del proyecto");
                await this._proyectoDM.ActualizarProyectoTemporalAsync(proyecto.idProyecto, JsonConvert.SerializeObject(proyecto), (int)EstatusProyecto.Enviando);

                this._clienteTelemetria.TrackTrace("Actualizando estatus del proyecto");
                await this._proyectoDM.ActualizarEstatusProyectoAsync(proyecto.idProyecto, (int)EstatusProyecto.Enviando);

                this._clienteTelemetria.TrackTrace("Agregando mensaje a cola");
                await this._queueStorage.QueueClient.GetQueueReference(nombreQueueEnvioProyecto).AddMessageAsync(new CloudQueueMessage(mensajeAgregar));

                transaccion.Complete();
            }

            return "-1";
        }

        public async Task<Tuple<string, string>> ObtenerEstadosMunicipiosViviendasAsync(List<string> idsEstados, List<Tuple<string, string>> idsMunicipios)
        {
            StringBuilder estados = new StringBuilder();
            StringBuilder municipios = new StringBuilder();
            string nombreEstado = "";
            string nombreMunicipio = "";
            var catalogoEstados = await this._servicioProyectosCatalogos.ObtenerCatalogoAsync(CatalogosProyectos.EntidadesFederativas);

            foreach (var estado in idsEstados)
            {
                nombreEstado = catalogoEstados.Where(e => int.Parse(e.id) == int.Parse(estado)).FirstOrDefault().valor;
                estados.Append(estados.Length == 0 ? nombreEstado : String.Format(", {0}", nombreEstado));
            }

            foreach (var municipio in idsMunicipios)
            {
                var catalogoEstadoMunicipio = await this._servicioProyectosCatalogos.ObtenerCatalogoAsync(CatalogosProyectos.Municipios, int.Parse(municipio.Item1));
                nombreMunicipio = catalogoEstadoMunicipio.Where(m => int.Parse(m.id) == int.Parse(municipio.Item2)).FirstOrDefault().valor;
                municipios.Append(municipios.Length == 0 ? nombreMunicipio : String.Format(", {0}", nombreMunicipio));
            }

            return new Tuple<string, string>(estados.ToString(), municipios.ToString());
        }

        public async Task<bool> EliminarEquipamientoTemporlAsync(string capa, int featId)
        {
            var result = await this._planoDataMapper.EliminarEquipamientoTemporlAsync(capa, featId);
            return result;
        }

        private async Task ValidarMismaTipologiaViviendaAsync(List<Vivienda> listaViviendas)
        {
            var resultado = new Tuple<string, string>("", "");
            var listaCUVs = listaViviendas.Select(v => v.cuv);
            var LiteralCUVs = string.Join(",", listaCUVs.ToArray());
            var listaCUVsAValidar = await this._proyectoDM.ValidarMismaTipologiaViviendaAsync(LiteralCUVs);
            var viviendaActual = new Vivienda();

            if (resultado != null && listaCUVsAValidar.Any())
            {
                foreach (var viviendaAValidar in listaCUVsAValidar)
                {
                    viviendaActual = listaViviendas.Where(v => v.idVivienda == viviendaAValidar.idVivienda).FirstOrDefault();

                    if (viviendaAValidar.idTipoVivienda != viviendaActual.idTipoVivienda)
                    {
                        throw new ExceptionProyectos(string.Format(mensajeValidacionMismaTipologia, viviendaActual.idVivienda));
                    }
                }
            }
        }
    }
}