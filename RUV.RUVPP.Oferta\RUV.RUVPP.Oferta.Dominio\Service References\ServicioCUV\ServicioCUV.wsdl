<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:wsap="http://schemas.xmlsoap.org/ws/2004/08/addressing/policy" xmlns:wsa10="http://www.w3.org/2005/08/addressing" xmlns:tns="http://tempuri.org/" xmlns:msc="http://schemas.microsoft.com/ws/2005/12/wsdl/contract" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:wsx="http://schemas.xmlsoap.org/ws/2004/09/mex" xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata" xmlns:wsa="http://schemas.xmlsoap.org/ws/2004/08/addressing" xmlns:wsaw="http://www.w3.org/2006/05/addressing/wsdl" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:xsd="http://www.w3.org/2001/XMLSchema" name="ServicioCUV" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <xs:schema elementFormDefault="qualified" targetNamespace="http://tempuri.org/" xmlns:xs="http://www.w3.org/2001/XMLSchema">
      <xs:import namespace="http://schemas.datacontract.org/2004/07/SIG.CUV.Entidades" />
      <xs:element name="ConsultarPreClavePunto">
        <xs:complexType>
          <xs:sequence>
            <xs:element xmlns:q1="http://schemas.datacontract.org/2004/07/SIG.CUV.Entidades" minOccurs="0" name="punto" nillable="true" type="q1:Punto" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="ConsultarPreClavePuntoResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element xmlns:q2="http://schemas.datacontract.org/2004/07/SIG.CUV.Entidades" minOccurs="0" name="ConsultarPreClavePuntoResult" nillable="true" type="q2:ResultadoCuv" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="ConsultarPreClaveGeometria">
        <xs:complexType>
          <xs:sequence>
            <xs:element xmlns:q3="http://schemas.datacontract.org/2004/07/SIG.CUV.Entidades" minOccurs="0" name="geometria" nillable="true" type="q3:Geometria" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="ConsultarPreClaveGeometriaResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element xmlns:q4="http://schemas.datacontract.org/2004/07/SIG.CUV.Entidades" minOccurs="0" name="ConsultarPreClaveGeometriaResult" nillable="true" type="q4:ResultadoCuv" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="CrearCuv">
        <xs:complexType>
          <xs:sequence>
            <xs:element xmlns:q5="http://schemas.datacontract.org/2004/07/SIG.CUV.Entidades" minOccurs="0" name="cuv" nillable="true" type="q5:Cuv" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="CrearCuvResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element xmlns:q6="http://schemas.datacontract.org/2004/07/SIG.CUV.Entidades" minOccurs="0" name="CrearCuvResult" nillable="true" type="q6:ResultadoCuv" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="ObtenerVertices">
        <xs:complexType>
          <xs:sequence>
            <xs:element xmlns:q7="http://schemas.datacontract.org/2004/07/SIG.CUV.Entidades" minOccurs="0" name="cuv" nillable="true" type="q7:Cuv" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="ObtenerVerticesResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element xmlns:q8="http://schemas.datacontract.org/2004/07/SIG.CUV.Entidades" minOccurs="0" name="ObtenerVerticesResult" nillable="true" type="q8:Vertice" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="ConsultaCuv">
        <xs:complexType>
          <xs:sequence>
            <xs:element xmlns:q9="http://schemas.datacontract.org/2004/07/SIG.CUV.Entidades" minOccurs="0" name="consulta" nillable="true" type="q9:Consulta" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="ConsultaCuvResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element xmlns:q10="http://schemas.datacontract.org/2004/07/SIG.CUV.Entidades" minOccurs="0" name="ConsultaCuvResult" nillable="true" type="q10:ConsultaBitacora" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
    </xs:schema>
    <xs:schema xmlns:tns="http://schemas.microsoft.com/2003/10/Serialization/" attributeFormDefault="qualified" elementFormDefault="qualified" targetNamespace="http://schemas.microsoft.com/2003/10/Serialization/" xmlns:xs="http://www.w3.org/2001/XMLSchema">
      <xs:element name="anyType" nillable="true" type="xs:anyType" />
      <xs:element name="anyURI" nillable="true" type="xs:anyURI" />
      <xs:element name="base64Binary" nillable="true" type="xs:base64Binary" />
      <xs:element name="boolean" nillable="true" type="xs:boolean" />
      <xs:element name="byte" nillable="true" type="xs:byte" />
      <xs:element name="dateTime" nillable="true" type="xs:dateTime" />
      <xs:element name="decimal" nillable="true" type="xs:decimal" />
      <xs:element name="double" nillable="true" type="xs:double" />
      <xs:element name="float" nillable="true" type="xs:float" />
      <xs:element name="int" nillable="true" type="xs:int" />
      <xs:element name="long" nillable="true" type="xs:long" />
      <xs:element name="QName" nillable="true" type="xs:QName" />
      <xs:element name="short" nillable="true" type="xs:short" />
      <xs:element name="string" nillable="true" type="xs:string" />
      <xs:element name="unsignedByte" nillable="true" type="xs:unsignedByte" />
      <xs:element name="unsignedInt" nillable="true" type="xs:unsignedInt" />
      <xs:element name="unsignedLong" nillable="true" type="xs:unsignedLong" />
      <xs:element name="unsignedShort" nillable="true" type="xs:unsignedShort" />
      <xs:element name="char" nillable="true" type="tns:char" />
      <xs:simpleType name="char">
        <xs:restriction base="xs:int" />
      </xs:simpleType>
      <xs:element name="duration" nillable="true" type="tns:duration" />
      <xs:simpleType name="duration">
        <xs:restriction base="xs:duration">
          <xs:pattern value="\-?P(\d*D)?(T(\d*H)?(\d*M)?(\d*(\.\d*)?S)?)?" />
          <xs:minInclusive value="-P10675199DT2H48M5.4775808S" />
          <xs:maxInclusive value="P10675199DT2H48M5.4775807S" />
        </xs:restriction>
      </xs:simpleType>
      <xs:element name="guid" nillable="true" type="tns:guid" />
      <xs:simpleType name="guid">
        <xs:restriction base="xs:string">
          <xs:pattern value="[\da-fA-F]{8}-[\da-fA-F]{4}-[\da-fA-F]{4}-[\da-fA-F]{4}-[\da-fA-F]{12}" />
        </xs:restriction>
      </xs:simpleType>
      <xs:attribute name="FactoryType" type="xs:QName" />
      <xs:attribute name="Id" type="xs:ID" />
      <xs:attribute name="Ref" type="xs:IDREF" />
    </xs:schema>
    <xs:schema xmlns:tns="http://schemas.datacontract.org/2004/07/SIG.CUV.Entidades" elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/SIG.CUV.Entidades" xmlns:xs="http://www.w3.org/2001/XMLSchema">
      <xs:complexType name="Punto">
        <xs:sequence>
          <xs:element minOccurs="0" name="Latitud" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="Longitud" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="Nivel" type="xs:int" />
          <xs:element minOccurs="0" name="Radio" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="Punto" nillable="true" type="tns:Punto" />
      <xs:complexType name="ResultadoCuv">
        <xs:sequence>
          <xs:element minOccurs="0" name="Clave" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="Creado" type="xs:boolean" />
          <xs:element minOccurs="0" name="Descripcion" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="ResultadoCuv" nillable="true" type="tns:ResultadoCuv" />
      <xs:complexType name="Geometria">
        <xs:sequence>
          <xs:element minOccurs="0" name="Esquema" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GeometriaNombreCampo" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="IdNombreCampo" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="IdValorCampo" type="xs:int" />
          <xs:element minOccurs="0" name="Nivel" type="xs:int" />
          <xs:element minOccurs="0" name="NombreTabla" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="TipoServicio" type="xs:int" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="Geometria" nillable="true" type="tns:Geometria" />
      <xs:complexType name="Cuv">
        <xs:sequence>
          <xs:element minOccurs="0" name="Esquema" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="GeometriaNombreCampo" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="IdNombreCampo" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="IdServicio" type="xs:int" />
          <xs:element minOccurs="0" name="IdValorCampo" nillable="true" type="xs:decimal" />
          <xs:element minOccurs="0" name="NivelDecimal" type="xs:int" />
          <xs:element minOccurs="0" name="NombreTabla" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="TipoGeometria" nillable="true" type="xs:decimal" />
          <xs:element minOccurs="0" name="Tipologia" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="Cuv" nillable="true" type="tns:Cuv" />
      <xs:complexType name="Vertice">
        <xs:sequence>
          <xs:element minOccurs="0" name="VerticesPoligono" nillable="true" type="tns:ArrayOfVertices" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="Vertice" nillable="true" type="tns:Vertice" />
      <xs:complexType name="ArrayOfVertices">
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="Vertices" nillable="true" type="tns:Vertices" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="ArrayOfVertices" nillable="true" type="tns:ArrayOfVertices" />
      <xs:complexType name="Vertices">
        <xs:sequence>
          <xs:element minOccurs="0" name="IdOrden" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="Latitud" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="Longitud" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="Vertices" nillable="true" type="tns:Vertices" />
      <xs:complexType name="Consulta">
        <xs:sequence>
          <xs:element minOccurs="0" name="Esquema" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="IdNombreCampo" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="IdValorCampo" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="NivelDecimal" type="xs:int" />
          <xs:element minOccurs="0" name="NombreTabla" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="Consulta" nillable="true" type="tns:Consulta" />
      <xs:complexType name="ConsultaBitacora">
        <xs:sequence>
          <xs:element minOccurs="0" name="CampoGeometrico" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CampoId" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ClaveGradicula" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="Consecutivo" type="xs:int" />
          <xs:element minOccurs="0" name="ConsecutivoH" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="Cuv" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="Esquema" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="IdRegistro" type="xs:int" />
          <xs:element minOccurs="0" name="IdServicio" type="xs:int" />
          <xs:element minOccurs="0" name="Latitud" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="Longitud" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="NivelD" type="xs:int" />
          <xs:element minOccurs="0" name="NivelH" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="OrigenTablaGeometrica" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ValorId" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:complexType>
      <xs:element name="ConsultaBitacora" nillable="true" type="tns:ConsultaBitacora" />
    </xs:schema>
  </wsdl:types>
  <wsdl:message name="IServicioCUV_ConsultarPreClavePunto_InputMessage">
    <wsdl:part name="parameters" element="tns:ConsultarPreClavePunto" />
  </wsdl:message>
  <wsdl:message name="IServicioCUV_ConsultarPreClavePunto_OutputMessage">
    <wsdl:part name="parameters" element="tns:ConsultarPreClavePuntoResponse" />
  </wsdl:message>
  <wsdl:message name="IServicioCUV_ConsultarPreClaveGeometria_InputMessage">
    <wsdl:part name="parameters" element="tns:ConsultarPreClaveGeometria" />
  </wsdl:message>
  <wsdl:message name="IServicioCUV_ConsultarPreClaveGeometria_OutputMessage">
    <wsdl:part name="parameters" element="tns:ConsultarPreClaveGeometriaResponse" />
  </wsdl:message>
  <wsdl:message name="IServicioCUV_CrearCuv_InputMessage">
    <wsdl:part name="parameters" element="tns:CrearCuv" />
  </wsdl:message>
  <wsdl:message name="IServicioCUV_CrearCuv_OutputMessage">
    <wsdl:part name="parameters" element="tns:CrearCuvResponse" />
  </wsdl:message>
  <wsdl:message name="IServicioCUV_ObtenerVertices_InputMessage">
    <wsdl:part name="parameters" element="tns:ObtenerVertices" />
  </wsdl:message>
  <wsdl:message name="IServicioCUV_ObtenerVertices_OutputMessage">
    <wsdl:part name="parameters" element="tns:ObtenerVerticesResponse" />
  </wsdl:message>
  <wsdl:message name="IServicioCUV_ConsultaCuv_InputMessage">
    <wsdl:part name="parameters" element="tns:ConsultaCuv" />
  </wsdl:message>
  <wsdl:message name="IServicioCUV_ConsultaCuv_OutputMessage">
    <wsdl:part name="parameters" element="tns:ConsultaCuvResponse" />
  </wsdl:message>
  <wsdl:portType name="IServicioCUV">
    <wsdl:operation name="ConsultarPreClavePunto">
      <wsdl:input wsaw:Action="http://tempuri.org/IServicioCUV/ConsultarPreClavePunto" message="tns:IServicioCUV_ConsultarPreClavePunto_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IServicioCUV/ConsultarPreClavePuntoResponse" message="tns:IServicioCUV_ConsultarPreClavePunto_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="ConsultarPreClaveGeometria">
      <wsdl:input wsaw:Action="http://tempuri.org/IServicioCUV/ConsultarPreClaveGeometria" message="tns:IServicioCUV_ConsultarPreClaveGeometria_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IServicioCUV/ConsultarPreClaveGeometriaResponse" message="tns:IServicioCUV_ConsultarPreClaveGeometria_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="CrearCuv">
      <wsdl:input wsaw:Action="http://tempuri.org/IServicioCUV/CrearCuv" message="tns:IServicioCUV_CrearCuv_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IServicioCUV/CrearCuvResponse" message="tns:IServicioCUV_CrearCuv_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="ObtenerVertices">
      <wsdl:input wsaw:Action="http://tempuri.org/IServicioCUV/ObtenerVertices" message="tns:IServicioCUV_ObtenerVertices_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IServicioCUV/ObtenerVerticesResponse" message="tns:IServicioCUV_ObtenerVertices_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="ConsultaCuv">
      <wsdl:input wsaw:Action="http://tempuri.org/IServicioCUV/ConsultaCuv" message="tns:IServicioCUV_ConsultaCuv_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IServicioCUV/ConsultaCuvResponse" message="tns:IServicioCUV_ConsultaCuv_OutputMessage" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="BasicHttpBinding_IServicioCUV" type="tns:IServicioCUV">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="ConsultarPreClavePunto">
      <soap:operation soapAction="http://tempuri.org/IServicioCUV/ConsultarPreClavePunto" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ConsultarPreClaveGeometria">
      <soap:operation soapAction="http://tempuri.org/IServicioCUV/ConsultarPreClaveGeometria" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CrearCuv">
      <soap:operation soapAction="http://tempuri.org/IServicioCUV/CrearCuv" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ObtenerVertices">
      <soap:operation soapAction="http://tempuri.org/IServicioCUV/ObtenerVertices" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ConsultaCuv">
      <soap:operation soapAction="http://tempuri.org/IServicioCUV/ConsultaCuv" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="ServicioCUV">
    <wsdl:port name="BasicHttpBinding_IServicioCUV" binding="tns:BasicHttpBinding_IServicioCUV">
      <soap:address location="http://map-q3-w2k8r2.cloudapp.net/ws/cuv/ServicioCUV.svc" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>