﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api
{
    public class TarifaDTO
    {
        public short? idTarifa { get; set; }
        public byte? idTipoPago { get; set; }
        public byte? idTipoTarifa { get; set; }
        public short? idServicio { get; set; }
        public short? idProducto { get; set; }
        public string nombre { get; set; }
        public string conceptoCobro { get; set; }
        public decimal? costoServicio { get; set; }
        public decimal? cargoServicio { get; set; }
        public decimal? descuento { get; set; }
        public DateTime? fechaRegistro { get; set; }
        public DateTime? fechaActualizacion { get; set; }
        public bool? activo { get; set; }

        public string nombreServicio { get; set; }
        public string nombreProducto { get; set; }

        public string configuracionJSON { get; set; }

        public bool? conIva { get; set; }

        public TarifaDTO clone { get; set; }
    }
}
