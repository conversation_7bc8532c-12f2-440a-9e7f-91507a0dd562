﻿{
  "idProducto": 4,
  "producto": "Oferta",
  "idServicio": 1,
  "servicio": "Alta de Oferta",
  "secciones": [
    {
      "idSeccion": 1,
      "nombre": "Viviendas",
      "tipo": "contenedor",
      "mostrarEnVyD": true,
      "validable": false,      
      "secciones": [],
      "elementos": [
        {
          "idElemento": "esSeleccionada",
          "mostrarEnVyD": false,
          "validable": false
        },
        {
          "idElemento": "nombreFrente",
          "nombre": "Nombre de frente",
          "tipo": "textbox",
          "mostrarEnVyD": false,
          "validable": true,
          "requerido": true
        }
      ]
    },
    {
      "idSeccion": 14,
      "nombre": "Prototipos",
      "tipo": "contenedor",
      "mostrarEnVyD": true,
      "ocultarEnRegistro": true,
      "validable": true,
      "secciones": [ ],
      "elementos": [       
        {
          "idElemento": "prototipos",
          "nombre": "prototipo",
          "mostrarEnVyD": true,
          "validable": true
        }
      ]
    },
    {
      "idSeccion": 15,
      "nombre": "Sembrado",
      "tipo": "contenedor",
      "mostrarEnVyD": true,
      "validable": false,
      "ocultarEnRegistro": true,
      "secciones": [],
      "elementos": []
    },
    {
      "idSeccion": 2,
      "nombre": "Documentos",
      "tipo": "contenedor",
      "mostrarEnVyD": true,
      "validable": true,
      "secciones": [
        {
          "idSeccion": 3,
          "nombre": "Licencia /Factibilidad",
          "tipo": "contenedor",
          "mostrarEnVyD": true,
          "validable": true,
          "secciones": [ ],
          "elementos": [
            {
              "idElemento": "documentosLicenciasFactibilidades",
              "nombre": "Lista Licencias/Factibilidades",
              "tipoDato": "bool",
              "mostrarEnVyD": false,
              "validable": true
            },
            {
              "idElemento": "documentoLicenciaFactibilidad",
              "nombre": "Documento Licencia/Factibilidad",
              "tipoDato": "bool",
              "mostrarEnVyD": false,
              "validable": true,
              "requerido": true
            },
            {
              "idElemento": "cuentaFechaVigencia",
              "nombre": "Fecha de Vigencia",
              "tipoDato": "bool",
              "mostrarEnVyD": false,
              "validable": true,
              "requerido": true
            },
            {
              "idElemento": "cuentaConDocumento",
              "nombre": "Fecha de Vigencia",
              "tipoDato": "bool",
              "mostrarEnVyD": false,
              "validable": true,
              "requerido": true
            },
            {
              "idElemento": "numeroOficio",
              "nombre": "Numero de Oficio",
              "tipo": "textbox",
              "longitudMaxima": 20,
              "tipoDato": "text",
              "mostrarEnVyD": false,
              "validable": true,
              "requerido": true,
              "expresionRegular": null
            },
            {
              "idElemento": "emitidoPor",
              "nombre": "Emitido por",
              "tipo": "textbox",
              "longitudMaxima": 100,
              "tipoDato": "text",
              "mostrarEnVyD": false,
              "validable": true,
              "requerido": true,
              "expresionRegular": null
            },
            {
              "idElemento": "fechaEmision",
              "nombre": "Fecha de Emisión",
              "tipo": "textbox",
              "longitudMaxima": 10,
              "tipoDato": "text",
              "mostrarEnVyD": false,
              "validable": true,
              "requerido": true,
              "expresionRegular": null
            },
            {
              "idElemento": "fechaVigencia",
              "nombre": "Fecha de Vigencia",
              "tipo": "textbox",
              "longitudMaxima": 10,
              "tipoDato": "text",
              "mostrarEnVyD": false,
              "validable": true,
              "requerido": true,
              "expresionRegular": null
            }
          ]
        },
        {
          "idSeccion": 4,
          "nombre": "Especificaciones de la vivienda",
          "tipo": "contenedor",
          "mostrarEnVyD": true,
          "validable": true,
          "secciones": [ ],
          "elementos": [
            {
              "idElemento": "prototipoDocumento",
              "nombre": "Listado prototipos",
              "mostrarEnVyD": false,
              "validable": false
            },
            {
              "idElemento": "prototiposDocumentoVyD",
              "nombre": "Listado prototipos",
              "mostrarEnVyD": false,
              "validable": true
            }
          ]
        },
        {
          "idSeccion": 5,
          "nombre": "Documentación Complementaria",
          "tipo": "contenedor",
          "mostrarEnVyD": true,
          "validable": true,
          "secciones": [ ],
          "elementos": [
            {
              "idElemento": "documentosComplementarios",
              "nombre": "Listado documentos",
              "mostrarEnVyD": false,
              "validable": true
            }
          ]
        }

      ]
    },
    {
      "idSeccion": 6,
      "nombre": "Datos Generales",
      "tipo": "contenedor",
      "mostrarEnVyD": true,
      "validable": true,
      "secciones": [
        {
          "idSeccion": 7,
          "nombre": "Director Responsable de Obra",
          "tipo": "contenedor",
          "mostrarEnVyD": true,
          "validable": true,
          "secciones": [ ],
          "elementos": [
            {
              "idElemento": "nombreDirectorOferta",
              "nombre": "Nombre",
              "tipo": "textbox",
              "longitudMaxima": 50,
              "tipoDato": "text",
              "mostrarEnVyD": true,
              "validable": true,
              "requerido": true,
              "expresionRegular": null
            },
            {
              "idElemento": "apellidoMaternoOferta",
              "nombre": "Apellido materno",
              "tipo": "textbox",
              "longitudMaxima": 50,
              "tipoDato": "text",
              "mostrarEnVyD": true,
              "validable": true,
              "requerido": true,
              "expresionRegular": null
            },
            {
              "idElemento": "apellidoPaternoOferta",
              "nombre": "Apellido paterno",
              "tipo": "textbox",
              "longitudMaxima": 50,
              "tipoDato": "text",
              "mostrarEnVyD": true,
              "validable": true,
              "requerido": true,
              "expresionRegular": null
            },
            {
              "idElemento": "numeroPeritoOferta",
              "nombre": "Número perito",
              "tipo": "textbox",
              "longitudMaxima": 50,
              "tipoDato": "text",
              "mostrarEnVyD": true,
              "validable": true,
              "requerido": true,
              "expresionRegular": null
            },
            {
              "idElemento": "fechaVigenciaOferta",
              "nombre": "Fecha vigencia",
              "tipo": "textbox",
              "longitudMaxima": 50,
              "tipoDato": "text",
              "mostrarEnVyD": true,
              "validable": true,
              "requerido": true,
              "expresionRegular": null
            },
            {
              "idElemento": "licenciaDROOferta",
              "nombre": "Licencia",
              "tipo": "textbox",
              "longitudMaxima": 50,
              "tipoDato": "text",
              "mostrarEnVyD": true,
              "validable": true,
              "requerido": true,
              "expresionRegular": null
            },
            {
              "idElemento": "identificacionDROOferta",
              "nombre": "Identificación Oficial",
              "tipo": "textbox",
              "longitudMaxima": 50,
              "tipoDato": "text",
              "mostrarEnVyD": true,
              "validable": true,
              "requerido": true,
              "expresionRegular": null
            }
          ]
        },
        {
          "idSeccion": 8,
          "nombre": "Propietario del Terreno",
          "tipo": "contenedor",
          "mostrarEnVyD": true,
          "validable": true,
          "secciones": [ ],
          "elementos": [
            {
              "idElemento": "nombrePropietarioOferta",
              "nombre": "Nombre",
              "tipo": "textbox",
              "longitudMaxima": 50,
              "tipoDato": "text",
              "mostrarEnVyD": true,
              "validable": true,
              "requerido": true,
              "expresionRegular": null
            },
            {
              "idElemento": "tomoOferta",
              "nombre": "Tomo",
              "tipo": "textbox",
              "longitudMaxima": 50,
              "tipoDato": "text",
              "mostrarEnVyD": true,
              "validable": true,
              "requerido": true,
              "expresionRegular": null
            },
            {
              "idElemento": "numeroEscrituraOferta",
              "nombre": "Número escritura",
              "tipo": "textbox",
              "longitudMaxima": 50,
              "tipoDato": "text",
              "mostrarEnVyD": true,
              "validable": true,
              "requerido": true,
              "expresionRegular": null
            },
            {
              "idElemento": "numeroCatastralOferta",
              "nombre": "Número catastral",
              "tipo": "textbox",
              "longitudMaxima": 50,
              "tipoDato": "text",
              "mostrarEnVyD": true,
              "validable": true,
              "requerido": true,
              "expresionRegular": null
            },
            {
              "idElemento": "fechaEscrituracionOferta",
              "nombre": "Fecha escrituración",
              "tipo": "textbox",
              "longitudMaxima": 50,
              "tipoDato": "date",
              "mostrarEnVyD": true,
              "validable": true,
              "requerido": true,
              "expresionRegular": null
            },
            {
              "idElemento": "volumenOferta",
              "nombre": "Volumen",
              "tipo": "textbox",
              "longitudMaxima": 50,
              "tipoDato": "text",
              "mostrarEnVyD": true,
              "validable": true,
              "requerido": true,
              "expresionRegular": null
            },
            {
              "idElemento": "areaTerrenoOferta",
              "nombre": "Área terreno",
              "tipo": "textbox",
              "longitudMaxima": 50,
              "tipoDato": "text",
              "mostrarEnVyD": true,
              "validable": true,
              "requerido": true,
              "expresionRegular": null
            },
            {
              "idElemento": "numeroNotarioOferta",
              "nombre": "Número notario",
              "tipo": "textbox",
              "longitudMaxima": 50,
              "tipoDato": "text",
              "mostrarEnVyD": true,
              "validable": true,
              "requerido": true,
              "expresionRegular": null
            },
            {
              "idElemento": "numeroRPPOferta",
              "nombre": "Número RPP",
              "tipo": "textbox",
              "longitudMaxima": 50,
              "tipoDato": "text",
              "mostrarEnVyD": true,
              "validable": true,
              "requerido": true,
              "expresionRegular": null
            }
          ]
        },
        {
          "idSeccion": 9,
          "nombre": "Constructor",
          "tipo": "contenedor",
          "mostrarEnVyD": true,
          "validable": true,
          "secciones": [ ],
          "elementos": [
            {
              "idElemento": "noRegistroRUVConstructorOferta",
              "nombre": "Número registro RUV",
              "tipo": "textbox",
              "longitudMaxima": 50,
              "tipoDato": "text",
              "mostrarEnVyD": true,
              "validable": true,
              "requerido": true,
              "expresionRegular": null
            }
          ]
        },
        {
          "idSeccion": 10,
          "nombre": "Promotores",
          "tipo": "contenedor",
          "mostrarEnVyD": true,
          "validable": true,
          "secciones": [ ],
          "elementos": [
            {
              "idElemento": "listaPromotoresOferta",
              "nombre": "Lista Promotores Oferta",
              "tipoDato": "bool",
              "mostrarEnVyD": true,
              "validable": true
            },
            {
              "idElemento": "rfcPromotorOferta",
              "nombre": "Rfc",
              "tipo": "textbox",
              "longitudMaxima": 50,
              "tipoDato": "text",
              "mostrarEnVyD": true,
              "validable": true,
              "requerido": true,
              "expresionRegular": null
            },
            {
              "idElemento": "nombreRazonSocialPromotorOferta",
              "nombre": "Nombre o razón social",
              "tipo": "textbox",
              "longitudMaxima": 50,
              "tipoDato": "text",
              "mostrarEnVyD": true,
              "validable": true,
              "requerido": true,
              "expresionRegular": null
            },
            {
              "idElemento": "telefonoPromotorOferta",
              "nombre": "Teléfono",
              "tipo": "textbox",
              "longitudMaxima": 50,
              "tipoDato": "text",
              "mostrarEnVyD": true,
              "validable": true,
              "requerido": true,
              "expresionRegular": null
            },
            {
              "idElemento": "correoElectronicoPromotorOferta",
              "nombre": "Correo electrónico",
              "tipo": "textbox",
              "longitudMaxima": 50,
              "tipoDato": "text",
              "mostrarEnVyD": true,
              "validable": true,
              "requerido": true,
              "expresionRegular": null
            }
          ]
        },
        {
          "idSeccion": 11,
          "nombre": "Vendedores",
          "tipo": "contenedor",
          "mostrarEnVyD": true,
          "validable": true,
          "secciones": [ ],
          "elementos": [
            {
              "idElemento": "listaVendedoresOferta",
              "nombre": "Lista Vendedores Oferta",
              "tipoDato": "bool",
              "mostrarEnVyD": true,
              "validable": true
            },
            {
              "idElemento": "rfcVendedorOferta",
              "nombre": "Rfc",
              "tipo": "textbox",
              "longitudMaxima": 50,
              "tipoDato": "text",
              "mostrarEnVyD": true,
              "validable": true,
              "requerido": true,
              "expresionRegular": null
            },
            {
              "idElemento": "nombreRazonSocialVendedorOferta",
              "nombre": "Nombre o razón social",
              "tipo": "textbox",
              "longitudMaxima": 50,
              "tipoDato": "text",
              "mostrarEnVyD": true,
              "validable": true,
              "requerido": true,
              "expresionRegular": null
            },
            {
              "idElemento": "telefonoVendedorOferta",
              "nombre": "Teléfono",
              "tipo": "textbox",
              "longitudMaxima": 50,
              "tipoDato": "text",
              "mostrarEnVyD": true,
              "validable": true,
              "requerido": true,
              "expresionRegular": null
            },
            {
              "idElemento": "correoElectronicoVendedorOferta",
              "nombre": "Correo electrónico",
              "tipo": "textbox",
              "longitudMaxima": 50,
              "tipoDato": "text",
              "mostrarEnVyD": true,
              "validable": true,
              "requerido": true,
              "expresionRegular": null
            }
          ]
        },
        {
          "idSeccion": 12,
          "nombre": "Zona Riesgo",
          "tipo": "contenedor",
          "mostrarEnVyD": true,
          "validable": false,
          "secciones": [ ],
          "elementos": [
            {
              "idElemento": "esZonaRiesgoOferta",
              "nombre": "¿Proyecto en zona de riesgo?",
              "tipoDato": "bool",
              "mostrarEnVyD": true,
              "validable": false
            },
            {
              "idElemento": "zonaRiesgoOferta",
              "nombre": "Zona de riesgo",
              "tipoDato": "text",
              "mostrarEnVyD": true,
              "validable": false,
              "enLista": true
            },
            {
              "idElemento": "dictamenRiesgoOferta",
              "nombre": "Dictamen de riesgo",
              "tipoDato": "text",
              "mostrarEnVyD": true,
              "validable": false
            }
          ]
        }
      ]
    },
    {
      "idSeccion": 13,
      "nombre": "Carta de responsabilidad",
      "tipo": "contenedor",
      "mostrarEnVyD": false,
      "validable": false,
      "secciones": [ ],
      "elementos": [
        {

          "idElemento": "carta",
          "validable": true
        }
      ]
    }
  ]
}