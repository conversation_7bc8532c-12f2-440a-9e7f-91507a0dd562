﻿using Microsoft.WindowsAzure.Storage;
using Microsoft.WindowsAzure.Storage.Table;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Dominio.Util
{
    public class TableStorage
    {
        
        private static string CONNECTION_STRING_AZURE;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="appSettings"></param>
        public static void AppSettings(string appSettings)
        {
            //TableStorage.CONNECTION_STRING_AZURE = ConfigurationManager.AppSettings[appSettings].ToString();
            TableStorage.CONNECTION_STRING_AZURE = ConfigurationManager.ConnectionStrings[appSettings].ConnectionString;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tableName"></param>
        /// <returns></returns>
        private static CloudTable getConnection(string tableName)
        {
            if (TableStorage.CONNECTION_STRING_AZURE == null)
            {
                CONNECTION_STRING_AZURE = ConfigurationManager.ConnectionStrings["BitacoraStorageConnectionString"].ConnectionString;

            }

         
            CloudStorageAccount cloudStorageAccount = CloudStorageAccount.Parse(TableStorage.CONNECTION_STRING_AZURE);

            CloudTableClient tableClient = cloudStorageAccount.CreateCloudTableClient();

            CloudTable cloudTable = tableClient.GetTableReference(tableName);

            cloudTable.CreateIfNotExists();

            return cloudTable;

        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tableName"></param>
        /// <param name="obj"></param>
        public static void Insert(string tableName, TableEntity obj)
        {
            CloudTable cloudTable = TableStorage.getConnection(tableName);

            TableOperation tableOperation = TableOperation.InsertOrMerge(obj);

            cloudTable.Execute(tableOperation);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="tableName"></param>
        /// <param name="list"></param>
        public static void Insert<T>(string tableName, List<T> list)
        {
            ArrayList lista = new ArrayList(list);

            CloudTable cloudTable = TableStorage.getConnection(tableName);

            TableBatchOperation tableBatchOperation = new TableBatchOperation();

            foreach (TableEntity item in lista)
            {
                tableBatchOperation.InsertOrMerge(item);
            }

            cloudTable.ExecuteBatch(tableBatchOperation);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="appSettings"></param>
        /// <param name="tableName"></param>
        /// <returns></returns>
        public static DynamicTableEntity query(string tableName, string partitionKey, string rowkey)
        {
            CloudTable cloudTable = TableStorage.getConnection(tableName);

            var tableResults = cloudTable.Execute(TableOperation.Retrieve(partitionKey, rowkey));

            var entitys = (DynamicTableEntity)tableResults.Result;

            return entitys;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="dynamicTableEntity"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        public static T getEntity<T>(DynamicTableEntity dynamicTableEntity, Type type)
        {
            object objEntity = null;

            if (dynamicTableEntity != null)
            {
                Reflections reflections = new Reflections();

                objEntity = reflections.create(type);

                reflections.setPropertyValue(objEntity, "PartitionKey", dynamicTableEntity.PartitionKey);
                reflections.setPropertyValue(objEntity, "RowKey", dynamicTableEntity.RowKey);
                reflections.setPropertyValue(objEntity, "Timestamp", dynamicTableEntity.Timestamp);
                reflections.setPropertyValue(objEntity, "ETag", dynamicTableEntity.ETag);

                foreach (KeyValuePair<string, EntityProperty> row in dynamicTableEntity.Properties)
                {

                    PropertyInfo propertyInfo = reflections.getProperty(objEntity, row.Key);

                    if (reflections.isString(propertyInfo))
                    {
                        reflections.setPropertyValue(objEntity, row.Key, row.Value.StringValue);
                    }

                    if (reflections.isInt64(propertyInfo))
                    {
                        reflections.setPropertyValue(objEntity, row.Key, row.Value.Int64Value);
                    }

                    if (reflections.isInt32(propertyInfo))
                    {
                        Int32? value = null;
                        try
                        {
                            value = row.Value.Int32Value;
                        }
                        catch (Exception e)
                        {
                            value = Convert.ToInt32(row.Value.Int64Value.Value);
                        }

                        reflections.setPropertyValue(objEntity, row.Key, value);
                    }


                }
            }

            return (T)objEntity;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tableName"></param>
        /// <returns></returns>
        public static List<DynamicTableEntity> query(string tableName)
        {
            CloudTable cloudTable = TableStorage.getConnection(tableName);

            var condition = TableQuery.GenerateFilterCondition("PartitionKey", QueryComparisons.NotEqual, "123456");
            var querys = new TableQuery().Where(condition);
            List<DynamicTableEntity> lst = cloudTable.ExecuteQuery(querys).ToList();

            return lst;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="listDynamicTableEntity"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        public static List<T> getListEntity<T>(List<DynamicTableEntity> listDynamicTableEntity, Type type)
        {
            List<T> listEntity = new List<T>();

            if (listDynamicTableEntity != null && listDynamicTableEntity.Any())
            {

                foreach (DynamicTableEntity dynamicTableEntity in listDynamicTableEntity)
                {
                    listEntity.Add(getEntity<T>(dynamicTableEntity, type));
                }
            }

            return listEntity;
        }

    }

    /// <summary>
    /// 
    /// </summary>
    public class BitacoraApp : TableEntity
    {
        public string app { get; set; }
        public string idUsuario { get; set; }
        public string json { get; set; }
        public int code { get; set; }
        public string message { get; set; }


        public BitacoraApp(string vat)
        {
            this.PartitionKey = vat;
            this.RowKey = Guid.NewGuid().ToString();
        }

        public BitacoraApp()
        {
            string id = Guid.NewGuid().ToString();
            this.PartitionKey = id;
            this.RowKey = id;
        }

    }

}
