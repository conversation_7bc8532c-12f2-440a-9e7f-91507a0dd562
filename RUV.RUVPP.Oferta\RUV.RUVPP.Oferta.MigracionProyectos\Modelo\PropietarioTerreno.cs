//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace RUV.RUVPP.Oferta.MigracionProyectos.Modelo
{
    using System;
    using System.Collections.Generic;
    
    public partial class PropietarioTerreno
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public PropietarioTerreno()
        {
            this.Proyecto = new HashSet<Proyecto>();
        }
    
        public int idPropietarioTerreno { get; set; }
        public string nombrePropietario { get; set; }
        public string numeroRPP { get; set; }
        public string numeroCatastral { get; set; }
        public decimal areaTerrenoEscriturado { get; set; }
        public string numeroEscritura { get; set; }
        public string tomo { get; set; }
        public string volumen { get; set; }
        public System.DateTime fechaEscrituracion { get; set; }
        public int numeroNotario { get; set; }
        public System.DateTime fechaRegistro { get; set; }
        public Nullable<System.DateTime> fechaActualizacion { get; set; }
        public bool activo { get; set; }
    
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Proyecto> Proyecto { get; set; }
    }
}
