﻿[
  {
    "idElemento": "carta",
    "validaciones": [
      {
        "nombre": "Aceptó carta",
        "orden": 1,
        "funcion": "(function() { if (modelo.aceptacionCartaResponsabilidad === true) { return true; } else { return false; } })()",
        "mensajeError": "Debe seleccionar la casilla de aceptación de carta de responsabilidad."
      }
    ]
  },
  {
    "idElemento": "nombreFrente",
    "validaciones": [
      {
        "nombre": "Nombre de frente",
        "orden": 1,
        "funcion": "(function () { return validaciones.formatoCampo(modelo.ofertaVivienda.nombreFrente, 'NT', 1, 100); })()",
        "mensajeError": "Se requiere un dato alfanumérico"
      }
    ]
  },
  {
    "idElemento": "prototiposOferta",
    "validaciones": [
      {
        "nombre": "Prototipos de Oferta",
        "orden": 1,
        "funcion": "(function () {if(modelo.listaPrototipos.length > 0){return true;}else{return false;} })()",
        "mensajeError": "Debe seleccionar al menos una vivienda para poder ver el prototipo."
      }
    ]
  },
  {
    "idElemento": "prototipoDocumento",
    "funcionLongitudIndice": "(function() { return modelo.listaPrototipos.length; })()",
    "validaciones": [
      {
        "nombre": "Documentos Prototipos",
        "orden": 2,
        "funcion": "(function () {if(indice != undefined){var proto = modelo.listaPrototipos[indice];  if (proto && R.any(function (s) { return s.idPrototipo == proto.idPrototipo && s.mostrar; }, modelo.listaDocumentosPrototipos)){ return true;}else { return false; }} })()",
        "mensajeError": "Debe cargar por lo menos un documento para el prototipo."
      }
    ]
  },
  {
    "idElemento": "esSeleccionada",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function () { return R.any(function(z) { return z.esSeleccionado == true }, modelo.listaViviendasProyecto); })()",
        "mensajeError": "Debe seleccionar al menos una vivienda"
      }
    ],
    "reglasDependientes": [

      { "idElemento": "prototiposOferta" },
      { "idElemento": "prototipoDocumento" }

    ]
  },
  {
    "idElemento": "nombreDirectorOferta",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function () { return validaciones.formatoCampo(modelo.directorResponsableObra.nombreDRO, 'NT', 1, 50); })()",
        "mensajeError": "Se requiere un dato alfanumérico"
      }
    ],
    "reglasDependientes": [
      {
        "idElemento": "apellidoMaternoOferta"
      }
    ]
  },
  {
    "idElemento": "apellidoPaternoOferta",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function () { return validaciones.formatoCampo(modelo.directorResponsableObra.apellidoPaterno, 'NT', 1, 50); })()",
        "mensajeError": "Se requiere un dato alfanumérico"
      }
    ],
    "reglasDependientes": [
      {
        "idElemento": "apellidoMaternoOferta"
      }
    ]
  },
  {
    "idElemento": "apellidoMaternoOferta",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function () { return validaciones.formatoCampo(modelo.directorResponsableObra.apellidoMaterno, 'NT', 0, 50); })()",
        "mensajeError": "Se requiere un dato alfanumérico"
      }
    ]
  },
  {
    "idElemento": "numeroPeritoOferta",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function () { return validaciones.formatoCampo(modelo.directorResponsableObra.noPerito, 'NT', 1, 50); })()",
        "mensajeError": "Se requiere un dato alfanumérico"
      }
    ]
  },
  {
    "idElemento": "fechaVigenciaOferta",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function () { return modelo.directorResponsableObra.fechaVigencia != ''; })()",
        "mensajeError": "Debe introducir una fecha de vigencia."
      },
      {
        "nombre": "Fecha invalida",
        "orden": 2,
        "funcion": "(function () { if(modelo.modalActiva === false){ return true; }else{ var fecha = new Date(); var fechaSeleccionada = new Date(modelo.directorResponsableObra.fechaVigencia); return ( fechaSeleccionada >= fecha ); } })()",
        "mensajeError": "La fecha de vigencia debe ser mayor a la fecha actual."
      }
    ]
  },
  {
    "idElemento": "licenciaDROOferta",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function () { return (modelo.directorResponsableObra.licencia.idDocumento > 0); })()",
        "mensajeError": "Debe cargar un documento"
      }
    ]
  },
  {
    "idElemento": "identificacionDROOferta",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function () { return (modelo.directorResponsableObra.identificacionOficial.idDocumento > 0); })()",
        "mensajeError": "Debe cargar un documento"
      }
    ]
  },
  {
    "idElemento": "nombrePropietarioOferta",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function () { return validaciones.formatoCampo(modelo.propietarioTerreno.nombrePropietario, 'NT', 1, 50); })()",
        "mensajeError": "Se requiere un dato alfanumérico"
      }
    ]
  },
  {
    "idElemento": "tomoOferta",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function () { return validaciones.formatoCampo(modelo.propietarioTerreno.tomo, 'NT', 1, 10); })()",
        "mensajeError": "Se requiere un dato alfanumérico"
      }
    ]
  },
  {
    "idElemento": "numeroEscrituraOferta",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function () { return validaciones.formatoCampo(modelo.propietarioTerreno.noEscritura, 'N', 1, 15); })()",
        "mensajeError": "Se requiere un dato numérico"
      }
    ]
  },
  {
    "idElemento": "numeroCatastralOferta",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function () { return validaciones.formatoCampo(modelo.propietarioTerreno.noCatastral, 'N', 1, 30); })()",
        "mensajeError": "Se requiere un dato numérico"
      }
    ]
  },
  {
    "idElemento": "volumenOferta",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function () { return validaciones.formatoCampo(modelo.propietarioTerreno.volumen, 'NT', 1, 15 ); })()",
        "mensajeError": "Se requiere un dato numérico"
      }
    ]
  },
  {
    "idElemento": "areaTerrenoOferta",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function () { return validaciones.formatoCampo(modelo.propietarioTerreno.areaTerrenoEscriturado, 'N', 1, 6 ); })()",
        "mensajeError": "Se requiere un dato numérico"
      }
    ]
  },
  {
    "idElemento": "numeroNotarioOferta",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function () { return validaciones.formatoCampo(modelo.propietarioTerreno.noNotario, 'N', 1, 5 ); })()",
        "mensajeError": "Se requiere un dato numérico"
      }
    ]
  },
  {
    "idElemento": "numeroRPPOferta",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function () { return validaciones.formatoCampo(modelo.propietarioTerreno.noRPP, 'N', 1, 8 ); })()",
        "mensajeError": "Se requiere un dato numérico"
      }
    ]
  },
  {
    "idElemento": "numeroCatastralOferta",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function () { return validaciones.formatoCampo(modelo.propietarioTerreno.noCatastral, 'N', 1, 30 ); })()",
        "mensajeError": "Se requiere un dato numérico"
      }
    ]
  },
  {
    "idElemento": "fechaEscrituracionOferta",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function () { return modelo.propietarioTerreno.fechaEscrituracion != ''; })()",
        "mensajeError": "Este dato es requerido"
      },
      {
        "nombre": "Fecha invalida",
        "orden": 2,
        "funcion": "(function () { if(modelo.modalActiva === false){ return true; }else{ var fecha = new Date(); var fechaSeleccionada = new Date(modelo.propietarioTerreno.fechaEscrituracion); return ( fechaSeleccionada <= fecha ); } })()",
        "mensajeError": "La fecha de escrituración debe ser menor a la fecha actual."
      }
    ]
  },
  {
    "idElemento": "esConstructorOferta",
    "validaciones": [
      {
        "nombre": "Existe constructor",
        "orden": 1,
        "funcion": "(function() { if (modelo.constructor.esElMismo === true || (modelo.constructor.esElMismo === false && validaciones.formatoCampo(modelo.constructor.noRegistroRUV, 'N', 8, 8 ))) { return true; } else { return false; } })() ",
        "mensajeError": "Debe introducir el número de registro ruv."
      }
    ],
    "reglasDependientes": [
      {
        "idElemento": "noRegistroRUVConstructorOferta"
      }
    ]
  },
  {
    "idElemento": "noRegistroRUVConstructorOferta",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function() { if (modelo.constructor.esElMismo === true || (modelo.constructor.esElMismo === false && modelo.constructor.noRegistroRUV != '')) { return true; } else { return false; } })()",
        "mensajeError": "Este dato es requerido"
      },
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function() { if (modelo.constructor.esElMismo === true || (modelo.constructor.esElMismo === false && validaciones.formatoCampo(modelo.constructor.noRegistroRUV, 'N', 8, 8 ))) { return true; } else { return false; } })()",
        "mensajeError": "Se requiere un número de 8 dígitos"
      }
    ]
  },
  {
    "idElemento": "esPromotorOferta",
    "validaciones": [
      {
        "nombre": "Existe promotor",
        "orden": 1,
        "funcion": "(function() { if (modelo.promotor.esElMismo === true || (modelo.promotor.esElMismo === false && modelo.listaPromotores.length > 0)) { return true; } else { return false; } })()",
        "mensajeError": "Debe introducir la información de al menos un promotor."
      }
    ],
    "reglasDependientes": [
      {
        "idElemento": "listaPromotoresOferta"
      }
    ]
  },
  {
    "idElemento": "listaPromotoresOferta",
    "validaciones": [
      {
        "nombre": "Número de promotores",
        "orden": 1,
        "funcion": "(function() { if (modelo.promotor.esElMismo === true) { return true; } else { return modelo.listaPromotores.filter(f => f.mostrar !== false).length > 0; } })()",
        "mensajeError": "Debe introducir al menos un promotor."
      }
    ]
  },
  {
    "idElemento": "rfcPromotorOferta",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function () { if (modelo.modalPromotorActiva === false || modelo.promotor.esElMismo === true) { return true; } else { return modelo.promotor.rfc != ''; } })()",
        "mensajeError": "Debe introducir el RFC."
      },
      {
        "nombre": "RFC Valido",
        "orden": 2,
        "funcion": "(function() { if (modelo.modalPromotorActiva === false || modelo.promotor.esElMismo === true) { return true; } else { return validaciones.esRfcValido(modelo.promotor.rfc); } })()",
        "mensajeError": "El formato del RFC no es el correcto."
      },
      {
        "nombre": "Longitud maxima",
        "orden": 3,
        "funcion": "(function() { if (modelo.modalPromotorActiva === false || modelo.promotor.esElMismo === true) { return true; } else { return modelo.promotor.rfc.length <= 13; } })()",
        "mensajeError": "El RFC no debe de ser mayor a 13 caracters."
      }
    ]
  },
  {
    "idElemento": "nombreRazonSocialPromotorOferta",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function() { if (modelo.modalPromotorActiva === false || modelo.promotor.esElMismo === true) { return true; } else { return validaciones.formatoCampo(modelo.promotor.nombreRazonSocial, 'NT', 1, 150 ); } })()",
        "mensajeError": "Debe introducir el nombre o la razón social del promotor."
      }
    ]
  },
  {
    "idElemento": "telefonoPromotorOferta",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function() { if (modelo.modalPromotorActiva === false || modelo.promotor.esElMismo === true) { return true; } else { return modelo.promotor.numeroTelefono != ''; } })() ",
        "mensajeError": "Debe introducir un teléfono."
      },
      {
        "nombre": "Dato requerido",
        "orden": 2,
        "funcion": "(function() { if (modelo.modalPromotorActiva === false || modelo.promotor.esElMismo === true) { return true; } else { return validaciones.esTelefonoValido(modelo.promotor.numeroTelefono); } })()",
        "mensajeError": "El teléfono debe de tener de 10 a 11 dígitos"
      }
    ]
  },
  {
    "idElemento": "correoElectronicoPromotorOferta",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function() { if (modelo.modalPromotorActiva === false || modelo.promotor.esElMismo === true) { return true; } else { return modelo.promotor.correoElectronico != ''; } })()",
        "mensajeError": "Debe introducir un correo electrónico."
      },
      {
        "nombre": "Dato requerido",
        "orden": 2,
        "funcion": "(function() { if (modelo.modalPromotorActiva === false || modelo.promotor.esElMismo === true) { return true; } else { return validaciones.esCorreoValido(modelo.promotor.correoElectronico); } })()",
        "mensajeError": "Debe introducir un correo electrónico valido, debe introducir solo letras minúsculas."
      }
    ]
  },
  {
    "idElemento": "esVendedorOferta",
    "validaciones": [
      {
        "nombre": "Existe vendedor",
        "orden": 1,
        "funcion": "(function() { if (modelo.vendedor.esElMismo === true || (modelo.vendedor.esElMismo === false && modelo.listaVendedores.length > 0)) { return true; } else { return false; } })()",
        "mensajeError": "Debe introducir la información de al menos un vendedor."
      }
    ],
    "reglasDependientes": [
      {
        "idElemento": "listaVendedoresOferta"
      }
    ]
  },
  {
    "idElemento": "listaVendedoresOferta",
    "validaciones": [
      {
        "nombre": "Número de vendedores",
        "orden": 1,
        "funcion": "(function() { if (modelo.vendedor.esElMismo === true) { return true; } else { return modelo.listaVendedores.filter(f => f.mostrar !== false).length > 0; } })()",
        "mensajeError": "Debe introducir al menos un vendedor."
      }
    ]
  },
  {
    "idElemento": "rfcVendedorOferta",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function () { if (modelo.modalVendedorActiva === false || modelo.vendedor.esElMismo === true) { return true; } else { return modelo.vendedor.rfc != ''; } })()",
        "mensajeError": "Debe introducir el RFC del vendedor."
      },
      {
        "nombre": "RFC Valido",
        "orden": 2,
        "funcion": "(function() { if (modelo.modalVendedorActiva === false || modelo.vendedor.esElMismo === true) { return true; } else { return validaciones.esRfcValido(modelo.vendedor.rfc); } })()",
        "mensajeError": "El formato del RFC no es el correcto."
      },
      {
        "nombre": "Longitud maxima",
        "orden": 3,
        "funcion": "(function() { if (modelo.modalVendedorActiva === false || modelo.vendedor.esElMismo === true) { return true; } else { return modelo.vendedor.rfc.length <= 13; } })()",
        "mensajeError": "El RFC no debe de ser mayor a 13 caracters."
      }
    ]
  },
  {
    "idElemento": "nombreRazonSocialVendedorOferta",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function() { if (modelo.modalVendedorActiva === false || modelo.vendedor.esElMismo === true) { return true; } else { return validaciones.formatoCampo(modelo.vendedor.nombreRazonSocial, 'NT', 1, 150 ); } })()",
        "mensajeError": "Debe introducir el nombre o razón social del vendedor."
      }
    ]
  },
  {
    "idElemento": "telefonoVendedorOferta",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function() { if (modelo.modalVendedorActiva === false || modelo.vendedor.esElMismo === true) { return true; } else { return modelo.vendedor.numeroTelefono != ''; } })() ",
        "mensajeError": "Debe introducir un teléfono."
      },
      {
        "nombre": "Dato requerido",
        "orden": 2,
        "funcion": "(function() { if (modelo.modalVendedorActiva === false || modelo.vendedor.esElMismo === true) { return true; } else { return validaciones.esTelefonoValido(modelo.vendedor.numeroTelefono); } })()",
        "mensajeError": "El teléfono debe de tener de 10 a 11 dígitos"
      }
    ]
  },
  {
    "idElemento": "correoElectronicoVendedorOferta",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function() { if (modelo.modalVendedorActiva === false || modelo.vendedor.esElMismo === true) { return true; } else { return modelo.vendedor.correoElectronico != ''; } })()",
        "mensajeError": "Debe introducir un correo electrónico."
      },
      {
        "nombre": "Dato requerido",
        "orden": 2,
        "funcion": "(function() { if (modelo.modalVendedorActiva === false || modelo.vendedor.esElMismo === true) { return true; } else { return validaciones.esCorreoValido(modelo.vendedor.correoElectronico); } })()",
        "mensajeError": "Debe introducir un correo electrónico valido, debe introducir solo letras minúsculas."
      }
    ]
  },
  {
    "idElemento": "esZonaRiesgoOferta",
    "validaciones": [
      {
        "nombre": "Hay zonas riego declaradas",
        "orden": 1,
        "funcion": "(function() { if (modelo.ofertaVivienda.esZonaRiesgo === true) { return R.any(function(z) { return z.esSeleccionada == true }, modelo.zonasRiesgo); } else if (modelo.ofertaVivienda.esZonaRiesgo === false) { return true; } })()",
        "mensajeError": "Es necesario indicar por lo menos una zona de riesgo."
      }
    ],
    "reglasDependientes": [
      {
        "idElemento": "zonaRiesgoOferta"
      }
    ]
  },
  {
    "idElemento": "zonaRiesgoOferta",
    "funcionLongitudIndice": "(function() { return modelo.zonasRiesgo.length; })()",
    "validaciones": [
      {
        "nombre": "Cuenta con solución",
        "orden": 1,
        "funcion": "(function () { var zona = modelo.zonasRiesgo[indice]; if(zona.esSeleccionada) { return zona.solucionMitigarRiesgo; } else { return true; } })()",
        "mensajeError": "Es necesario colocar una solución para la zona de riesgo."
      },
      {
        "nombre": "Longitud de campo",
        "orden": 2,
        "funcion": "(function () { var zona = modelo.zonasRiesgo[indice]; if(zona.esSeleccionada) { return zona.solucionMitigarRiesgo && zona.solucionMitigarRiesgo.length <= 500; } else { return true; } })()",
        "mensajeError": "La solución para mitigar el riesgo solo admite 500 caracteres."
      }
    ],
    "reglasDependientes": [
      {
        "idElemento": "esZonaRiesgoOferta"
      }
    ]
  },
  {
    "idElemento": "documentosComplementarios",
    "validaciones": [
      {
        "nombre": "Documento requerido",
        "orden": 1,
        "funcion": "(function () { return( modelo.documentosComplementarios.length >= 0 ) })()",
        "mensajeError": ""
      }
    ]
  },
  {
    "idElemento": "documentosLicenciasFactibilidades",
    "validaciones": [
      {
        "nombre": "Documento requerido",
        "orden": 1,
        "funcion": "(function () { return( modelo.licenciasFactibilidades.filter(f => f.mostrar !== false).length > 0 ) })()",
        "mensajeError": "Debe cargar por lo menos 1 documento de Licencia de Construcción o Factibilidad"
      }
    ]
  },
  {
    "idElemento": "documentoLicenciaFactibilidad",
    "validaciones": [
      {
        "nombre": "Requerido",
        "orden": 1,
        "funcion": "(function () { if(modelo.modalActiva === false){ return true; }else{ return (modelo.licenciaFactibilidadSeleccionado.documentoCargado.nombreArchivo != '');} })()",
        "mensajeError": "Debe cargar el documento de Licencia de Construcción o Factibilidad"
      }
    ]
  },
  {
    "idElemento": "numeroOficio",
    "validaciones": [
      {
        "nombre": "Requerido",
        "orden": 1,
        "funcion": "(function () { if(modelo.modalActiva === false){ return true; }else{ return (modelo.licenciaFactibilidadSeleccionado.numeroOficio != null && modelo.licenciaFactibilidadSeleccionado.numeroOficio != undefined && modelo.licenciaFactibilidadSeleccionado.numeroOficio != '');} })()",
        "mensajeError": "Debe introducir un numero de oficio."
      }
    ]
  },
  {
    "idElemento": "emitidoPor",
    "validaciones": [
      {
        "nombre": "Requerido",
        "orden": 1,
        "funcion": "(function () { if(modelo.modalActiva === false){ return true; }else{ return (modelo.licenciaFactibilidadSeleccionado.emitidoPor != null && modelo.licenciaFactibilidadSeleccionado.emitidoPor != undefined && modelo.licenciaFactibilidadSeleccionado.emitidoPor != '');} })()",
        "mensajeError": "Debe indicar por quien fue emitido el documento."
      }
    ]
  },
  {
    "idElemento": "fechaEmision",
    "validaciones": [
      {
        "nombre": "Fecha Requerida",
        "orden": 1,
        "funcion": "(function () { if(modelo.modalActiva === false){ return true; }else{ return ( modelo.licenciaFactibilidadSeleccionado.fechaEmision != null && modelo.licenciaFactibilidadSeleccionado.fechaEmision != undefined && modelo.licenciaFactibilidadSeleccionado.fechaEmision != ''); } })()",
        "mensajeError": "Debe introducir una fecha de emisión."
      },
      {
        "nombre": "Fecha invalida",
        "orden": 2,
        "funcion": "(function () { if(modelo.modalActiva === false){ return true; }else{ var fecha = new Date(); var fechaSeleccionada = new Date(modelo.licenciaFactibilidadSeleccionado.fechaEmision); return ( fechaSeleccionada < fecha ); } })()",
        "mensajeError": "La fecha de emisión debe ser menor a la fecha actual."
      }
    ]
  },
  {
    "idElemento": "cuentaConDocumento",
    "validaciones": [
      {
        "nombre": "Dato invalido",
        "orden": 1,
        "funcion": "(function () { if(modelo.modalActiva === false){ return true; }else{ return (modelo.licenciaFactibilidadSeleccionado.esLicenciafactibilidad  === true || modelo.licenciaFactibilidadSeleccionado.esLicenciafactibilidad  === false ); } })()",
        "mensajeError": "Debe seleccionar el tipo de documento"
      }
    ],
    "reglasDependientes": [
      {
        "idElemento": "documentoLicenciaFactibilidad"
      }
    ]
  },
  {
    "idElemento": "cuentaFechaVigencia",
    "validaciones": [
      {
        "nombre": "Dato invalido",
        "orden": 1,
        "funcion": "(function () { if(modelo.modalActiva === false){ return true; }else{ return (modelo.licenciaFactibilidadSeleccionado.tieneFechaVigencia  === true || modelo.licenciaFactibilidadSeleccionado.tieneFechaVigencia  === false ); } })()",
        "mensajeError": "Debe seleccionar si tiene fecha de vigencia"
      }
    ],
    "reglasDependientes": [
      {
        "idElemento": "fechaVigencia"
      },
      {
        "idElemento": "documentosLicenciasFactibilidades"
      },
      {
        "idElemento": "cuentaConDocumento"
      }
    ]
  },
  {
    "idElemento": "fechaVigencia",
    "validaciones": [
      {
        "nombre": "Fecha Requerida",
        "orden": 1,
        "funcion": "(function () { if(modelo.modalActiva === false){ return true; }else{  if(modelo.licenciaFactibilidadSeleccionado.tieneFechaVigencia === true){ return ( modelo.licenciaFactibilidadSeleccionado.fechaVigencia != null && modelo.licenciaFactibilidadSeleccionado.fechaVigencia != undefined && modelo.licenciaFactibilidadSeleccionado.fechaVigencia != '');} else{ return true;} } })()",
        "mensajeError": "Debe introducir una fecha de vigencia."
      },
      {
        "nombre": "Fecha invalida",
        "orden": 2,
        "funcion": "(function () { if(modelo.modalActiva === false){ return true; }else{ if(modelo.licenciaFactibilidadSeleccionado.tieneFechaVigencia === true){ var fecha = new Date(); var fechaSeleccionada = new Date(modelo.licenciaFactibilidadSeleccionado.fechaVigencia);  return( fechaSeleccionada > fecha ) }else{ return true; } } })()",
        "mensajeError": "La fecha de vigencia debe ser mayor a la fecha actual."
      }
    ]
  }
]