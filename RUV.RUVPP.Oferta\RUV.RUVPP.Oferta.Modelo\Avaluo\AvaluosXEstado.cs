﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.Avaluo
{
    public class AvaluosXEstado
    {
        public string Estado { get; set; }

        public List<DTOAvaluo> listaDTOAvaluos { get; set; }

        public bool desplegado { get; set; }

        public int idBitacoraAvaluo { get; set; }

        public bool estaSeleccionado { get; set; }
    }
}
