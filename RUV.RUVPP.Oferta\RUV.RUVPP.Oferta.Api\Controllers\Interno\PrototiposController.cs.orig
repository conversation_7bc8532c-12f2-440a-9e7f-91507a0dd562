﻿using Microsoft.ApplicationInsights;
using RUV.RUVPP.Oferta.Modelo.Comun;
using RUV.RUVPP.Oferta.Dominio.Prototipo;
using RUV.RUVPP.Oferta.Modelo.Prototipos.Api;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;
using System.Web.Http.Description;

namespace RUV.RUVPP.Oferta.Api.Controllers.Interno
{    
    /// <summary>
    /// 
    /// </summary>
    [RoutePrefix("interno/api/prototipos")]
    public class PrototiposController : ApiControllerBase
    {
        private readonly IServicioPrototipo _servicioPrototipo;
        private readonly IServicioPrototipoCatalogos _servicioPrototipoCatalogos;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="clienteTelemetria"></param>
        /// <param name="servicioPrototipo"></param>
        public PrototiposController(TelemetryClient clienteTelemetria, IServicioPrototipo servicioPrototipo, IServicioPrototipoCatalogos servicioPrototipoCatalogos)
            : base(clienteTelemetria)
        {
            this._servicioPrototipo = servicioPrototipo;
            this._servicioPrototipoCatalogos = servicioPrototipoCatalogos;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet, Route("catalogos/tipologiavivienda")]
        [ResponseType(typeof(List<TipologiaVivienda>))]
        public async Task<HttpResponseMessage> ObtenerCatalogoTipologiaViviendaAsync()
        {
            var result = await this._servicioPrototipoCatalogos.ObtenerCatalogoDeTipologiaViviendaAsync();

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet, Route("catalogos/clasificacionvivienda")]
        [ResponseType(typeof(List<TipologiaVivienda>))]
        public async Task<HttpResponseMessage> ObtenerCatalogoClasificacionViviendaAsync()
        {
            var result = await this._servicioPrototipoCatalogos.ObtenerCatalogoDeClasificacionViviendaAsync();

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }


        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet, Route("catalogos/tipoDimensionAreaVivienda")]
        [ResponseType(typeof(List<TipoDimensionAreaVivienda>))]
        public async Task<HttpResponseMessage> ObtenerCatalogoTipoDimensionAreaViviendaAsync()
        {
            var result = await this._servicioPrototipoCatalogos.ObtenerCatalogoTipoDimensionAreaViviendaAsync();

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet, Route("catalogos/{idTipoDistribucion}/clasificacionareavivienda")]
        [ResponseType(typeof(List<AreaVivienda>))]
        public async Task<HttpResponseMessage> ObtenerCatalogoAreaViviendaAsync(int idTipoDistribucion)
        {
            var result = await this._servicioPrototipoCatalogos.ObtenerCatalogoAreaViviendaAsync(idTipoDistribucion);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="prototipo"></param>
        /// <returns></returns>
        [HttpPost, Route("prototipo")]
        [ResponseType(typeof(Prototipo))]
        public async Task<HttpResponseMessage> GuardarPrototipoAsync(Prototipo prototipo)
        {
            var result = await this._servicioPrototipo.GuardarPrototipoAsync(prototipo);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="idEmpresa"></param>
        /// <param name="idPrototipo"></param>
        /// <param name="precioDesde"></param>
        /// <param name="precioHasta"></param>
        /// <param name="idTipologiaVivienda"></param>
        /// <param name="nombre"></param>
        /// <returns></returns>
        [HttpGet, Route("obtenerprototipos")]
        [ResponseType(typeof(ResultadoPaginado<List<Prototipo>>))]
        public async Task<HttpResponseMessage> ObtenerPrototiposFiltradoAsync(int tamanioPagina, int pagina, int idEmpresa, int? idPrototipo = null, int? precioDesde = null, int? precioHasta = null, int? idTipologiaVivienda = null, string nombre = null)
        {
            var result = await this._servicioPrototipo.ObtenerPrototiposFiltradoAsync(tamanioPagina, pagina, idEmpresa, idPrototipo, precioDesde, precioHasta, idTipologiaVivienda, nombre);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

<<<<<<< Updated upstream
        /// <summary>
        /// 
        /// </summary>
        /// <param name="idPrototipo"></param>
        /// <param name="idEmpresa"></param>
        /// <returns></returns>
        [HttpGet, Route("eliminarprototipo")]
        [ResponseType(typeof(bool))]
        public async Task<HttpResponseMessage> EliminarPrototipoAsync(int idPrototipo, int idEmpresa)
        {
            var result = await this._servicioPrototipo.EliminarPrototipoAsync(idPrototipo, idEmpresa);
=======
        [HttpGet, Route("prototipo/{idPrototipo}")]
        [ResponseType(typeof(ResultadoPaginado<Prototipo>))]
        public async Task<HttpResponseMessage> ObtenerPrototipoPorIdAsync(int idPrototipo)
        {
            var result = await this._servicioPrototipo.ObtenerPrototipoPorIdAsync(idPrototipo);
>>>>>>> Stashed changes

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }
    }
}