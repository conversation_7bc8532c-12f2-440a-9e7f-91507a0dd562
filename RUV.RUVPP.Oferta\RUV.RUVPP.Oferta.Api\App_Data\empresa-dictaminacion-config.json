﻿{
  "idProducto": 1,
  "idServicio": 1,
  "idRegistro": 1,
  "idOrdenTrabajo": 1234,
  "secciones": [
    {
      "idSeccion": 2,
      "estatus": null,
      "mensajeRechazo": null,
      "mensajeRechazoAnterior": null,
      "actualizadoPorUsuario": null,
      "controles": null,
      "secciones": [
        {
          "idSeccion": 3,
          "estatus": null,
          "mensajeRechazo": null,
          "mensajeRechazoAnterior": null,
          "actualizadoPorUsuario": null,
          "secciones": null,
          "controles": [
            {
              "idControl": "rfc",
              "estatus": null,
              "actualizadoPorUsuario": null,
              "mensajeRechazo": "Error en el RFC...",
              "mensajeRechazoAnterior": null              
            },
            {
              "idControl": "rsocial",
              "estatus": null,
              "actualizadoPorUsuario": null,
              "mensajeRechazo": null,
              "mensajeRechazoAnterior": null
            },
            {
              "idControl": "numemp",
              "estatus": null,
              "actualizadoPorUsuario": null,
              "mensajeRechazo": "Error en el Numero de empleados...",
              "mensajeRechazoAnterior": null
            },
            {
              "idControl": "opcionPrimera",
              "estatus": null,
              "actualizadoPorUsuario": null,
              "mensajeRechazo": "Error la opcion primera...",
              "mensajeRechazoAnterior": null
            }
          ]
        },
        {
          "idSeccion": 5,
          "estatus": null,
          "actualizadoPorUsuario": null,
          "controles": null,
          "mensajeRechazo": null,
          "mensajeRechazoAnterior": null,
          "secciones": null
        },
        {
          "idSeccion": 6,
          "estatus": null,
          "actualizadoPorUsuario": null,
          "controles": null,
          "mensajeRechazo": null,
          "mensajeRechazoAnterior": null,
          "secciones": null
        }
      ]
    },
    {
      "idSeccion": 7,
      "estatus": null,
      "actualizadoPorUsuario": null,
      "controles": null,
      "mensajeRechazo": null,
      "mensajeRechazoAnterior": null,
      "secciones": [
        {
          "idSeccion": 8,
          "estatus": null,
          "actualizadoPorUsuario": null,
          "controles": null,
          "mensajeRechazo": null,
          "mensajeRechazoAnterior": null,
          "secciones": null
        },
        {
          "idSeccion": 9,
          "estatus": null,
          "actualizadoPorUsuario": null,
          "controles": null,
          "mensajeRechazo": null,
          "mensajeRechazoAnterior": null,
          "secciones": null
        },
        {
          "idSeccion": 10,
          "estatus": null,
          "actualizadoPorUsuario": null,
          "controles": null,
          "mensajeRechazo": null,
          "mensajeRechazoAnterior": null,
          "secciones": null
        }
      ]
    },
    {
      "idSeccion": 12,
      "estatus": null,
      "actualizadoPorUsuario": null,
      "controles": null,
      "mensajeRechazo": null,
      "mensajeRechazoAnterior": null,
      "secciones": [
        {
          "idSeccion": 13,
          "estatus": null,
          "actualizadoPorUsuario": null,
          "controles": null,
          "mensajeRechazo": null,
          "mensajeRechazoAnterior": null,
          "secciones": null
        },
        {
          "idSeccion": 17,
          "estatus": null,
          "actualizadoPorUsuario": null,
          "controles": null,
          "mensajeRechazo": null,
          "mensajeRechazoAnterior": null,
          "secciones": null
        },
        {
          "idSeccion": 18,
          "estatus": null,
          "actualizadoPorUsuario": null,
          "controles": null,
          "mensajeRechazo": null,
          "mensajeRechazoAnterior": null,
          "secciones": null
        }
      ]
    },
    {
      "idSeccion": 19,
      "estatus": null,
      "actualizadoPorUsuario": true,
      "controles": null,
      "mensajeRechazo": "Seccion padre Mal",
      "mensajeRechazoAnterior": null,
      "fechaDictaminacion": 1471381167881,
      "secciones": [
        {
          "idSeccion": 21,
          "estatus": null,
          "actualizadoPorUsuario": true,
          "mensajeRechazo": "Subseccion padre Mal",
          "mensajeRechazoAnterior": null,
          "fechaDictaminacion": 1471381167881,
          "secciones": null,
          "controles": [
            {
              "idControl": "ActaConstitutiva",
              "estatus": null,
              "actualizadoPorUsuario": null,
              "mensajeRechazo": "Acta Mal",
              "mensajeRechazoAnterior": null,
              "fechaDictaminacion": 1471381167881
            },
            {
              "idControl": "EstadoCuenta",
              "estatus": null,
              "actualizadoPorUsuario": null,
              "mensajeRechazo": "Edo cuenta Mal",
              "mensajeRechazoAnterior": null,
              "fechaDictaminacion": 1471381167881
            },
            {
              "idControl": "ComprobanteDomicilio",
              "estatus": null,
              "actualizadoPorUsuario": null,
              "mensajeRechazo": "Comprobante de domicilio Mal",
              "mensajeRechazoAnterior": null,
              "fechaDictaminacion": 1471381167881
            },
            {
              "idControl": "CedulaFiscal",
              "estatus": null,
              "actualizadoPorUsuario": null,
              "mensajeRechazo": "Cedula fiscal Mal",
              "mensajeRechazoAnterior": null,
              "fechaDictaminacion": 1471381167881
            },
            {
              "idControl": "CedulaProfesional",
              "estatus": null,
              "actualizadoPorUsuario": true,
              "mensajeRechazo": null,
              "mensajeRechazoAnterior": null
            },
            {
              "idControl": "PoderNotarial",
              "estatus": null,
              "actualizadoPorUsuario": null,
              "mensajeRechazo": "",
              "mensajeRechazoAnterior": null
            }
          ]
        }
      ]
    }
  ]
}
