﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api
{
    public class OrdenDeVerificacion
    {
        public string ordenVerificacion { get; set; }
        public int numeroViviendas { get; set; }
        public string fechaRegistro { get; set; }
        public string estatus { get; set; }
        public string verificador { get; set; }
        public string aseguradora { get; set; }
        public int idVerificador { get; set; }
        public int idAseguradora { get; set; }
        public int idRelacionComercial { get; set; }
        public string idEmpresaInstAseguradora { get; set; }
        public string razonSocialAseguradora { get; set; }
        public string rfcAseguradora { get; set; }        
        public string idEmpresaInstDesarrollador { get; set; }
        public string razonSocialDesarrollador { get; set; }
        public string rfcDesarrollador { get; set; }
        public string tipoAsignacion { get; set; }
        public string fechaAsignacion { get; set; }
        public string noContrato { get; set; }
        public bool evaluacionRiesgo { get; set; }

        public string domicilio { get; set; }

        public string email { get; set; }

        public string telefono { get; set; }

        public int numeroViviendassinpoliza { get; set; }

        public int idTipoAsignacion { get; set; }

        public DateTime? fechaasignacion { get; set; }

        public string fechaAceptacion { get; set; }

        public string costoPoliza  { get; set; }

        public string numeroContrato { get; set; }

        public bool activo { get; set; }

        public int? idDocumentoContrato { get; set; }

        public string urlDocumento { get; set; }

        public string nombreArchivo { get; set; }

        public List<CuvSeleccionada> listaCuvs { get; set;}

        public int? idEstatusRelacionComercial { get; set; }

        public int? idEstatusPagoEvaluacionRiesgo { get; set; }

        public int idOferente { get; set; }

        public string razonSocialVerificador { get; set; }
        public string rfcVerificador { get; set; }

        public int? idOferta { get; set; }
        public string fechaInicioVerificacion { get; set; }
        public string fechaFinVerificacion { get; set; }
        public string fechaInicioPrestacion { get; set; }

        public bool? confPagoAntEvaRiesgo { get; set; }

        public bool? confPagoPoliza { get; set; }

        public bool? confPagoDiferencia { get; set; }

        public string motivoRechazo { get; set; }

        public string motivoRechazoEvaluacionRiesgo { get; set; }

        public string nombreFrente { get; set; }

        public int? idUsuarioConfirmoCosto { get; set; }

        public string nombreUsuarioConfirmo { get; set; }
        public DateTime? fechaAsignacionAseguradora { get; set; }
        public bool cuvsBloqueadasDias { get; set; }
        public string fechaBloqueo { get; set; }
        public bool cuvsBloqueadas { get; set; }
        public string usuarioSeguridadVerificador { get; set; }
        public int? porcentajeAvance { get; set; }
        public int? idAcreedorERP { get; set; }
    }
}
