﻿{
  "idProducto": 1,
  "producto": "Empresa",
  "idServicio": 1,
  "servicio": "Registro de empresa",
  "secciones": [
    {
      "idSeccion": 1,
      "nombre": "Términos legales",
      "tipo": "contenedor",
      "mostrarEnVyD": false,
      "validable": false,
      "ruta": "TerminosLegales",
      "secciones": [ ]
    },
    {
      "idSeccion": 2,
      "nombre": "Datos generales",
      "tipo": "contenedor",
      "mostrarEnVyD": false,
      "validable": false,
      "secciones": [
        {
          "idSeccion": 3,
          "nombre": "Generales",
          "tipo": "contenedor",
          "mostrarEnVyD": false,
          "validable": false,
          "ruta": "DatosGenerales/Generales",
          "secciones": [ ],
          "controles": [
            {
              "idControl": "rfc",
              "nombre": "RFC",
              "tipo": "textbox",
              "longitudMaxima": 5,
              "tipoDato": "text",
              "validable": true,
              "requerido": true,
              "expresionRegular": null
            },
            {
              "idControl": "rsocial",
              "nombre": "Razon Social",
              "tipo": "textbox",
              "longitudMaxima": 10,
              "tipoDato": "text",
              "validable": true,
              "requerido": true,
              "expresionRegular": null
            },
            {
              "idControl": "numemp",
              "nombre": "Numero de empleados",
              "tipo": "textbox",
              "longitudMaxima": null,
              "tipoDato": "number",
              "validable": true,
              "requerido": true,
              "expresionRegular": null,
              "rangoMinimo": 1,
              "rangoMaximo": 5
            },
            {
              "idControl": "nrp",
              "nombre": "Numero de Registro Patronal",
              "tipo": "textbox",
              "longitudMaxima": 10,
              "tipoDato": "text",
              "validable": true,
              "requerido": true,
              "expresionRegular": null
            },
            {
              "idControl": "nomRep",
              "nombre": "Nombre",
              "estaEnLista": true,
              "validable": true,
              "requerido": false
            }
          ]
        },
        {
          "idSeccion": 4,
          "nombre": "Ubicación",
          "tipo": "contenedor",
          "mostrarEnVyD": false,
          "validable": false,
          "ruta": "DatosGenerales/Ubicacion",
          "secciones": [ ]
        },
        {
          "idSeccion": 5,
          "nombre": "Domicilio fiscal",
          "tipo": "contenedor",
          "mostrarEnVyD": false,
          "validable": false,
          "ruta": "DatosGenerales/DomicilioFiscal",
          "secciones": [ ]
        },
        {
          "idSeccion": 6,
          "nombre": "Datos de contacto",
          "tipo": "contenedor",
          "mostrarEnVyD": false,
          "validable": false,
          "ruta": "DatosGenerales/DatosContacto",
          "secciones": [ ]
        }
      ]
    },
    {
      "idSeccion": 7,
      "nombre": "Datos Empresa",
      "tipo": "contenedor",
      "mostrarEnVyD": false,
      "validable": false,
      "secciones": [
        {
          "idSeccion": 8,
          "nombre": "Datos Empresa/Institución",
          "tipo": "contenedor",
          "mostrarEnVyD": false,
          "validable": false,
          "ruta": "DatosEmpresa/DatosEmpresaInstitucion",
          "secciones": [ ]
        },
        {
          "idSeccion": 9,
          "nombre": "Cuentas bancarias",
          "tipo": "contenedor",
          "mostrarEnVyD": false,
          "validable": false,
          "ruta": "DatosEmpresa/CuentasBancarias",
          "secciones": [ ]
        },
        {
          "idSeccion": 10,
          "nombre": "Empresa filial",
          "tipo": "contenedor",
          "mostrarEnVyD": false,
          "validable": false,
          "ruta": "DatosEmpresa/EmpresaFilial",
          "secciones": [ ]
        }
      ]
    },
    {
      "idSeccion": 12,
      "nombre": "Datos Representante",
      "tipo": "contenedor",
      "mostrarEnVyD": false,
      "validable": false,
      "secciones": [
        {
          "idSeccion": 13,
          "nombre": "Datos accionistas",
          "tipo": "contenedor",
          "mostrarEnVyD": false,
          "validable": false,
          "ruta": "DatosRepresentante/DatosAccionistas",
          "secciones": [ ]
        },
        {
          "idSeccion": 17,
          "nombre": "Datos representante legal",
          "tipo": "contenedor",
          "mostrarEnVyD": false,
          "validable": false,
          "ruta": "DatosRepresentante/DatosRepresentanteLegal",
          "secciones": [ ]
        },
        {
          "idSeccion": 18,
          "nombre": "Datos representante tecnico",
          "tipo": "contenedor",
          "mostrarEnVyD": false,
          "validable": false,
          "ruta": "DatosRepresentante/DatosRepresentanteTecnico",
          "secciones": [ ]
        }
      ]
    },
    {
      "idSeccion": 19,
      "nombre": "Documentación",
      "tipo": "contenedor",
      "mostrarEnVyD": true,
      "validable": true,
      "secciones": [
        {
          "idSeccion": 21,
          "nombre": "Documentación",
          "tipo": "contenedor",
          "mostrarEnVyD": true,
          "ruta": "Documentacion/Documentacion",
          "secciones": [ ],
          "controles": [
            {
              "idControl": "ActaConstitutiva",
              "nombre": "Acta constitutiva",
              "tipo": "cargaArchivo",
              "longitudMaxima": 100,
              "tipoDato": "archivo",
              "validable": true,
              "requerido": true,
              "expresionRegular": null
            },
            {
              "idControl": "EstadoCuenta",
              "nombre": "Estado de cuenta",
              "tipo": "cargaArchivo",
              "longitudMaxima": 100,
              "tipoDato": "archivo",
              "validable": true,
              "requerido": true,
              "expresionRegular": null
            },
            {
              "idControl": "ComprobanteDomicilio",
              "nombre": "Comprobante de domicilio",
              "tipo": "cargaArchivo",
              "longitudMaxima": 100,
              "tipoDato": "archivo",
              "validable": true,
              "requerido": true,
              "expresionRegular": null
            },
            {
              "idControl": "CedulaFiscal",
              "nombre": "Cédula fiscal",
              "tipo": "cargaArchivo",
              "longitudMaxima": 100,
              "tipoDato": "archivo",
              "validable": true,
              "requerido": true,
              "expresionRegular": null
            },
            {
              "idControl": "CedulaProfesional",
              "nombre": "Cédula profesional",
              "tipo": "cargaArchivo",
              "longitudMaxima": 100,
              "tipoDato": "archivo",
              "validable": true,
              "requerido": true,
              "expresionRegular": null
            },
            {
              "idControl": "PoderNotarial",
              "nombre": "Poder notarial",
              "tipo": "cargaArchivo",
              "longitudMaxima": 100,
              "tipoDato": "archivo",
              "validable": true,
              "requerido": true,
              "expresionRegular": null
            }
          ]
        }
      ]
    }
    //{
    //  "idSeccion": 20,
    //  "nombre": "Carta de responsabilidad",
    //  "tipo": "contenedor",
    //  "mostrarEnVyD": false,
    //  "ruta": "CartaResponsabilidad",
    //  "secciones": [ ]
    //}
  ]
}