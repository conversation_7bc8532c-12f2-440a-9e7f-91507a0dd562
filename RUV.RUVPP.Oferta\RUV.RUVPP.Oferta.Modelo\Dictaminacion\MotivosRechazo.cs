﻿using System;
using System.Collections.Generic;

namespace RUV.RUVPP.Oferta.Modelo.Dictaminacion
{
    public class MotivosRechazo
    {
        public int idOrdenTrabajo { get; set; }
        public int idProducto { get; set; }
        public int idRegistro { get; set; }
        public int idServicio { get; set; }
        public List<Secciones> secciones { get; set; }
        public List<MotivosRechazo> motivosRechazoArchivados { get; set; }
    }

    public class Secciones
    {
        public int idSeccion { get; set; }
        public bool estatus { get; set; }
        public string mensajeRechazoAnterior { get; set; }
        public bool? actualizadoPorUsuario { get; set; }
        public string fechaDictaminacionAnterior { get; set; }
        public bool _mostrarMensajeRechazo { get; set; }
        public bool _mostrarMensajeRechazoAnterior { get; set; }
        public string _mensajeRechazoTemporal { get; set; }
        public string mensajeRechazo { get; set; }
        public string fechaDictaminacion { get; set; }
        public List<string> elementos { get; set; }
    }
}
