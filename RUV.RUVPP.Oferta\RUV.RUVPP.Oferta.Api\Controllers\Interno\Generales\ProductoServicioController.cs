﻿using Microsoft.ApplicationInsights;
using RUV.Comun.Web.Http;
using RUV.RUVPP.Oferta.Dominio.ProductoServicio;
using System;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;
using System.Web.Http.Description;
using RUV.RUVPP.Oferta.GeneralesInterop.Modelo.ConfiguracionSeccion;
using RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Reglas;
using RUV.RUVPP.Oferta.Modelo.Dictaminacion;
using RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Estatus;

namespace RUV.RUVPP.Oferta.Api.Controllers
{
    /// <summary>
    /// Expone funcionalidad para trabajar con la configuracion de las aplicaciones.
    /// </summary>
    [RoutePrefix("interno/api/productos")]
    public class ProductoServicioController : ApiControllerBase
    {
        #region Campos
        
        private IServicioProductoServicio _productoservicio;

        #endregion

        #region Constructor

        /// <summary>
        /// Constructor de la clase.
        /// </summary>
        /// <param name="clienteTelemetria">Cliente para comunicación con Applications Insights.</param>
        public ProductoServicioController(IServicioProductoServicio productoservicio)
            : base()
        {
            this._productoservicio = productoservicio;
        }

        #endregion Constructor

        #region Acciones

        /// <summary>
        /// Obtiene la configuración de la interfaz gráfica para el producto y servicio especificado.
        /// </summary>
        /// <param name="idServicio">Id del servicio.</param>
        /// <returns>Configuración de las secciones</returns>
        [HttpGet, Route("{idProducto}/servicios/{idServicio}/secciones")]
        [ResponseType(typeof(ConfiguracionSeccion))]
        public async Task<HttpResponseMessage> ObtenerConfiguracionSecciones(int idProducto, int idServicio)
        {
            ConfiguracionSeccion configuracion = await this._productoservicio.ObtenerConfiguracionAsync(idServicio);

            return Request.CreateResponse(HttpStatusCode.OK, configuracion);
        }

        /// <summary>
        /// Obtiene las reglas para los controles en el producto y servicio especificado.
        /// </summary>
        /// <param name="idServicio">Id del servicio.</param>
        /// <returns>Configuración de las secciones</returns>
        [HttpGet, Route("{idProducto}/servicios/{idServicio}/secciones-reglas")]
        [ResponseType(typeof(Tuple<ConfiguracionSeccion, ReglaElemento[]>))]
        public async Task<HttpResponseMessage> ObtenerConfiguracionReglasSecciones(int idProducto, int idServicio)
        {
            var configuracionReglas = await this._productoservicio.ObtenerConfiguracionReglasAsync(idServicio);

            return Request.CreateResponse(HttpStatusCode.OK, configuracionReglas);
        }

        /// <summary>
        /// Obtiene el estatus de un formulario del RUV, con base a sus servicio y etapa.
        /// </summary>
        /// <param name="idServicio">Id del servicio.</param>
        /// <param name="idRegistro">Id del registro.</param>
        /// <param name="etapa">Etapa del registro.</param>
        /// <param name="esFinal">Bandera que indica si es el json final de estatus</param>
        /// <returns></returns>
        [HttpGet, Route("{idProducto}/servicios/{idServicio}/registros/{idRegistro}/estatus-secciones")]
        [ResponseType(typeof(EstatusProcedimiento))]
        public async Task<HttpResponseMessage> ObtenerEstatusSecciones(short idServicio, int idRegistro, [FromUri] bool etapa, [FromUri] bool? esFinal=null)
        {
            //etapa: false=>esRegistro, true=>esVyD
            var jsonEstatus = await this._productoservicio.ObtenerJsonEstatusAsync(idServicio, idRegistro, !etapa, etapa, esFinal);
            return Request.CreateResponse(HttpStatusCode.OK, jsonEstatus);
        }

        /// <summary>
        /// Guarda el estatus de un formulario del RUV, con base a su producto, servicio y etapa.
        /// </summary>
        /// <param name="idServicio">Id del servicio.</param>
        /// <param name="idRegistro">Id del registro.</param>
        /// <param name="etapa">Etapa del registro.</param>
        /// <param name="esActualizacion">Indica si se trata de una actualizaación o no.</param>
        /// <param name="json">JSON de estatus</param>
        /// <returns></returns>
        [HttpPost, Route("{idProducto}/servicios/{idServicio}/registros/{idRegistro}/estatus-secciones")]
        [ResponseType(typeof(bool))]
        public async Task<HttpResponseMessage> GuardarEstatusSecciones(short idServicio, int idRegistro, [FromUri] bool etapa, [FromUri] bool esActualizacion, [FromBody]CuerpoEstatus json, [FromUri] bool? esFinal=null)
        {
            //etapa: false=>esRegistro, true=>esVyD
            bool resultado;
            if (!esActualizacion)
                resultado = await this._productoservicio.GuardarJsonEstatusAsync(idServicio, idRegistro, json.EstatusJSON, !etapa, etapa, esFinal);
            else
                resultado = await this._productoservicio.ActualizarJsonEstatusAsync(idServicio, idRegistro, json.EstatusJSON, !etapa, etapa, esFinal);
            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }

        /// <summary>
        /// Elimina el estatus de un formulario del RUV, con base a su producto, servicio y etapa.
        /// </summary>
        /// <param name="idServicio">Id del servicio.</param>
        /// <param name="idRegistro">Id del registro.</param>
        /// <param name="etapa">Etapa del registro.</param>        
        /// <param name="esFinal">Indica si es el json final</param>
        /// <returns></returns>
        [HttpDelete, Route("{idProducto}/servicios/{idServicio}/registros/{idRegistro}/estatus-secciones")]
        [ResponseType(typeof(bool))]
        public async Task<HttpResponseMessage> EliminarEstatusSecciones(short idServicio, int idRegistro, [FromUri] bool etapa, [FromUri] bool esFinal)
        {
            bool resultado;
            resultado = await this._productoservicio.EliminarJsonEstatusAsync(idServicio, idRegistro, !etapa, etapa, esFinal);
            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }

        #endregion

        #region Disposible

        protected override void Dispose(bool disposing)
        {
            base.Dispose(disposing);
        }

        #endregion
    }
}