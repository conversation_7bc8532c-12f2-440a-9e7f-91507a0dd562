﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api
{
    public class Sancion
    {
        public int? idSancion { get; set; }

        public DateTime? fechaInicioSancion { get; set; }

        public string fechaInicioSancionString { get; set; }

        public DateTime? fechaFinSancion { get; set; }

        public string fechaFinSancionString { get; set; }

        public string comentario { get; set; }

        public string comentarioRevocacion { get; set; }

        public DateTime? fechaRegistro { get; set; }

        public string fechaRegistroString { get; set; }

        public DateTime? fechaActualizacion { get; set; }

        public string fechaActualizacionString { get; set; }

        public bool? activo { get; set; }

        public int? idTipoSancion { get; set; }

        public string tipoSancion { get; set; }

        public int? idAseguradora { get; set; }

        public int? idUsuarioSanciono { get; set; }

        public string usuarioSanciono { get; set; }

        public int? idUsuarioRevoco { get; set; }

        public string usuarioRevoco { get; set; }

        public List<DocumentoSancion> documentos { get; set; }

    }
}
