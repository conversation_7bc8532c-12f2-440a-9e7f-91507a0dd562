﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.Promotor.Api
{
    /// <summary>
    /// <AUTHOR> 
    /// </summary>
    public class GrupoNotificacionOfertaxPromotor
    {
        public int? idGrupoNotificacion { get; set; }
        public int? estatusEnviO { get; set; }
        public DateTime? fechaRegistro { get; set; }
        public DateTime? fechaActualizacion { get; set; }
        public string respuesta { get; set; }

    }
}
