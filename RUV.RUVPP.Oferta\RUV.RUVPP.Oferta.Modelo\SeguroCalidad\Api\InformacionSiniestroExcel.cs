﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api
{
    public class InformacionSiniestroExcel
    {
        public string cuv { get; set; }

        public string numeroCredito { get; set; }

        public string numeroPoliza { get; set; }

        public string aseguradora { get; set; }

        public string fechaRecepcionAseguradora { get; set; }

        public string derechoHabiente { get; set; }

        public string vigenciaEstructura { get; set; }

        public string vigenciaImpermeabilizacion { get; set; }

        public string vigenciaInstalaciones { get; set; }

        public int? numeroReportesAbiertosEstructura { get; set; }

        public int? numeroReportesAbiertosImpermeabilizacion { get; set; }

        public int? numeroReportesAbiertosInstalaciones { get; set; }

        public string coberturaafectadam { get; set; }

        public string estatus { get; set; }

        public string tipoReporte { get; set; }

        public string fechaRegistro { get; set; }

        public string fechaAtencion { get; set; }

        public string fechaSiniestro { get; set; }

        public string fechaInicioPoliza { get; set; }

        public string fechaFinPoliza { get; set; }

        public string ajustador { get; set; }

        public string estimacion { get; set; }

        public string ajusteReserva { get; set; }

        public string claveAseguradora { get; set; }

        public int? idPoliza { get; set; }

        public int? idSiniestro { get; set; }

        public int? idTipoReporte { get; set; }

        public int? idTipocobertura { get; set; }

        public int? idEstatusReporte { get; set; }

        public int? idAseguradora { get; set; }

        public string fechaInicial { get; set; }

        public string fechaFinal { get; set; }

        public int? tamanioPagina { get; set; }

        public int? pagina { get; set; }

        public int? idUsuarioRegistro { get; set; }

        public string usuarioRegistra { get; set; }

        public string direccionAseguradora { get; set; }

        public string direccionCuv { get; set; }

        public bool soloSiniestros { get; set; }

        public string descripcion { get; set; }

        public string personaAfectada { get; set; }

        public string telefono { get; set; }

        public string telefono2 { get; set; }

        public string email { get; set; }

        public string email2 { get; set; }

        public string fechaRespuestaAseguradora { get; set; }

        public string respuesta { get; set; }

        public int? idTipoMitigacion { get; set; }

        public string TipoMitigacion { get; set; }

        public string noReporte { get; set; }

        public string aniosiniestro { get; set; }
        public string folioreporteAseguradora { get; set; }
        public string estado { get; set; }
        public string municipio { get; set; }
        public string numeroCertificado { get; set; }
        public string nombreAcreditado { get; set; }
        public string nombreDesarrollador { get; set; }
        public string causadeSiniestro { get; set; }

        public string fechadedeterminación { get; set; }

        public string fechadecierredeSiniestro { get; set; }

        public string montoAvalúo { get; set; }
        public string observacionesdeSiniestro { get; set; }

        public string nombreArchivo { get; set; }

        public string fechaInspeccionAjustador { get; set; }

        public string rutaarchivo { get; set; }

        public string montoPagoHonorarios { get; set; }
        public string fechaPagoHonorarios { get; set; }
        public string montoPagoIndemnizacion { get; set; }
        public string fechaPagoIndemnizacion { get; set; }
        public string montoPagoGastos { get; set; }
        public string fechaPagoGastos { get; set; }
        public string montoPagoTotal { get; set; }
        public string fechaPagoTotal { get; set; }
        public string reservaFinal { get; set; }
      
        public int? idTipoCoberturaSiniestro { get; set; }

        public string estatusReporte { get; set; }
    }
}
