﻿using RUV.Comun.Utilerias;
using RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Datos.Banco
{
   public  interface IBancoDataMapper : IDisposable
    {

        Task<Tuple<int, List<RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.Avaluo>>> ObtenerDesarrolladorPaginadoAsync(int tamanioPagina, int pagina, string rnombre);
        Task<PagoDiferenciaOV> AgregarRelacionbancoDesarrolladorAsync();
        Task<bool> AgregarMarcaOfertaAsync();

    }
}
