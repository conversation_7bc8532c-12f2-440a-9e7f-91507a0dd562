﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api
{
    public class Viviendas
    {
        public int idVivienda { get; set; }
        public string cuv { get; set; }
        public decimal valorAvaluo { get; set; }
        public decimal costoPoliza { get; set; }
        public decimal costoPolizaRelacion { get; set; }
        public decimal valorComercialVivienda { get; set; }
        public bool pagoPorEvaluacionRiesgo { get; set; }
        public string nombreBeneficiario { get; set; }
        public string fechaCobertura { get; set; }
        public Direccion direccion { get; set; }
        public string numeroPoliza { get; set; }
        public string urlDocumentoPoliza { get; set; }
        public string mensaje { get; set; }
        public string domicilio { get; set; }
        public string numeroCredito { get; set; }
        
    }
}
