﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api
{
    public class ViviendaAseguradora
    {
        public string CUV { get; set; }

        public int Id { get; set; }

        public decimal CostoPolizaPactado { get; set; }

        public decimal ValorComercialVivienda { get; set; }

        public decimal PagoEvaluacionRiesgo { get; set; }

        public DireccionAseguradora Direccion { get; set; }
    }
}
