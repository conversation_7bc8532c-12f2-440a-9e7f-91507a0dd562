﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api
{
    public class RespuestaSiniestro
    {
        public string folioReporteRuv { get; set; }
        public string cuv { get; set; }
        //public string domicilio { get; set; }
        //public string numeroCredito { get; set; }
        public string fechaOcurrencia { get; set; }
        public string fechaReporte { get; set; }
        //public string personaReporta { get; set; }
        public string poliza { get; set; }
        public string vigenciaFechaInicioPoliza { get; set; }
        public string vigenciaFechaFinPoliza { get; set; }
        public string descripcionSiniestro { get; set; }
        public string coberturaAfectada { get; set; }
        public string fechaRespuestaAseguradora { get; set; }
        public string ajustador { get; set; }
        public string fechaInspeccionAjustador { get; set; }
        public decimal? estimacion { get; set; }
        public decimal? ajusteReserva { get; set; }
        public decimal? pagosIndemnizacionCantidad { get; set; }
        public string pagosIndemnizacionFecha { get; set; }
        public decimal? pagosHonorariosCantidad { get; set; }
        public string pagosHonorariosFecha { get; set; }
        public decimal? pagosGastosCantidad { get; set; }
        public string pagosGastosFecha { get; set; }
        public decimal? totalPagadoCantidad { get; set; }
        public string totalPagoFecha { get; set; }
        public decimal? reservaFinal { get; set; }
        public string estatusReporte { get; set; }
        public string tipoMitigacion { get; set; }
        public string respuesta { get; set; }
        public int idSiniestro { get; set; }

        //add flieds 2023-03-24

        public string aseguradora { get; set; }
        public string aniosiniestro { get; set; }
        public string folioreporteAseguradora { get; set; }
        public string estado { get; set; }
        public string municipio { get; set; }
        public string numeroCertificado { get; set; }
        public string nombreAcreditado { get; set; }
        public string nombreDesarrollador { get; set; }
        public string causadeSiniestro { get; set; }

        public string fechadedeterminacion { get; set; }

        public string fechadecierredeSiniestro { get; set; }

        public string montoAvaluo { get; set; }
        public string observacionesdeSiniestro { get; set; }

        public Byte[] documentoBytes { get; set; }

        public int idDocumento { get; set; }

    }
}
