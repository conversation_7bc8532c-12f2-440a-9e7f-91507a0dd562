﻿<?xml version="1.0" encoding="utf-8"?>
<!--
This file is used by the publish/package process of your Web project. You can customize the behavior of this process
by editing this MSBuild file. In order to learn more about this please visit http://go.microsoft.com/fwlink/?LinkID=208121. 
-->
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <WebPublishMethod>FTP</WebPublishMethod>
    <LastUsedBuildConfiguration>Release</LastUsedBuildConfiguration>
    <LastUsedPlatform>Any CPU</LastUsedPlatform>
    <SiteUrlToLaunchAfterPublish>https://ruvpp-oferta-api-dev.azurewebsites.net</SiteUrlToLaunchAfterPublish>
    <LaunchSiteAfterPublish>True</LaunchSiteAfterPublish>
    <ExcludeApp_Data>False</ExcludeApp_Data>
    <publishUrl>ftp://waws-prod-blu-057dr.ftp.azurewebsites.windows.net</publishUrl>
    <DeleteExistingFiles>False</DeleteExistingFiles>
    <FtpPassiveMode>True</FtpPassiveMode>
    <FtpSitePath>site/wwwroot</FtpSitePath>
    <UserName>ruvpp-oferta-api-dev\$ruvpp-oferta-api-dev</UserName>
    <_SavePWD>True</_SavePWD>
  </PropertyGroup>
</Project>