﻿using RUV.Comun.Utilerias;
using RUV.RUVPP.Oferta.Comun.Modelo;
using RUV.RUVPP.Oferta.Modelo.Dictaminacion;
using RUV.RUVPP.Oferta.Modelo.Historico.Data;
using RUV.RUVPP.Oferta.Modelo.Oferta.Api;
using RUV.RUVPP.Oferta.Modelo.Oferta.Data;
using RUV.RUVPP.Oferta.Modelo.Proyectos.Data;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Dominio.Oferta
{
    public interface IServicioOferta : IDisposable
    {
        Task<int> GuardarOfertaAsync(int idProyecto, string nombre, string proyecto);
        Task<int> GuardarOfertaDetalleAsync(Modelo.Oferta.Api.Oferta oferta);
        Task<int> ActualizarOfertaAsync(OfertaVivienda ofertaVivienda, string oferta);       
        Task<int> ActualizarOfertaDetalleAsync(Modelo.Oferta.Api.Oferta oferta);
        Task EliminarOfertaParcialAsync(int idParcialOferta);

        Task<ResultadoPaginado<List<ViviendaSinAsignar>>> ObtenerViviendasSinAsingarOfertaAsync(int idProyecto, int idEstatusOferta, int tamanioPagina, int pagina, int? idOferta);

        Task<List<ViviendaPrototipo>> ObtenerViviendasPrototiposAsync(int idProyecto, int? idOferta);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="idOferta"></param>
        /// <returns></returns>
        Task<Modelo.Oferta.Api.Oferta> ObtenerOfertaPorIdAsync(int idOferta);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="idOferta"></param>
        /// <param name="nombreProyecto"></param>
        /// <param name="idEmpresa"></param>
        /// <returns></returns>
        Task<ResultadoPaginado<List<Modelo.Oferta.Api.Oferta>>> ObtenerOfertasFiltradoAsync(int tamanioPagina, int pagina, int? idOferta, string nombreProyecto, int? idEmpresa);
        Task<Modelo.Oferta.Api.Oferta> ObtenerDatosGeneralesAsync(int idOferta);
        Task<Modelo.Oferta.Api.Oferta> ObtenerOfertaAsync(int idOferta);
        Task<OfertaVivienda> ObtenerOfertaDetalleAsync(int idOferta);
        Task<Dictionary<int, string>> ObtenerProyectosEmpresaAsync(int idEmpresa);
        Task<Modelo.Oferta.Api.Oferta> ObtenerOfertaxProyectoAsync(int idProyecto);

        Task<List<Modelo.Oferta.Api.Oferta>> ObtenerOfertasxProyectoAsync(int idProyecto);

        Task <List<Modelo.Oferta.Data.PrototipoCatalogo>>ObtenerCatalogoPrototipoAsyn();
        Task<List<Modelo.Oferta.Data.Documento>> ObtenerCatalogoDocumentosAsyn();
        Task<bool> ActualizarEstatusOfertaViviendaAsync(int idOfertaVivienda, int idEstatus);
        Task<int> DesvincularViviendaAsync(int idOfertaVivienda);
        Task<bool> EnviarDictaminacionOfertaVivienda(int idOrdenTrabajo, short idServicio, int idRegistro, ParametroBodyDictaminacion parametrosDictaminacion);

<<<<<<< HEAD
        Task<List<Vivienda>> ObtenerViviendasPorOfertaVivienda(int idOfertaVivienda);
=======
        /// <summary>
        /// Obtiene el listado de ordenes de trabajo para una oferta por idOferta
        /// </summary>  
        Task<ResultadoPaginado<List<OrdenTrabajoOferta>>> ObtenerOrdenesTrabajoPorOfertaPaginadoAsync(int idOfertaVivienda, int tamanioPagina, int pagina);
>>>>>>> b31489300476573d134fc4a8438001685ad8b685

        #region Pago Oferta
        /// <summary>
        /// Metodo para confirmar el pago en SAP una vez que éste sea pagado
        /// </summary>
        /// <param name="idOfertaVivienda">id de la oferta de vivienda</param>
        /// <returns>Regresa true si el proceso se ejecuta correctamente</returns>
        Task<bool> ConfirmarPagoPorIdAsync(int idOfertaVivienda);

        /// <summary>
        ///Metodo para calcular el monto de las viviendas seleccionadas
        /// </summary>
        /// <param name="viviendasProyecto">Viviendas del proyecto</param>
        /// /// <param name="idProyecto">Id del proyecto donde se va a generar la oferta de vivienda</param>
        /// <returns>Regresa el monto total de la oferta</returns>
        Task<decimal> ObtenerMontoOfertaAsync(List<ViviendaPrototipo> viviendasProyecto, int idProyecto);
        #endregion
    }
}