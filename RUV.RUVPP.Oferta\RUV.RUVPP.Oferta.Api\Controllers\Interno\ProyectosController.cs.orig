﻿using Microsoft.ApplicationInsights;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Http;
using System.Web.Http.Description;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.ApplicationInsights;
using RUV.RUVPP.Oferta.Dominio.Proyectos;

namespace RUV.RUVPP.Oferta.Api.Controllers.Interno
{
    [RoutePrefix("interno/api/proyectos")]
    public class ProyectosController : ApiControllerBase
<<<<<<< HEAD
    {      

        public ProyectosController(TelemetryClient clienteTelemetria)
            : base(clienteTelemetria)
        {
            
=======
    {
        private readonly IServicioProyectos _servicioProyectos;
        private readonly IServicioProyectosCatalogos _servicioProyectosCatalogos;

        public ProyectosController(TelemetryClient clienteTelemetria, IServicioProyectos servicioProyectos, IServicioProyectosCatalogos servicioProyectosCatalogos)
            : base(clienteTelemetria)
        {
            this._servicioProyectos = servicioProyectos;
            this._servicioProyectosCatalogos = servicioProyectosCatalogos;
        }

        [HttpGet, Route("")]
        [ResponseType(typeof(List<>))]
        public async Task<HttpResponseMessage> ObtenerProyectoAsync(string nombre)
        {
            var result = await this._servicioPrototipoCatalogos.ObtenerCatalogoDeClasificacionViviendaAsync();

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpPost, Route("")]
        [ResponseType(typeof(List<>))]
        public async Task<HttpResponseMessage> GuardarProyectoAsync()
        {
            var result = await this._servicioPrototipoCatalogos.ObtenerCatalogoDeClasificacionViviendaAsync();

            return Request.CreateResponse(HttpStatusCode.OK, result);
>>>>>>> feature/creacion-proyectos
        }

    }
}