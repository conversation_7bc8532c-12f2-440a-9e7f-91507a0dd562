﻿using RUV.RUVPP.Oferta.Comun.Modelo;
using RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Dictaminacion;
using RUV.RUVPP.Oferta.Modelo.Oferta.Data;
using RUV.RUVPP.Oferta.Modelo.Proyectos.Data;
using System;
using System.Collections.Generic;

namespace RUV.RUVPP.Oferta.Modelo.Oferta.Api
{
    public class Oferta
    {
        public int? idOferta { get; set; }
        public string claveOfertaVivienda { get; set; }
        public int? idProyecto { get; set; }
        public string nombreProyecto { get; set; }
        public string nombreFrente { get; set; }
        public int? idEmpresa { get; set; }
        public DateTime? fechaRegistro { get; set; }
        public string fechaRegistroUTC { get; set; }
        public DateTime? fechaActualizacion { get; set; }
        public string fechaActualizacionUTC { get; set; }
        public DateTime? fechaAceptacion { get; set; }
        public int? idEstatusOfertaVivienda { get; set; }
        public string estatusOferta { get; set; }
        public string nombreRazonSocial { get; set; }
        public string IdEmpresaInst { get; set; }
        public bool aceptacionCartaResponsabilidad { get; set; }
        public int? cuvs { get; set; }
        public int? cuvsInactivas { get; set; }
        public int? viviendas { get; set; }
        public int? prototipos { get; set; }
        public List<ViviendaPrototipo> listaViviendasProyecto { get; set; }
        public List<LicenciaFactibilidad> licenciasFactibilidades { get; set; }
        public List<PrototipoVivienda> listaDocumentosPrototipos { get; set; }
        public List<PrototipoVivienda> listaPrototipos { get; set; }
        public List<DocumentoComplementario> documentosComplementarios { get; set; }
        public DocumentoRuv dictamenRiesgo { get; set; }
        public DRO directorResponsableObra { get; set; }
        public PropietarioTerreno propietarioTerreno { get; set; }
        public DatosCPV constructor { get; set; }
        public DatosCPV promotor { get; set; }
        public DatosCPV vendedor { get; set; }
        public List<DatosCPV> listaPromotores { get; set; }
        public List<DatosCPV> listaVendedores { get; set; }
        public List<ZonaRiesgo> zonasRiesgo { get; set; }
        public ViviendaRiesgoOferta viviendaRiesgoOferta { get; set; } //Quitar
        public LicenciaFactibilidad licenciaFactibilidadSeleccionado { get; set; }
        public OfertaVivienda ofertaVivienda { get; set; }
        public bool iniciaRegistro { get; set; }
        public bool fueronModificadosDatosSensibles { get; set; }
        public bool cambioPrototipo { get; set; }
        public bool esAccesible { get; set; }
        public string direccion { get; set; }
        public bool tieneDetalle { get; set; }
        public decimal latitud { get; set; }
        public decimal longitud { get; set; }
        public MotivoRechazo[] listaMotivosRechazo { get; set; }

        public static implicit operator List<object>(Oferta v)
        {
            throw new NotImplementedException();
        }
    }
}