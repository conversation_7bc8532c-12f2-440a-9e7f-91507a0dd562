﻿<?xml version="1.0" encoding="utf-8"?>
<!--
  For more information on how to configure your ASP.NET application, please visit
  http://go.microsoft.com/fwlink/?LinkId=301879
  -->
<configuration>
  <configSections>
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
    <section name="oracle.manageddataaccess.client" type="OracleInternal.Common.ODPMSectionHandler, Oracle.ManagedDataAccess, Version=*********, Culture=neutral, PublicKeyToken=89b483f429c47342" />
  </configSections>
  <connectionStrings>
    <!--<add name="RuvAsisConnection" connectionString="data source=************,35000;initial catalog=RUVdev;persist security info=True;user id=RUvPLu$Pr4pP04#;password=&amp;hT7G75%&amp;n2D$;multipleactiveresultsets=True" providerName="System.Data.SqlClient" />
    <add name="GeneralesConnection" connectionString="data source=bd-qadev02-sd;initial catalog=RUVplusDEVOferta;persist security info=True;user id=RuVplu$_Q4Of4pp738;password=6D#hd4%nSe;multipleactiveresultsets=True" providerName="System.Data.SqlClient" />
    <add name="OfertaConnection" connectionString="data source=bd-qadev02-sd;initial catalog=RUVplusDEVOferta;persist security info=True;user id=RuVplu$_Q4Of4pp738;password=6D#hd4%nSe;multipleactiveresultsets=True" providerName="System.Data.SqlClient" />
    <add name="HistoricosConnection" connectionString="data source=ruvpp-sql-dev.eastus.cloudapp.azure.com,1433;initial catalog=ruvPlusDesarrolloHistoricos;persist security info=True;user id=RUVd3S4RR0LLo;password=**********;multipleactiveresultsets=True" providerName="System.Data.SqlClient" />
    <add name="OracleConnection" connectionString="DATA SOURCE=oradb-qa02.eastus.cloudapp.azure.com:1521/OracleAdmin.ruv.net;PERSIST SECURITY INFO=True;USER ID=RUV_OFERTA2;PASSWORD=**$6qJm$Y" providerName="Oracle.DataAccess.Client" />
    <add name="GeneralesStorageConnection" connectionString="DefaultEndpointsProtocol=https;AccountName=ruvarchivosempresa;AccountKey=****************************************************************************************" />
    <add name="NotificacionesStorageConnectionString" connectionString="DefaultEndpointsProtocol=https;AccountName=ruvnotificacionescorreo;AccountKey=a5EJuUhcacgScjgecuCMos9L3cbq+ztK4K**33yQWCscMr3EcYWDKjgzDtfVmAr4h3jhmPdjbQO9V9E+qH3szg==" />
    <add name="BitacoraStorageConnectionString" connectionString="DefaultEndpointsProtocol=https;AccountName=ruvcomun;AccountKey=****************************************************************************************" />
    
    -->

	<add name="RuvAsisConnection" connectionString="data source=bd-qadev02-sd;initial catalog=RUV_DEV;persist security info=True;user id=EX34ppRuvAs1sDevnT5);password=************;multipleactiveresultsets=True" providerName="System.Data.SqlClient" />
	<add name="GeneralesConnection" connectionString="data source=bd-qadev02-sd;initial catalog=RUVPlus_DEV;persist security info=True;user id=3XeAPPrUVM45Devs%3M;password=*********$RF(;multipleactiveresultsets=True" providerName="System.Data.SqlClient" />
  <add name="OfertaConnection" connectionString="data source=bd-qadev02-sd;initial catalog=RUVPlus_DEV;persist security info=True;user id=3XeAPPrUVM45Devs%3M;password=*********$RF(;multipleactiveresultsets=True" providerName="System.Data.SqlClient" />
	<add name="HistoricosConnection" connectionString="data source=bd-qadev02-sd;initial catalog=RUVPlus_DEV;persist security info=True;user id=3XeAPPrUVM45Devs%3M;password=*********$RF(;multipleactiveresultsets=True" providerName="System.Data.SqlClient" />
  <add name="OracleConnection" connectionString="DATA SOURCE=map-q2-sd.cloudapp.net:25450/DSIGRUV;PERSIST SECURITY INFO=True;USER ID=OFERTADEV;PASSWORD=**$$w0rdDEV" providerName="Oracle.DataAccess.Client" />
  <add name="RuvAvaluosBitacora" connectionString="data source=bd-qadev02-sd;initial catalog=RUV_DEV;persist security info=True;user id=EX34ppRuvAs1sDevnT5);password=************;multipleactiveresultsets=True" providerName="System.Data.SqlClient" />
  <add name="GeneralesStorageConnection" connectionString="DefaultEndpointsProtocol=https;AccountName=ruvarchivosempresa;AccountKey=****************************************************************************************" />
  <add name="NotificacionesStorageConnectionString" connectionString="DefaultEndpointsProtocol=https;AccountName=ruvnotificacionescorreo;AccountKey=a5EJuUhcacgScjgecuCMos9L3cbq+ztK4K**33yQWCscMr3EcYWDKjgzDtfVmAr4h3jhmPdjbQO9V9E+qH3szg==" />
  <add name="BitacoraStorageConnectionString" connectionString="DefaultEndpointsProtocol=https;AccountName=ruvcomun;AccountKey=****************************************************************************************" />
    <!--<add name="BitacoraStorageConnectionString" connectionString="DefaultEndpointsProtocol=https;AccountName=ruvcomun;AccountKey=****************************************************************************************" />-->
    <add name="RUV_LOG" connectionString="Data Source=bd-sap02-qa.eastus.cloudapp.azure.com;Initial Catalog=RUV_LOG;User ID=sa;Password=***********" providerName="System.Data.SqlClient"/>

	  <!--<add name="BitacoraStorage" connectionString="DefaultEndpointsProtocol=https;AccountName=ruvstoragedocs01;AccountKey=****************************************************************************************" />-->    
  </connectionStrings>
  <appSettings>
    <!--Conexiones Generales-->
    <add key="ConexionAsIs" value="RuvAsisConnection" />
    
    <!-- Configuracion para CORS en ASP Web API -->
    <add key="CorsOrigins" value="http://localhost:55523" />

    <!-- Configuración de Application Insights -->
    <add key="InstrumentationKey" value="cf08ab50-29a4-4797-ac2f-382d13ca7442" />
    
    <!-- Configuracion de acceso a datos -->
    <add key="RUV.RUVPP.Command.TimeoutSeconds" value="1200" />
    <add key="RUV.RUVPP.Transaccion.Timeout" value="01:00:00" />
    <add key="RUV.RUVPP.WebRequests.Timeout" value="00:05:00" />
    <add key="RUV.RUVPP.RetryTimes" value="4" />

    <!-- Configuraciones del Framework -->
    <add key="LogBookInterceptor.Enable" value="true" />
    <add key="LogBook.Storage.ConnectionName" value="BitacoraStorageConnectionString" />

    <!-- Nombre de la cadena de conexión utilizada por ensamblados Generales. -->
    <add key="NombreVariableConexion" value="GeneralesConnection" />
    <add key="nameDataStorageConnectionString" value="GeneralesStorageConnection" />

    <!-- Configuraciones para la carga de documentos -->
    <add key="Documentos.numeroIntentos" value="1" />
    <add key="Documentos.blockDataStorage" value="10" />
    <add key="Documentos.tiempoEsperaSegundos" value="30" />
    <add key="Documentos.tamañomaximo" value="5" />

    <!--Servicio de subscripción Signal R-->
    <add key="RUV.RUVPP.UI.Usuarios.Suscripcion" value="http://ruvpp-servicio-notificaciones-odt-dev.azurewebsites.net/" />
    <add key="RUV.RUVPP.Generales.URLFichaPagoRiesgo" value="http://localhost:55616/confirmacionpagos/generacion-ficha-riesgo" />
    <add key="RUV.RUVPP.Generales.URLFichaPagoPoliza" value="http://localhost:55616/confirmacionpagos/generacion-ficha-riesgo" />
    <add key="RUV.RUVPP.Generales.URLFichaPagoDiferencias" value="http://localhost:55616/confirmacionpagos/generacion-ficha-riesgo" />

    <!--Servicio de Consulta para GenerarDispersion-->
    <add key="GenerarDispersion" value="http://ruvpp-generales-api-qa.azurewebsites.net/interno/api/pagos/GenerarDispersion2?isSC=false" />
    
    <!--Servicio de SAP pagos-->
<!--<add key="Servicio.SAP.Devolucion" value="http://bd-sap01-prod.eastus.cloudapp.azure.com/WSOrdenPago/service.asmx?op=WSRegistraPago" />-->
    <add key="Servicio.SAP.Devolucion" value="http://sap-dev02-sd.eastus.cloudapp.azure.com/WSOrdenPago/service.asmx?op=WSRegistraPago" />
    <add key="Servicio.SAP.Confirmacion" value="http://sap-dev02-sd.eastus.cloudapp.azure.com/WSOrdenPago/service.asmx?op=WSConsultaOrdenPago" />

    <!--Parametros de Correo-->
    <add key="Notificaciones.ConnectionName" value="NotificacionesStorageConnectionString" />
    <add key="Notificaciones.QueueName" value="notificacionescorreo" />
    <add key="Notificaciones.TableName" value="plantillastipocorreo" />
    <add key="Notificaciones.BlobName" value="adjuntoscorreo" />
    <add key="Notificaciones.NumeroIntentos" value="1" />
    <add key="Notificaciones.TiempoEspera" value="1" />
    <add key="Notificaciones.TamanoBloque" value="1" />

    <!--Parametros de WorkerRole-->
    <add key="Notificaciones.TiempoEsperaWR" value="60" />
    <add key="Notificaciones.QueueAlerta" value="notificacionesalerta" />
    <add key="Notificaciones.TokenAlerta" value="0D472D28-C528-4D12-935D-34214395140E" />

    <!--Parametros de Conexion Oracle para FDO-->
    <add key="SDFschema" value="Schema1" />
    <add key="OracleUser" value="OFERTADEV" />
    <add key="OraclePassword" value="**$$w0rdDEV" />
    <add key="OracleUrl" value="map-q2-sd.cloudapp.net:25450/DSIGRUV" />
    <add key="OracleSchema" value="OFERTADEV" />

    <!--Parametros de Validacion con SIG-->
    <add key="ActivarIntegracionSIG" value="1" />   
    
    <!--Generacion CUV Geografica-->
    <add key="UrlSigCuvGeografica" value="http://map-q3-w2k8r2.cloudapp.net/ws/cuvdev/ServicioCUV.svc" />
    <add key="CuvGeografica.Esquema" value="OFERTADEV" />
    <add key="CuvGeografica.GeometriaNombreCampo" value="GEOM" />
    <add key="CuvGeografica.IdNombreCampo" value="FEATID" />
    <add key="CuvGeografica.IdServicio" value="2" />
    <add key="CuvGeografica.NivelDecimal" value="0" />
    <add key="CuvGeografica.NombreTabla" value="SMB_CONSTRUCCIONES" />
    <add key="CuvGeografica.TipoGeometria" value="1" />

    <!--Parámetros de servicio de convivencia ASIS-->
    <add key="ActivarConvivenciaAsIs" value="1" />
    <add key="UrlAsIsGenerarCuvs" value="OfertaVivienda/api/generarCuvs" />
    <add key="UrlActualizarOfertaVivienda" value="OfertaVivienda/api/actualizarrOfertaVivienda" />
    <add key="UrlActualizarPrototipo" value="OfertaVivienda/api/actualizarPrototipo" />
    <add key="UrlActualizarVivienda" value="OfertaVivienda/api/actualizarVivienda " />
    <add key="UrlBaseConvivencia" value="http://ruvintmigradeve.cloudapp.net/" />
    <add key="UrlCancelarViviendas" value="OfertaVivienda/api/cancelarCuvs" />
    <add key="UrlDisponibilizarViviendas" value="OfertaVivienda/api/cambioEstatusVivienda" />
    <add key="UrlEliminarPrototipo" value="OfertaVivienda/api/borrarPrototipo" />
    <add key="UrlGuardarOfertaVivienda" value="OfertaVivienda/api/guardarOfertaVivienda" />
    <add key="UrlGuardarPrototipo" value="OfertaVivienda/api/guardarPrototipo" />
    <add key="UrlIndividualizarViviendas" value="OfertaVivienda/api/individualizarVivienda" />
    <add key="UrlObtenerPuntajesCUV" value="OfertaVivienda/api/consultarPuntaje " />
    <add key="UrlValidarCUV" value="OfertaVivienda/api/validarPaqueteCUV   " />
    <add key="UrlValidarPrototipo" value="OfertaVivienda/api/validarPrototipo   " />
    <add key="UserASIS" value="IN094146" />
    <add key="PasswordASIS" value="ANGEL079 " />
    <add key="PrefijoClaveOferta" value="60" />
    <add key="TamanioListaValidarCUV" value="20" />

    <add key="Servicio.Infonavit.InicioSesion.Avaluo" value="http://**************/apiUsuario/services/{0}" />

    <!---Configuraciones  de seguridad -->
    <add key="Seguridad.Autenticacion.NumeroMaximoIntentos" value="4" />

    <!--Parametros asincronia-->
    <add key="NumeroMaximoViviendas" value="150" />

    <!--Configuraciones JWT-->
    <add key="JwtRuv:JwtSigningKeyAsUtf8" value="5A0AB091-3F84-4EC4-B227-0834FCD8B1B4" />

    <!--Parámetros de servicio de Infonavit ( Promotores oferta )-->

    <!--<add key="UrlAsignarPromotor" value="https://**************/ofertaPromotor/asignaOfertasPromotor.json" />
   <add key="UrlRegistrarPromotor" value="https://**************/ofertaPromotor/asignaOfertasPromotor.json" />-->
    <add key="UrlAsignarPromotor" value="http://trendeviviendaseguro.infonavit.org.mx/ofertaPromotor/asignaOfertasPromotor.json" />
    <add key="UrlRegistrarPromotor" value="http://trendeviviendaseguro.infonavit.org.mx/WSPromotoresWeb/promotores/registrarPromotor.json" />

    <!-- Configuraciones para seguro calidad -->
      <add key="RUV.RUVPP.SeguroCalidad.Vivienda.PorcentajeAvance" value="80" />
    
    <!-- Parametro para registro en SAP -->
    <add key="WS-FICHAPAGOPREPAGO" value="http://bd-sap02-qa.eastus.cloudapp.azure.com/WebApiSap/WebApi.svc/FichaPagoPrepago"/>
  
  </appSettings>
  <system.web>
    <customErrors mode="Off" />
    <authentication mode="None" />
    <compilation debug="true" targetFramework="4.5.2" />
    <httpRuntime targetFramework="4.5.2" maxRequestLength="20971520" />
    <httpModules>
      <add name="ApplicationInsightsWebTracking" type="Microsoft.ApplicationInsights.Web.ApplicationInsightsHttpModule, Microsoft.AI.Web" />
    </httpModules>
  </system.web>
  <system.webServer>
    <modules>
      <remove name="FormsAuthentication" />
      <remove name="ApplicationInsightsWebTracking" />
      <add name="ApplicationInsightsWebTracking" type="Microsoft.ApplicationInsights.Web.ApplicationInsightsHttpModule, Microsoft.AI.Web" preCondition="managedHandler" />
    </modules>
    <handlers>
      <remove name="ExtensionlessUrlHandler-Integrated-4.0" />
      <remove name="OPTIONSVerbHandler" />
      <remove name="TRACEVerbHandler" />
      <add name="ExtensionlessUrlHandler-Integrated-4.0" path="*." verb="*" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0" />
    </handlers>
    <validation validateIntegratedModeConfiguration="false" />
  </system.webServer>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-3.0.1.0" newVersion="3.0.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security.OAuth" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-3.0.1.0" newVersion="3.0.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security.Cookies" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-3.0.1.0" newVersion="3.0.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-3.0.1.0" newVersion="3.0.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" culture="neutral" publicKeyToken="30ad4fe6b2a6aeed" />
        <bindingRedirect oldVersion="0.0.0.0-9.0.0.0" newVersion="9.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Optimization" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-1.1.0.0" newVersion="1.1.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-1.6.5135.21930" newVersion="1.6.5135.21930" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Helpers" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-3.0.0.0" newVersion="3.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-5.2.3.0" newVersion="5.2.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.WebPages" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-3.0.0.0" newVersion="3.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Http" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.2.3.0" newVersion="5.2.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Net.Http.Formatting" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.2.3.0" newVersion="5.2.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.ApplicationInsights" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.2.0.0" newVersion="2.2.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Cors" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.2.3.0" newVersion="5.2.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <publisherPolicy apply="no" />
        <assemblyIdentity name="Oracle.ManagedDataAccess" publicKeyToken="89b483f429c47342" culture="neutral" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.AI.Agent.Intercept" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.0.7.0" newVersion="2.0.7.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Antlr3.Runtime" publicKeyToken="eb42632606e9261f" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.5.0.2" newVersion="3.5.0.2" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.SqlServer.Types" publicKeyToken="89845dcd8080cc91" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-14.0.0.0" newVersion="14.0.0.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <entityFramework>
    <defaultConnectionFactory type="System.Data.Entity.Infrastructure.LocalDbConnectionFactory, EntityFramework">
      <parameters>
        <parameter value="mssqllocaldb" />
      </parameters>
    </defaultConnectionFactory>
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
    </providers>
  </entityFramework>
  <system.data>
    <DbProviderFactories>
      <remove invariant="Oracle.ManagedDataAccess.Client" />
      <add name="ODP.NET, Managed Driver" invariant="Oracle.ManagedDataAccess.Client" description="Oracle Data Provider for .NET, Managed Driver" type="Oracle.ManagedDataAccess.Client.OracleClientFactory, Oracle.ManagedDataAccess, Version=*********, Culture=neutral, PublicKeyToken=89b483f429c47342" />
    </DbProviderFactories>
  </system.data>
  <oracle.manageddataaccess.client>
    <version number="*">
      <dataSources>
        <dataSource alias="SampleDataSource" descriptor="(DESCRIPTION=(ADDRESS=(PROTOCOL=tcp)(HOST=localhost)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=ORCL))) " />
      </dataSources>
    </version>
  </oracle.manageddataaccess.client>
  <system.serviceModel>
    <bindings>
      <basicHttpBinding>
        <binding name="BasicHttpBinding_IServicioCUV" />
        <binding name="SeguridadSoapBinding" />
        <binding name="SI_recibeFoto_SOBinding">
          <security mode="Transport">
            <transport clientCredentialType="Basic" proxyCredentialType="None"></transport>
          </security>
        </binding>
      </basicHttpBinding>
    </bindings>
    <client>
      <endpoint address="http://map-q3-w2k8r2.cloudapp.net/ws/cuv/ServicioCUV.svc" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_IServicioCUV" contract="ServicioCUV.IServicioCUV" name="BasicHttpBinding_IServicioCUV" />
      <endpoint address="http://convivenciaqaasis.ruv.org.mx/WSSeguridad/services/Seguridad" binding="basicHttpBinding" bindingConfiguration="SeguridadSoapBinding" contract="ServicioLonigAsis.Seguridad" name="Seguridad" />

      <endpoint address="https://serviciosweb.infonavit.org.mx:8997/XISOAPAdapter/MessageServlet_recibeFoto_QA" binding="basicHttpBinding" bindingConfiguration="SI_recibeFoto_SOBinding" contract="Servlet_recibeFoto.SI_recibeFoto_SO" name="HTTP_Port" />
    </client>
  </system.serviceModel>
  <system.transactions>
    <defaultSettings timeout="01:00:00" />
  </system.transactions>
  <system.codedom>
    <compilers>
      <compiler language="c#;cs;csharp" extension=".cs" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.CSharpCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" warningLevel="4" compilerOptions="/langversion:6 /nowarn:1659;1699;1701" />
      <compiler language="vb;vbs;visualbasic;vbscript" extension=".vb" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.VBCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" warningLevel="4" compilerOptions="/langversion:14 /nowarn:41008 /define:_MYTYPE=\&quot;Web\&quot; /optionInfer+" />
    </compilers>
  </system.codedom>
  <system.diagnostics>
    <trace autoflush="true" indentsize="0">
      <listeners>
        <add name="myAppInsightsListener" type="Microsoft.ApplicationInsights.TraceListener.ApplicationInsightsTraceListener, Microsoft.ApplicationInsights.TraceListener" />
      </listeners>
    </trace>
  </system.diagnostics>
</configuration>