﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion
{
    public class ExcelPOI
    {

        public enum EXTENSION
        {
            XLSX,
            XLS
        }


        #region Certificado Aseguradora
        public async Task<byte[]> CrearExcel(List<DatosAseguradora> gmx, List<DatosAseguradora> latino, List<DatosAseguradora> spp, EXTENSION extension)
        {
            List<Sheet> listSheet = new List<Sheet>();

            var hojaGmx = await CrearHojaGmx(gmx);
            listSheet.Add(hojaGmx);

            var hojaLatino = await CrearHojaLatino(latino);
            listSheet.Add(hojaLatino);

            var hojaSpp = await CrearHojaSpp(spp);
            listSheet.Add(hojaSpp);

            byte[] result = null;

            result = WriteExcelWithNPOI(listSheet, extension);

            return result;
        }

        public async Task<Sheet> CrearHojaGmx(List<DatosAseguradora> listaGmx)
        {

            DataTable dataTable = new DataTable();

            dataTable.Columns.Add("Clave Cliente", typeof(string));
            dataTable.Columns.Add("Clave Acreedor", typeof(string));
            dataTable.Columns.Add("Proyecto", typeof(string));
            dataTable.Columns.Add("Artículo", typeof(string));
            dataTable.Columns.Add("Moneda", typeof(string));
            dataTable.Columns.Add("Importe", typeof(string));
            dataTable.Columns.Add("Número Pago", typeof(string));
            dataTable.Columns.Add("Ejercicio", typeof(string));
            dataTable.Columns.Add("Clabe", typeof(string));
            dataTable.Columns.Add("Cuv", typeof(string));
            dataTable.Columns.Add("Orden Verificación", typeof(string));
            dataTable.Columns.Add("Fecha Individualización", typeof(string));
            dataTable.Columns.Add("RFC Oferente", typeof(string));
            dataTable.Columns.Add("Monto Avaluo", typeof(string));
            dataTable.Columns.Add("Costo Relación Comercial (Póliza)", typeof(string));

            if (listaGmx != null && listaGmx.Any())
            {

                foreach (DatosAseguradora entity in listaGmx)
                {
                    List<object> listRows = new List<object>();

                    {
                        object value = entity.claveCliente;

                        listRows.Add(this.getValue(value));
                    }

                    {
                        object value = entity.claveAcreedor;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.proyecto;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.articulo;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.moneda;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.costoPoliza;

                        listRows.Add(this.getValue(value));

                    }


                    {
                        object value = entity.noPago;

                        listRows.Add(this.getValue(value));

                    }


                    {
                        object value = entity.ejercicio;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.cuentaBancaria;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.cuv;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.ordenVerificacion;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.fechaInicioVigencia;

                        listRows.Add(this.getValue(value));

                    }
                    
                    {
                        object value = entity.rfc;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.montoAvaluo;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.costoRelacion;

                        listRows.Add(this.getValue(value));

                    }

                    dataTable.Rows.Add(listRows.ToArray());

                }

            }

            Sheet sheet = new Sheet() { name = "Aseguradora GMX", dataTable = dataTable };

            return sheet;
        }

        public async Task<Sheet> CrearHojaLatino(List<DatosAseguradora> listaLatino)
        {

            DataTable dataTable = new DataTable();

            dataTable.Columns.Add("Clave Cliente", typeof(string));
            dataTable.Columns.Add("Clave Acreedor", typeof(string));
            dataTable.Columns.Add("Proyecto", typeof(string));
            dataTable.Columns.Add("Artículo", typeof(string));
            dataTable.Columns.Add("Moneda", typeof(string));
            dataTable.Columns.Add("Importe", typeof(string));
            dataTable.Columns.Add("Número Pago", typeof(string));
            dataTable.Columns.Add("Ejercicio", typeof(string));
            dataTable.Columns.Add("Clabe", typeof(string));
            dataTable.Columns.Add("Cuv", typeof(string));
            dataTable.Columns.Add("Orden Verificación", typeof(string));
            dataTable.Columns.Add("Fecha Individualización", typeof(string));
            dataTable.Columns.Add("RFC Oferente", typeof(string));
            dataTable.Columns.Add("Monto Avaluo", typeof(string));
            dataTable.Columns.Add("Costo Relación Comercial (Póliza)", typeof(string));


            if (listaLatino != null && listaLatino.Any())
            {

                foreach (DatosAseguradora entity in listaLatino)
                {
                    List<object> listRows = new List<object>();

                    {
                        object value = entity.claveCliente;

                        listRows.Add(this.getValue(value));
                    }

                    {
                        object value = entity.claveAcreedor;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.proyecto;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.articulo;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.moneda;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.costoPoliza;

                        listRows.Add(this.getValue(value));

                    }


                    {
                        object value = entity.noPago;

                        listRows.Add(this.getValue(value));

                    }


                    {
                        object value = entity.ejercicio;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.cuentaBancaria;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.cuv;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.ordenVerificacion;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.fechaInicioVigencia;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.rfc;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.montoAvaluo;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.costoRelacion;

                        listRows.Add(this.getValue(value));

                    }

                    dataTable.Rows.Add(listRows.ToArray());


                }

            }

            Sheet sheet = new Sheet() { name = "Aseguradora Latino", dataTable = dataTable };

            return sheet;
        }

        public async Task<Sheet> CrearHojaSpp(List<DatosAseguradora> listaSpp)
        {

            DataTable dataTable = new DataTable();

            dataTable.Columns.Add("Clave Cliente", typeof(string));
            dataTable.Columns.Add("Clave Acreedor", typeof(string));
            dataTable.Columns.Add("Proyecto", typeof(string));
            dataTable.Columns.Add("Artículo", typeof(string));
            dataTable.Columns.Add("Moneda", typeof(string));
            dataTable.Columns.Add("Importe", typeof(string));
            dataTable.Columns.Add("Número Pago", typeof(string));
            dataTable.Columns.Add("Ejercicio", typeof(string));
            dataTable.Columns.Add("Clabe", typeof(string));
            dataTable.Columns.Add("Cuv", typeof(string));
            dataTable.Columns.Add("Orden Verificación", typeof(string));
            dataTable.Columns.Add("Fecha Individualización", typeof(string));
            dataTable.Columns.Add("RFC Oferente", typeof(string));
            dataTable.Columns.Add("Monto Avaluo", typeof(string));
            dataTable.Columns.Add("Costo Relación Comercial (Póliza)", typeof(string));


            if (listaSpp != null && listaSpp.Any())
            {

                foreach (DatosAseguradora entity in listaSpp)
                {
                    List<object> listRows = new List<object>();

                    {
                        object value = entity.claveCliente;

                        listRows.Add(this.getValue(value));
                    }

                    {
                        object value = entity.claveAcreedor;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.proyecto;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.articulo;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.moneda;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.costoPoliza;

                        listRows.Add(this.getValue(value));

                    }


                    {
                        object value = entity.noPago;

                        listRows.Add(this.getValue(value));

                    }


                    {
                        object value = entity.ejercicio;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.cuentaBancaria;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.cuv;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.ordenVerificacion;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.fechaInicioVigencia;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.rfc;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.montoAvaluo;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.costoRelacion;

                        listRows.Add(this.getValue(value));

                    }

                    dataTable.Rows.Add(listRows.ToArray());


                }

            }

            Sheet sheet = new Sheet() { name = "Aseguradora SPP", dataTable = dataTable };

            return sheet;
        }
        #endregion


        #region PERPPS
        public async Task<byte[]> CrearExcel(List<ReporteExcelPPS> listaPPS, List<ReporteExcelPER> listaPER, EXTENSION extension)
        {

            
            List<Sheet> listSheet = new List<Sheet>();

            var hojaPPS = await CrearHojaPPS(listaPPS);
            listSheet.Add(hojaPPS);

            var hojaPER = await CrearHojaPER(listaPER);
            listSheet.Add(hojaPER);

            byte[] result = null;

            result = WriteExcelWithNPOI(listSheet, extension);
            
            return result;
        }



        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public async Task<Sheet> CrearHojaPPS(List<ReporteExcelPPS> listaPPS)
        {
            
            DataTable dataTable = new DataTable();

            dataTable.Columns.Add("Clave Cliente", typeof(string));
            dataTable.Columns.Add("Clave Acreedor", typeof(string));
            dataTable.Columns.Add("Proyecto", typeof(string));
            dataTable.Columns.Add("Artículo", typeof(string));
            dataTable.Columns.Add("Moneda", typeof(string));
            dataTable.Columns.Add("Importe", typeof(string));
            dataTable.Columns.Add("Número Pago", typeof(string));
            dataTable.Columns.Add("Ejercicio", typeof(string));
            dataTable.Columns.Add("Clabe", typeof(string));
            dataTable.Columns.Add("Cuv", typeof(string));
            dataTable.Columns.Add("Orden Verificación", typeof(string));
            dataTable.Columns.Add("Fecha Individualización", typeof(string));
            dataTable.Columns.Add("Referencia", typeof(string));
            dataTable.Columns.Add("Fecha Pago Póliza", typeof(string));
            dataTable.Columns.Add("Fecha Pago Aseguradora", typeof(string));
            dataTable.Columns.Add("RFC Oferente", typeof(string));
            dataTable.Columns.Add("Monto Avaluo", typeof(string));
            dataTable.Columns.Add("Costo Relación Comercial (Póliza)", typeof(string));
            dataTable.Columns.Add("ONAVI", typeof(string));
            dataTable.Columns.Add("Días Diferencia", typeof(string));




            if (listaPPS != null && listaPPS.Any())
            {

                foreach (ReporteExcelPPS entity in listaPPS)
                {
                    List<object> listRows = new List<object>();

                    {
                        object value = entity.acreedorSAPaseguradora;

                        listRows.Add(this.getValue(value));
                    }

                    {
                        object value = entity.acreedorSAPoferente;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.proyecto;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.articulo;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.moneda;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.importe;

                        listRows.Add(this.getValue(value));

                    }


                    {
                        object value = entity.idVivienda;

                        listRows.Add(this.getValue(value));

                    }


                    {
                        object value = entity.ejercicio;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.clabeBancaria;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.cuv;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.ordenVerificacion;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.fechaInicioVigencia;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.referencia;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.fechaPago;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.fechaPagoAseguradora;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.rfc;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.montoAvaluo;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.costoRelacion;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.nombre;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.diasDiferencia;

                        listRows.Add(this.getValue(value));

                    }

                    dataTable.Rows.Add(listRows.ToArray());


                }

            }

            Sheet sheet = new Sheet() { name = "PPS", dataTable = dataTable };

            return sheet;
        }



        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public async Task<Sheet> CrearHojaPER(List<ReporteExcelPER> listaPER)
        {

            DataTable dataTable = new DataTable();

            dataTable.Columns.Add("Clave Cliente", typeof(string));
            dataTable.Columns.Add("Clave Acreedor", typeof(string));
            dataTable.Columns.Add("Proyecto", typeof(string));
            dataTable.Columns.Add("Artículo", typeof(string));
            dataTable.Columns.Add("Moneda", typeof(string));
            dataTable.Columns.Add("Importe", typeof(string));
            dataTable.Columns.Add("Número Pago", typeof(string));
            dataTable.Columns.Add("Ejercicio", typeof(string));
            dataTable.Columns.Add("Clabe", typeof(string));
            dataTable.Columns.Add("Cuv", typeof(string));
            dataTable.Columns.Add("Orden Verificación", typeof(string));
            dataTable.Columns.Add("Fecha Individualización", typeof(string));
            dataTable.Columns.Add("Referencia", typeof(string));
            dataTable.Columns.Add("Fecha Pago Póliza", typeof(string));
            dataTable.Columns.Add("Fecha Pago Aseguradora", typeof(string));
            dataTable.Columns.Add("RFC Oferente", typeof(string));
            dataTable.Columns.Add("ONAVI", typeof(string));
            dataTable.Columns.Add("Días Diferencia", typeof(string));            

            if (listaPER != null && listaPER.Any())
            {

                foreach (ReporteExcelPER entity in listaPER)
                {
                    List<object> listRows = new List<object>();

                    {
                        object value = entity.acreedorSAPaseguradora;

                        listRows.Add(this.getValue(value));
                    }

                    {
                        object value = entity.acreedorSAPoferente;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.proyecto;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.articulo;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.moneda;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.costoEvaluacionRiesgo;

                        listRows.Add(this.getValue(value));

                    }


                    {
                        object value = entity.indentificadorVivienda;

                        listRows.Add(this.getValue(value));

                    }


                    {
                        object value = entity.ejercicio;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.clabeBancaria;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.cuv;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.ordenVerificacion;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.fechaInicioVigencia;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.referencia;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.fechaPago;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.fechaPagoAseguradora;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.rfc;

                        listRows.Add(this.getValue(value));

                    }

                   

                    {
                        object value = entity.nombre;

                        listRows.Add(this.getValue(value));

                    }

                    {
                        object value = entity.diasDiferencia;

                        listRows.Add(this.getValue(value));

                    }

                    dataTable.Rows.Add(listRows.ToArray());


                }

            }

            Sheet sheet = new Sheet() { name = "PER", dataTable = dataTable };

            return sheet;
        }

        #endregion

        /// <summary>
        /// 
        /// </summary>
        /// <param name="listHoja"></param>
        /// <param name="extension"></param>
        /// <returns></returns>
        public byte[] WriteExcelWithNPOI(List<Sheet> listSheet, EXTENSION extension)
        {

            byte[] file = null;

            if (listSheet != null && listSheet.Any())
            {
                IWorkbook workbook = null;

                if (extension.ToString().ToUpper() == "XLSX")
                {
                    workbook = new XSSFWorkbook();
                }

                if (extension.ToString().ToUpper() == "XLS")
                {
                    workbook = new HSSFWorkbook();
                }

                if (workbook == null)
                {
                    throw new Exception("This format is not supported");
                }

                foreach (Sheet sheet in listSheet)
                {
                    ISheet sheet1 = workbook.CreateSheet(sheet.name);

                    //make a header row
                    IRow row1 = sheet1.CreateRow(0);

                    for (int j = 0; j < sheet.dataTable.Columns.Count; j++)
                    {

                        ICell cell = row1.CreateCell(j);
                        String columnName = sheet.dataTable.Columns[j].ToString();
                        cell.SetCellValue(columnName);
                    }

                    //loops through data
                    for (int i = 0; i < sheet.dataTable.Rows.Count; i++)
                    {
                        IRow row = sheet1.CreateRow(i + 1);
                        for (int j = 0; j < sheet.dataTable.Columns.Count; j++)
                        {

                            ICell cell = row.CreateCell(j);
                            String columnName = sheet.dataTable.Columns[j].ToString();
                            cell.SetCellValue(sheet.dataTable.Rows[i][columnName].ToString());
                        }
                    }


                }

                MemoryStream exportData = null;

                using (exportData = new MemoryStream())
                {
                    workbook.Write(exportData);
                }

                file = exportData.ToArray();

            }

            return file;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        private object getValue(object value)
        {
            if (value != null)
            {
                return value;
            }
            else
            {
                return "";
            }
        }



    }



    /// <summary>
    /// 
    /// </summary>
    public class Sheet
    {
        public DataTable dataTable { get; set; }
        public string name { get; set; }
    }


}
