﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.Oferta.Api
{
    public enum EstatusOferta
    {
        EnRegistro = 1,
        Validacion = 2,
        Rechazada = 3,
        ValidacionDePago = 4,
        ActualizacionPorRechazo = 5,
        Aceptada = 6,
        Actualizacion = 7,
        Baja = 8,
        Cerrada = 9,
        ValidacionPorActualizacion = 10,
        RechazadaPorActualizacion = 11,
        TerminandoAceptacion = 12
    }
}
