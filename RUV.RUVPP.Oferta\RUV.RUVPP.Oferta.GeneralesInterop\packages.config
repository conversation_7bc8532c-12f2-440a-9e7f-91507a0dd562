﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="CommonServiceLocator" version="1.3" targetFramework="net452" />
  <package id="Dapper" version="1.50.2" targetFramework="net452" />
  <package id="EnterpriseLibrary.TransientFaultHandling" version="6.0.1304.0" targetFramework="net452" />
  <package id="EnterpriseLibrary.TransientFaultHandling.Data" version="6.0.1304.1" targetFramework="net452" />
  <package id="iTextSharp" version="5.5.13.1" targetFramework="net452" />
  <package id="Microsoft.ApplicationInsights" version="2.2.0" targetFramework="net452" />
  <package id="Microsoft.ApplicationInsights.Agent.Intercept" version="2.0.7" targetFramework="net452" />
  <package id="Microsoft.ApplicationInsights.DependencyCollector" version="2.2.0" targetFramework="net452" />
  <package id="Microsoft.ApplicationInsights.PerfCounterCollector" version="2.2.0" targetFramework="net452" />
  <package id="Microsoft.ApplicationInsights.PersistenceChannel" version="1.2.3" targetFramework="net452" />
  <package id="Microsoft.ApplicationInsights.Web" version="2.2.0" targetFramework="net452" />
  <package id="Microsoft.ApplicationInsights.WindowsServer" version="2.2.0" targetFramework="net452" />
  <package id="Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel" version="2.2.0" targetFramework="net452" />
  <package id="Microsoft.AspNet.Mvc" version="5.2.3" targetFramework="net452" />
  <package id="Microsoft.AspNet.Razor" version="3.2.3" targetFramework="net452" />
  <package id="Microsoft.AspNet.WebApi" version="5.2.3" targetFramework="net452" />
  <package id="Microsoft.AspNet.WebApi.Client" version="5.2.3" targetFramework="net452" />
  <package id="Microsoft.AspNet.WebApi.Core" version="5.2.3" targetFramework="net452" />
  <package id="Microsoft.AspNet.WebApi.WebHost" version="5.2.3" targetFramework="net452" />
  <package id="Microsoft.AspNet.WebPages" version="3.2.3" targetFramework="net452" />
  <package id="Microsoft.Azure.KeyVault.Core" version="1.0.0" targetFramework="net452" />
  <package id="Microsoft.Bcl" version="1.1.10" targetFramework="net452" />
  <package id="Microsoft.Bcl.Async" version="1.0.168" targetFramework="net452" />
  <package id="Microsoft.Bcl.Build" version="1.0.21" targetFramework="net452" />
  <package id="Microsoft.Data.Edm" version="5.6.4" targetFramework="net452" />
  <package id="Microsoft.Data.OData" version="5.6.4" targetFramework="net452" />
  <package id="Microsoft.Data.Services.Client" version="5.6.4" targetFramework="net452" />
  <package id="Microsoft.Diagnostics.Tracing.EventSource.Redist" version="1.1.28" targetFramework="net452" />
  <package id="Microsoft.Owin" version="3.0.1" targetFramework="net452" />
  <package id="Microsoft.Owin.Security" version="3.0.1" targetFramework="net452" />
  <package id="Microsoft.Web.Infrastructure" version="1.0.0.0" targetFramework="net452" />
  <package id="Microsoft.WindowsAzure.ConfigurationManager" version="3.2.1" targetFramework="net452" />
  <package id="Newtonsoft.Json" version="9.0.1" targetFramework="net452" />
  <package id="Oracle.ManagedDataAccess" version="12.1.24160719" targetFramework="net452" />
  <package id="Owin" version="1.0" targetFramework="net452" />
  <package id="RestSharp" version="105.2.3" targetFramework="net452" />
  <package id="RUV.Comun" version="2.6.0" targetFramework="net452" />
  <package id="RUV.Comun.Datos" version="2.6.2.1" targetFramework="net452" />
  <package id="RUV.Comun.Negocio" version="2.6.0.2" targetFramework="net452" />
  <package id="RUV.Comun.Servicios" version="2.6.1" targetFramework="net452" />
  <package id="RUV.Comun.Utilerias" version="2.6.2.4" targetFramework="net452" />
  <package id="RUV.Comun.Web" version="2.6.2.3" targetFramework="net452" />
  <package id="RUV.RUVPP.Datos.Comun.Contratos" version="2.5.0.6" targetFramework="net452" />
  <package id="RUV.RUVPP.Datos.Empresa.SqlAzure" version="2.5.0.5" targetFramework="net452" />
  <package id="RUV.RUVPP.Entidades.Comun" version="1.0.0.9" targetFramework="net452" />
  <package id="RUV.RUVPP.Entidades.Empresa" version="2.5.0.8" targetFramework="net452" />
  <package id="RUV.RUVPP.Entidades.General.Notificaciones" version="2.5.1.32" targetFramework="net452" />
  <package id="RUV.RUVPP.Negocio.Empresa" version="2.5.0.14" targetFramework="net452" />
  <package id="RUV.RUVPP.Negocio.General.Documentos" version="2.5.2.1" targetFramework="net452" />
  <package id="RUV.RUVPP.Negocio.General.Notificaciones" version="2.5.1.23" targetFramework="net452" />
  <package id="RUV.RUVPP.Negocio.General.OrdenTrabajo" version="2.5.0.7" targetFramework="net452" />
  <package id="RUV.RUVPP.Negocio.General.Seguridad" version="2.5.0.15" targetFramework="net452" />
  <package id="RUV.RUVPP.Negocio.General.Tarificador" version="2.5.2.55" targetFramework="net452" />
  <package id="StackExchange.Redis" version="1.0.394" targetFramework="net452" />
  <package id="System.Configuration.Abstractions" version="2.0.2.26" targetFramework="net452" />
  <package id="System.Spatial" version="5.6.4" targetFramework="net452" />
  <package id="Unity" version="4.0.1" targetFramework="net452" />
  <package id="Unity.Interception" version="4.0.1" targetFramework="net452" />
  <package id="WindowsAzure.Storage" version="7.0.0" targetFramework="net452" />
</packages>