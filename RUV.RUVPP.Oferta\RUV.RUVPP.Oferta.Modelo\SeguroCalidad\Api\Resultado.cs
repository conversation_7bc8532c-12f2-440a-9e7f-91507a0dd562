﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Serialization;

namespace RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api
{
    /// <summary>
    /// 
    /// </summary>
    [Serializable, XmlRoot("resultado")]
    public class Resultado
    {
        /// <summary>
        /// 
        /// </summary>
        [XmlElement(ElementName = "descripcion")]
        public string descripcion { get; set; }
        /// <summary>
        /// 
        /// </summary>
        [XmlElement(ElementName = "valor")]
        public string valor { get; set; }
    }
}
