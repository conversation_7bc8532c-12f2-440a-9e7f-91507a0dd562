﻿using RUV.RUVPP.Oferta.Comun.Modelo;
using RUV.RUVPP.Oferta.Modelo.Oferta.Data;
using System;

namespace RUV.RUVPP.Oferta.Modelo.Oferta.Api
{
    public class PrototipoVivienda
    {
        public int idPrototipo { get; set; }
        public string nombrePrototipo { get; set; }
        public DocumentoRuv documentoPrototipo { get; set; }
        public bool estaSeleccionado { get; set; }

        public bool esActualizacion { get; set; }
        public bool mostrar { get; set; }
        public string fechaCarga { get; set; }
        public DateTime fechaCargaUTC { get; set; }
    }
}