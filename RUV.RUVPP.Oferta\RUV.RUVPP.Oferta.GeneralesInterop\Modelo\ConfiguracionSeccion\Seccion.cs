﻿using System.Collections.Generic;

namespace RUV.RUVPP.Oferta.GeneralesInterop.Modelo.ConfiguracionSeccion
{
    public class Seccion
    {
        public int? idSeccion { get; set; }
        public string nombre { get; set; }
        public bool? mostrarEnVyD { get; set; }
        public bool? validable { get; set; }
        public List<Seccion> secciones { get; set; }
        public List<Elemento> elementos { get; set; }
        public bool? ocultarEnRegistro { get; set; }
    }
}
