﻿using RUV.RUVPP.Negocio.General.OrdenTrabajo.Servicios;
using RUV.RUVPP.Entidades.Comun.Enums;

namespace RUV.RUVPP.Oferta.GeneralesInterop.OrdenTrabajo
{
    public class ServicioOrdenTrabajoProyectoActualizacion : ServicioOrdenTrabajo
    {
        public ServicioOrdenTrabajoProyectoActualizacion()
            : base(Producto.Oferta, ServicioProducto.ActualizacionUbicacion, null)
        {
        }
    }
}
