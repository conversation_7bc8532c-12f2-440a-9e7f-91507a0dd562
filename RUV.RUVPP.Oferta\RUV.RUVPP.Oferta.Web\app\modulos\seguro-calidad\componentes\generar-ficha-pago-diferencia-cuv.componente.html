<div class="panel panel-default">
    <div class="panel-heading">
        <h4 class="panel-title">
            <i class="fa fa-file-text-o"></i> Generar Ficha de Pago de Diferencia por CUV
        </h4>
    </div>
    <div class="panel-body">
        <div class="row">
            <div class="col-md-12">
                <p class="text-info">
                    <i class="fa fa-info-circle"></i>
                    Este servicio genera una ficha de pago de diferencia de Orden de Verificación (clave PDIF) 
                    utilizando únicamente la CUV como dato de entrada.
                </p>
            </div>
        </div>

        <form class="form-horizontal" (ngSubmit)="generarFichaPago()" #formulario="ngForm">
            <div class="form-group">
                <label for="cuv" class="col-sm-2 control-label">CUV <span class="text-danger">*</span></label>
                <div class="col-sm-4">
                    <input type="text" 
                           class="form-control" 
                           id="cuv" 
                           name="cuv"
                           [(ngModel)]="cuv" 
                           placeholder="Ingrese la CUV"
                           maxlength="50"
                           required
                           [disabled]="procesando">
                </div>
                <div class="col-sm-6">
                    <button type="submit" 
                            class="btn btn-primary" 
                            [disabled]="!formulario.form.valid || procesando">
                        <i class="fa fa-cog" [class.fa-spin]="procesando"></i>
                        {{ procesando ? 'Procesando...' : 'Generar Ficha de Pago' }}
                    </button>
                    <button type="button" 
                            class="btn btn-default" 
                            (click)="limpiar()"
                            [disabled]="procesando"
                            style="margin-left: 10px;">
                        <i class="fa fa-refresh"></i> Limpiar
                    </button>
                </div>
            </div>
        </form>

        <!-- Resultado -->
        <div *ngIf="resultado" class="row" style="margin-top: 20px;">
            <div class="col-md-12">
                <div class="alert" 
                     [class.alert-success]="resultado.resultado === 'OK'"
                     [class.alert-danger]="resultado.resultado !== 'OK'">
                    <h4>
                        <i class="fa" 
                           [class.fa-check-circle]="resultado.resultado === 'OK'"
                           [class.fa-exclamation-triangle]="resultado.resultado !== 'OK'"></i>
                        {{ resultado.resultado === 'OK' ? 'Operación Exitosa' : 'Error en la Operación' }}
                    </h4>
                    <p>{{ resultado.descripcion }}</p>
                </div>
            </div>
        </div>

        <!-- Información adicional -->
        <div class="row" style="margin-top: 30px;">
            <div class="col-md-12">
                <div class="panel panel-info">
                    <div class="panel-heading">
                        <h5 class="panel-title">
                            <i class="fa fa-info"></i> Información del Proceso
                        </h5>
                    </div>
                    <div class="panel-body">
                        <ul>
                            <li>El sistema buscará automáticamente la orden de verificación asociada a la CUV.</li>
                            <li>Calculará la diferencia de pago basada en el monto del avalúo y el costo de la orden de verificación.</li>
                            <li>Solo se generará la ficha si existe una diferencia positiva a pagar.</li>
                            <li>La ficha generada tendrá la clave "PDIF" para identificarla como pago de diferencia.</li>
                            <li>Se utilizarán los stored procedures: [contabilidad].[usp_AGREGARYOBTENERFichaPago] y [seguroCalidad].[InsertarFichaPagoSeguroCalidad].</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
