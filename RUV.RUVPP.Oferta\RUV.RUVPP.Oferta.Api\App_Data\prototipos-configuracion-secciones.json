﻿{
  "idProducto": 2,
  "producto": "Oferta",
  "idServicio": 4,
  "servicio": "Alta de Oferta",
  "secciones": [
    {
      "idSeccion": 1,
      "nombre": "Datos Generales",
      "mostrarEnVyD": false,
      "validable": false,
      "secciones": [],
      "elementos": [
        {
          "idElemento": "nombre",
          "nombre": "Nombre",
          "validable": true
        },
        {
          "idElemento": "numeroNivelesVivienda",
          "nombre": "Niveles de la vivienda",
          "validable": true
        },
        {
          "idElemento": "numeroRecamaras",
          "nombre": "Récamaras",
          "validable": true
        },
        {
          "idElemento": "idTipologiaVivienda",
          "nombre": "Tipología de la vivienda",
          "validable": true
        },
        {
          "idElemento": "areaLote",
          "nombre": "Área del lote",
          "validable": true
        },
        {
          "idElemento": "medidasFrente",
          "nombre": "Medidas de frente",
          "validable": true
        },
        {
          "idElemento": "numeroNivelesPrototipo",
          "nombre": "Niveles del prototipo",
          "validable": true
        },
        {
          "idElemento": "numeroAlcobas",
          "nombre": "Alcobas",
          "validable": true
        },
        {
          "idElemento": "numeroBaniosCompletos",
          "nombre": "Baños",
          "validable": true
        },
        {
          "idElemento": "numeroBaniosMedios",
          "nombre": "Medio baño",
          "validable": true
        },
        {
          "idElemento": "precioVivienda",
          "nombre": "Precio",
          "validable": true
        },
        {
          "idElemento": "idClasificacionVivienda",
          "nombre": "Clasificación",
          "validable": true
        }
      ]
    },
    {
      "idSeccion": 2,
      "nombre": "Datos Opcionales",
      "mostrarEnVyD": false,
      "validable": false,
      "secciones": [],
      "elementos": [
        {
          "idElemento": "alturaLotes",
          "nombre": "Altura de locales",
          "validable": true
        },
        {
          "idElemento": "huellas",
          "nombre": "Huellas",
          "validable": true
        },
        {
          "idElemento": "peraltes",
          "nombre": "Peraltes",
          "validable": true
        },
        {
          "idElemento": "anchoRampa",
          "nombre": "Ancho de rampa",
          "validable": true
        },
        {
          "idElemento": "escaleras",
          "nombre": "Escaleras",
          "validable": true
        },
        {
          "idElemento": "cargaPlano",
          "validable": true
        }
      ]
    },
    {
      "idSeccion": 3,
      "nombre": "Distribución",
      "mostrarEnVyD": false,
      "validable": false,
      "secciones": [],
      "elementos": [
        {
          "idElemento": "totalSuperficie",
          "validable": true
        },
        {
          "idElemento": "ancho",
          "nombre": "Ancho",
          "validable": true
        },
        {
          "idElemento": "largo",
          "nombre": "Largo",
          "validable": true
        },
        {
          "idElemento": "areaMuros",
          "nombre": "AreaMuros",
          "validable": true
        },
        {
          "idElemento": "areaAdicional",
          "nombre": "AreaMuros",
          "validable": true
        },
        {
          "idElemento": "superficieVolados",
          "nombre": "SuperficieVolados",
          "validable": true
        },
        {
          "idElemento": "superficieIndivisosaCubierto",
          "nombre": "SuperficieIndivisosaCubierto",
          "validable": true
        }
      ]
    }
  ]
}