﻿import { Injectable } from '@angular/core';
import { URLSearchParams } from '@angular/http';
import { Http, Response, Headers } from '@angular/http';

import { ResponseConsultaDevolucionesInterno, ResponseConsultaDevoluciones, DevolucionEvaluacion, Respuesta, DevolucionPoliza, PagosAseguradora, GuardarDocumentoPDF, JsonResponse, SiniestroRespuestaAseguradora, EstatusSiniestro, SiniestroCompleto, TipoCoberturaSiniestro, TipoSiniestro, InformacionSiniestro, InformacionSiniestroExcel, ResultadoPeticion, Aseguradora, parametrosConfigs, Opcion, IncidenciaGestion, CuvValida, CuvSeleccionada, EvaluacionRiesgo, Incidencia, Mitigacion, InformacionPreviaNotificacion, OrdenDeVerificacion, RelacionComercial, DetalleSeleccionAseguradora, FichaPagoRiesgo, DatosGeneralesEmpresa, pagoDiferenciaOV, RespuestaPrepago, ConfirmarPrepago, CargoPrepago, RequestUrlReporteAseguradora, ResponseUrlReporteAseguradora, ResponseAseguradoras, RespuestaERP, EstadoDeCuenta, SaldosEstadoDeCuenta, MovimientosEstadoDeCuenta, Movimiento } from './../modelo/index';
import { ServicioDatosBase, ServicioAlertas, ResultadoPaginado, CatalogoBase } from './../../../../libs/ruvpp-angular2/index';

@Injectable()
export class ServicioDatosSeguroCalidad extends ServicioDatosBase {

    private urls = {
        obtenerAseguradoraConPaginado: "/seguro-calidad/aseguradoras",
        obtenerAseguradoraEnPadronConPaginado: "/seguro-calidad/aseguradoras-padron",
        pasarAseguradoraPadron: "/seguro-calidad/pasar-aseguradora-padron/{0}",
        guardarSancion: "/seguro-calidad/guardar-sancion",
        guardarRelacion: "/seguro-calidad/guardar-relacion",
        actualizarSancion: "/seguro-calidad/actualizar-sancion",
        actualizarAseguradoraOrdenPadron: "/seguro-calidad/actualizar-aseguradora-orden",
        obtenerSancionesAseguradora: "/seguro-calidad/aseguradoras-sanciones",
        obtenerCatalogoTipoIncidencias: "/seguro-calidad/catalogo/tipo-incidencias",
        ObtenerCatalogoEstatusIncidencias: "/seguro-calidad/catalogo/estatus-incidencias",
        ObtenerEstatusPagoEvaluacionRiesgoAsync: "/seguro-calidad/catalogo/estatus-pago-evaluacion",
        ObtenerEstatusPagosAsync: "/seguro-calidad/catalogo/estatus-pagos",
        ObtenerCatalooClasificacionIncidencias: "/seguro-calidad/catalogo/clasificacion-incidencias",
        ObtenerIncidenciasPaginado: '/seguro-calidad/incidencias',
        ObtenerIncidenciasSinPaginado: '/seguro-calidad/incidencias-sin-paginado',
        ObtenerIncidenciasInternoPaginado: '/seguro-calidad/incidencias-Interno',
        ObtenerIncidenciasInternoSinPaginado: '/seguro-calidad/incidencias-interno-sin-paginado',
        obtenerRelacionComercial: "/seguro-calidad/relacion-comercial",
        obtenerAseguradoraRelacionComercialConPaginado: "/seguro-calidad/aseguradoras-relacion-comercial",
        obtenerDesarrolladoresRelacionComercialConPaginado: "/seguro-calidad/desarrollador-relacion-comercial",
        obtenerAseguradoraPadronSinOferenteRelacionComercialConPaginado: "/seguro-calidad/aseguradoras-padron-sinoferente-relacion-comercial",
        ObtenerAseguradoraEnPadronSinOferenteXDefectoConPaginado: "/seguro-calidad/aseguradoras-padron-sinoferente-relacion-comercial-defecto",
        actualizarRelacionComercial: "/seguro-calidad/actualizar-relacion-comercial",
        actualizarRelacionComercialXRechazo: "/seguro-calidad/actualizar-relacion-comercial-rechazo",
        ObtenerCatalogoOrdenesVerificacion: '/seguro-calidad/catalogo/ordenes-verificacion',
        ObtenerCatalogoOrdenesVerificacionConsulta: '/seguro-calidad/catalogo/ordenes-verificacion-consulta',
        ObtenerCatalogoOVDiferencias: '/seguro-calidad/catalogo/ov-diferencias',
        ObtenerDatosOVNuevaIncidencia: '/seguro-calidad/datos-ov-incidencia',
        ValidarCuvValida: '/seguro-calidad/cuv/{0}/validar',
        ObtenerCuvsXOV: '/seguro-calidad/orden-verificacion/{0}/cuvs',
        ObtenerCuvsXOVPaginado: '/seguro-calidad/orden-verificacion/{0}/cuvs/paginado',
        ObtenerCatalogoCoberturaAfectada: '/seguro-calidad/catalogo/cobertura-afectada',
        ObtenerCatalogoClasificacionRiesgo: '/seguro-calidad/catalogo/clasificacion-riesgo',
        guardarIncidencia: "/seguro-calidad/incidencia",
        ObtenerInformacionPreviaNotificacion: '/seguro-calidad/notificacion/{0}/informacion-previa',
        ObtenerCuvsXOVEvaluacionRiesgos: '/seguro-calidad/orden-verificacion/{0}/cuvs-evaluacion-riesgos',
        ObtenerCuvsXOVEvaluacionRiesgosPaginado: '/seguro-calidad/orden-verificacion/{0}/cuvs-evaluacion-riesgos/paginado',
        ValidarCuvValidaEvaluacionRiesgos: '/seguro-calidad/cuv/{0}/validar-evaluacion-riesgos',
        guardarEvaluacionRiesgo: "/seguro-calidad/evaluacion-riesgo",
        ObtenerDetalleIncidencia: '/seguro-calidad/incidencia/{0}/detalle',
        ObtenerDatosRegistroMitigacion: '/seguro-calidad/datos-registro-mitigacion',
        guardarMitigacionIncidencia: '/seguro-calidad/mitigacion',
        cerrarIncidencia: '/seguro-calidad/cerrar/incidencia',
        rechazarMitigacion: '/seguro-calidad/rechazar/mitigacion-incidencia',
        ordenesVerificacion: '/seguro-calidad/ordenes-verificacion',
        obtenerRelacionesComerciales: '/seguro-calidad/obtener-relaciones-comerciales',
        guardarRelacionComercialOrden: '/seguro-calidad/guardar-relacion-comercial-orden',
        guardarRelacionPrepagoComercialOrden: '/seguro-calidad/guardar-prepago-relacion-comercial-orden',
        obtenerRelacionComercialOrden: '/seguro-calidad/obtener-relacion-comercial-orden',
        obtenerListaViviendasOrden: '/seguro-calidad/obtener-lista-viviendas',
        obtenerDatosContactoDireccion: '/seguro-calidad/obtener-datos-contacto-direccion',
        obtenerCuvSinPolizasConPaginado: '/seguro-calidad/obtener-cuvsinpolizas',
        obtenerCuvConPolizasConPaginado: '/seguro-calidad/obtener-cuvconpolizas',
        obtenerListaAvaluosAsync: '/seguro-calidad/obtener-lista-avaluos',
        obtenerInfoOrdenVerificacionAsync: '/seguro-calidad/obtener-info-orden',
        obtenerParametrosConfigs90DiasAsync: '/seguro-calidad/obtener-parametros-config-90Dias',
        actualizarParametrosConfigs90DiasAsync: '/seguro-calidad/actualizar-parametros-config-90Dias',
        agregarPolizaAsync: '/seguro-calidad/agregar-poliza',
        obtenerConsultaAseguradoraConPaginadoAsync: '/seguro-calidad/obtener-consulta-aseguradora',
        obtenerCostoEvaluacion: '/seguro-calidad/obtener-costo-evaluacion',
        obtenerPagoDiferenciaOV: '/seguro-calidad/obtener-pago-diferencia-OV',
        agregarPagoDiferenciaOV: '/seguro-calidad/agregar-pago-diferencia-OV',
        actualizarPagoDiferenciaOV: '/seguro-calidad/actualizar-pago-diferencia-OV',
        obtenerConsultaPagoDiferenciaOV: '/seguro-calidad/obtener-consulta-pago-diferencia-OV',
        generarFichaPagoDiferenciaCuv: '/seguro-calidad/generar-ficha-pago-diferencia-cuv',
        obtenerInformacionSiniestro: '/seguro-calidad/obtener-informacion-siniestro',
        obtenerInformacionSiniestroExcel: '/seguro-calidad/obtener-informacion-siniestro-excel',
        obtenerTipoCoberturaSiniestro: '/seguro-calidad/obtener-TipoCoberturaSiniestro',
        obtenerTipoSiniestro: '/seguro-calidad/obtener-TipoSiniestro',
        guardarRegistroSiniesto: '/seguro-calidad/guardar-reporte-siniestro',
        obtenerEstatusSiniestro: '/seguro-calidad/obtener-EstatusSiniestro',
        obtenerSiniestroRespuestaAseguradora: '/seguro-calidad/obtener-SiniestroRespuesta',
        obtenerAseguradorasSiniestro: '/seguro-calidad/obtener-Aseguradoras-Siniestro',
        obtenerParametro: '/seguro-calidad/obtener-parametro',
        validarPrepago: '/seguro-calidad/validar-prepapago',
        confirmarPrepago: '/seguro-calidad/confirmar-prepago',
        guardarPDF: '/seguro-calidad/guardar-factura-verificador',
        validarSaldoCostoEvaluacion: '/seguro-calidad/validar-saldo-costo-evaluacion',
        confirmarPrepagoEvaluacion: '/seguro-calidad/confirmar-prepago-evaluacion',
        obtenerPagosAseguradora: '/seguro-calidad/consultar-pagos-aseguradora',
        obtenerReportePagoAseguradoraPPSPER: '/seguro-calidad/obtener-reporteurl-PER-PPS',
        obtenerAseguradorasPPSPER: '/seguro-calidad/obtener-aseguradoras',
        descargarPagosAseguradoraExcel: '/seguro-calidad/descargar-pagos-aseguradora-excel',
        obtenerAseguradoras: '/seguro-calidad/obtener-aseguradoras',
        consultaDevolucionPolizaPaginado: '/seguro-calidad/consulta-devolucion-poliza-paginado',
        consultaDevolucionEvaluacionPaginado: '/seguro-calidad/consulta-devolucion-evaluacion-paginado',
        agregarDevolucionPoliza: '/seguro-calidad/solicitar-devolucion-poliza',
        agregarDevolucionEvaluacion: '/seguro-calidad/solicitar-devolucion-evaluacion',
        consultaDevolucionesPaginado: '/seguro-calidad/consulta-devoluciones-paginado',
        descargarConsultaDevolucionesExcel: '/seguro-calidad/obtener-reporteurl-consulta-devoluciones',
        consultaDevolucionesInternoPaginado: '/seguro-calidad/consulta-devoluciones-Interno-paginado',
        descargarConsultaDevolucionesInternoExcel: '/seguro-calidad/obtener-reporteurl-consulta-devoluciones-interno',
        obtenerReportePagoPlataforma: '/seguro-calidad/obtener-reporte-pago-plataforma',

        solicitarPagoPolizaAseguradora: '/seguro-calidad/solicitar-pago-poliza-aseguradora',
        solicitarPagoRiesgoAseguradora: '/seguro-calidad/solicitar-pago-riesgo-aseguradora',
        consultaPagoAsegfuradora: '/seguro-calidad/consultar-pago-aseguradora',

        obtenerDatosEmpresa: "/seguro-calidad/obtener-datos-estado-cuenta",
        obtenerSaldosEmpresa: "/seguro-calidad/obtener-saldos-estado-cuenta",
        obtenerMovimientosEmpresa: "/seguro-calidad/obtener-movimientos-estado-cuenta",
    };

    constructor(http: Http, servicioAlertas: ServicioAlertas) {
        super(http, servicioAlertas, CONFIG.urlApiBaseExterno);
    }

    public consultaDevolucionesInternoPaginado(tamanioPagina: number, pagina: number, ordenVerificacion: string, cuv: string, idDesarrollador: string, idEstatusDevolucion: number, fechaInicio: string, fechaFin: string) {
        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'tamanioPagina', tamanioPagina);
        super.agregarParametroQueryString(parametros, 'pagina', pagina);
        super.agregarParametroQueryString(parametros, 'idDesarrollador', idDesarrollador);
        super.agregarParametroQueryString(parametros, 'ordenVerificacion', ordenVerificacion);
        super.agregarParametroQueryString(parametros, 'cuv', cuv);
        super.agregarParametroQueryString(parametros, 'idEstatusDevolucion', idEstatusDevolucion);
        super.agregarParametroQueryString(parametros, 'fechaInicio', fechaInicio);
        super.agregarParametroQueryString(parametros, 'fechaFin', fechaFin);

        return this.get<ResultadoPaginado<ResponseConsultaDevolucionesInterno>>(this.urls.consultaDevolucionesInternoPaginado, undefined, parametros);
    }

    public obtenerDatosEmpresaEstadoCuentaAsync(idEmpresa: number) {

        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'idEmpresa', idEmpresa);

        return this.get<EstadoDeCuenta>(this.urls.obtenerDatosEmpresa, undefined, parametros);
    }

    public obtenerSaldosEmpresaEstadoCuentaAsync(idEmpresa: number) {

        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'idEmpresa', idEmpresa);

        return this.get<SaldosEstadoDeCuenta>(this.urls.obtenerSaldosEmpresa, undefined, parametros);
    }

    public obtenerMovimientosEmpresaEstadoCuentaAsync(tamanioPagina: number, pagina: number, idEmpresa: number) {

        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'tamanioPagina', tamanioPagina);
        super.agregarParametroQueryString(parametros, 'pagina', pagina);
        super.agregarParametroQueryString(parametros, 'idEmpresa', idEmpresa);

        return this.get<ResultadoPaginado<MovimientosEstadoDeCuenta>>(this.urls.obtenerMovimientosEmpresa, undefined, parametros);
    }

    public DescargarConsultaDevolucionesExcel(idEmpresa:number, idOferente: number, ordenVerificacion: string, cuv: string, fechaInicio: string, fechaFin: string) {
        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'idEmpresa', idEmpresa);
        super.agregarParametroQueryString(parametros, 'idOferente', idOferente);
        super.agregarParametroQueryString(parametros, 'ordenVerificacion', ordenVerificacion);
        super.agregarParametroQueryString(parametros, 'cuv', cuv);
        super.agregarParametroQueryString(parametros, 'fechaInicio', fechaInicio);
        super.agregarParametroQueryString(parametros, 'fechaFin', fechaFin);

        return this.get<string>(this.urls.descargarConsultaDevolucionesExcel, undefined, parametros);
    }

    public DescargarConsultaDevolucionesInternoExcel(idEmpresa: number, ordenVerificacion: string, cuv: string, idDesarrollador: string, idEstatusDevolucion: number, fechaInicio: string, fechaFin: string) {
        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'idEmpresa', idEmpresa);
        super.agregarParametroQueryString(parametros, 'idDesarrollador', idDesarrollador);
        super.agregarParametroQueryString(parametros, 'ordenVerificacion', ordenVerificacion);
        super.agregarParametroQueryString(parametros, 'cuv', cuv);
        super.agregarParametroQueryString(parametros, 'idEstatusDevolucion', idEstatusDevolucion);
        super.agregarParametroQueryString(parametros, 'fechaInicio', fechaInicio);
        super.agregarParametroQueryString(parametros, 'fechaFin', fechaFin);

        return this.get<string>(this.urls.descargarConsultaDevolucionesInternoExcel, undefined, parametros);
    }

    public consultaDevolucionesPaginado(tamanioPagina: number, pagina: number, ordenVerificacion: string, cuv: string, idOferente: number, fechaInicio: string, fechaFin: string) {
        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'tamanioPagina', tamanioPagina);
        super.agregarParametroQueryString(parametros, 'pagina', pagina);
        super.agregarParametroQueryString(parametros, 'idOferente', idOferente);
        super.agregarParametroQueryString(parametros, 'ordenVerificacion', ordenVerificacion);
        super.agregarParametroQueryString(parametros, 'cuv', cuv);
        super.agregarParametroQueryString(parametros, 'fechaInicio', fechaInicio);
        super.agregarParametroQueryString(parametros, 'fechaFin', fechaFin);

        return this.get<ResultadoPaginado<ResponseConsultaDevoluciones>>(this.urls.consultaDevolucionesPaginado, undefined, parametros);
    }

    public solicitarDevolucionPoliza(listaDevolucion) {
        return this.post<Respuesta>(this.urls.agregarDevolucionPoliza, JSON.stringify(listaDevolucion));
    }

    public solicitarDevolucionEvaluacion(listaDevolucion) {
        return this.post<Respuesta>(this.urls.agregarDevolucionEvaluacion, JSON.stringify(listaDevolucion));
    }

    public consultaDevolucionPolizaPaginado(tamanioPagina: number, pagina: number, ordenVerificacion: string, cuv: string, idOferente: number, fechaInicio: string, fechaFin: string) {
        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'tamanioPagina', tamanioPagina);
        super.agregarParametroQueryString(parametros, 'pagina', pagina);
        super.agregarParametroQueryString(parametros, 'idOferente', idOferente);
        super.agregarParametroQueryString(parametros, 'ordenVerificacion', ordenVerificacion);
        super.agregarParametroQueryString(parametros, 'cuv', cuv);
        super.agregarParametroQueryString(parametros, 'fechaInicio', fechaInicio);
        super.agregarParametroQueryString(parametros, 'fechaFin', fechaFin);

        return this.get<ResultadoPaginado<DevolucionPoliza>>(this.urls.consultaDevolucionPolizaPaginado, undefined, parametros);
    }

    public consultaDevolucionEvaluacionPaginado(tamanioPagina: number, pagina: number, ordenVerificacion: string, cuv: string, idOferente: number, fechaInicio: string, fechaFin: string) {
        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'tamanioPagina', tamanioPagina);
        super.agregarParametroQueryString(parametros, 'pagina', pagina);
        super.agregarParametroQueryString(parametros, 'idOferente', idOferente);
        super.agregarParametroQueryString(parametros, 'ordenVerificacion', ordenVerificacion);
        super.agregarParametroQueryString(parametros, 'cuv', cuv);
        super.agregarParametroQueryString(parametros, 'fechaInicio', fechaInicio);
        super.agregarParametroQueryString(parametros, 'fechaFin', fechaFin);

        return this.get<ResultadoPaginado<DevolucionEvaluacion>>(this.urls.consultaDevolucionEvaluacionPaginado, undefined, parametros);
    }

    public ObtenerAseguradoras() {
        return this.get<Aseguradora[]>(this.urls.obtenerAseguradoras, undefined, null);
    }

    public ObtenerPagosAseguradoraPaginado(tamanioPagina: number, pagina: number, ordenVerificacion: string, cuv: string, idOferente: string, idAseguradora: string)
    {
        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'tamanioPagina', tamanioPagina);
        super.agregarParametroQueryString(parametros, 'pagina', pagina);
        super.agregarParametroQueryString(parametros, 'ordenVerificacion', ordenVerificacion);
        super.agregarParametroQueryString(parametros, 'cuv', cuv);
        super.agregarParametroQueryString(parametros, 'idOferente', idOferente);
        super.agregarParametroQueryString(parametros, 'idAseguradora', idAseguradora);

        return this.get<ResultadoPaginado<PagosAseguradora>>(this.urls.obtenerPagosAseguradora, undefined, parametros);
    }

    public DescargarPagosAseguradoraExcel(idEmpresa: number, ordenVerificacion: string, cuv: string, idOferente: string, idAseguradora: string)
    {
        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'idEmpresa', idEmpresa);
        super.agregarParametroQueryString(parametros, 'ordenVerificacion', ordenVerificacion);
        super.agregarParametroQueryString(parametros, 'cuv', cuv);
        super.agregarParametroQueryString(parametros, 'idOferente', idOferente);
        super.agregarParametroQueryString(parametros, 'idAseguradora', idAseguradora);

        return this.get<string>(this.urls.descargarPagosAseguradoraExcel, undefined, parametros);
    }

    //Prepago
    public ValidarPrepago(entity: ConfirmarPrepago) {

        return this.post<RespuestaPrepago>(this.urls.validarPrepago, JSON.stringify(entity));
    }

    public ConfirmarPrepago(entity: CargoPrepago)
    {
        return this.post<RespuestaPrepago>(this.urls.confirmarPrepago, entity);
    }


    public ConfirmarPrepagoEvaluacion(ordenVerificacion: string, idRUVAsIs: number, idUsuario: number, monto: number) {

        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'idRUVAsIs', idRUVAsIs);
        super.agregarParametroQueryString(parametros, 'idUsuario', idUsuario);
        super.agregarParametroQueryString(parametros, 'ordenVerificacion', ordenVerificacion);
        super.agregarParametroQueryString(parametros, 'monto', monto);

        return this.get<RespuestaPrepago>(this.urls.confirmarPrepagoEvaluacion, undefined, parametros);
    }


    //Factura Verificador
    public GuardarDocumentoFactura(entity: GuardarDocumentoPDF) {

        return this.post<number>(this.urls.guardarPDF, JSON.stringify(entity));
    }


    public ObtenerAseguradorasSiniestro() {

        return this.get<Aseguradora[]>(this.urls.obtenerAseguradorasSiniestro, undefined, null);
    }

    public obtenerTipoCoberturaSiniestro() {

        return this.get<TipoCoberturaSiniestro>(this.urls.obtenerTipoCoberturaSiniestro, undefined, null);
    }

    public obtenerTipoSiniestro() {

        return this.get<TipoSiniestro>(this.urls.obtenerTipoSiniestro, undefined, null);
    }

    public obtenerEstatusSiniestro() {

        return this.get<EstatusSiniestro>(this.urls.obtenerEstatusSiniestro, undefined, null);
    }

    public obtenerSiniestroRespuestaAseguradora(entity: SiniestroRespuestaAseguradora) {

        return this.post<EstatusSiniestro>(this.urls.obtenerSiniestroRespuestaAseguradora, JSON.stringify(entity));
    }

    public guardarRegistroSiniesto(entity: SiniestroCompleto) {

        return this.post<JsonResponse>(this.urls.guardarRegistroSiniesto, JSON.stringify(entity));
    }

    public ObtenerConsultaAseguradoraConPaginadoAsync(tamanioPagina: number, pagina: number, noRuv: string, razonSocial: string, ordenVerificacion: string, noContrato: string, fechaInicial: string, fechaFinal: string, fechaAceptacionInicio: string, fechaAceptacionFinal: string, idTipoAsignacion: number = null, idOferenteExterno: number = null, idAseguradoraExterno: number = null, idEstatusPagoEvaluacionRiesgo: number = null, soloRelacionComercial: boolean = null) {

        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'tamanioPagina', tamanioPagina);
        super.agregarParametroQueryString(parametros, 'pagina', pagina);
        super.agregarParametroQueryString(parametros, 'noRegistroRUV', noRuv);
        super.agregarParametroQueryString(parametros, 'razonSocial', razonSocial);
        super.agregarParametroQueryString(parametros, 'ordenVerificacion', ordenVerificacion);
        super.agregarParametroQueryString(parametros, 'noContrato', noContrato);
        super.agregarParametroQueryString(parametros, 'fechaInicial', fechaInicial);
        super.agregarParametroQueryString(parametros, 'fechaFinal', fechaFinal);
        super.agregarParametroQueryString(parametros, 'fechaAceptacionInicio', fechaAceptacionInicio);
        super.agregarParametroQueryString(parametros, 'fechaAceptacionFinal', fechaAceptacionFinal);
        super.agregarParametroQueryString(parametros, 'idTipoAsignacion', idTipoAsignacion);
        super.agregarParametroQueryString(parametros, 'idOferenteExterno', idOferenteExterno);
        super.agregarParametroQueryString(parametros, 'idAseguradoraExterno', idAseguradoraExterno);
        super.agregarParametroQueryString(parametros, 'idEstatusPagoEvaluacionRiesgo', idEstatusPagoEvaluacionRiesgo);
        super.agregarParametroQueryString(parametros, 'soloRelacionComercial', soloRelacionComercial);

        return this.get<ResultadoPaginado<Aseguradora>>(this.urls.obtenerConsultaAseguradoraConPaginadoAsync, undefined, parametros);
    }

    public obtenerInformacionSiniestro(informacionSiniestro: InformacionSiniestro)
    {
        return this.post<ResultadoPaginado<InformacionSiniestro>>(this.urls.obtenerInformacionSiniestro, JSON.stringify(informacionSiniestro));
    }

    public obtenerInformacionSiniestroExcel(informacionSiniestroExcel: InformacionSiniestroExcel)
    {
        return this.post<ResultadoPaginado<InformacionSiniestroExcel>>(this.urls.obtenerInformacionSiniestroExcel, JSON.stringify(informacionSiniestroExcel));
    }

    public ObtenerAseguradoraConPaginado(tamanioPagina: number, pagina: number, noRuv: string, razonSocial: string) {

        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'tamanioPagina', tamanioPagina);
        super.agregarParametroQueryString(parametros, 'pagina', pagina);
        super.agregarParametroQueryString(parametros, 'noRegistroRUV', noRuv);
        super.agregarParametroQueryString(parametros, 'razonSocial', razonSocial);

        return this.get<ResultadoPaginado<Aseguradora>>(this.urls.obtenerAseguradoraConPaginado, undefined, parametros);
    }

    public ObtenerAseguradoraRelacionComercialConPaginado(tamanioPagina: number, pagina: number, noRuv: string, razonSocial: string, idDesarrollador: number, idAseguradora: number) {

        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'tamanioPagina', tamanioPagina);
        super.agregarParametroQueryString(parametros, 'pagina', pagina);
        super.agregarParametroQueryString(parametros, 'noRegistroRUV', noRuv);
        super.agregarParametroQueryString(parametros, 'razonSocial', razonSocial);
        super.agregarParametroQueryString(parametros, 'idDesarrollador', idDesarrollador);
        super.agregarParametroQueryString(parametros, 'idAseguradora', idAseguradora);

        return this.get<ResultadoPaginado<Aseguradora>>(this.urls.obtenerAseguradoraRelacionComercialConPaginado, undefined, parametros);
    }

    public obtenerCuvSinPolizasConPaginado(tamanioPagina: number, pagina: number, idOferente: number, idAseguradora: number, cuv: string, ordenVerificacion: string, cambioValorAvaluo: boolean = null) {

        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'tamanioPagina', tamanioPagina);
        super.agregarParametroQueryString(parametros, 'pagina', pagina);
        super.agregarParametroQueryString(parametros, 'cuv', cuv);
        super.agregarParametroQueryString(parametros, 'idOferente', idOferente);
        super.agregarParametroQueryString(parametros, 'idAseguradora', idAseguradora);
        super.agregarParametroQueryString(parametros, 'cambioValorAvaluo', cambioValorAvaluo);
        super.agregarParametroQueryString(parametros, 'ordenVerificacion', ordenVerificacion);

        return this.get<ResultadoPaginado<Aseguradora>>(this.urls.obtenerCuvSinPolizasConPaginado, undefined, parametros);
    }

    public obtenerCuvConPolizasConPaginado(tamanioPagina: number, pagina: number, idOferente: number, idAseguradora: number, cuv: string, ordenVerificacion: string) {

        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'tamanioPagina', tamanioPagina);
        super.agregarParametroQueryString(parametros, 'pagina', pagina);
        super.agregarParametroQueryString(parametros, 'cuv', cuv);
        super.agregarParametroQueryString(parametros, 'idOferente', idOferente);
        super.agregarParametroQueryString(parametros, 'idAseguradora', idAseguradora);
        super.agregarParametroQueryString(parametros, 'ordenVerificacion', ordenVerificacion);

        return this.get<ResultadoPaginado<Aseguradora>>(this.urls.obtenerCuvConPolizasConPaginado, undefined, parametros);
    }

    public obtenerListaAvaluosAsync(tamanioPagina: number, pagina: number, cuv: string) {

        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'tamanioPagina', tamanioPagina);
        super.agregarParametroQueryString(parametros, 'pagina', pagina);
        super.agregarParametroQueryString(parametros, 'cuv', cuv);

        return this.get<ResultadoPaginado<Aseguradora>>(this.urls.obtenerListaAvaluosAsync, undefined, parametros);
    }

    public obtenerInfoOrdenVerificacionAsync(ordenVerificacion: string) {

        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'ordenVerificacion', ordenVerificacion);

        return this.get<OrdenDeVerificacion>(this.urls.obtenerInfoOrdenVerificacionAsync, undefined, parametros);
    }

    public ObtenerOrdenesVerificacionConPaginado(tamanioPagina: number, pagina: number, idRuvAsis: number, ordenVerificacion: string,
        idTipoAsignacion: string, idEmpresaInstAseguradora: string, razonSocialAseguradora: string, noContrato: string) {

        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'tamanioPagina', tamanioPagina);
        super.agregarParametroQueryString(parametros, 'pagina', pagina);
        super.agregarParametroQueryString(parametros, 'idRuvAsis', idRuvAsis);
        super.agregarParametroQueryString(parametros, 'ordenVerificacion', ordenVerificacion);
        super.agregarParametroQueryString(parametros, 'idTipoAsignacion', idTipoAsignacion);
        super.agregarParametroQueryString(parametros, 'idEmpresaInstAseguradora', idEmpresaInstAseguradora);
        super.agregarParametroQueryString(parametros, 'razonSocialAseguradora', razonSocialAseguradora);
        super.agregarParametroQueryString(parametros, 'noContrato', noContrato);

        return this.get<ResultadoPaginado<OrdenDeVerificacion>>(this.urls.ordenesVerificacion, undefined, parametros);
    }

    public ObtenerRelacionesComercialesConPaginado(tamanioPagina: number, pagina: number, idRuvAsis: number, idEmpresaInst: string, nombreRazonSocial: string ) {

        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'tamanioPagina', tamanioPagina);
        super.agregarParametroQueryString(parametros, 'pagina', pagina);
        super.agregarParametroQueryString(parametros, 'idRuvAsis', idRuvAsis);
        super.agregarParametroQueryString(parametros, 'idEmpresaInst', idEmpresaInst);
        super.agregarParametroQueryString(parametros, 'nombreRazonSocial', nombreRazonSocial);

        return this.get<ResultadoPaginado<RelacionComercial>>(this.urls.obtenerRelacionesComerciales, undefined, parametros);
    }


    public GuardarRelacionComercialOrden(relacionComercialOrden) {

        return this.post<number>(this.urls.guardarRelacionComercialOrden, JSON.stringify(relacionComercialOrden));
    }

    public GuardarRelacionPrepagoComercialOrden(relacionComercialOrden) {

        return this.post<number>(this.urls.guardarRelacionPrepagoComercialOrden, JSON.stringify(relacionComercialOrden));
    }


    public AgregarPolizaAsync(listaCuvPolizas) {

        return this.post<number[]>(this.urls.agregarPolizaAsync, JSON.stringify(listaCuvPolizas));
    }

    public ObtenerDesarrolladoresRelacionComercialConPaginado(tamanioPagina: number, pagina: number, noRuv: string, razonSocial: string, ordenVerificacion: string, estatusCosto: string, idDesarrollador: number, idAseguradora: number) {

        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'tamanioPagina', tamanioPagina);
        super.agregarParametroQueryString(parametros, 'pagina', pagina);
        super.agregarParametroQueryString(parametros, 'noRegistroRUV', noRuv);
        super.agregarParametroQueryString(parametros, 'razonSocial', razonSocial);
        super.agregarParametroQueryString(parametros, 'ordenVerificacion', ordenVerificacion);
        super.agregarParametroQueryString(parametros, 'estatusCosto', estatusCosto);
        super.agregarParametroQueryString(parametros, 'idDesarrollador', idDesarrollador);
        super.agregarParametroQueryString(parametros, 'idAseguradora', idAseguradora);

        return this.get<ResultadoPaginado<Aseguradora>>(this.urls.obtenerDesarrolladoresRelacionComercialConPaginado, undefined, parametros);
    }

    public ObtenerAseguradoraPadronSinOferenteRelacionComercialConPaginado(tamanioPagina: number, pagina: number, noRuv: string, razonSocial: string, idDesarrollador: number) {

        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'tamanioPagina', tamanioPagina);
        super.agregarParametroQueryString(parametros, 'pagina', pagina);
        super.agregarParametroQueryString(parametros, 'noRegistroRUV', noRuv);
        super.agregarParametroQueryString(parametros, 'razonSocial', razonSocial);
        super.agregarParametroQueryString(parametros, 'idDesarrollador', idDesarrollador);

        return this.get<ResultadoPaginado<Aseguradora>>(this.urls.obtenerAseguradoraPadronSinOferenteRelacionComercialConPaginado, undefined, parametros);
    }

    public ObtenerAseguradoraEnPadronSinOferenteXDefectoConPaginado(tamanioPagina: number, pagina: number, idDesarrollador: number) {

        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'idDesarrollador', idDesarrollador);

        return this.get<ResultadoPaginado<Aseguradora>>(this.urls.ObtenerAseguradoraEnPadronSinOferenteXDefectoConPaginado, undefined, parametros);
    }

    public obtenerAseguradoraEnPadronConPaginado(tamanioPagina: number, pagina: number, noRuv: string, razonSocial: string) {

        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'tamanioPagina', tamanioPagina);
        super.agregarParametroQueryString(parametros, 'pagina', pagina);
        super.agregarParametroQueryString(parametros, 'noRegistroRUV', noRuv);
        super.agregarParametroQueryString(parametros, 'razonSocial', razonSocial);

        return this.get<ResultadoPaginado<Aseguradora>>(this.urls.obtenerAseguradoraEnPadronConPaginado, undefined, parametros);
    }

    public ObtenerSancionesAseguradora(idSancion?: number, idAseguradora?: number) {

        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'idSancion', idSancion);
        super.agregarParametroQueryString(parametros, 'idAseguradora', idAseguradora);

        return this.get<ResultadoPaginado<Aseguradora>>(this.urls.obtenerSancionesAseguradora, undefined, parametros);
    }

    public ObtenerRelacionComercial(idDesarrollador?: number, idAseguradora?: number) {

        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'idDesarrollador', idDesarrollador);
        super.agregarParametroQueryString(parametros, 'idAseguradora', idAseguradora);

        return this.get<ResultadoPaginado<Aseguradora>>(this.urls.obtenerRelacionComercial, undefined, parametros);
    }

    public obtenerAseguradoraPadronSinOferenteRelacionComercialConPaginado(tamanioPagina: number, pagina: number, noRuv: string, razonSocial: string, idDesarrollador?: number) {

        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'tamanioPagina', tamanioPagina);
        super.agregarParametroQueryString(parametros, 'pagina', pagina);
        super.agregarParametroQueryString(parametros, 'noRegistroRUV', noRuv);
        super.agregarParametroQueryString(parametros, 'razonSocial', razonSocial);
        super.agregarParametroQueryString(parametros, 'idDesarrollador', idDesarrollador);

        return this.get<ResultadoPaginado<Aseguradora>>(this.urls.obtenerAseguradoraPadronSinOferenteRelacionComercialConPaginado, undefined, parametros);
    }

    public pasarAseguradoraPadron(aseguradoras, enPadronAseguradora) {

        return this.post<boolean>(this.urls.pasarAseguradoraPadron.format(enPadronAseguradora), JSON.stringify(aseguradoras));
    }

    public obtenerParametrosConfigs90DiasAsync(listaParametrosConfig) {

        return this.post<parametrosConfigs>(this.urls.obtenerParametrosConfigs90DiasAsync, JSON.stringify(listaParametrosConfig));
    }

    public actualizarParametrosConfigs90DiasAsync(listaParametrosConfig) {

        return this.post<boolean>(this.urls.actualizarParametrosConfigs90DiasAsync, JSON.stringify(listaParametrosConfig));
    }

    public guardarSancion(sancion) {

        return this.post<number>(this.urls.guardarSancion, JSON.stringify(sancion));
    }

    public guardarRelacionComercial(listaRelacion) {

        return this.post<number>(this.urls.guardarRelacion, JSON.stringify(listaRelacion));
    }

    public actualizarSancion(sancion) {

        return this.post<number>(this.urls.actualizarSancion, JSON.stringify(sancion));
    }

    public actualizarAseguradoraOrdenPadron(listaAseguradoras) {

        return this.post<number>(this.urls.actualizarAseguradoraOrdenPadron, JSON.stringify(listaAseguradoras));
    }

    public actualizarRelacionComercial(relacion) {

        return this.post<number>(this.urls.actualizarRelacionComercial, JSON.stringify(relacion));
    }

    public actualizarRelacionComercialXRechazo(relacion) {

        return this.post<number>(this.urls.actualizarRelacionComercialXRechazo, JSON.stringify(relacion));
    }

    public ObtenerEstatusPagoEvaluacion() {

        return this.get<Opcion>(this.urls.ObtenerEstatusPagoEvaluacionRiesgoAsync, undefined, undefined);
    }

    public ObtenerEstatusPagosAsync() {

        return this.get<Opcion>(this.urls.ObtenerEstatusPagosAsync, undefined, undefined);
    }

    //Gestion de incidencias

    //Obtiene las incidencias paginadas
    public obtenerIncidenciasPaginadas(tamanioPagina: number, pagina: number, ordenVerificacion?: string, cuv?: string, desarrollador?: string, claveIncidencia?: string, idTipoIncidencia?: string,
        idEstatusIncidencia?: string, idClasificacionIncidencia?: string, esFechaRegistro?: boolean, esFechaAtencion?: boolean, fechaInicial?: string, fechaFinal?: string, conRieso?: string, idGrupoIncidencia?: string) {
        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'tamanioPagina', tamanioPagina);
        super.agregarParametroQueryString(parametros, 'pagina', pagina);
        super.agregarParametroQueryString(parametros, 'ordenVerificacion', ordenVerificacion);
        super.agregarParametroQueryString(parametros, 'cuv', cuv);
        super.agregarParametroQueryString(parametros, 'desarrollador', desarrollador);
        super.agregarParametroQueryString(parametros, 'claveIncidencia', claveIncidencia);
        super.agregarParametroQueryString(parametros, 'idTipoIncidencia', idTipoIncidencia);
        super.agregarParametroQueryString(parametros, 'idEstatusIncidencia', idEstatusIncidencia);
        super.agregarParametroQueryString(parametros, 'idClasificacionIncidencia', idClasificacionIncidencia);
        super.agregarParametroQueryString(parametros, 'esFechaRegistro', esFechaRegistro);
        super.agregarParametroQueryString(parametros, 'esFechaAtencion', esFechaAtencion);
        super.agregarParametroQueryString(parametros, 'fechaInicial', fechaInicial);
        super.agregarParametroQueryString(parametros, 'fechaFinal', fechaFinal);
        super.agregarParametroQueryString(parametros, 'conRiesgo', conRieso);
        super.agregarParametroQueryString(parametros, 'idGrupoIncidencia', idGrupoIncidencia);

        return this.get<ResultadoPaginado<IncidenciaGestion>>(this.urls.ObtenerIncidenciasPaginado, undefined, parametros);
    }

    //Obtiene las incidencias sin paginar
    public obtenerIncidenciasSinPagianr(ordenVerificacion?: string, cuv?: string, desarrollador?: string, claveIncidencia?: string, idTipoIncidencia?: string,
        idEstatusIncidencia?: string, idClasificacionIncidencia?: string, esFechaRegistro?: boolean, esFechaAtencion?: boolean, fechaInicial?: string,
        fechaFinal?: string, conRieso?: string, idGrupoIncidencia?: string) {
        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'ordenVerificacion', ordenVerificacion);
        super.agregarParametroQueryString(parametros, 'cuv', cuv);
        super.agregarParametroQueryString(parametros, 'desarrollador', desarrollador);
        super.agregarParametroQueryString(parametros, 'claveIncidencia', claveIncidencia);
        super.agregarParametroQueryString(parametros, 'idTipoIncidencia', idTipoIncidencia);
        super.agregarParametroQueryString(parametros, 'idEstatusIncidencia', idEstatusIncidencia);
        super.agregarParametroQueryString(parametros, 'idClasificacionIncidencia', idClasificacionIncidencia);
        super.agregarParametroQueryString(parametros, 'esFechaRegistro', esFechaRegistro);
        super.agregarParametroQueryString(parametros, 'esFechaAtencion', esFechaAtencion);
        super.agregarParametroQueryString(parametros, 'fechaInicial', fechaInicial);
        super.agregarParametroQueryString(parametros, 'fechaFinal', fechaFinal);
        super.agregarParametroQueryString(parametros, 'conRiesgo', conRieso);
        super.agregarParametroQueryString(parametros, 'idGrupoIncidencia', idGrupoIncidencia);

        return this.get<ResultadoPaginado<IncidenciaGestion>>(this.urls.ObtenerIncidenciasSinPaginado, undefined, parametros);
    }

    //Obtiene el catalogo de tipo de incidencias
    public ObtenerTipoIncidencias() {

        return this.get<Opcion>(this.urls.obtenerCatalogoTipoIncidencias, undefined, undefined);
    }

    //Obtiene el catalogo estatus de incidencias
    public ObtenerEstatusIncidencias() {

        return this.get<Opcion>(this.urls.ObtenerCatalogoEstatusIncidencias, undefined, undefined);
    }

    //Obtiene el catalogo clasificacion de incidencias
    public ObtenerClasificacionIncidencias() {

        return this.get<Opcion>(this.urls.ObtenerCatalooClasificacionIncidencias, undefined, undefined);
    }

    //Obtiene el catalogo de cobertura afectada
    public ObtenerCatalogoCoberturaAfectada() {

        return this.get<Opcion>(this.urls.ObtenerCatalogoCoberturaAfectada, undefined, undefined);
    }

    //Obtiene el catalogo de cobertura afectada
    public ObtenerCatalogoClasificacionRiesgo() {

        return this.get<Opcion>(this.urls.ObtenerCatalogoClasificacionRiesgo, undefined, undefined);
    }

    //Obtiene el catalogo de Ordenes de verificacion con los parametros especificados
    public ObtenerCatalogoOrdenesVerificacion(idEntidad: string, idEmpresa: string) {

        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'idEntidad', idEntidad);
        super.agregarParametroQueryString(parametros, 'idEmpresa', idEmpresa);

        return this.get<Opcion>(this.urls.ObtenerCatalogoOrdenesVerificacion, undefined, parametros);
    }

    //Obtiene el catalogo de Ordenes de verificacion con los parametros especificados
    public ObtenerCatalogoOVDiferencias(idEmpresa: number) {

        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'idVerificador', idEmpresa);

        return this.get<Opcion>(this.urls.ObtenerCatalogoOVDiferencias, undefined, parametros);
    }

    //Obtiene el catalogo de Ordenes de verificacion para la consulta
    public ObtenerCatalogoOrdenesVerificacionConsulta(noRuv: string, razonSocial: string, rfc: string) {

        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'noRuv', noRuv);
        super.agregarParametroQueryString(parametros, 'razonSocial', razonSocial);
        super.agregarParametroQueryString(parametros, 'rfc', rfc);

        return this.get<Opcion>(this.urls.ObtenerCatalogoOrdenesVerificacionConsulta, undefined, parametros);
    }

    //Obtiene los datos generales a mostrar en el nuevo registro de una incidencia/notificacion
    public ObtenerDatosOVNuevaIncidencia(ordenVerificacion: string, cuv: string) {

        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'ordenVerificacion', ordenVerificacion);
        super.agregarParametroQueryString(parametros, 'cuv', cuv);

        return this.get<Opcion>(this.urls.ObtenerDatosOVNuevaIncidencia, undefined, parametros);
    }

    //Verifica que una cuv especifica pertenezca a un verificador o bien tenga una relacion comercial aseuradora - desarrollador, dependiendo del perfil del usuario firmado
    public validarCuvValida(claveCuv: string) {

        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'claveCuv', claveCuv);

        return this.get<CuvValida>(this.urls.ValidarCuvValida.format(claveCuv), undefined, parametros);
    }


    //Obtiene una lista de cuvs que pertenecen a una OV
    public obtenerCuvsXOrdenVerificacion(ordenVerificacion: string, cuv: string) {

        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'ordenVerificacion', ordenVerificacion);
        super.agregarParametroQueryString(parametros, 'cuv', cuv);

        return this.get<CuvSeleccionada[]>(this.urls.ObtenerCuvsXOV.format(ordenVerificacion), undefined, parametros);
    }

    //Obtiene una lista de cuvs que pertenecen a una OV PAGINASO
    public obtenerCuvsPaginadas(tamanioPagina: number, pagina: number, ordenVerificacion?: string, cuv?: string) {

        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'tamanioPagina', tamanioPagina);
        super.agregarParametroQueryString(parametros, 'pagina', pagina);
        super.agregarParametroQueryString(parametros, 'ordenVerificacion', ordenVerificacion);
        super.agregarParametroQueryString(parametros, 'cuv', cuv);

        return this.get<ResultadoPaginado<CuvSeleccionada>>(this.urls.ObtenerCuvsXOVPaginado.format(ordenVerificacion), undefined, parametros);
    }

    /**
     * /Guarda una lista de incidencias
     * @param incidencia
     */
    public GuardarIncidencia(incidencia: Incidencia) {
        return this.post<boolean>(this.urls.guardarIncidencia, JSON.stringify(incidencia));
    }

    //Obtiene la descripción y archivos cargados de una notificación previa para registrar una nueva incidencia
    public obtenerInformacionPreviaNotificacion(idIncidencia: string, idVvGeneral: string) {

        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'idIncidencia', idIncidencia);
        super.agregarParametroQueryString(parametros, 'idVvGeneral', idVvGeneral);

        return this.get<ResultadoPaginado<InformacionPreviaNotificacion>>(this.urls.ObtenerInformacionPreviaNotificacion.format(idIncidencia), undefined, parametros);
    }

    //Obtiene una lista de cuvs que pertenecen a una OV
    public obtenerCuvsXOrdenVerificacionEvaluacionRiesgos(ordenVerificacion: string, cuv: string) {

    let parametros = new URLSearchParams();
    super.agregarParametroQueryString(parametros, 'ordenVerificacion', ordenVerificacion);
    super.agregarParametroQueryString(parametros, 'cuv', cuv);

    return this.get<CuvSeleccionada[]>(this.urls.ObtenerCuvsXOVEvaluacionRiesgos.format(ordenVerificacion), undefined, parametros);
}

    //Obtiene una lista de cuvs que pertenecen a una OV PAGINASO
    public obtenerCuvsEvaluacionRiesgosPaginadas(tamanioPagina: number, pagina: number, ordenVerificacion ?: string, cuv ?: string) {

    let parametros = new URLSearchParams();
    super.agregarParametroQueryString(parametros, 'tamanioPagina', tamanioPagina);
    super.agregarParametroQueryString(parametros, 'pagina', pagina);
    super.agregarParametroQueryString(parametros, 'ordenVerificacion', ordenVerificacion);
    super.agregarParametroQueryString(parametros, 'cuv', cuv);

    return this.get<ResultadoPaginado<CuvSeleccionada>>(this.urls.ObtenerCuvsXOVEvaluacionRiesgosPaginado.format(ordenVerificacion), undefined, parametros);
    }

    //Verifica que una cuv especifica pertenezca a un verificador o bien tenga una relacion comercial aseuradora - desarrollador, dependiendo del perfil del usuario firmado y que no tengan evaluacion de riesgos regstrada
    public validarCuvValidaEvaluacionRiesgos(claveCuv: string) {

        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'claveCuv', claveCuv);

        return this.get<CuvValida>(this.urls.ValidarCuvValidaEvaluacionRiesgos.format(claveCuv), undefined, parametros);
    }


    /**
     * /Guarda una lista de evaluaciones de riesgo
     * @param incidencia
     */
    public GuardarEvaluacionRiesgo(evaluacion: EvaluacionRiesgo) {
        return this.post<boolean>(this.urls.guardarEvaluacionRiesgo, JSON.stringify(evaluacion));
    }

    //Obtiene el detalle de una incidenai
    public ObtenerDetalleIncidencia(idIncidencia: number, idVvGeneral: number) {

        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'idIncidencia', idIncidencia);
        super.agregarParametroQueryString(parametros, 'idVvGeneral', idVvGeneral);

        return this.get<ResultadoPaginado<CuvSeleccionada>>(this.urls.ObtenerDetalleIncidencia.format(idIncidencia), undefined, parametros);
    }


    //Obtiene lso datos mostrados en el registro de mitigacion de incidencia
    public ObtenerDatosRegistroMitigacion(idIncidencia: number) {

        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'idIncidencia', idIncidencia);

        return this.get<ResultadoPaginado<CuvSeleccionada>>(this.urls.ObtenerDatosRegistroMitigacion, undefined, parametros);
    }

    /**
    * /Guarda una mitigación de incidencia
    * @param incidencia
    */
    public GuardarMitigacionIncidencia(mitigacion: Mitigacion) {
        return this.post<boolean>(this.urls.guardarMitigacionIncidencia, JSON.stringify(mitigacion));
    }

    /**
    * /Cierra una incidencia
    * @param incidencia
    */
    public CerrarIncidencia(incidencia: IncidenciaGestion[]) {

        return this.post<boolean>(this.urls.cerrarIncidencia, JSON.stringify(incidencia));
    }

    /**
    * /Rechaza la mitigacín de una incidencia
    * @param incidencia
    */
    public RechazarMitigacionIncidencia(incidencia: IncidenciaGestion[]) {
        return this.post<boolean>(this.urls.rechazarMitigacion, JSON.stringify(incidencia));
    }

    public ObtenerDetalleSeleccionOrden(idOrdenVerificacion: string) {

        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'idOrdenVerificacion', idOrdenVerificacion);

        return this.get<DetalleSeleccionAseguradora>(this.urls.obtenerRelacionComercialOrden, undefined, parametros);
    }

    public ObtenerListaViviendasOrden(ordenVerificacion: string, idRuvAsIs: number) {
        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'ordenVerificacion', ordenVerificacion);
        super.agregarParametroQueryString(parametros, 'idRuvAsIs', idRuvAsIs);

        return this.get<FichaPagoRiesgo>(this.urls.obtenerListaViviendasOrden, undefined, parametros);
    }

    public ObtenerDatosContactoDireccion(idRuvAsIs: number) {
        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'idRuvAsIs', idRuvAsIs);

        return this.get<DatosGeneralesEmpresa>(this.urls.obtenerDatosContactoDireccion, undefined, parametros);
    }


    //Obtiene las incidencias Interno sin paginar
    public obtenerIncidenciasInternoSinPaginar(ordenVerificacion?: string, cuv?: string, desarrollador?: string, claveIncidencia?: string, idTipoIncidencia?: string,
        idEstatusIncidencia?: string, idClasificacionIncidencia?: string, esFechaRegistro?: boolean, esFechaAtencion?: boolean, fechaInicial?: string, fechaFinal?: string, conRieso?: string, verificador?: string, aseguradora?: string) {
        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'ordenVerificacion', ordenVerificacion);
        super.agregarParametroQueryString(parametros, 'cuv', cuv);
        super.agregarParametroQueryString(parametros, 'desarrollador', desarrollador);
        super.agregarParametroQueryString(parametros, 'claveIncidencia', claveIncidencia);
        super.agregarParametroQueryString(parametros, 'idTipoIncidencia', idTipoIncidencia);
        super.agregarParametroQueryString(parametros, 'idEstatusIncidencia', idEstatusIncidencia);
        super.agregarParametroQueryString(parametros, 'idClasificacionIncidencia', idClasificacionIncidencia);
        super.agregarParametroQueryString(parametros, 'esFechaRegistro', esFechaRegistro);
        super.agregarParametroQueryString(parametros, 'esFechaAtencion', esFechaAtencion);
        super.agregarParametroQueryString(parametros, 'fechaInicial', fechaInicial);
        super.agregarParametroQueryString(parametros, 'fechaFinal', fechaFinal);
        super.agregarParametroQueryString(parametros, 'conRiesgo', conRieso);
        super.agregarParametroQueryString(parametros, 'verificador', verificador);
        super.agregarParametroQueryString(parametros, 'aseguradora', aseguradora);

        return this.get<ResultadoPaginado<IncidenciaGestion>>(this.urls.ObtenerIncidenciasInternoSinPaginado, undefined, parametros);
    }
    //Obtiene las incidencias Interno paginadas
    public obtenerIncidenciasInternoPaginadas(tamanioPagina: number, pagina: number, ordenVerificacion?: string, cuv?: string, desarrollador?: string, claveIncidencia?: string, idTipoIncidencia?: string,
        idEstatusIncidencia?: string, idClasificacionIncidencia?: string, esFechaRegistro?: boolean, esFechaAtencion?: boolean, fechaInicial?: string, fechaFinal?: string, conRieso?: string, verificador?: string, aseguradora?: string) {
        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'tamanioPagina', tamanioPagina);
        super.agregarParametroQueryString(parametros, 'pagina', pagina);
        super.agregarParametroQueryString(parametros, 'ordenVerificacion', ordenVerificacion);
        super.agregarParametroQueryString(parametros, 'cuv', cuv);
        super.agregarParametroQueryString(parametros, 'desarrollador', desarrollador);
        super.agregarParametroQueryString(parametros, 'claveIncidencia', claveIncidencia);
        super.agregarParametroQueryString(parametros, 'idTipoIncidencia', idTipoIncidencia);
        super.agregarParametroQueryString(parametros, 'idEstatusIncidencia', idEstatusIncidencia);
        super.agregarParametroQueryString(parametros, 'idClasificacionIncidencia', idClasificacionIncidencia);
        super.agregarParametroQueryString(parametros, 'esFechaRegistro', esFechaRegistro);
        super.agregarParametroQueryString(parametros, 'esFechaAtencion', esFechaAtencion);
        super.agregarParametroQueryString(parametros, 'fechaInicial', fechaInicial);
        super.agregarParametroQueryString(parametros, 'fechaFinal', fechaFinal);
        super.agregarParametroQueryString(parametros, 'conRiesgo', conRieso);
        super.agregarParametroQueryString(parametros, 'verificador', verificador);
        super.agregarParametroQueryString(parametros, 'aseguradora', aseguradora);

        return this.get<ResultadoPaginado<IncidenciaGestion>>(this.urls.ObtenerIncidenciasInternoPaginado, undefined, parametros);
    }

    public obtenerCostoEvaluacionRiesgo(ordenVerificacion: string) {
        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'ordenVerificacion', ordenVerificacion);

        return this.get<string>(this.urls.obtenerCostoEvaluacion, undefined, parametros);
    }

    public ValidarSaldoEvaluacionRiesgo(ordenVerificacion: string, idRUVAsis: number) {
        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'ordenVerificacion', ordenVerificacion);
        super.agregarParametroQueryString(parametros, 'idRUVAsis', idRUVAsis);

        return this.get<string>(this.urls.validarSaldoCostoEvaluacion, undefined, parametros);
    }

    public obtenerPagoDiferenciaOV(tamanioPagina: number, pagina: number, ordenVerificacion: string, idEstatusPago: string = null) {
        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'tamanioPagina', tamanioPagina);
        super.agregarParametroQueryString(parametros, 'pagina', pagina);
        super.agregarParametroQueryString(parametros, 'ordenVerificacion', ordenVerificacion);
        super.agregarParametroQueryString(parametros, 'idEstatusPago', idEstatusPago);

        return this.get<string>(this.urls.obtenerPagoDiferenciaOV, undefined, parametros);
    }

    public obtenerConsultaPagoDiferenciaOV(tamanioPagina: number, pagina: number, ordenVerificacion: string, idEstatusPago: string = null, idEmpresaInst: string = null) {
        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'tamanioPagina', tamanioPagina);
        super.agregarParametroQueryString(parametros, 'pagina', pagina);
        super.agregarParametroQueryString(parametros, 'ordenVerificacion', ordenVerificacion);
        super.agregarParametroQueryString(parametros, 'idEstatusPago', idEstatusPago);
        super.agregarParametroQueryString(parametros, 'idEmpresaInst', idEmpresaInst);

        return this.get<string>(this.urls.obtenerConsultaPagoDiferenciaOV, undefined, parametros);
    }

    public agregarPagoDiferenciaOV(numeroFactura: string, ordenVerificacion: string, fechaEntregaFactura: string) {
        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'numeroFactura', numeroFactura);
        super.agregarParametroQueryString(parametros, 'ordenVerificacion', ordenVerificacion);
        super.agregarParametroQueryString(parametros, 'fechaEntregaFactura', fechaEntregaFactura);

        return this.get<string>(this.urls.agregarPagoDiferenciaOV, undefined, parametros);
    }

    public actualizarPagoDiferenciaOV(seleccionadas: pagoDiferenciaOV[]) {

        return this.post<ResultadoPeticion>(this.urls.actualizarPagoDiferenciaOV, JSON.stringify(seleccionadas));
    }

    public generarFichaPagoDiferenciaPorCUV(cuv: string) {
        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'cuv', cuv);

        return this.post<ResultadoPeticion>(this.urls.generarFichaPagoDiferenciaCuv, undefined, parametros);
    }
    public obtenerParametro(parametro: string) {
        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'clave', parametro);

        return this.get<parametrosConfigs>(this.urls.obtenerParametro, undefined, parametros);
    }



    //Fin gestion de incidencias

    //Reporte Pago Aeguradora PPS PER
    public ObtenerURLreporteAseguradoraPPSPER(parametro: RequestUrlReporteAseguradora) {
        return this.post<ResponseUrlReporteAseguradora>(this.urls.obtenerReportePagoAseguradoraPPSPER, JSON.stringify(parametro));
    }

    public obtenerAseguradorasPPSPER() {
        let parametros = new URLSearchParams();
        return this.get<ResponseAseguradoras>(this.urls.obtenerAseguradorasPPSPER, undefined, parametros);
    }

    //FIN Reporte Pago Aeguradora PPS PER

    public obtenerReportePagoPlataforma(fecha: string) {
        let parametros = new URLSearchParams();
        super.agregarParametroQueryString(parametros, 'fecha', fecha);
        return this.get<string>(this.urls.obtenerReportePagoPlataforma, undefined, parametros);
    }

    // Consultar Pagos Aseguradora
    // Solicitar Pago Poliza Aseguradora
    // solicitar PagoRies goAseguradora
    public solicitarPagoPolizaAseguradora(parametro: RequestUrlReporteAseguradora) {
        return this.post<RespuestaERP>(this.urls.solicitarPagoPolizaAseguradora, JSON.stringify(parametro));
    }

    public solicitarPagoRiesgoAseguradora(parametro: RequestUrlReporteAseguradora) {
        return this.post<RespuestaERP>(this.urls.solicitarPagoRiesgoAseguradora, JSON.stringify(parametro));
    }

    public consultaPagoAsegfuradora(parametro: RequestUrlReporteAseguradora) {
        return this.post<ResponseUrlReporteAseguradora>(this.urls.consultaPagoAsegfuradora, JSON.stringify(parametro));
    }
}