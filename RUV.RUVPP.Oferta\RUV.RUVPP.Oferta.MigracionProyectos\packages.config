﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="CommonServiceLocator" version="1.3" targetFramework="net452" />
  <package id="EntityFramework" version="6.1.3" targetFramework="net452" />
  <package id="Microsoft.ApplicationInsights" version="2.1.0" targetFramework="net452" />
  <package id="Microsoft.Azure.KeyVault.Core" version="1.0.0" targetFramework="net452" />
  <package id="Microsoft.Data.Edm" version="5.6.4" targetFramework="net452" />
  <package id="Microsoft.Data.OData" version="5.6.4" targetFramework="net452" />
  <package id="Microsoft.Data.Services.Client" version="5.6.4" targetFramework="net452" />
  <package id="Microsoft.WindowsAzure.ConfigurationManager" version="3.2.1" targetFramework="net452" />
  <package id="Newtonsoft.Json" version="10.0.2" targetFramework="net452" />
  <package id="RUV.Comun" version="2.6.0" targetFramework="net452" />
  <package id="RUV.Comun.Utilerias" version="2.6.2.4" targetFramework="net452" />
  <package id="System.Spatial" version="5.6.4" targetFramework="net452" />
  <package id="Unity" version="4.0.1" targetFramework="net452" />
  <package id="WindowsAzure.Storage" version="7.0.0" targetFramework="net452" />
</packages>