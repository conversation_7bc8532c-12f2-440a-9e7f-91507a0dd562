﻿using Microsoft.ApplicationInsights;
using Microsoft.ApplicationInsights.DataContracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.CargaSDF
{
    /// <summary>
    /// Contiene métodos de extensión para el cliente de Application Insights.
    /// </summary>
    public static class TelemetryClientExtensions
    {
        /// <summary>
        /// Permite ejecutar una dependencia midiendo su duración y registrandola en Application Insights.
        /// </summary>
        /// <param name="cliente">Cliente de telemetría.</param>
        /// <param name="nombreDependencia">Nombre de la dependencia.</param>
        /// <param name="nombreComando">Nombre del comando ejecutado.</param>
        /// <param name="accion">Acción de la dependencia.</param>       
        /// <param name="nombreTipoDependencia">Nombre del tipo de dependencia.</param>
        /// <param name="codigoResultado">Resultado del comando.</param>
        /// <param name="propiedades">Lista de propiedades definidas por la aplicación</param>
        public static void MedirDependencia(this TelemetryClient cliente, string nombreDependencia, string nombreComando, Action accion, string nombreTipoDependencia = null, string codigoResultado = null, IDictionary<string, string> propiedades = null)
        {
            bool exito = false;
            var horaInicial = DateTime.UtcNow;
            var timer = System.Diagnostics.Stopwatch.StartNew();

            try
            {
                accion();
                exito = true;
            }
            finally
            {
                timer.Stop();

                var telemetria = GenerarTelemetriaDeDependencia(nombreDependencia, nombreComando, horaInicial, timer.Elapsed, exito, nombreTipoDependencia, codigoResultado, propiedades);

                cliente.TrackDependency(telemetria);
            }
        }

        /// <summary>
        /// Permite ejecutar una dependencia midiendo su duración y registrandola en Application Insights.
        /// </summary>
        /// <typeparam name="R">Tipo del resultado.</typeparam>
        /// <param name="cliente">Cliente de telemetría.</param>
        /// <param name="nombreDependencia">Nombre de la dependencia.</param>
        /// <param name="nombreComando">Nombre del comando ejecutado.</param>
        /// <param name="accion">Acción de la dependencia.</param>
        /// <param name="nombreTipoDependencia">Nombre del tipo de dependencia.</param>
        /// <param name="codigoResultado">Resultado del comando.</param>
        /// <param name="propiedades">Lista de propiedades definidas por la aplicación</param>
        /// <returns>Resultado de la dependencia.</returns>
        public static R MedirDependencia<R>(this TelemetryClient cliente, string nombreDependencia, string nombreComando, Func<R> accion, string nombreTipoDependencia = null, string codigoResultado = null, IDictionary<string, string> propiedades = null)
        {
            R resultado = default(R);
            bool exito = false;
            var horaInicial = DateTime.UtcNow;
            var timer = System.Diagnostics.Stopwatch.StartNew();

            try
            {
                resultado = accion();
                exito = true;

                return resultado;
            }
            finally
            {
                timer.Stop();

                var telemetria = GenerarTelemetriaDeDependencia(nombreDependencia, nombreComando, horaInicial, timer.Elapsed, exito, nombreTipoDependencia, codigoResultado, propiedades);

                cliente.TrackDependency(telemetria);
            }
        }


        /// <summary>
        /// Permite ejecutar una dependencia asincrona midiendo su duración y registrandola en Application Insights.
        /// </summary>
        /// <typeparam name="R">Tipo del resultado.</typeparam>
        /// <param name="cliente">Cliente de telemetría.</param>
        /// <param name="nombreDependencia">Nombre de la dependencia.</param>
        /// <param name="nombreComando">Nombre del comando ejecutado.</param>
        /// <param name="accion">Acción de la dependencia.</param>
        /// <param name="nombreTipoDependencia">Nombre del tipo de dependencia.</param>
        /// <param name="codigoResultado">Resultado del comando.</param>
        /// <param name="propiedades">Lista de propiedades definidas por la aplicación</param>
        /// <returns>Resultado de la dependencia.</returns>
        public static async Task MedirDependenciaAsync(this TelemetryClient cliente, string nombreDependencia, string nombreComando, Func<Task> accionAsync, string nombreTipoDependencia = null, string codigoResultado = null, IDictionary<string, string> propiedades = null)
        {
            bool exito = false;
            var horaInicial = DateTime.UtcNow;
            var timer = System.Diagnostics.Stopwatch.StartNew();

            try
            {
                await accionAsync();
                exito = true;
            }
            finally
            {
                timer.Stop();

                var telemetria = GenerarTelemetriaDeDependencia(nombreDependencia, nombreComando, horaInicial, timer.Elapsed, exito, nombreTipoDependencia, codigoResultado, propiedades);

                cliente.TrackDependency(telemetria);
            }
        }

        /// <summary>
        /// Permite ejecutar una dependencia asincrona midiendo su duración y registrandola en Application Insights.
        /// </summary>
        /// <typeparam name="R">Tipo del resultado.</typeparam>
        /// <param name="cliente">Cliente de telemetría.</param>
        /// <param name="nombreDependencia">Nombre de la dependencia.</param>
        /// <param name="nombreComando">Nombre del comando ejecutado.</param>
        /// <param name="accion">Acción de la dependencia.</param>
        /// <param name="nombreTipoDependencia">Nombre del tipo de dependencia.</param>
        /// <param name="codigoResultado">Resultado del comando.</param>
        /// <param name="propiedades">Lista de propiedades definidas por la aplicación</param>
        /// <returns>Resultado de la dependencia.</returns>
        public static async Task<R> MedirDependenciaAsync<R>(this TelemetryClient cliente, string nombreDependencia, string nombreComando, Func<Task<R>> accion, string nombreTipoDependencia = null, string codigoResultado = null, IDictionary<string, string> propiedades = null)
        {
            R resultado;
            bool exito = false;
            var horaInicial = DateTime.UtcNow;
            var timer = System.Diagnostics.Stopwatch.StartNew();

            try
            {
                resultado = await accion();
                exito = true;

                return resultado;
            }
            finally
            {
                timer.Stop();

                var telemetria = GenerarTelemetriaDeDependencia(nombreDependencia, nombreComando, horaInicial, timer.Elapsed, exito, nombreTipoDependencia, codigoResultado, propiedades);

                cliente.TrackDependency(telemetria);
            }
        }

        /// <summary>
        /// Genera la información de telemetría de una dependencia.
        /// </summary>
        /// <param name="nombreDependencia">Nombre de la dependencia.</param>
        /// <param name="nombreComando">Nombre del comando ejecutado.</param>
        /// <param name="horaInicio">Hora de inicio del comando.</param>
        /// <param name="duracion">Duración del comando.</param>
        /// <param name="exito">Tuvo exito el comando.</param>
        /// <param name="nombreTipoDependencia">Nombre del tipo de dependencia.</param>
        /// <param name="codigoResultado">Resultado del comando.</param>
        /// <param name="propiedades">Lista de propiedades definidas por la aplicación</param>
        /// <returns>Información de telemetría.</returns>
        private static DependencyTelemetry GenerarTelemetriaDeDependencia(string nombreDependencia, string nombreComando, DateTime horaInicio, TimeSpan duracion, bool exito, string nombreTipoDependencia = null, string codigoResultado = null, IDictionary<string, string> propiedades = null)
        {
            var telemetria = new DependencyTelemetry(nombreDependencia, nombreComando, horaInicio, duracion, exito)
            {
                DependencyTypeName = nombreTipoDependencia,
                ResultCode = codigoResultado
            };

            if (propiedades != null)
            {
                foreach (var propiedad in propiedades)
                {
                    telemetria.Properties.Add(propiedad);
                }
            }

            return telemetria;
        }
    }
}
