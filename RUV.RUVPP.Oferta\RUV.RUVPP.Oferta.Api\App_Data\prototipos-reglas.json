﻿[
  {
    "idElemento": "nombre",
    "validaciones": [
      {
        "nombre": "Requerido",
        "orden": 1,
        "funcion": "(function () { return (modelo.nombre != null && modelo.nombre != undefined && modelo.nombre != ''); })()",
        "mensajeError": "El nombre del prototipo es requerido."
      }
    ]
  },
  {
    "idElemento": "numeroNivelesVivienda",
    "validaciones": [
      {
        "nombre": "numero niveles vivienda Requerido",
        "orden": 1,
        "funcion": "(function () { return (modelo.numeroNivelesVivienda > 0); })()",
        "mensajeError": "El nivel de la vivienda es requerido."
      }
    ]
  },
  {
    "idElemento": "numeroRecamaras",
    "validaciones": [
      {
        "nombre": "recamaras Requerido",
        "orden": 1,
        "funcion": "(function () { return (modelo.numeroRecamaras > 0); })()",
        "mensajeError": "Las recámaras son requeridas."
      }
    ],
    "reglasDependientes": [
      {
        "idElemento": "ancho"
      },
      {
        "idElemento": "largo"
      },
      {
        "idElemento": "areaAdicional"
      },
      {
        "idElemento": "totalSuperficie"
      },
      {
        "idElemento": "superficieIndivisosaCubierto"
      },
      {
        "idElemento": "superficieVolados"
      },
      {
        "idElemento": "areaMuros"
      }
    ]
  },
  {
    "idElemento": "idTipologiaVivienda",
    "validaciones": [
      {
        "nombre": "tipologia Requerido",
        "orden": 1,
        "funcion": "(function () { return (modelo.idTipologiaVivienda > 0); })()",
        "mensajeError": "El tipo de tipología es requerido."
      }
    ]
  },
  {
    "idElemento": "areaLote",
    "validaciones": [
      {
        "nombre": "area lote Requerido",
        "orden": 1,
        "funcion": "(function () { return (modelo.areaLote > 0); })()",
        "mensajeError": "El área del lote es requerido."
      },
      {
        "nombre": "area lote formato",
        "orden": 2,
        "funcion": "(function () { return (validaciones.esFormatoMedidasValido(modelo.areaLote, 4, 2)); })()",
        "mensajeError": "El área del lote no tiene el formato correcto, debe ser hasta 4 enteros y sólo 2 decimales"
      }
    ]
  },
  {
    "idElemento": "medidasFrente",
    "validaciones": [
      {
        "nombre": "medidas frente Requerido",
        "orden": 1,
        "funcion": "(function () { return (modelo.metrosFrenteLote > 0); })()",
        "mensajeError": "Los metros frente de lote son requeridos."
      },
      {
        "nombre": "medidas frente formato",
        "orden": 2,
        "funcion": "(function () { return (validaciones.esFormatoMedidasValido(modelo.metrosFrenteLote, 4, 2)); })()",
        "mensajeError": "Los metros frente de lote no tienen el formato correcto, debe ser hasta 4 enteros y sólo 2 decimales"
      }
    ]
  },
  {
    "idElemento": "numeroNivelesPrototipo",
    "validaciones": [
      {
        "nombre": "numero niveles prototipo Requerido",
        "orden": 1,
        "funcion": "(function () { return (modelo.numeroNivelesPrototipo > 0); })()",
        "mensajeError": "El número de los niveles del prototipo es requerido."
      },
      {
        "nombre": "numero niveles prototipo no coincide",
        "orden": 2,
        "funcion": "(function () {  var valorRequerido = 0; var mayorA = false; switch (modelo.idTipologiaVivienda) {case 1: valorRequerido = 1; break; case 2: valorRequerido = 2; break; case 3: valorRequerido = 1; break; case 4: valorRequerido = 3; mayorA = true; break; case 5: valorRequerido = 3; break;  case 6: valorRequerido = 1; break; case 7: valorRequerido = 1; break; case 8: valorRequerido = 2; break; } return (modelo.idTipologiaVivienda == 0 || (modelo.numeroNivelesPrototipo == valorRequerido && !mayorA) || (modelo.numeroNivelesPrototipo > valorRequerido && mayorA)); })()",
        "mensajeError": "El número de los niveles del prototipo no coincide con la tipología"
      }
    ],
    "reglasDependientes": [
      {
        "idElemento": "alturaLotes"
      },
      {
        "idElemento": "huellas"
      },
      {
        "idElemento": "peraltes"
      },
      {
        "idElemento": "anchoRampa"
      },
      {
        "idElemento": "escaleras"
      },
      {
        "idElemento": "ancho"
      },
      {
        "idElemento": "largo"
      },
      {
        "idElemento": "areaAdicional"
      },
      {
        "idElemento": "cargaPlano"
      }
    ]
  },
  {
    "idElemento": "numeroAlcobas",
    "validaciones": [
      {
        "nombre": "numero alcobas Requerido",
        "orden": 1,
        "funcion": "(function () { return (modelo.numeroAlcobas > 0); })()",
        "mensajeError": "El número de alcobas es requerido."
      }
    ],
    "reglasDependientes": [
      {
        "idElemento": "ancho"
      },
      {
        "idElemento": "largo"
      },
      {
        "idElemento": "areaAdicional"
      },
      {
        "idElemento": "totalSuperficie"
      },
      {
        "idElemento": "superficieIndivisosaCubierto"
      },
      {
        "idElemento": "superficieVolados"
      },
      {
        "idElemento": "areaMuros"
      }
    ]
  },
  {
    "idElemento": "numeroBaniosCompletos",
    "validaciones": [
      {
        "nombre": "numero banios Requerido",
        "orden": 1,
        "funcion": "(function () { return (modelo.numeroBaniosCompletos > 0 || modelo.numeroBaniosMedios > 0) })()",
        "mensajeError": "El número de baños completos es requerido."
      }
    ],
    "reglasDependientes": [
      {
        "idElemento": "numeroBaniosMedios"
      },
      {
        "idElemento": "ancho"
      },
      {
        "idElemento": "largo"
      },
      {
        "idElemento": "areaAdicional"
      },
      {
        "idElemento": "totalSuperficie"
      },
      {
        "idElemento": "superficieIndivisosaCubierto"
      },
      {
        "idElemento": "superficieVolados"
      },
      {
        "idElemento": "areaMuros"
      }
    ]
  },
  {
    "idElemento": "numeroBaniosMedios",
    "validaciones": [
      {
        "nombre": "numero banios medios Requerido",
        "orden": 1,
        "funcion": "(function () { return (modelo.numeroBaniosCompletos > 0 || modelo.numeroBaniosMedios > 0) })()",
        "mensajeError": "El número de baños medios es requerido."
      }
    ],
    "reglasDependientes": [
      {
        "idElemento": "numeroBaniosCompletos"
      },
      {
        "idElemento": "ancho"
      },
      {
        "idElemento": "largo"
      },
      {
        "idElemento": "areaAdicional"
      },
      {
        "idElemento": "totalSuperficie"
      },
      {
        "idElemento": "superficieIndivisosaCubierto"
      },
      {
        "idElemento": "superficieVolados"
      },
      {
        "idElemento": "areaMuros"
      }
    ]
  },
  {
    "idElemento": "precioVivienda",
    "validaciones": [
      {
        "nombre": "precio Requerido",
        "orden": 1,
        "funcion": "(function () { return (modelo.precioVivienda > 0); })()",
        "mensajeError": "El precio de la vivienda es requerido."
      },
      {
        "nombre": "precio formato",
        "orden": 2,
        "funcion": "(function () { return (validaciones.esFormatoMedidasValido(modelo.precioVivienda, 8, 2)) })()",
        "mensajeError": "El precio no tiene el formato correcto, debe ser hasta 8 enteros y sólo 2 decimales"
      }
    ]
  },
  {
    "idElemento": "idClasificacionVivienda",
    "validaciones": [
      {
        "nombre": "tipologia Requerido",
        "orden": 1,
        "funcion": "(function () { return (modelo.idClasificacionVivienda > 0); })()",
        "mensajeError": "La clasificación de la Vivienda es requerido."
      }
    ]
  },
  {
    "idElemento": "alturaLotes",
    "validaciones": [
      {
        "nombre": "Requerido",
        "orden": 1,
        "funcion": "(function () { return (modelo.alturaLotes == 0 || modelo.alturaLotes == undefined || modelo.alturaLotes == null || (modelo.alturaLotes > 0 && validaciones.esFormatoMedidasValido(modelo.alturaLotes, 2, 2))) })()",
        "mensajeError": "La altura del lote no tiene el formato correcto, debe ser hasta 2 enteros y sólo 2 decimales"
      }
    ],
    "reglasDependientes": [
      {
        "idElemento": "huellas"
      },
      {
        "idElemento": "peraltes"
      },
      {
        "idElemento": "anchoRampa"
      },
      {
        "idElemento": "escaleras"
      },
      {
        "idElemento": "cargaPlano"
      }
    ]
  },
  {
    "idElemento": "huellas",
    "validaciones": [
      {
        "nombre": "Requerido",
        "orden": 1,
        "funcion": "(function () { return (modelo.numeroNivelesPrototipo == 1 || (modelo.numeroNivelesPrototipo > 1 && modelo.huellas > 0) || (modelo.numeroNivelesPrototipo == 0 || modelo.numeroNivelesPrototipo == undefined || modelo.numeroNivelesPrototipo == null)) })()",
        "mensajeError": "Las huellas son requeridas."
      },
      {
        "nombre": "huellas formato",
        "orden": 2,
        "funcion": "(function () { if (modelo.huellas > 0) { return validaciones.esFormatoMedidasValido(modelo.huellas, 2, 2) } else if ((modelo.numeroNivelesPrototipo == 1 || modelo.numeroNivelesPrototipo == 0 || modelo.numeroNivelesPrototipo == undefined || modelo.numeroNivelesPrototipo == null) && (modelo.huellas == 0 || modelo.huellas == '' || modelo.huellas == undefined)) { return true; }})()",
        "mensajeError": "Las huellas no tienen el formato correcto, debe ser hasta 2 enteros y sólo 2 decimales"
      }
    ],
    "reglasDependientes": [
      {
        "idElemento": "alturalotes"
      },
      {
        "idElemento": "cargaPlano"
      }
    ]
  },
  {
    "idElemento": "peraltes",
    "validaciones": [
      {
        "nombre": "Requerido",
        "orden": 1,
        "funcion": "(function () { return (modelo.numeroNivelesPrototipo == 1 || (modelo.numeroNivelesPrototipo > 1 && modelo.peraltes > 0) || (modelo.numeroNivelesPrototipo == 0 || modelo.numeroNivelesPrototipo == undefined || modelo.numeroNivelesPrototipo == null)) })()",
        "mensajeError": "Los peraltes son requeridos."
      },
      {
        "nombre": "peralte formato",
        "orden": 2,
        "funcion": "(function () { if (modelo.peraltes > 0) { return validaciones.esFormatoMedidasValido(modelo.peraltes, 2, 2) } else if ((modelo.numeroNivelesPrototipo == 1 || modelo.numeroNivelesPrototipo == 0 || modelo.numeroNivelesPrototipo == undefined || modelo.numeroNivelesPrototipo == null) && (modelo.peraltes == 0 || modelo.peraltes == '' || modelo.peraltes == undefined)) { return true; }})()",
        "mensajeError": "Los peraltes no tienen el formato correcto, debe ser hasta 2 enteros y sólo 2 decimales"
      }
    ],
    "reglasDependientes": [
      {
        "idElemento": "alturalotes"
      },
      {
        "idElemento": "cargaPlano"
      }
    ]
  },
  {
    "idElemento": "anchoRampa",
    "validaciones": [
      {
        "nombre": "Requerido",
        "orden": 1,
        "funcion": "(function () { return (modelo.numeroNivelesPrototipo == 1 || (modelo.numeroNivelesPrototipo > 1 && modelo.anchoRampa > 0) || (modelo.numeroNivelesPrototipo == 0 || modelo.numeroNivelesPrototipo == undefined || modelo.numeroNivelesPrototipo == null)) })()",
        "mensajeError": "El ancho de la rampa es requerido."
      },
      {
        "nombre": "ancho rampa formato",
        "orden": 2,
        "funcion": "(function () { if (modelo.anchoRampa > 0) { return validaciones.esFormatoMedidasValido(modelo.anchoRampa, 2, 2) } else if ((modelo.numeroNivelesPrototipo == 1 || modelo.numeroNivelesPrototipo == 0 || modelo.numeroNivelesPrototipo == undefined || modelo.numeroNivelesPrototipo == null) && (modelo.anchoRampa == 0 || modelo.anchoRampa == '' || modelo.anchoRampa == undefined)) { return true; }})()",
        "mensajeError": "El ancho de la rampa no tiene el formato correcto, debe ser hasta 2 enteros y sólo 2 decimales"
      }
    ],
    "reglasDependientes": [
      {
        "idElemento": "alturalotes"
      },
      {
        "idElemento": "cargaPlano"
      }
    ]
  },
  {
    "idElemento": "escaleras",
    "validaciones": [
      {
        "nombre": "Requerido",
        "orden": 1,
        "funcion": "(function () { return (modelo.numeroNivelesPrototipo == 1 || (modelo.numeroNivelesPrototipo > 1 && modelo.longitudEscaleras > 0) || (modelo.numeroNivelesPrototipo == 0 || modelo.numeroNivelesPrototipo == undefined || modelo.numeroNivelesPrototipo == null))})()",
        "mensajeError": "Las escaleras son requeridas."
      }
    ],
    "reglasDependientes": [
      {
        "idElemento": "alturalotes"
      },
      {
        "idElemento": "cargaPlano"
      }
    ]
  },
  {
    "idElemento": "cargaPlano",
    "validaciones": [
      {
        "nombre": "Requerido",
        "orden": 1,
        "funcion": "(function () { if(modelo.cargaPlano) { return modelo.cargaPlano.idDocumento; } else { return false; } })()",
        "mensajeError": "Debes cargar el plano"
      }
    ],
    "reglasDependientes": [
      {
        "idElemento": "huellas"
      },
      {
        "idElemento": "peraltes"
      },
      {
        "idElemento": "anchoRampa"
      },
      {
        "idElemento": "escaleras"
      },
      {
        "idElemento": "alturalotes"
      }
    ]
  },
  {
    "idElemento": "ancho",
    "funcionLongitudIndice": "(function() { return modelo.distribucionAreaVivienda.length; })()",
    "validaciones": [
      {
        "nombre": "Escaleras Mayor a 0",
        "orden": 1,
        "funcion": "(function () { var distribucion = modelo.distribucionAreaVivienda[indice]; if (distribucion.capturaManual || distribucion.idTipoAreaVivienda == 8 || distribucion.idTipoAreaVivienda == 17 || distribucion.idTipoAreaVivienda == 9 || distribucion.idTipoAreaVivienda == 18 || ((distribucion.idTipoAreaVivienda == 10 || distribucion.idTipoAreaVivienda == 19) && (modelo.numeroNivelesPrototipo == 1 || modelo.numeroNivelesPrototipo == undefined || (modelo.numeroNivelesPrototipo > 1 && distribucion.ancho > 0)))) { return true; } else { return distribucion.ancho != undefined && validaciones.esNumerico(distribucion.ancho) && distribucion.ancho > 0; }})()",
        "mensajeError": "El ancho debe ser mayor a cero."
      },
      {
        "nombre": "ancho no coincide formato",
        "orden": 2,
        "funcion": "(function () { var distribucion = modelo.distribucionAreaVivienda[indice]; if (distribucion.idTipoAreaVivienda == 8 || distribucion.idTipoAreaVivienda == 17 || distribucion.idTipoAreaVivienda == 9 || distribucion.idTipoAreaVivienda == 18 || ((distribucion.idTipoAreaVivienda == 10 || distribucion.idTipoAreaVivienda == 19) && (modelo.numeroNivelesPrototipo == 1 || modelo.numeroNivelesPrototipo == undefined || (modelo.numeroNivelesPrototipo > 1 && distribucion.ancho > 0)))) { return true } else { return validaciones.esFormatoMedidasValido(distribucion.ancho, 2, 2); } })()",
        "mensajeError": "El ancho no tiene el formato correcto, debe ser hasta 2 enteros y sólo 2 decimales"
      }
    ],
    "reglasDependientes": [
      {
        "idElemento": "areaAdicional"
      }     
    ]
  },
  {
    "idElemento": "largo",
    "funcionLongitudIndice": "(function() { return modelo.distribucionAreaVivienda.length; })()",
    "validaciones": [
      {
        "nombre": "Escaleras Mayor a 0",
        "orden": 1,
        "funcion": "(function () { var distribucion = modelo.distribucionAreaVivienda[indice]; if (distribucion.capturaManual || distribucion.idTipoAreaVivienda == 8 || distribucion.idTipoAreaVivienda == 17 || distribucion.idTipoAreaVivienda == 9 || distribucion.idTipoAreaVivienda == 18 || ((distribucion.idTipoAreaVivienda == 10 || distribucion.idTipoAreaVivienda == 19) && (modelo.numeroNivelesPrototipo == 1 || modelo.numeroNivelesPrototipo == undefined || (modelo.numeroNivelesPrototipo > 1 && distribucion.largo > 0)))) { return true; } else { return distribucion.largo != undefined && validaciones.esNumerico(distribucion.largo) && distribucion.largo > 0; }})()",
        "mensajeError": "El largo debe ser mayor a cero."
      },
      {
        "nombre": "largo no coincide formato",
        "orden": 2,
        "funcion": "(function () {var distribucion = modelo.distribucionAreaVivienda[indice];  if (distribucion.idTipoAreaVivienda == 8 || distribucion.idTipoAreaVivienda == 17 || distribucion.idTipoAreaVivienda == 9 || distribucion.idTipoAreaVivienda == 18 || ((distribucion.idTipoAreaVivienda == 10 || distribucion.idTipoAreaVivienda == 19) && (modelo.numeroNivelesPrototipo == 1 || modelo.numeroNivelesPrototipo == undefined || (modelo.numeroNivelesPrototipo > 1 && distribucion.largo > 0)))) { return true } else { return validaciones.esFormatoMedidasValido(distribucion.largo, 2, 2); } })()",
        "mensajeError": "El largo no tiene el formato correcto, debe ser hasta 2 enteros y sólo 2 decimales"
      }
    ],
    "reglasDependientes": [
      {
        "idElemento": "areaAdicional"
      }
    ]
  },
  {
    "idElemento": "areaAdicional",
    "funcionLongitudIndice": "(function() { return modelo.distribucionAreaVivienda.length; })()",
    "validaciones": [
      {
        "nombre": "Area adicional no coincide formato",
        "orden": 1,
        "funcion": "(function () {var distribucion = modelo.distribucionAreaVivienda[indice];  return (distribucion.areaAdicional == '' || distribucion.areaAdicional == 0 || validaciones.esFormatoMedidasValido(distribucion.areaAdicional, 2, 2)); })()",
        "mensajeError": "El área adicional no tiene el formato correcto, debe ser hasta 2 enteros y sólo 2 decimales"
      }
    ],
    "reglasDependientes": [
      {
        "idElemento": "ancho"
      },
      {
        "idElemento": "largo"
      }
    ]
  },
  {
    "idElemento": "totalSuperficie",
    "funcionLongitudIndice": "(function() { return modelo.distribucionAreaVivienda.length; })()",
    "validaciones": [
      {
        "nombre": "Escaleras Mayor a 0",
        "orden": 1,
        "funcion": "(function () { var distribucion = modelo.distribucionAreaVivienda[indice]; if (!distribucion.capturaManual || distribucion.idTipoAreaVivienda == 8 || distribucion.idTipoAreaVivienda == 17 || distribucion.idTipoAreaVivienda == 9 || distribucion.idTipoAreaVivienda == 10 || distribucion.idTipoAreaVivienda == 18 || distribucion.idTipoAreaVivienda == 19) { return true; } else { return distribucion.totalSuperficie != undefined && validaciones.esNumerico(distribucion.totalSuperficie)  && validaciones.esFormatoMedidasValido(distribucion.totalSuperficie, 4, 2) && distribucion.totalSuperficie > 0; }})()",
        "mensajeError": "El total de superficie debe ser mayor a cero."
      }
    ]
  },
  {
    "idElemento": "areaMuros",
    "validaciones": [
      {
        "nombre": "Requerido",
        "orden": 1,
        "funcion": "(function () { return (modelo.areaMuros != null && modelo.areaMuros != undefined) })()",
        "mensajeError": "El área de muros es requerido."
      },
      {
        "nombre": "area de muros Mayor a 0",
        "orden": 2,
        "funcion": "(function () { return modelo.areaMuros > 0; })()",
        "mensajeError": "El área de muros debe ser mayor a cero."
      },
      {
        "nombre": "area de muros formato invalido",
        "orden": 3,
        "funcion": "(function () { return validaciones.esFormatoMedidasValido(modelo.areaMuros, 2, 2); })()",
        "mensajeError": "El área de muros no tiene el formato correcto, debe ser hasta 2 enteros y sólo 2 decimales"
      }
    ],
    "reglasDependientes": [      
      {
        "idElemento": "ancho"
      },
      {
        "idElemento": "largo"
      },
      {
        "idElemento": "areaAdicional"
      },
      {
        "idElemento": "totalSuperficie"
      }
    ]
  },
  {
    "idElemento": "superficieVolados",
    "validaciones": [
      {
        "nombre": "Requerido",
        "orden": 1,
        "funcion": "(function () { return (modelo.superficieVolados != null && modelo.superficieVolados != undefined) })()",
        "mensajeError": "La superficie de volados es requerida."
      },
      {
        "nombre": "superficie volados Mayor a 0",
        "orden": 2,
        "funcion": "(function () { return modelo.superficieVolados > 0; })()",
        "mensajeError": "La superficie de volados debe ser mayor a cero."
      },
      {
        "nombre": "superficie volados formato invalido",
        "orden": 3,
        "funcion": "(function () { return validaciones.esFormatoMedidasValido(modelo.superficieVolados, 2, 2); })()",
        "mensajeError": "Superficie de volados no tiene el formato correcto, debe ser hasta 2 enteros y sólo 2 decimales"
      }
    ],
    "reglasDependientes": [
      {
        "idElemento": "ancho"
      },
      {
        "idElemento": "largo"
      },
      {
        "idElemento": "areaAdicional"
      },
      {
        "idElemento": "totalSuperficie"
      }
    ]
  },
  {
    "idElemento": "superficieIndivisosaCubierto",
    "validaciones": [
      {
        "nombre": "Requerido",
        "orden": 1,
        "funcion": "(function () { return (modelo.superficieIndivisosaCubierto != null && modelo.superficieIndivisosaCubierto != undefined) })()",
        "mensajeError": "La superficie de indivisos a cubierto es requerida."
      },
      {
        "nombre": "Superficie Indivisos a Cubierto Mayor a 0",
        "orden": 2,
        "funcion": "(function () { return modelo.superficieIndivisosaCubierto > 0; })()",
        "mensajeError": "La superficie de indivisos a cubierto debe ser mayor a cero."
      },
      {
        "nombre": "Superficie Indivisos formato invalido",
        "orden": 3,
        "funcion": "(function () { return validaciones.esFormatoMedidasValido(modelo.superficieIndivisosaCubierto, 2, 2); })()",
        "mensajeError": "La superficie de indivisos a cubierto no tiene el formato correcto, debe ser hasta 2 enteros y sólo 2 decimales"
      }
    ],
    "reglasDependientes": [
      {
        "idElemento": "ancho"
      },
      {
        "idElemento": "largo"
      },
      {
        "idElemento": "areaAdicional"
      },
      {
        "idElemento": "totalSuperficie"
      }
    ]
  }

]