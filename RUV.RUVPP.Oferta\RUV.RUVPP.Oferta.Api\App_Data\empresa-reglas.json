﻿[
  {
    "idControl": "rfc",
    "validaciones": [
      {
        "nombre": "RFC Requerido",
        "orden": 1,
        "detieneEjecucion": true,
        "funcion": "(function () { return modelo.rfc; })()",
        "mensajeError": "El RFC es requerido."
      },
      {
        "nombre": "RFC Valido",
        "orden": 2,
        "funcion": "(function () { return validaciones.esRfcValido(modelo.rfc); })()",
        "mensajeError": "El formato del RFC no es el correcto."
      },
      {
        "nombre": "Longitud maxima",
        "orden": 3,
        "funcion": "(function () { return modelo.rfc.length <= 13; })()",
        "mensajeError": "El RFC no debe de ser mayor a 13 caracters."
      }
    ]
  },
  {
    "idControl": "rsocial",
    "validaciones": [
      {
        "nombre": "Razon Social Requerida",
        "orden": 1,
        "detieneEjecucion": true,
        "funcion": "(function () { return modelo.razonSocial; })()",
        "mensajeError": "La razon social el requerida."
      },
      {
        "nombre": "Longitud válida",
        "orden": 2,
        "funcion": "(function () { return modelo.razonSocial.length >= 5 && modelo.razonSocial.length <= 50; })()",
        "mensajeError": "La razon social debe tener entre 5 y 50 caracteres."
      }
    ]
  },
  {
    "idControl": "numemp",
    "validaciones": [
      {
        "nombre": "Es número válido",
        "orden": 1,
        "detieneEjecucion": true,
        "funcion": "(function () { return validaciones.esNumerico(modelo.numeroEmpleados); })()",
        "mensajeError": "El número de empleados debe ser numerico."
      },
      {
        "nombre": "Mayor a 0",
        "orden": 2,
        "detieneEjecucion": true,
        "funcion": "(function () { return modelo.numeroEmpleados > 0; })()",
        "mensajeError": "El número de empleados debe de ser mayor a 0."
      }
    ]
  },
  {
    "idControl": "nrp",
    "validaciones": [
      {
        "nombre": "Requerido",
        "orden": 1,
        "detieneEjecucion": true,
        "funcion": "(function () { if(modelo.tieneNrp) { return modelo.nrp; } else { return true; } })()",
        "mensajeError": "El Número de registro patronal es requerido"
      }
    ]
  }
]