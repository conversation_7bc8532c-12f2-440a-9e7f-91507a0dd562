﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.Proyectos.Api
{
    public class MensajeDictaminacionProyecto : MensajeEnvioProyecto
    {

        /// <summary>
        /// Id de la orden de trabajo a dictaminar.
        /// </summary>
        public int IdOrdenTrabajo { get; set; }

        /// <summary>
        /// Id del servicio a dictaminar
        /// </summary>
        public short IdServicio { get; set; }

        /// <summary>
        /// Indica si la dictaminacion es aceptacion o rechazo
        /// </summary>
        public bool Aceptacion { get; set; }

        /// <summary>
        /// Indica si es una nueva dictaminacion o una actualizacion.
        /// </summary>
        public bool SeActualizaDictaminacion { get; set; }

        /// <summary>
        /// Id del proyecto a dictaminar
        /// </summary>
        public int IdRegistro { get; set; }
    }
}
