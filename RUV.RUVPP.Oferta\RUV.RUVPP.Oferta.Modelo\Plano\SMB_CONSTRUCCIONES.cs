﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.Plano
{
    public class SMB_CONSTRUCCIONES
    {
        public int featID { get; set; }
        public int featID_anterior { get; set; }
        public string numeroManzana { get; set; }
        public string numeroLote { get; set; }
        public string numeroInterior { get; set; }
        public string numeroExterior { get; set; }
        public string cuv { get; set; }
        public string idOferta { get; set; }
        public string idVivienda { get; set; }
        public string idTipologia { get; set; }
        public int nivel { get; set; }
        public int idOV { get; set; }
        public int idPrototipo { get; set; }
        public string nombreAsentamiento { get; set; }
        public string localidad { get; set; }
        public int idTipoZona { get; set; }
        public int idTipoVialidad { get; set; }
        public string nombreVialidad { get; set; }
        public string supermanzana { get; set; }
        public string manzana { get; set; }
        public string lote { get; set; }
        public string numeroExteriorNumerico { get; set; }
        public string numeroExteriorAlfanumerico { get; set; }
        public string numeroInteriorNumerico { get; set; }
        public string numeroInteriorAlfanumerico { get; set; }
        public string numeroCatastralLote { get; set; }
        public string edificio { get; set; }
        public string descripcionNivel { get; set; }
        public string estacionamiento { get; set; }
        public int idTipoVlddPmerEntrvldd { get; set; }
        public string nombreTipoVlddPmerEntrvldd { get; set; }
        public int idTipoVlddSgdaEntrvldd { get; set; }
        public string nombreTipoVlddSgdaEntrvldd { get; set; }
        public int idTipVlddEntrvlddPost { get; set; }
        public string nombreTipVlddEntrvlddPost { get; set; }
        public string costo { get; set; }
        public string metrosCuadradosLote { get; set; }
        public string metrosFrente { get; set; }
        public int idOrientacion { get; set; }
        public string origen { get; set; }
        public string destino { get; set; }
        public string margen { get; set; }
        public string cadenamiento { get; set; }
        public string idAdministracion { get; set; }
        public string idDerechoTransito { get; set; }
        public string codigoCarretera { get; set; }
        public string codigoPostal { get; set; }
        public string nombreCondominio { get; set; }
        public string idTipoCamino { get; set; }
        public int idEstatusVivienda { get; set; }
        public int idTipoAsentamiento { get; set; }
        public string folioSeplade { get; set; }
        public string folioAyuntamiento { get; set; }
        public string numeroExteriorAnterior { get; set; }
        public string entreCalles { get; set; }                
        public string claveEstado { get; set; }
        public string claveMunicipio { get; set; }
        public int numTipologia { get; set; }
        public string idAsentamiento { get; set; }
        public int idVialidad { get; set; }
        public int idVialidadPmer { get; set; }
        public int idVialidadSgda { get; set; }
        public int idVialidadPost { get; set; }
        public string cuv_asis { get; set; }
        public string cuv_geografica { get; set; }
        public string nombreVialidadP { get; set; }
        public string nombreVialidad1 { get; set; }
        public string nombreVialidad2 { get; set; }
        public string nombreVialidad3 { get; set; }
    }
}
