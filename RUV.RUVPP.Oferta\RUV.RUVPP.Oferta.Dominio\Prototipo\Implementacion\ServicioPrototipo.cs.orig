﻿using Microsoft.ApplicationInsights;
using Newtonsoft.Json;
using RUV.RUVPP.Negocio.General.Documentos.Abstracta;
using RUV.Comun.Negocio;
using RUV.RUVPP.Oferta.Comun.Modelo;
using RUV.RUVPP.Oferta.Datos.Prototipos;
using RUV.RUVPP.Oferta.Dominio.Dictaminacion;
using RUV.RUVPP.Oferta.GeneralesInterop.Documentos;
using RUV.RUVPP.Oferta.Modelo.Prototipos.Api;
using RUV.RUVPP.Oferta.Modelo.Proyectos.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Transactions;
using RUV.RUVPP.Negocio.General.Documentos.Abstracta;
using RUV.RUVPP.Oferta.GeneralesInterop.Documentos;
using RUV.RUVPP.Oferta.Comun.Modelo;
using RUV.RUVPP.Oferta.Dominio.Dictaminacion;
using RUV.RUVPP.Oferta.Modelo.Proyectos.Data;
using RUV.RUVPP.Oferta.Dominio.ProductoServicio;
using RUV.Comun.Utilerias;

namespace RUV.RUVPP.Oferta.Dominio.Prototipo.Implementacion
{
    /// <summary>
    ///
    /// </summary>
    public class ServicioPrototipo : ServicioDominioBase, IServicioPrototipo
    {
        #region Campos

        /// <summary>
        ///
        /// </summary>
        private readonly IPrototiposDataMapper _prototipoDM;

        /// <summary>
        ///
        /// </summary>
        private readonly IServicioDocumento<DocumentoOferta> _servicioDocumento;

        /// <summary>
        ///
        /// </summary>
        private readonly IServicioDictaminacion _servicioDictaminacion;
        private IServicioProductoServicio _servicioProductoServicio;
        #endregion

        #region Constructor

        /// <summary>
        ///
        /// </summary>
        /// <param name="clienteTelemetria"></param>
        /// <param name="prototipoDM"></param>
        /// <param name="servicioDocumento"></param>
        public ServicioPrototipo(TelemetryClient clienteTelemetria, IPrototiposDataMapper prototipoDM, IServicioDocumento<DocumentoOferta> servicioDocumento,
            IServicioDictaminacion servicioDictaminacion, IServicioProductoServicio servicioProductoServicio)
            : base(clienteTelemetria)
        {
            this._prototipoDM = prototipoDM;
            this._servicioDocumento = servicioDocumento;
            this._servicioDictaminacion = servicioDictaminacion;
            this._servicioProductoServicio = servicioProductoServicio;
        }

        #endregion Constructor

        #region Acciones

        /// <summary>
        ///
        /// </summary>
        /// <param name="nombre"></param>
        /// <param name="idEmpresa"></param>
        /// <param name="idPrototipo"></param>
        /// <returns></returns>
        public async Task<int> ExistePrototipoPorNombreAsync(string nombre, int idEmpresa, int? idPrototipo)
        {
            return await this._prototipoDM.ExistePrototipoPorNombreAsync(nombre, idEmpresa, idPrototipo);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="prototipo"></param>
        /// <param name="guardadoTemporal"></param>
        /// <returns></returns>
        public async Task<Modelo.Prototipos.Api.Prototipo> GuardarPrototipoAsync(Modelo.Prototipos.Api.Prototipo prototipo, bool guardadoTemporal)
        {
            using (var scope = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
            {
                bool tieneIdPrototipo = false;

                prototipo.activo = true;
                prototipo.fechaRegistro = DateTime.Now;

                if (prototipo.idPrototipo != null)
                    tieneIdPrototipo = true;

                //Aqui poner el identificadorPrototipo
                var prototiposEmpresa = await ObtenerPrototiposFiltradoAsync(9999, 1, prototipo.idEmpresa, null, null, null, null, null, null, null);

                prototipo.identificadorPrototipo = prototiposEmpresa.Resultado.Select(r => r.identificadorPrototipo).Max() + 1;

                if (guardadoTemporal)
                    prototipo.temporalJSON = JsonConvert.SerializeObject(prototipo);

                if (!tieneIdPrototipo)
                    prototipo.idPrototipo = await this._prototipoDM.GuardarPrototipoAsync(prototipo);                
                else
                    await this._prototipoDM.ActualizarPrototipoAsync(prototipo);
                                   
                if (!guardadoTemporal)
                {
                    await GuardarDetallePrototipoAsync(prototipo);
                }

                scope.Complete();
            }

            return new Modelo.Prototipos.Api.Prototipo { idPrototipo = prototipo.idPrototipo.Value, identificadorPrototipo = prototipo.identificadorPrototipo.Value };
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="prototipo"></param>
        /// <returns></returns>
        public async Task<Modelo.Prototipos.Api.Prototipo> GuardarDetallePrototipoAsync(Modelo.Prototipos.Api.Prototipo prototipo)
        {

            await this._prototipoDM.GuardarDetallePrototipoAsync(prototipo);

            //Se manda guardar cada distribucion
            foreach (DistribucionAreaVivienda distribucion in prototipo.distribucionAreaVivienda)
            {
                if (distribucion.tipoDimensionAreaVivienda.Sum(p => p.cantidad) > 0)
                {
                    foreach (TipoDimensionAreaVivienda tipoDimensionArea in distribucion.tipoDimensionAreaVivienda)
                    {
                        await this._prototipoDM.GuardarTipoDistribucionViviendaAsync(
                            tipoDimensionArea.idTipoDimensionAreaVivienda.Value,
                            tipoDimensionArea.cantidad,
                            prototipo.idPrototipo.Value,
                            distribucion.idTipoAreaVivienda.Value);
                    }
                }
                else
                {
                    await this._prototipoDM.GuardarTipoDistribucionViviendaAsync(
                            4,
                            distribucion.totalSuperficie,
                            prototipo.idPrototipo.Value,
                            distribucion.idTipoAreaVivienda.Value);
                }
            }

            //Se guarda el plano
            await this._prototipoDM.GuardarDocumentosPrototipoAsync(prototipo.cargaPlano, prototipo.idPrototipo.Value);

            //Se guardan las imagenes
            foreach (DocumentoRuv imagen in prototipo.imagenes)
            {
                await this._prototipoDM.GuardarDocumentosPrototipoAsync(imagen, prototipo.idPrototipo.Value);
            }
                
            return new Modelo.Prototipos.Api.Prototipo { idPrototipo = prototipo.idPrototipo.Value, identificadorPrototipo = prototipo.identificadorPrototipo.Value };
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="idPrototipo"></param>
        /// <param name="nombrePrototipo"></param>
        /// <returns></returns>
        public async Task<Modelo.Prototipos.Api.Prototipo> DuplicarPrototipoAsync(int idPrototipo, string nombrePrototipo)
        {
            Modelo.Prototipos.Api.Prototipo prototipoDuplicado = new Modelo.Prototipos.Api.Prototipo();

            using (var scope = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
            {
                //obtenemos el prototipo
                Modelo.Prototipos.Api.Prototipo prototipo = await this._prototipoDM.ObtenerPrototipoPorIdAsync(idPrototipo);

                prototipo.nombre = nombrePrototipo;
                prototipo.activo = true;
                prototipo.fechaRegistro = DateTime.Now;

                prototipo.cargaPlano.IdDocumento = await this._prototipoDM.GuardarDocumentosComunAsync(new DocumentoRuv
                {
                    IdDocumento = prototipo.cargaPlano.IdDocumento,
                    NombreArchivo = prototipo.cargaPlano.NombreArchivo,
                    UrlArchivo = prototipo.cargaPlano.UrlArchivo
                }, 200);

                foreach (DocumentoRuv archivo in prototipo.imagenes)
                {
                    archivo.IdDocumento = await this._prototipoDM.GuardarDocumentosComunAsync(new DocumentoRuv
                    {
                        IdDocumento = archivo.IdDocumento,
                        NombreArchivo = archivo.NombreArchivo,
                        UrlArchivo = archivo.UrlArchivo
                    }, 32);
                }

                prototipoDuplicado = await GuardarPrototipoAsync(prototipo, false);
<<<<<<< Updated upstream

=======
                
>>>>>>> Stashed changes
                dynamic jsonObj = await _servicioProductoServicio.ObtenerJsonEstatusAsync(37, idPrototipo, true, false);
                jsonObj["idRegistro"] = prototipoDuplicado.idPrototipo;
                string output = Newtonsoft.Json.JsonConvert.SerializeObject(jsonObj, Newtonsoft.Json.Formatting.Indented);
                await _servicioProductoServicio.GuardarJsonEstatusAsync(37, prototipoDuplicado.idPrototipo.Value, output, true, false);

                scope.Complete();
            }

            return prototipoDuplicado;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="prototipo"></param>
        /// <param name="guardadoTemporal"></param>
        /// <returns></returns>
        public async Task<int> ActualizarPrototipoAsync(Modelo.Prototipos.Api.Prototipo prototipo, bool guardadoTemporal)
        {
            using (var scope = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
            {
                prototipo.activo = true;
                prototipo.fechaActualizacion = DateTime.Now;

                if(guardadoTemporal)
                    prototipo.temporalJSON = JsonConvert.SerializeObject(prototipo);

                await this._prototipoDM.ActualizarPrototipoAsync(prototipo);

                if (!guardadoTemporal)
                {
                    await this._prototipoDM.ActualizarDetallePrototipoAsync(prototipo);

                    //Eliminamos la informacion del tipo de dimension vivienda, para poner la nueva
                    await this._prototipoDM.EliminarTipoDistribucionViviendaAsync(prototipo.idPrototipo.Value);

                    //Se manda guardar cada distribucion
                    foreach (DistribucionAreaVivienda distribucion in prototipo.distribucionAreaVivienda)
                    {
                        if (distribucion.tipoDimensionAreaVivienda.Sum(p => p.cantidad) > 0)
                        {
                            foreach (TipoDimensionAreaVivienda tipoDimensionArea in distribucion.tipoDimensionAreaVivienda)
                            {
                                await this._prototipoDM.GuardarTipoDistribucionViviendaAsync(
                                    tipoDimensionArea.idTipoDimensionAreaVivienda.Value,
                                    tipoDimensionArea.cantidad,
                                    prototipo.idPrototipo.Value,
                                    distribucion.idTipoAreaVivienda.Value);
                            }
                        }
                        else
                        {
                            await this._prototipoDM.GuardarTipoDistribucionViviendaAsync(
                                   4,
                                   distribucion.totalSuperficie,
                                   prototipo.idPrototipo.Value,
                                   distribucion.idTipoAreaVivienda.Value);
                        }
                    }

                    //Primero se revisa si el iddocumento es el mismo que en la base, si no son iguales se borra el de la base y se agrega el nuevo
                    var plano = await this._prototipoDM.ObtenerListaDocumentosRuvAsync(prototipo.idPrototipo.Value, 200);

                    var archivos = await this._prototipoDM.ObtenerListaDocumentosRuvAsync(prototipo.idPrototipo.Value, 32);

                    //Aqui eliminamos los documentos
                    await this._prototipoDM.EliminarDocumentoPrototipoAsync(prototipo.idPrototipo.Value, null);

                    //Se guarda el plano
                    await this._prototipoDM.GuardarDocumentosPrototipoAsync(prototipo.cargaPlano, prototipo.idPrototipo.Value);

                    //Se guardan las imagenes
                    foreach (DocumentoRuv imagen in prototipo.imagenes)
                    {
                        await this._prototipoDM.GuardarDocumentosPrototipoAsync(imagen, prototipo.idPrototipo.Value);
                    }
                }

                scope.Complete();
            }

            return prototipo.idPrototipo.Value;
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="idPrototipo"></param>
        /// <returns></returns>
        public async Task<Modelo.Prototipos.Api.Prototipo> ObtenerPrototipoPorIdAsync(int idPrototipo)
        {
            var prototipo = await this._prototipoDM.ObtenerPrototipoPorIdAsync(idPrototipo);

            if (!string.IsNullOrEmpty(prototipo.temporalJSON))
            {
                var id = prototipo.idPrototipo;
                prototipo = JsonConvert.DeserializeObject<Modelo.Prototipos.Api.Prototipo>(prototipo.temporalJSON);
                prototipo.idPrototipo = id;
            }

            return prototipo;
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="idPrototipo"></param>
        /// <returns></returns>
        public async Task<ResultadoPaginado<List<Vivienda>>> ObtenerViviendasPorPrototipoIdAsync(int tamanioPagina, int pagina, int idPrototipo)
        {
            ResultadoPaginado<List<Vivienda>> resultado = new ResultadoPaginado<List<Vivienda>>()
            {
                TamanioPagina = tamanioPagina,
                PaginaActual = pagina
            };

            var data = await this._prototipoDM.ObtenerViviendasPorPrototipoIdAsync(tamanioPagina, pagina, idPrototipo);

            if (data.Item1 > 0)
            {
                resultado.TotalRegistros = data.Item1;
                resultado.TotalPaginas = (int)Math.Ceiling((double)(resultado.TotalRegistros / tamanioPagina));
            }

            resultado.Resultado = data.Item2;

            return resultado;
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="idEmpresa"></param>
        /// <param name="idPrototipo"></param>
        /// <param name="precioDesde"></param>
        /// <param name="precioHasta"></param>
        /// <param name="idTipologiaVivienda"></param>
        /// <param name="nombre"></param>
        /// <param name="oferente"></param>
        /// <param name="recamaras"></param>
        /// <returns></returns>
        public async Task<ResultadoPaginado<List<Modelo.Prototipos.Api.Prototipo>>> ObtenerPrototiposFiltradoAsync(int tamanioPagina, int pagina, int? idEmpresa, string identificadorPrototipo, int? precioDesde, int? precioHasta, int? idTipologiaVivienda, string nombre, string oferente, int? recamaras)
        {
            ResultadoPaginado<List<Modelo.Prototipos.Api.Prototipo>> resultado = new ResultadoPaginado<List<Modelo.Prototipos.Api.Prototipo>>()
            {
                TamanioPagina = tamanioPagina,
                PaginaActual = pagina
            };

            bool? esPorPrecio = null;

            if (precioDesde != null && precioHasta != null)
            {
                esPorPrecio = true;
            }

            var data = await this._prototipoDM.ObtenerPrototiposFiltradoAsync(tamanioPagina, pagina, idEmpresa, identificadorPrototipo, precioDesde, precioHasta, idTipologiaVivienda, nombre, esPorPrecio, oferente, recamaras);

            if (data.Item1 > 0)
            {
                resultado.TotalRegistros = data.Item1;
                resultado.TotalPaginas = (int)Math.Ceiling((double)(resultado.TotalRegistros / tamanioPagina));
            }

            resultado.Resultado = data.Item2;

            return resultado;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="idPrototipo"></param>
        /// <param name="soloJSON"></param>
        /// <returns></returns>
        public async Task<bool> EliminarPrototipoAsync(int idPrototipo, bool soloJSON)
        {
            Modelo.Prototipos.Api.Prototipo prototipo = new Modelo.Prototipos.Api.Prototipo();
            prototipo.activo = true;
            prototipo.idPrototipo = idPrototipo;
            using (var scope = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
            {
                if (soloJSON)
                {
                    prototipo.temporalJSON = null;
                    await this._prototipoDM.ActualizarPrototipoAsync(prototipo);
                }
                else
                {
                    await this._prototipoDM.EliminarDimensionAreaAsync(prototipo);
                    await this._prototipoDM.EliminarDocumentoPrototipoAsync(prototipo);
                    await this._prototipoDM.EliminarDetallePrototipoAsync(prototipo);
                    await this._prototipoDM.EliminarPrototipoAsync(prototipo);
                }

                
                scope.Complete();
            }
            return true;
        }      

        /// <summary>
        ///
        /// </summary>
        /// <param name="idPrototipo"></param>
        /// <param name="idDocumento"></param>
        /// <returns></returns>
        public async Task<bool> EliminarDocumentoPrototipoAsync(int idPrototipo, int idDocumento)
        {
            await this._prototipoDM.EliminarDocumentoPrototipoAsync(idPrototipo, idDocumento);

            return true;
        }

        #endregion Acciones

        #region Metodos sobreescritos

        protected override void DisposeManagedResources()
        {
            this._prototipoDM.Dispose();
            this._servicioDictaminacion.Dispose();
        }

        #endregion Metodos sobreescritos
    }
}