﻿using Microsoft.ApplicationInsights;
using RUV.Comun.Seguridad.JWT;
using RUV.Comun.Utilerias;
using RUV.Comun.Web;
using RUV.Comun.Web.Http;
using RUV.RUVPP.Oferta.Dominio.Historico;
using RUV.RUVPP.Oferta.Dominio.Oferta;
using RUV.RUVPP.Oferta.Dominio.Proyectos;
using RUV.RUVPP.Oferta.Dominio.Viviendas;
using RUV.RUVPP.Oferta.Modelo.AgenteServicio;
using RUV.RUVPP.Oferta.Modelo.Historico.Data;
using RUV.RUVPP.Oferta.Modelo.Proyectos;
using RUV.RUVPP.Oferta.Modelo.Proyectos.Data;
using RUV.RUVPP.Oferta.Modelo.Viviendas;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;
using System.Web.Http.Description;
using System.Web.WebPages.Html;

namespace RUV.RUVPP.Oferta.Api.Controllers.Interno
{
    /// <summary>
    /// 
    /// </summary>
    [RoutePrefix("interno/api/viviendas")]
    public class ViviendasController : ApiControllerBase
    {
        private readonly IServicioProyectos _servicioProyectos;
        private readonly IServicioVivienda _servicioVivienda;
        private readonly IServicioHistoricos _servicioHistoricos;
        private readonly IServicioOferta _servicioOfertas;
        /// <summary>
        /// 
        /// </summary>
        /// <param name="clienteTelemetria"></param>
        /// <param name="servicioProyectos"></param>
        /// <param name="servicioVivienda"></param>
        public ViviendasController(IServicioProyectos servicioProyectos, IServicioVivienda servicioVivienda, IServicioHistoricos servicioHistoricos, IServicioOferta servicioOferta) : base()
        {
            this._servicioProyectos = servicioProyectos;
            this._servicioVivienda = servicioVivienda;
            this._servicioHistoricos = servicioHistoricos;
            this._servicioOfertas = servicioOferta;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="idProyecto"></param>
        /// <returns></returns>
        [HttpGet, Route("{idProyecto}")]
        [ResponseType(typeof(bool))]
        public async Task<HttpResponseMessage> GenerarCuvsAsync(int idProyecto)
        {
            List<Vivienda> listaVivienda = await this._servicioProyectos.ObtenerVivendasPorIdProyectoAsync(idProyecto);
            /*int x = 1;
            foreach (var vivienda_ in listaVivienda) {
                vivienda_.identificadorVivienda = x;
                x++;
            }*/
            Vivienda vivienda = listaVivienda.FirstOrDefault();
            listaVivienda = await this._servicioVivienda.GenerarCuvs(idProyecto, listaVivienda, DateTime.Now, vivienda.idEstado, vivienda.idMunicipio);

            return Request.CreateResponse(System.Net.HttpStatusCode.OK, true);
        }

        [HttpGet, Route("estatus/vivienda")]
        [ResponseType(typeof(List<EstatusVivienda>))]
        public async Task<HttpResponseMessage> ObtenerEstatusVivienda()
        {
            var lista = Enum.GetValues(typeof(EstatusVivienda)).Cast<EstatusVivienda>().Select(v => new SelectListItem
            {
                Text = v.ToString(),
                Value = ((int)v).ToString()
            }).ToList();

            return Request.CreateResponse(HttpStatusCode.OK, lista);
        }        

        /// <summary>
        /// Obtiene el historico de una cuv por idVivienda
        /// </summary>
        /// <param name="idVivienda">Identificador de la vivienda</param>
        /// <returns></returns>
        [HttpGet, Route("{idVivienda}/historicos/cuvs")]
        [ResponseType(typeof(bool))]
        public async Task<HttpResponseMessage> ObtenerHistoricoCuvAsync(int idVivienda)
        {
            List<HistoricoCuv> historicoCuvs = await this._servicioHistoricos.ObtenerHistoricoCuvPorIdViviendaAsync(idVivienda);
            return Request.CreateResponse(HttpStatusCode.OK, historicoCuvs);
        }

        /// <summary>
        /// Elimina las cuvs (viviendas) especificadas
        /// </summary>
        /// <param name="idsVivienda">Identificadores de vivienda</param>
        /// <returns></returns>
        [HttpDelete, Route("cuvs")]
        [ResponseType(typeof(bool))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> EliminarCuvsAsync([FromUri]string idsVivienda)
        {
            var usuario = (CustomUserRuv)this.User;
            int[] ids = idsVivienda.Split(',').Select(Int32.Parse).ToArray();
            var resultado = await this._servicioOfertas.EliminarCuvsPorIdsViviendaAsync(ids, usuario);
            return Request.CreateResponse(System.Net.HttpStatusCode.OK, resultado);
        }

        /// <summary>
        /// Actualiza el estatus de las CUV a Individualizada.
        /// </summary>
        /// <param name="idsVivienda">Identificadores de vivienda como 'string' separados por comas.</param>
        /// <param name="fecha">Fecha de Individualizar.</param>
        /// <param name="medioIndividualizacion">Identificador del Medio de Individualizacion.</param>
        /// <param name="tipoIndividualizacion">Identificador del Tipo de Individualizacion.</param>
        /// <returns></returns>
        [HttpPut, Route("cuvs/estatus/individualizadas")]
        [ResponseType(typeof(bool))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ActualizarCuvsAIndividualizadasAsync([FromUri]string idsVivienda, string fecha, int medioIndividualizacion, int? tipoIndividualizacion)
        {
            var usuario = (CustomUserRuv)this.User;
            int[] ids = idsVivienda.Split(',').Select(int.Parse).ToArray();
            var resultado = await this._servicioOfertas.ActualizaCuvsAIndividualizadaPorIdsViviendaAsync(ids, fecha, medioIndividualizacion, tipoIndividualizacion, usuario);
            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }

        /// <summary>
        /// Actualiza el estatus de las CUV a Disponible.
        /// </summary>
        /// <param name="listaIdVivienda">Arreglo de Identificadores de Vivienda.</param>
        /// <param name="listaIdDocumento">Arreglo de Identificadores de Documentos Asociados.</param>
        /// <returns></returns>
        [HttpGet, Route("disponibilizar")]
        [ResponseType(typeof(bool))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> DisponibilizarListaCuv(string listaIdVivienda, string listaIdDocumento)
        {
            var usuario = (CustomUserRuv)this.User;

            int[] arregloViviendas = listaIdVivienda.Split(',').Select(int.Parse).ToArray();
            int[] arregloDocumentos = listaIdDocumento.Split(',').Select(int.Parse).ToArray();

            bool resultado = await this._servicioOfertas.DisponibilizarListaCuv(arregloViviendas, arregloDocumentos, usuario);
            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }

        /// <summary>
        /// Obtiene los documentos ligados a un Evento Vivienda.
        /// </summary>
        /// <param name="idEventoVivienda">Arreglo de Identificadores de Vivienda.</param>
        /// <returns></returns>
        [HttpGet, Route("documentos/idEventoVivienda/{idEventoVivienda}")]
        [ResponseType(typeof(List<DocumentoEventoVivienda>))]
        public async Task<HttpResponseMessage> ObtenerDocumentosEventoVivienda(int idEventoVivienda)
        {
            List<DocumentoEventoVivienda> resultado = await this._servicioOfertas.ObtenerDocumentosEventoVivienda(idEventoVivienda);
            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }

        /// <summary>
        /// Obtiene los documentos ligados a los identificadores enviados.
        /// </summary>
        /// <param name="idDocumentos">Arreglo de Identificadores de Documentos.</param>
        /// <returns></returns>
        [HttpGet, Route("documentos/idDocumentos/{idDocumentos}")]
        [ResponseType(typeof(List<DocumentoEventoVivienda>))]
        public async Task<HttpResponseMessage> ObtenerDocumentosPorIdentificadores(string idDocumentos)
        {
            int[] arregloDocumentos = idDocumentos.Split(',').Select(int.Parse).ToArray();
            List<DocumentoEventoVivienda> resultado = await this._servicioOfertas.ObtenerDocumentosPorIdentificadores(arregloDocumentos);
            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }

        /// <summary>
        /// Inserta valores en la tabla de Documentos por Cambio de Estatus de Vivienda.
        /// </summary>
        /// <param name="idDocumento">Identificador del Documento.</param>
        /// <param name="idVivienda">Identificador de la Vivienda.</param>
        /// <param name="descripcionArchivo">Descripción del Documento.</param>
        /// <returns></returns>
        [HttpGet, Route("documentos/descripcion/{idDocumento}/{idVivienda}/{descripcionArchivo}")]
        [ResponseType(typeof(bool))]
        public async Task<HttpResponseMessage> InsertarValoresDocumentoPorEstatusVivienda(int idDocumento, int idVivienda, string descripcionArchivo)
        {
            bool resultado = await this._servicioOfertas.InsertarValoresDocumentoPorEstatusVivienda(idDocumento, idVivienda, descripcionArchivo);
            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }

        /// <summary>
        /// Procedimiento Almacenado para Eliminar un registro de la tabla de Descripciones de Documentos por Evento Vivienda.
        /// </summary>
        /// <param name="idDocumento">Identificador del Documento.</param>
        /// <returns></returns>
        [HttpGet, Route("documentos/eliminar/{idDocumento}")]
        [ResponseType(typeof(bool))]
        public async Task<HttpResponseMessage> EliminarRegistrosDocumentoPorEventoVivienda(int idDocumento)
        {
            bool resultado = await this._servicioOfertas.EliminarRegistrosDocumentoPorEventoVivienda(idDocumento);
            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }

        /// <summary>
        /// Obtiene los Medios de Individualización.
        /// </summary>
        /// <returns></returns>
        [HttpGet, Route("individualizacion/mediosIndividualizacion")]
        [ResponseType(typeof(List<Tuple<int,string>>))]
        public async Task<HttpResponseMessage> ObtenerMedioIndividualizacion()
        {
            List<Tuple<int, string>> resultado = await this._servicioOfertas.ObtenerMedioIndividualizacion();
            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }

        /// <summary>
        /// Obtiene los Bancos como Medio de Individualización.
        /// </summary>
        /// <returns></returns>
        [HttpGet, Route("individualizacion/bancosIndividualizacion")]
        [ResponseType(typeof(List<Tuple<int, string>>))]
        public async Task<HttpResponseMessage> ObtenerBancosIndividualizacion()
        {
            List<Tuple<int, string>> resultado = await this._servicioOfertas.ObtenerBancosIndividualizacion();
            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }

        /// <summary>
        /// Obtiene los Tipos de Individualización de acuerdo al Medio introducido.
        /// </summary>
        /// <param name="idMedioIndividualizacion">Identificador del Medio de Individualización.</param>
        /// <returns></returns>
        [HttpGet, Route("individualizacion/tiposIndividualizacion")]
        [ResponseType(typeof(List<Tuple<int, string>>))]
        public async Task<HttpResponseMessage> ObtenerTipoIndividualizacion(int idMedioIndividualizacion)
        {
            List<Tuple<int, string>> resultado = await this._servicioOfertas.ObtenerTipoIndividualizacion(idMedioIndividualizacion);
            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }

        /// <summary>
        /// Actualiza el estatus de las cuvs a individualizadas
        /// </summary>
        /// <param name="archivo">Archivo xls para obtener un listado de CUV
        /// para individualizar con sus respectivos datos.</param>
        /// <returns></returns>
        [HttpPost, Route("cuvs/estatus/individualizar/masivo")]
        [ResponseType(typeof(bool))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> IndividualizacionMasivaAsync()
        {
            var usuario = (CustomUserRuv)this.User;
            HttpResponseMessage respuesta = null;

            //Obtener CUV, Fecha y ONAVI.
            if (!Request.Content.IsMimeMultipartContent())
                respuesta = Request.CreateErrorResponse(HttpStatusCode.UnsupportedMediaType, "La solicitud no tiene contenido válido.");
            else
            {
                var multipartData = await Request.Content.ParseMultipartAsync();

                foreach (var archivoActual in multipartData.Files)
                {
                    var ms = new MemoryStream(archivoActual.Value.Data);

                    StreamReader reader = new StreamReader(ms, System.Text.Encoding.UTF8, true);

                    char[] separator = new[] { ',' };
                    string currentLine;

                    int contador = 0;

                    List<ViviendaIndividualizable> viviendaIndividualizableLista = new List<ViviendaIndividualizable>();

                    while ((currentLine = reader.ReadLine()) != null)
                    {
                        if (contador > 0)
                        {
                            var renglon = currentLine.Split(separator, StringSplitOptions.None);

                            ViviendaIndividualizable viviendaIndividualizable = new ViviendaIndividualizable(renglon[0], renglon[1], renglon[2], renglon[3]);

                            viviendaIndividualizableLista.Add(viviendaIndividualizable);
                        }

                        contador++;
                    }

                    bool resultado = await this._servicioOfertas.IndividualizarViviendasMasivo(viviendaIndividualizableLista, usuario);
                    respuesta = Request.CreateResponse(HttpStatusCode.OK, resultado);
                }
            }

            return await Task.FromResult(respuesta);                        
        }

        [HttpGet, Route("{idCuv}/conceptos/tipo/{idTipoPuntaje}")]
        [ResponseType(typeof(bool))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerConceptos(int idCuv, int idTipoPuntaje)
        {
            var resultado = await this._servicioOfertas.ObtenerConceptosViviendaAsync(idCuv, idTipoPuntaje);
            return Request.CreateResponse(System.Net.HttpStatusCode.OK, resultado);
        }

        [HttpPut, Route("{idCuv}/elementos")]
        [ResponseType(typeof(bool))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerElementos(int idCuv)
        {
            var resultado = await this._servicioOfertas.ObtenerElementosViviendaAsync(idCuv);
            return Request.CreateResponse(System.Net.HttpStatusCode.OK, resultado);
        }

        [HttpGet, Route("puntajes")]
        [ResponseType(typeof(List<Vivienda>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerPuntajes(int tamanioPagina, int pagina, int? idProyecto = null, int? idOferta = null, int? idOrdenVerificacion = null, string idCuv = null)
        {
            var resultado = await this._servicioOfertas.ObtenerPuntajesViviendasPaginadoAsync(tamanioPagina, pagina, idProyecto, idOferta, idOrdenVerificacion, idCuv);
            return Request.CreateResponse(System.Net.HttpStatusCode.OK, resultado);
        }

        /// <summary>
        /// Obtiene el catalogo de estatus de vivienda
        /// </summary>       
        [HttpGet, Route("catalogos/estatus-vivienda")]
        [ResponseType(typeof(List<EnumEstatusVivienda>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerCatalogoEstatusViviendaAsync()
        {
            var lista = Enum.GetValues(typeof(EnumEstatusVivienda)).Cast<EnumEstatusVivienda>().Select(v => new SelectListItem
            {
                Text = v.ToString(),
                Value = ((int)v).ToString()
            }).ToList();

            foreach (var lis in lista)
            {
                switch (lis.Value)
                {
                    case "2":
                        lis.Text = "Sin validar";
                        break;
                    case "4":
                        lis.Text = "En solicitud de crédito";
                        break;
                    case "6":
                        lis.Text = "En proceso de verficación";
                        break;
                }
            }

            return Request.CreateResponse(HttpStatusCode.OK, lista);
        }

        #region Disposible

        protected override void Dispose(bool disposing)
        {
            base.Dispose(disposing);

            this._servicioProyectos.Dispose();
            this._servicioVivienda.Dispose();
            this._servicioHistoricos.Dispose();
            this._servicioOfertas.Dispose();
        }

        #endregion
    }
}