//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace RUV.RUVPP.Oferta.MigracionProyectos.Modelo
{
    using System;
    using System.Collections.Generic;
    
    public partial class Proyecto
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public Proyecto()
        {
            this.DocumentoxProyecto = new HashSet<DocumentoxProyecto>();
            this.Vivienda = new HashSet<Vivienda>();
            this.ProyectoxRiesgoOferta = new HashSet<ProyectoxRiesgoOferta>();
            this.DirectorResponsableObra = new HashSet<DirectorResponsableObra>();
            this.PromotorExterno = new HashSet<PromotorExterno>();
            this.PropietarioTerreno = new HashSet<PropietarioTerreno>();
        }
    
        public int idProyecto { get; set; }
        public Nullable<byte> idEstatusProyecto { get; set; }
        public int idEmpresa { get; set; }
        public string nombre { get; set; }
        public string temporalJSON { get; set; }
        public Nullable<System.DateTime> fechaRegistro { get; set; }
        public Nullable<System.DateTime> fechaActualizacion { get; set; }
        public Nullable<bool> activo { get; set; }
    
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<DocumentoxProyecto> DocumentoxProyecto { get; set; }
        public virtual DetalleProyecto DetalleProyecto { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Vivienda> Vivienda { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<ProyectoxRiesgoOferta> ProyectoxRiesgoOferta { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<DirectorResponsableObra> DirectorResponsableObra { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<PromotorExterno> PromotorExterno { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<PropietarioTerreno> PropietarioTerreno { get; set; }
    }
}
