﻿using RUV.Comun.Datos;
using RUV.Comun.Utilerias;
using RUV.RUVPP.Datos.Comun.Contratos;
using RUV.RUVPP.Datos.General.SqlAzure.Documentos;
using RUV.RUVPP.Entidades.Empresa;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RUV.RUVPP.Entidades.General.Documentos;

namespace RUV.RUVPP.Oferta.GeneralesInterop.Documentos
{
    public class DocumentoOfertaDA : BdConexion, IPersistible<DocumentoOferta>
    {
        #region Campos

        //dac base - contiene operaciones comunes de documentos       
        private DocumentoDA _documentoDA = new DocumentoDA();

        #endregion

        #region Metodos publicos

        public DocumentoOferta ObtenerPorId(DocumentoOferta entidad)
        {
            throw new NotImplementedException();
        }

        public int Agregar(DocumentoOferta elemento)
        {
            int resultado = 0;

            elemento.fechaCarga = AzureDateTime.Now;
            elemento.fechaActualizacion = AzureDateTime.Now;
            elemento.documentoActivo = true;
            elemento.numeroVersion = 1;

            DocumentoDto documento = _documentoDA.AgregarYObtener(elemento);
            elemento.idDocumento = documento.idDocumento;

            if (documento.idDocumento != 0)
                resultado = 1;

            return resultado;
        }

        public DocumentoOferta AgregarYObtener(DocumentoOferta elemento)
        {
            elemento.fechaCarga = AzureDateTime.Now;
            elemento.fechaActualizacion = AzureDateTime.Now;
            elemento.documentoActivo = true;
            elemento.numeroVersion = 1;

            DocumentoDto documento = _documentoDA.AgregarYObtener(elemento);
            elemento.idDocumento = documento.idDocumento;

            return new DocumentoOferta(documento);
        }

        public int Actualizar(DocumentoOferta elemento)
        {
            elemento.fechaActualizacion = AzureDateTime.Now;
            elemento.documentoActivo = true;

            int idsalida = _documentoDA.Actualizar(elemento);
            int idsalida2 = Actualizar(elemento);

            return idsalida2;
        }

        public DocumentoOferta ActualizarYObtener(DocumentoOferta elemento)
        {
            elemento.fechaCarga = AzureDateTime.Now;
            elemento.fechaActualizacion = AzureDateTime.Now;
            //  solo se actualiza la tabla comun idEmpresa y idDocmuento permanecen
            _documentoDA.ActualizarYObtener(elemento);
            return elemento;
        }

        public IEnumerable<DocumentoOferta> ObtenerConReflexion(DocumentoOferta elemento)
        {
            return _documentoDA.ObtenerConReflexion(elemento)
                .Select(d => new DocumentoOferta(d));
        }

        public IEnumerable<DocumentoOferta> Obtener(DocumentoOferta elemento)
        {
            return _documentoDA.Obtener(elemento)
                .Select(d => new DocumentoOferta(d));
        }

        public int Eliminar(DocumentoOferta elemento)
        {
            // Se elimina  dato en tabla principal
            int resultado = _documentoDA.Eliminar(elemento);

            return resultado;
        }

        #endregion
    }
}
