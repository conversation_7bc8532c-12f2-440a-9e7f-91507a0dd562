﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.Proyectos.Data
{
    public class PropietarioTerreno
    {
        public int idPropietarioTerreno { get; set; }
        public string nombrePropietario { get; set; }
        public string noRPP { get; set; }
        public string noCatastral { get; set; }
        public decimal areaTerrenoEscriturado { get; set; }
        public string noEscritura { get; set; }
        public string tomo { get; set; }
        public string volumen { get; set; }
        public DateTime? fechaEscrituracion { get; set; }
        public int noNotario { get; set; }
    }
}
