﻿using Dapper;
using Microsoft.ApplicationInsights;
using RUV.Comun.Datos.Mapper;
using RUV.RUVPP.Oferta.Modelo.Mediciones;
using RUV.RUVPP.Oferta.Modelo.Proyectos.Data;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Datos.Mediciones.Implementacion
{
    /// <summary>
    /// 
    /// </summary>
    public class MedicionesDataMapper : SqlDataMapperBase, IMedicionesDataMapper
    {
        /// <summary>
        /// Constructor de la Clase.
        /// </summary>
        /// <param name="clienteTelemetria">Cliente de Telemetri para Application Insights.</param>
        /// <param name="gestorConexiones">Gestor de conexiones a utilizar.</param>
        /// <param name="nombreCadenaConexion">Cadena de Conexión a Base de Datos.</param>
        public MedicionesDataMapper(string nombreCadenaConexion, GestorConexiones gestorConexiones = null) : base(nombreCadenaConexion, gestorConexiones)
        {

        }

        
        /// <summary>
        /// Obtiene las cuvs por oferta y/o orden de verificacion desde el ASIS
        /// </summary>
        /// <param name="ClaveOferta">Identificador de oferta</param>
        /// <param name="IdOrdenVerificacion">Identificador de orden de verificacion</param>
        /// <returns></returns>
        public async Task<Dictionary<string, ConsultaCuv>> ObtenerCuvsAsync(string ClaveOferta, string IdOrdenVerificacion)
        {
            Dictionary<string, ConsultaCuv> cuvs = new Dictionary<string, ConsultaCuv>();
            IDictionary<string, object> cuv;
            var parametros = new DynamicParameters();
            parametros.Add("@idOfertaVivienda", ClaveOferta);
            parametros.Add("@idOrdenVerificacion", IdOrdenVerificacion);

            var reader = await this._conexion.QueryAsync("integracion.usp_ConsultarViviendasxOfertayOV", parametros, commandType: CommandType.StoredProcedure);
            foreach (var cuvRow in reader)
            {
                cuv = (IDictionary<string, object>)cuvRow;
                cuvs.Add(cuv["CUV"].ToString(), new ConsultaCuv() { cuv = cuv["CUV"].ToString(), ordenVerificacion = cuv["OrdenVerficacion"].ToString(), porcentajeAvanceObra = int.Parse(cuv["Porcentaje Avance"].ToString()) });
            }
            return cuvs;
        }

        /// <summary>
        /// Obtiene datos de las cuvs especificadas para la oferta y/o orden de verificacion desde el ASIS
        /// </summary>       
        /// <param name="ClaveOferta">Identificador de oferta</param>
        /// <param name="IdOrdenVerificacion">Identificador de orden de verificacion</param>
        /// <param name="filtroCuvs">Lista de cuvs a buscar</param>
        /// <returns></returns>
        public async Task<Dictionary<string, ConsultaCuv>> ObtenerDatosCuvsAsync(string ClaveOferta, string IdOrdenVerificacion, List<ConsultaCuv> filtroCuvs)
        {
            Dictionary<string, ConsultaCuv> cuvs = new Dictionary<string, ConsultaCuv>();
            IDictionary<string, object> cuv;
            var parametros = new DynamicParameters();

            System.Data.DataTable dtCuvs = new System.Data.DataTable();
            System.Data.DataRow renglon;
            dtCuvs.Columns.Add("Valor", typeof(string));
            filtroCuvs.ForEach(c =>
            {
                renglon = dtCuvs.NewRow();
                renglon["Valor"] = c.cuv;
                dtCuvs.Rows.Add(renglon);
            });

            parametros.Add("@idOfertaVivienda", ClaveOferta);
            parametros.Add("@idOrdenVerificacion", IdOrdenVerificacion);
            parametros.Add("@cuvs", dtCuvs, DbType.Object);

            var reader = await this._conexion.QueryAsync("integracion.usp_ConsultarViviendasxOfertayOVXCuvs", parametros, commandType: CommandType.StoredProcedure);

            foreach (var cuvRow in reader)
            {
                cuv = (IDictionary<string, object>)cuvRow;
                cuvs.Add(cuv["CUV"].ToString(), new ConsultaCuv() { cuv = cuv["CUV"].ToString(), ordenVerificacion = cuv["OrdenVerficacion"].ToString(), porcentajeAvanceObra = int.Parse(cuv["Porcentaje Avance"].ToString()) });
            }
            return cuvs;
        }

        /// <summary>
        /// Obtiene las cuvs por oferta y/o orden de verificacion desde el ASIS con paginación
        /// </summary>
        /// <param name="ClaveOferta">Identificador de oferta</param>
        /// <param name="IdOrdenVerificacion">Identificador de orden de verificacion</param>
        /// <param name="pagina">pagina consultada</param>
        /// <param name="tamanioPagina">tamaño de pagina</param>
        /// <returns></returns>
        public async Task<Tuple<int, Dictionary<string, ConsultaCuv>>> ObtenerCuvsPaginadoAsync(int tamanioPagina, int pagina, string ClaveOferta, string IdOrdenVerificacion)
        {
            Dictionary<string, ConsultaCuv> cuvs = new Dictionary<string, ConsultaCuv>();
            IDictionary<string, object> cuv;
            var parametros = new DynamicParameters();
            parametros.Add("@idOfertaVivienda", ClaveOferta);
            parametros.Add("@idOrdenVerificacion", IdOrdenVerificacion);
            parametros.Add("@tamanioPagina", tamanioPagina);
            parametros.Add("@pagina", pagina);

            var reader = await this._conexion.QueryMultipleAsync("integracion.usp_ConsultarViviendasxOfertayOVConPaginado", parametros, commandType: CommandType.StoredProcedure);

            var conteo = await reader.ReadAsync<int>();

            var cuvAsis = await reader.ReadAsync();

            foreach (var cuvRow in cuvAsis)
            {
                cuv = (IDictionary<string, object>)cuvRow;
                cuvs.Add(cuv["CUV"].ToString(), new ConsultaCuv() { cuv = cuv["CUV"].ToString(), ordenVerificacion = cuv["OrdenVerficacion"].ToString(), porcentajeAvanceObra = int.Parse(cuv["Porcentaje Avance"].ToString()) });
            }
            return Tuple.Create(conteo.FirstOrDefault(), cuvs);
        }

        public async Task<Tuple<int, List<Ecotecnologias>>> ObtenerEcotecnologiasXCuvsAsync(int tamanioPagina, int pagina, string cuv)
        {
            var parameters = new DynamicParameters();
            parameters.Add("@pagina", pagina);
            parameters.Add("@tamanioPagina", tamanioPagina);
            parameters.Add("@cuv", cuv);

            var reader = await this._conexion.QueryMultipleAsync("integracion.usp_ConsultarEcotecnologiasxCUVConPaginado", parameters, commandType: CommandType.StoredProcedure);

            var conteo = await reader.ReadAsync<int>();

            var ecotecnologias = await reader.ReadAsync<Ecotecnologias>();

            return Tuple.Create(conteo.FirstOrDefault(), ecotecnologias.ToList());
        }

        public async Task<Tuple<int, List<Equipamiento>>> ObtenerEquipamientoPorOferta(int tamanioPagina, int pagina, string claveOferta)
        {
            var parameters = new DynamicParameters();
            parameters.Add("@pagina", pagina);
            parameters.Add("@tamanioPagina", tamanioPagina);
            parameters.Add("@idOfertaVivienda", claveOferta);            

            var reader = await this._conexion.QueryMultipleAsync("integracion.usp_ConsultarEquipamientoxOfertaConPaginado", parameters, commandType: CommandType.StoredProcedure);

            var conteo = await reader.ReadAsync<int>();

            var equipamiento = await reader.ReadAsync<Equipamiento>();

            return Tuple.Create(conteo.FirstOrDefault(), equipamiento.ToList());
        }

        public async Task<Tuple<int, List<Equipamiento>>> ObtenerEquipamientoPorOrden(int tamanioPagina, int pagina, string idOrdenVerificacion)
        {
            var parameters = new DynamicParameters();
            parameters.Add("@pagina", pagina);
            parameters.Add("@tamanioPagina", tamanioPagina);
            parameters.Add("@idOrdenVerificacion", idOrdenVerificacion);            

            var reader = await this._conexion.QueryMultipleAsync("integracion.usp_ConsultarEquipamientoxOfertaConPaginado", parameters, commandType: CommandType.StoredProcedure);

            var conteo = await reader.ReadAsync<int>();

            var equipamiento = await reader.ReadAsync<Equipamiento>();

            //if (equipamiento.Count() > 0 && equipamiento.ToList().FirstOrDefault().Descripcion == null)
            //{
            //    equipamiento.ToList().RemoveAt(0);
            //}

            return Tuple.Create(conteo.FirstOrDefault(), equipamiento.ToList());
        }

        public async Task<Tuple<int, List<Vivienda>>> ObtenerViviendasPorEquipamiento(int tamanioPagina, int pagina, string atributo, string claveOferta)
        {
            var parameters = new DynamicParameters();
            parameters.Add("@pagina", pagina);
            parameters.Add("@tamanioPagina", tamanioPagina);
            parameters.Add("@idOfertaVivienda", claveOferta);
            parameters.Add("@idAtributo", atributo);

            var reader = await this._conexion.QueryMultipleAsync("integracion.usp_ConsultarViviendaxEquipamientoConPaginado", parameters, commandType: CommandType.StoredProcedure);

            var conteo = await reader.ReadAsync<int>();

            var viviendas = await reader.ReadAsync<Vivienda>();

            return Tuple.Create(conteo.FirstOrDefault(), viviendas.ToList());
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="cuv"></param>
        /// <returns></returns>
        public async Task<Tuple<int, List<Equipamiento>>> ObtenerEquipamientoPorVivienda(int tamanioPagina, int pagina, string cuv)
        {
            var parameters = new DynamicParameters();
            parameters.Add("@pagina", pagina);
            parameters.Add("@tamanioPagina", tamanioPagina);            
            parameters.Add("@cuv", cuv);

            var reader = await this._conexion.QueryMultipleAsync("integracion.usp_ConsultarEquipamientoxCuvConPaginado", parameters, commandType: CommandType.StoredProcedure);

            var conteo = await reader.ReadAsync<int>();

            var equipamiento = await reader.ReadAsync<Equipamiento>();

            return Tuple.Create(conteo.FirstOrDefault(), equipamiento.ToList());
        }

        public async Task<Tuple<int, List<Atributos>>> ObtenerAtributosXCuvsAsync(int tamanioPagina, int pagina, string cuv)
        {
            var parameters = new DynamicParameters();
            parameters.Add("@pagina", pagina);
            parameters.Add("@tamanioPagina", tamanioPagina);
            parameters.Add("@cuv", cuv);

            var reader = await this._conexion.QueryMultipleAsync("integracion.usp_ConsultarAtributosxCUVConPaginado", parameters, commandType: CommandType.StoredProcedure, commandTimeout: 160);

            var conteo = await reader.ReadAsync<int>();

            var atributos = await reader.ReadAsync<Atributos>();

            return Tuple.Create(conteo.FirstOrDefault(), atributos.ToList());
        }

        public async Task<List<Vivienda>> ObtenerDatosCuvsAsync(string cuvs)
        {
            var parameters = new DynamicParameters();
            parameters.Add("@cuv", cuvs);

            var reader = await this._conexion.QueryMultipleAsync("integracion.usp_ConsultarDatosCUV", parameters, commandType: CommandType.StoredProcedure, commandTimeout: 160);

            var viviendas = await reader.ReadAsync<Vivienda>();

            return viviendas.ToList();
        }
    }
}
