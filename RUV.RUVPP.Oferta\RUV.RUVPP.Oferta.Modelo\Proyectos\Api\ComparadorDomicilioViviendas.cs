﻿using RUV.RUVPP.Oferta.Modelo.Proyectos.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.Proyectos.Api
{
    public class ComparadorDomicilioViviendas : IEqualityComparer<Vivienda>
    {
        public bool Equals(Vivienda x, Vivienda y)
        {
            var resultado = false;

            //Verifica si los objetos comparados referencian el mismo dato.
            if (Object.ReferenceEquals(x, y)) return true;

            //Verifica si uno de los objetos es nulo.
            if (Object.ReferenceEquals(x, null) || Object.ReferenceEquals(y, null))
                return false;

            //Verifica si los campos de doomicilio son iguales
            return x.domicilioGeografico.cp == y.domicilioGeografico.cp &&
                    x.domicilioGeografico.idEstado == y.domicilioGeografico.idEstado &&
                    x.domicilioGeografico.idmunicipio == y.domicilioGeografico.idmunicipio &&
                    x.domicilioGeografico.idLocalidad == y.domicilioGeografico.idLocalidad &&
                    x.domicilioGeografico.idAsentamiento.Substring(0,4) == y.domicilioGeografico.idAsentamiento.Substring(0,4) &&
                    x.idTipoZonaVivienda == y.idTipoZonaVivienda &&
                    x.domicilioGeografico.idVialidadPrincipal == y.domicilioGeografico.idVialidadPrincipal &&
                    x.domicilioGeografico.superManzana == y.domicilioGeografico.superManzana &&
                    x.domicilioGeografico.manzana == y.domicilioGeografico.manzana &&
                    x.domicilioGeografico.lote == y.domicilioGeografico.lote &&
                    x.domicilioGeografico.numeroExteriorNumerico == y.domicilioGeografico.numeroExteriorNumerico &&
                    x.domicilioGeografico.numeroExteriorAlfanumerico == y.domicilioGeografico.numeroExteriorAlfanumerico &&
                    x.domicilioGeografico.numeroExteriorAnt == y.domicilioGeografico.numeroExteriorAnt &&
                    x.domicilioGeografico.numeroInteriorNumerico == y.domicilioGeografico.numeroInteriorNumerico &&
                    x.domicilioGeografico.numeroInteriorAlfanumerico == y.domicilioGeografico.numeroInteriorAlfanumerico &&
                    x.numeroCatastralLote == y.numeroCatastralLote &&
                    x.idNivelVivienda == y.idNivelVivienda &&
                    x.noEstacionamientos == y.noEstacionamientos &&
                    x.domicilioGeografico.idVialidad1 == y.domicilioGeografico.idVialidad1 &&
                    x.domicilioGeografico.idVialidad2 == y.domicilioGeografico.idVialidad2 &&
                    x.domicilioGeografico.idVialidad3 == y.domicilioGeografico.idVialidad3 &&
                    x.costo == y.costo &&
                    x.metros2Lote == y.metros2Lote &&
                    x.metrosFrenteLote == y.metrosFrenteLote &&
                    x.idOrientacionVivienda == y.idOrientacionVivienda &&
                    x.domicilioCarreteraCamino.origen == y.domicilioCarreteraCamino.origen &&
                    x.domicilioCarreteraCamino.destino == y.domicilioCarreteraCamino.destino &&
                    x.domicilioCarreteraCamino.idMargen == y.domicilioCarreteraCamino.idMargen &&
                    x.domicilioCarreteraCamino.cadenamiento == y.domicilioCarreteraCamino.cadenamiento &&
                    x.domicilioCarreteraCamino.idAdministracion == y.domicilioCarreteraCamino.idAdministracion &&
                    x.domicilioCarreteraCamino.idDerechoTransito == y.domicilioCarreteraCamino.idDerechoTransito &&
                    x.domicilioCarreteraCamino.codigo == y.domicilioCarreteraCamino.codigo;
        }

        public int GetHashCode(Vivienda vivienda)
        {
            //Verifica si el objeto es nulo 
            if (Object.ReferenceEquals(vivienda, null)) return 0;

            return ((vivienda.domicilioGeografico.cp ?? String.Empty) +
                    (vivienda.domicilioGeografico.idEstado ?? String.Empty) +
                    (vivienda.domicilioGeografico.idmunicipio ?? String.Empty) +
                    (vivienda.domicilioGeografico.idLocalidad ?? String.Empty) +
                    (vivienda.domicilioGeografico.idAsentamiento.Substring(0, 4) ?? String.Empty) +
                    (vivienda.idTipoZonaVivienda.ToString() ?? String.Empty) +
                    (vivienda.domicilioGeografico.idVialidadPrincipal.ToString() ?? String.Empty) +
                    (vivienda.domicilioGeografico.superManzana ?? String.Empty) +
                    (vivienda.domicilioGeografico.manzana ?? String.Empty) +
                    (vivienda.domicilioGeografico.lote ?? String.Empty) +
                    (vivienda.domicilioGeografico.numeroExteriorNumerico.ToString() ?? String.Empty) +
                    (vivienda.domicilioGeografico.numeroExteriorAlfanumerico ?? String.Empty) +
                    (vivienda.domicilioGeografico.numeroExteriorAnt ?? String.Empty) +
                    (vivienda.domicilioGeografico.numeroInteriorNumerico.ToString() ?? String.Empty) +
                    (vivienda.domicilioGeografico.numeroInteriorAlfanumerico ?? String.Empty) +
                    (vivienda.numeroCatastralLote ?? String.Empty) +
                    (vivienda.idNivelVivienda.ToString() ?? String.Empty) +
                    (vivienda.noEstacionamientos.ToString() ?? String.Empty) +
                    (vivienda.domicilioGeografico.idVialidad1.ToString() ?? String.Empty) +
                    (vivienda.domicilioGeografico.idVialidad2.ToString() ?? String.Empty) +
                    (vivienda.domicilioGeografico.idVialidad3.ToString() ?? String.Empty) +
                    (vivienda.costo.ToString() ?? String.Empty) +
                    (vivienda.metros2Lote.ToString() ?? String.Empty) +
                    (vivienda.metrosFrenteLote.ToString() ?? String.Empty) +
                    (vivienda.idOrientacionVivienda.ToString() ?? String.Empty) +
                    (vivienda.domicilioCarreteraCamino.origen ?? String.Empty) +
                    (vivienda.domicilioCarreteraCamino.destino ?? String.Empty) +
                    (vivienda.domicilioCarreteraCamino.idMargen.ToString() ?? String.Empty) +
                    (vivienda.domicilioCarreteraCamino.cadenamiento ?? String.Empty) +
                    (vivienda.domicilioCarreteraCamino.idAdministracion.ToString() ?? String.Empty) +
                    (vivienda.domicilioCarreteraCamino.idDerechoTransito.ToString() ?? String.Empty) +
                    (vivienda.domicilioCarreteraCamino.codigo ?? String.Empty)).GetHashCode();
        }
    }
}
