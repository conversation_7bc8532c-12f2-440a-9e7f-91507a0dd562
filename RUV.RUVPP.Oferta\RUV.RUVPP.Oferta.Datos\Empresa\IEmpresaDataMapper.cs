﻿using RUV.RUVPP.Oferta.Modelo.Proyectos.Data;
using RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Datos.Empresa
{
    public interface IEmpresaDataMapper : IDisposable
    {
        Task<DatosCPV> ConsultaEmpresaPorNRRUV(int? idEmpresa, string nrruv);
        Task<string> ObtenerNumeroRegistroRUV(int id);
        Task<DatosCPV> ConsultaEmpresaInterno(int idProyecto, int idOferta, string cuv);
        Task<DatosGeneralesEmpresa> ConsultarDatosGeneralesEmpresa(int idRuvAsIs);
        Task<DatosGeneralesEmpresa> ObtenerEmpresaxUsuario(int idUsuario);
    }
}
