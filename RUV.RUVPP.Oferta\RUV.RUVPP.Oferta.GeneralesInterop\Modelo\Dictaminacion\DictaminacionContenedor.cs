﻿using System.Collections.Generic;

namespace RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Dictaminacion
{
    public class DictaminacionContenedor
    {
        public int? idOrdenTrabajo { get; set; }
        public int? idProducto { get; set; }
        public int? idRegistro { get; set; }
        public int? idServicio { get; set; }
        public List<DictaminacionSeccion> secciones { get; set; }
        public List<MotivoRechazo> motivosRechazoArchivados { get; set; }
    }
}
