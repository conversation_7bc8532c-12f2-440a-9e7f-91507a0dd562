﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.Proyectos.Data
{
    public class DomicilioGeografico
    {
        public int idDomicilioGeografico { get; set; }
        public int idTipoDomicilioINEGI { get; set; }
        public string idEstado { get; set; }
        public string estado { get; set; }
        public string idmunicipio { get; set; }
        public string municipio { get; set; }        
        public string idLocalidad { get; set; }
        public int idPeriodo { get; set; }
        public string numeroExteriorNumerico { get; set; }
        public string numeroExteriorAlfanumerico { get; set; }
        public string numeroExteriorAnt { get; set; }
        public string numeroInteriorNumerico { get; set; }
        public string numeroInteriorAlfanumerico { get; set; }
        public string idAsentamiento { get; set; }
        public string cp { get; set; }
        public int idVialidad1 { get; set; }
        public int idVialidad2 { get; set; }
        public int idVialidad3 { get; set; }
        public string descripcion { get; set; }
        public string domicilioGeografico { get; set; }
        public int idVialidadPrincipal { get; set; }
        public int idTipoDomicilioRUV { get; set; }
        public string referenciaPrevia { get; set; }
        public string nombreVialidadPrincipal { get; set; }
        public string nombreVialidad1 { get; set; }
        public string nombreVialidad2 { get; set; }
        public string nombreVialidad3 { get; set; }
        public int tipoVialidadP { get; set; }
        public int tipoVialidad1 { get; set; }
        public int tipoVialidad2 { get; set; }
        public int tipoVialidad3 { get; set; }
        public string latitud { get; set; }
        public string longitud { get; set; }
        public string nombreTipoVialidadPrincipal { get; set; }
        public string nombreTipoVialidad1 { get; set; }
        public string nombreTipoVialidad2 { get; set; }
        public string nombreTipoVialidad3 { get; set; }
        public string nombreAsentamiento { get; set; }
        public string manzana { get; set; }
        public string superManzana { get; set; }
        public string lote { get; set; }
        public string localidad { get; set; }
        public string xmlSIG { get; set; }
    }
}
