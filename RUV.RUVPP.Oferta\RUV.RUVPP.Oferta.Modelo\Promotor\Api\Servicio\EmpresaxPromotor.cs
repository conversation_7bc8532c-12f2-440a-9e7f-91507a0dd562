﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.Promotor.Api.Servicio
{
   public class EmpresaxPromotor
    {
        public string idempresa { get; set; }
        public int idempresaInst { get; set; }
        public string nombreRazonSocial { get; set; }
        public string apellidoPaterno { get; set; }
        public string apellidoMaterno { get; set; }
        public string rfc { get; set; }
        public string numeroRegistroPatronal { get; set; }
        public string direccion { get; set; }
        public string objetoSocialEmpresa { get; set; }
 
    }
}
