﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.Historico.Data
{
    /// <summary>
    /// contenedor de resultado de consulta de historico de validaciones (lista de ordenes de trabajo de una oferta/proyecto)
    /// </summary>
    public class OrdenTrabajoHistoricoValidacion
    {
        /// <summary>
        /// Nombre del validador
        /// </summary>
        public string Validador { get; set; }
        /// <summary>
        /// Clave de la orden de trabajo
        /// </summary>
        public string claveODT { get; set; }
        /// <summary>
        /// Fecha de registro de la orden de trabajo
        /// </summary>
        public DateTime? FechaInicio { get; set; }
        /// <summary>
        /// Fecha de inicio de atencion de la orden de trabajo
        /// </summary>
        public DateTime? FechaValidacion { get; set; }
        /// <summary>
        /// Fecha de termino de atencion de la orden de trabajo
        /// </summary>
        public DateTime? FechaFinAtencion { get; set; }
        /// <summary>
        /// Identificador del estatus de la orden de trabajo
        /// </summary>
        public int IdEstatus { get; set; }
        /// <summary>
        /// Nombre del estatus de la orden de trabajo
        /// </summary>
        public string NombreEstatus { get; set; }
        /// <summary>
        /// Identificador de la orden de trabajo
        /// </summary>
        public int IdOrdenTrabajo { get; set; }
        /// <summary>
        /// Identificador de la orden de servicio
        /// </summary>
        public int IdOrdenServicio { get; set; }
        /// <summary>
        /// Identificador del usuario validador
        /// </summary>
        public int? IdValidador { get; set; }
    }
}
