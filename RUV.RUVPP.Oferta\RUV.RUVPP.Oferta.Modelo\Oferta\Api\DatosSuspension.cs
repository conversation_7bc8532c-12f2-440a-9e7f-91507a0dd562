﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.Oferta.Api
{
    public class DatosSuspension
    {
        public int porcentajeSuspension { get;set;}
        public int porcentajeActual { get;set;}
        public string ordenVerificacion  {get;set;}
        public string fechaSolicitud  {get;set;}
        public string fechaSuspension { get;set;}
        public string fechaActivacion { get;set;}
        public string estatusActual { get;set;}
        public string estatusOrden { get;set;}
        public int periodoMeses { get;set;}
        public string idEmpresa  {get;set;}
        public string razonSocialEmpresa  {get;set;}
        public string fechaInicio  {get;set;}
        public string fechaFin  {get;set; }
        public int numeroViviendas { get; set; }
        
    }
}
