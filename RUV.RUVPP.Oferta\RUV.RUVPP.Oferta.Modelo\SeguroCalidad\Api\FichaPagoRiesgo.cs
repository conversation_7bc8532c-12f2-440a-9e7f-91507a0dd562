﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api
{
    public class FichaPagoRiesgo
    {
        public string idOrdenVerificacion { get; set; }
        public int idEmpresaAsIs { get; set; }
        public int idCuvsSolicitadas { get; set; }
        public decimal costoRelacion { get; set; }
        public List<PrecioViviendas> precioViviendas { get;set;}
        public List<PreciosxId> listaViviendas { get; set; }
        public bool esPrepago { get; set; }
        public string rfcUsuario { get; set; }
        


    }
}
