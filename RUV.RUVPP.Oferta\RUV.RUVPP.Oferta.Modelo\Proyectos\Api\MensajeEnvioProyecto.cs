﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.Proyectos.Api
{
    /// <summary>
    /// Representa un mensaje para procesar el envio de un proyecto
    /// </summary>
    public class MensajeEnvioProyecto
    {
        /// <summary>
        /// Id del usuario que envia el proyecto.
        /// </summary>
        public int IdUsuario { get; set; }

        /// <summary>
        /// Nombre del usuario que envia el proyecto.
        /// </summary>
        public string NombreUsuario { get; set; }

        /// <summary>
        /// Token de acceso del usuario que envia el proyecto.
        /// </summary>
        public string TokenGuid { get; set; }

        /// <summary>
        /// Id de la empresa que envia el proyectos.
        /// </summary>
        public int? IdEmpresa { get; set; }

        /// <summary>
        /// Id del proyecto enviado.
        /// </summary>
        public int IdProyecto { get; set; }

        /// <summary>
        /// Id del estatus previo del proyecto
        /// </summary>
        public int IdEstatusPrevio { get; set; }
    }
}
