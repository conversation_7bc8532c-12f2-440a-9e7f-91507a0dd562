﻿

using RUV.RUVPP.Oferta.Modelo.Mediciones;
using System.Web.Script.Serialization;

namespace RUV.RUVPP.Oferta.Modelo.Proyectos.Data
{
    public class Vivienda
    {
        public int featID { get; set; }
        public int idVivienda { get; set; }
        public int? idViviendaPlanoSIG { get; set; }
        public int idProyecto { get; set; }
        public string nombreProyecto { get; set; }
        public int idPrototipo { get; set; }
        public int idOfertaVivienda { get; set; }
        public string nombrePrototipo { get; set; }
        public int idDomicilioGeografico { get; set; }
        public int idOrientacionVivienda { get; set; }
        public string ordenVerificacion { get; set; }
        public int idNivelVivienda { get; set; }
        public int idTipoVivienda { get; set; }

        public string claveTipoVivienda { get; set; }
        public int idModalidadVivienda { get; set; }
        public int idEstatusJuridicoVivienda { get; set; }
        public int idTipoZonaVivienda { get; set; }
        public int idTipoAportacionVivienda { get; set; }
        public int identificadorVivienda { get; set; }
        public int idTipoProgramaVivienda { get; set; }
        public string numeroCatastralLote { get; set; }
        public string edificio { get; set; }
        public string noEstacionamientos { get; set; }
        public string planta { get; set; }
        public decimal costo { get; set; }
        public decimal metros2Lote { get; set; }
        public decimal metrosFrenteLote { get; set; }
        public int? idEstatusVivienda { get; set; }
        public string estatusVivienda { get; set; }
        public string fechaRegistro { get; set; }
        public string fechaActualizacion { get; set; }
        public string cuv { get; set; }
        public string cuvGeografica { get; set; }
        public DomicilioGeografico domicilioGeografico { get; set; }
        public DomicilioCarreteraCamino domicilioCarreteraCamino { get; set; }
        public string nombreCondominio { get; set; }
        public string nombreFrente { get; set; }
        public int idTipoAsentamiento { get; set; }
        public string direccion { get; set; }

        public string precio { get; set; }

        public string onavi { get; set; }

        public string sinInicioDeObra { get; set; }

        public string porcentajeDeAvance { get; set; }

        public string luz { get; set; }

        public string agua { get; set; }

        public string drenaje { get; set; }

        public string condicionesFisicas { get; set; }
        public string tipologia { get; set; }
        public string idEstado { get; set; }
        public string idMunicipio { get; set; }

        public string distancia { get; set; }

        public string puntaje { get; set; }
        public string habitabilidad { get; set; }
        public PuntajeSalida Puntajes { get; set; }
        public int numeroAtributos { get; set; }
        public int numeroEcotecnologias { get; set; }
        
        public int estatusASIS { get; set; }
        public bool? esViviendaModificada { get; set; }

        public int idEstatusOferta { get; set; }
    }
}