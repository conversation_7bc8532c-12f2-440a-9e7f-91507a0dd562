﻿using RUV.Comun.Utilerias;
using RUV.RUVPP.Oferta.Modelo.Promotor.Api;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Dominio.Promotor
{
    public interface IServicioPromotor : IDisposable
    {
        #region Promotor

        Task<ResultadoPaginado<List<RUV.RUVPP.Oferta.Modelo.Promotor.Data.OfertaViviendaAsis>>> ObtenerOfertaViviendaPaginado(int tamanioPagina, int pagina, RUV.RUVPP.Oferta.Modelo.Promotor.Data.OfertaViviendaAsis entidad);
        Task<int> GuardarPromotor(RUV.RUVPP.Oferta.Modelo.Promotor.Api.PromotorDTO entidad);
        Task<int> ActualizarPromotor(RUV.RUVPP.Oferta.Modelo.Promotor.Api.PromotorDTO entidad);
        Task<ResultadoPaginado<List<RUV.RUVPP.Oferta.Modelo.Promotor.Api.Promotor>>> ObtenerPromotorPaginado(int tamanioPagina, int pagina, RUV.RUVPP.Oferta.Modelo.Promotor.Api.Promotor entidad);
        Task<RUV.RUVPP.Oferta.Modelo.Promotor.Api.Promotor> ObtenerPromotor(RUV.RUVPP.Oferta.Modelo.Promotor.Api.Promotor entidad);
        Task<List<RUV.RUVPP.Oferta.Modelo.Promotor.Api.PromotorXCobertura>> ObtenerListadoPromotorXCobertura(int idPromotor);
        Task<bool> EliminarPromotorXCobertura(PromotorXCoberturaDTO dto);
        Task<bool> GuardarOfertaxPromotor(OfertaxPromotorDTO dto);
        Task<bool> EliminarOfertaxPromotor(OfertaxPromotorDTO dto);
        Task<bool> ActualizarOfertaxPromotor(OfertaxPromotorDTO dto);
        Task<List<RUV.RUVPP.Oferta.Modelo.Promotor.Api.OfertaxPromotor>> ObtenerListadoOfertaxPromotor(RUV.RUVPP.Oferta.Modelo.Promotor.Api.OfertaxPromotor entidad);
        Task<ResultadoPaginado<List<RUV.RUVPP.Oferta.Modelo.Promotor.Api.Promotor>>> ObtenerOfertaxPromotorPaginado(int tamanioPagina, int pagina, string[] idOfertaVivienda, RUV.RUVPP.Oferta.Modelo.Promotor.Api.Promotor entidad);
        Task<int> GuardarDocumento(RUV.RUVPP.Oferta.Modelo.Promotor.Api.Documentos entidad);
        Task<List<RUV.RUVPP.Oferta.Modelo.Promotor.Api.Documentos>> ObtenerListadoDocumentos(int[] idDocumentos);
        Task<List<RUV.RUVPP.Oferta.Modelo.Promotor.Api.CatEstado>> ObtenerListadoCatEstado();
        Task<int> GuardarOfertaxPromotorNotificacion(OfertaxPromotorNotificacion entity);
        Task<List<OfertaxPromotorNotificacion>> ObtenerListadoOfertaxPromotorNotificacion(OfertaxPromotorNotificacion entity);
        Task<int> GuardarGrupoNotificacionOfertaxPromotor(GrupoNotificacionOfertaxPromotor entity);
        Task<List<GrupoNotificacionOfertaxPromotor>> ObtenerListadoGrupoNotificacionOfertaxPromotor(GrupoNotificacionOfertaxPromotor entity);
        Task<int> ActualizarGrupoNotificacionOfertaxPromotor(GrupoNotificacionOfertaxPromotor entity);
        Task<bool> reEnviarJSON();

        #endregion Promotor

        #region servicio promotor


        Task<bool> registrarPromotor();
        
        #endregion
    }
}
