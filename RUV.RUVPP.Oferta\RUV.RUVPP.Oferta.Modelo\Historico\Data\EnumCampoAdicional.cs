﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.Historico.Data
{
    /// <summary>
    /// 
    /// </summary>
    public enum EnumCampoAdicional
    {
        [Description("ID Proyecto ID del Proyecto de Oferta")]
        IdProyecto = 1,
        [Description("Referencia del pago de oferta")]
        Referencia = 2,
        [Description("Operador que atendio la ODS")]
        OperadorODS = 3,
        [Description("Numero de Autorización")]
        Autorizacion = 4,
        [Description("Numero de Orden de Verificación")]
        OV = 5,
        [Description("Numero de Crédito")]
        NumeroCredito = 6,
        [Description("Operador ODT    Operador que atendio la ODT")]
        OperadorODT = 7,
        [Description("Id Oferta de Vivienda")]
        IdOfertaVivienda = 8,
        [Description("Usuario Usuario que desvíncula")]
        UsuarioDesvincula = 9,
        [Description("Usuario que solicita eliminación")]
        UsuarioSolicitudEliminacion = 10,
        [Description("Usuario que realiza la actualización")]
        UsuarioRealizaActualizacion = 11,
        [Description("Usuario que realza el cambio de estatus manual y el estatus al que se cambia.")]
        UsuarioCambiaEstatusManual = 12,
        [Description("Fecha en que se cambia de estatus.")]
        Fecha = 13
    }
}
