﻿using RUV.RUVPP.Oferta.Modelo.Mediciones;
using RUV.RUVPP.Oferta.Modelo.Proyectos.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Datos.Mediciones
{
    /// <summary>
    /// Data Mapper de mediciones
    /// </summary>
    public interface IMedicionesDataMapper : IDisposable
    {
        /// <summary>
        /// Obtiene las cuvs por oferta y/o orden de verificacion desde el ASIS
        /// </summary>
        /// <param name="ClaveOferta">Clave de oferta</param>
        /// <param name="IdOrdenVerificacion">Identificador de orden de verificacion</param>
        /// <returns></returns>
        Task<Dictionary<string, ConsultaCuv>> ObtenerCuvsAsync(string ClaveOferta, string IdOrdenVerificacion);

        /// <summary>
        /// Obtiene las cuvs por oferta y/o orden de verificacion desde el ASIS con paginación
        /// </summary>
        /// <param name="ClaveOferta">Identificador de oferta</param>
        /// <param name="IdOrdenVerificacion">Identificador de orden de verificacion</param>
        /// <param name="pagina">pagina consultada</param>
        /// <param name="tamanioPagina">tamaño de pagina</param>
        /// <returns></returns>
        Task<Tuple<int, Dictionary<string, ConsultaCuv>>> ObtenerCuvsPaginadoAsync(int tamanioPagina, int pagina, string ClaveOferta, string IdOrdenVerificacion);

        /// <summary>
        /// Obtiene datos de las cuvs especificadas para la oferta y/o orden de verificacion desde el ASIS
        /// </summary>       
        /// <param name="ClaveOferta">Identificador de oferta</param>
        /// <param name="IdOrdenVerificacion">Identificador de orden de verificacion</param>
        /// <param name="filtroCuvs">Lista de cuvs a buscar</param>
        /// <returns></returns>
        Task<Dictionary<string, ConsultaCuv>> ObtenerDatosCuvsAsync(string ClaveOferta, string IdOrdenVerificacion, List<ConsultaCuv> filtroCuvs);

        /// <summary>
        /// Obtiene la lista paginada de ecotecnologias de una cuv.
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="cuv">clave de la cuv</param>
        /// <returns>Lista con ecotecnologias de la cuv</returns>
        Task<Tuple<int, List<Ecotecnologias>>> ObtenerEcotecnologiasXCuvsAsync(int tamanioPagina, int pagina, string cuv);

        /// <summary>
        /// Obtiene la lista paginada de atributos de una cuv.
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="cuv">clave de la cuv</param>
        /// <returns>Lista con atributos de la cuv</returns>
        Task<Tuple<int, List<Atributos>>> ObtenerAtributosXCuvsAsync(int tamanioPagina, int pagina, string cuv);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="idOrdenVerificacion"></param>
        /// <returns></returns>
        Task<Tuple<int, List<Equipamiento>>> ObtenerEquipamientoPorOrden(int tamanioPagina, int pagina, string idOrdenVerificacion);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="claveOferta"></param>
        /// <returns></returns>
        Task<Tuple<int, List<Equipamiento>>> ObtenerEquipamientoPorOferta(int tamanioPagina, int pagina, string claveOferta);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="atributo"></param>
        /// <param name="claveOferta"></param>
        /// <returns></returns>
        Task<Tuple<int, List<Vivienda>>> ObtenerViviendasPorEquipamiento(int tamanioPagina, int pagina, string atributo, string claveOferta);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="cuv"></param>
        /// <returns></returns>
        Task<Tuple<int, List<Equipamiento>>> ObtenerEquipamientoPorVivienda(int tamanioPagina, int pagina, string cuv);


        /// <summary>
        /// 
        /// </summary>
        /// <param name="cuvs"></param>
        /// <returns></returns>
        Task<List<Vivienda>> ObtenerDatosCuvsAsync(string cuvs);
    }
}
