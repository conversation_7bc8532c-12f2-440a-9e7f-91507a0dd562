﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api
{
    public class ReporteExcelPPS
    {
        public string acreedorSAPaseguradora { get; set; }
        public string acreedorSAPoferente { get; set; }
        public string proyecto { get; set; }
        public string articulo { get; set; }
        public string moneda { get; set; }
        public string importe { get; set; }
        public string idVivienda { get; set; }
        public string ejercicio { get; set; }
        public string clabeBancaria { get; set; }
        public string cuv { get; set; }
        public string ordenVerificacion { get; set; }
        public string fechaInicioVigencia { get; set; }
        public string referencia { get; set; }
        public string fechaPago { get; set; }
        public string fechaPagoAseguradora { get; set; }
        public string rfc { get; set; }
        public string montoAvaluo { get; set; }
        public string costoRelacion { get; set; }
        public string nombre { get; set; }
        public string diasDiferencia { get; set; }


    }
}
