﻿namespace RUV.RUVPP.Oferta.Modelo.AgenteServicio
{
    /// <summary>
    /// Objeto que contiene las propiedades necesarias para ejecutar el servicio de cancelación.
    /// </summary>
    public class CancelacionCuvDTO
    {
        /// <summary>
        /// Constructor de la clase CancelacionCuvDTO.
        /// </summary>
        /// <param name="cuv">Clave Única de Vivienda que se quiere cancelar.</param>
        /// <param name="usuario">Usuario que ejecuta el servicio.</param>
        public CancelacionCuvDTO (string cuv, string usuario)
        {
            this.cuv = cuv;
            this.usuario = usuario;
        }
        /// <summary>
        /// Clave Única de Vivienda de 16 dígitos.
        /// </summary>
        public string cuv { get; set; }
        /// <summary>
        /// Usuario que ejecuta el servicio.
        /// </summary>
        public string usuario { get; set; }
    }
}
