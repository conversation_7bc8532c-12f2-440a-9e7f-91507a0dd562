﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.Prototipos.Api
{
    public class ClasificacionVivienda
    {
        public int idClasificacionVivienda { get; set; }
        public string nombre { get; set; }
        public string descripcion { get; set; }
        public decimal? salariosMin { get; set; }
        public decimal? salariosMax { get; set; }
        public decimal? costoMin { get; set; }
        public decimal? costoMax { get; set; }
        public DateTime fechaRegistro { get; set; }
        public DateTime? fechaActualizacion { get; set; }
    }
}
