﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api
{
    public class Movimiento
    {
        public string nombreMovimiento { get; set; }
        public string claveProducto { get; set; }
        public DateTime fechaOperacion { get; set; }
        public decimal monto { get; set; }
        public decimal montoAbono { get; set; }
        public string tipoOperacion { get; set; }
        public string tipoProductoCargo { get; set; }
        public string descripcionCargo { get; set; }
        public decimal saldoInicial { get; set; }
        public decimal saldoFinal { get; set; }
        public string usuario { get; set; }
        public string fechaRegistro { get; set; }
        public string estado { get; set; }
        public string municipio { get; set; }
        public string ordenVerificacion { get; set; }
    }
}
