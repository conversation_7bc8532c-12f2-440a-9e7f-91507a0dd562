﻿using RUV.RUVPP.Oferta.Comun.Modelo;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.Proyectos.Data
{
    public class DRO
    {
        public int idDRO { get; set; }
        public string nombreDRO { get; set; }
        public string apellidoPaterno { get; set; }
        public string apellidoMaterno { get; set; }
        public string noPerito { get; set; }
        public DateTime? fechaVigencia { get; set; }
        public DocumentoRuv identificacionOficial { get; set; }
        public DocumentoRuv licencia { get; set; }
    }
}
