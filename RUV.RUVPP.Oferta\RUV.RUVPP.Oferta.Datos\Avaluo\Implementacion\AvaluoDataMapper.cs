﻿using Dapper;
using RUV.Comun.Datos.Mapper;
using RUV.RUVPP.Negocio.General.Documentos;
using RUV.RUVPP.Oferta.Modelo.Avaluo;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Datos.Avaluo.Implementacion
{
    public class AvaluoDataMapper : SqlDataMapperBase, IAvaluoDataMapper
    {
        public AvaluoDataMapper(string nombreCadenaConexion, GestorConexiones gestorConexiones = null) : base(nombreCadenaConexion, gestorConexiones)
        {
        }

        public async Task<int> NotificacionDatosAvaluo(DatosAvaluoSolicitud entity)
        {
            DynamicParameters parametros = new DynamicParameters();

            parametros.Add("@idAvaluo", entity.id_avaluo);
            parametros.Add("@idUnidadValuacion", entity.id_uv);
            parametros.Add("@cuv", entity.cuv);
            parametros.Add("@fechaSolicitud", entity.fechaSolicitud);
            parametros.Add("@estatusAvaluo", 1);
            //parametros.Add("@IdPeritoSHF", entity.peritoSHF);

            var reader = await this._conexion.ExecuteScalarAsync("[seguroCalidad].[NOTIFICACIONDATOSAVALUO]", parametros, commandType: CommandType.StoredProcedure);
            var result = reader ?? 0;

            return Convert.ToInt32(result);
        }
        public async Task<int> ReasignacionAvaluo(DatosReasignacion entity)
        {
            DynamicParameters parametros = new DynamicParameters();
            parametros.Add("@idAvaluo", entity.id_avaluo);
            parametros.Add("@idUnidadValuacion", entity.id_uv);
            parametros.Add("@fechasolicitud", entity.fechaSolicitud);

            var resultado = await this._conexion.ExecuteScalarAsync("[seguroCalidad].[REASIGNACIONAVALUO]", parametros, commandType: CommandType.StoredProcedure);
            //Hasta preguntar a Cabagne que resultado regresara
            var result = resultado ?? 0;

            return Convert.ToInt32(result);
        }
        public async Task<int> CancelacionAvaluo(DatosCancelacion entity)
        {
            string respons;
            DynamicParameters parametros = new DynamicParameters();
            parametros.Add("@idAvaluo", entity.id_avaluo);
            parametros.Add("@fechaCancelacion", entity.fecha_cancelacion);

            var resultado = await this._conexion.ExecuteScalarAsync("[seguroCalidad].[CANCELACIONAVALUO]", parametros, commandType: CommandType.StoredProcedure);
            //Hasta preguntar a Cabagne que resultado regresara
            var result = resultado ?? 0;

            return Convert.ToInt32(result);
        }
        public async Task<Int32> ConsultaCuv(string cuv)
        {

            DynamicParameters parametros = new DynamicParameters();

            parametros.Add("@cuv", cuv);
            var reader = await this._conexion.ExecuteScalarAsync("[seguroCalidad].[BuscarCuvAvaluo]", parametros, commandType: CommandType.StoredProcedure);

            var result = reader ?? 0;

            return Convert.ToInt32(result);
        }
        public async Task<string> ConsultaAsignacionAvaluo(string idavaluo)
        {
            string respons;
            DynamicParameters parametros = new DynamicParameters();

            parametros.Add("@avaluo", idavaluo);
            var resultado = await this._conexion.QueryMultipleAsync("[seguroCalidad].[ConsultaBitacoraAvaluo]", parametros, commandType: CommandType.StoredProcedure);

            var conteo = await resultado.ReadAsync<string>();
            respons = conteo.FirstOrDefault();
            return respons;
        }   
        public async Task<List<DatosAvaluoLogingUv>> DatosAvaluoLogingUv(DatosAvaluoSolicitud entity)
        {
            List<DatosAvaluoLogingUv> datosAvaluoLogingUvs = new List<DatosAvaluoLogingUv>();

            DynamicParameters parametros = new DynamicParameters();
            parametros.Add("@idAvaluo", entity.usuario);
            parametros.Add("@password", entity.password);

            var resultado = await this._conexion.QueryMultipleAsync("[seguroCalidad].[ConsultaUnidadValuacion]", parametros, commandType: CommandType.StoredProcedure);
            datosAvaluoLogingUvs = resultado.Read<DatosAvaluoLogingUv>().ToList();
            return datosAvaluoLogingUvs;
        }
        public async Task<DTOAvaluo> filtroAvaluoViviviendaMAI(string cuv, string cve_ent)
        {
            DTOAvaluo dto = null;

            DynamicParameters parametros = new DynamicParameters();
            parametros.Add("@cuv", cuv);
            parametros.Add("@cve_ent", cve_ent);

            var resultado = await this._conexion.QueryMultipleAsync("[seguroCalidad].[DIRECCION _AVALUO_VIVIENDA_MAI]", parametros, commandType: CommandType.StoredProcedure);
            var proyecto = await resultado.ReadAsync<DTOAvaluo>();

            return proyecto.FirstOrDefault();

        }
        public async Task<DTOAvaluo> filtroAvaluoVivivienda(string cuv, string cve_ent)
        {
            DTOAvaluo dto = null; ;
            DynamicParameters parametros = new DynamicParameters();
            parametros.Add("@cuv", cuv);
            parametros.Add("@cve_ent", cve_ent);

            var resultado = await this._conexion.QueryMultipleAsync("[seguroCalidad].[DIRECCION _AVALUO_VIVIENDA]", parametros, commandType: CommandType.StoredProcedure);
            var proyecto = await resultado.ReadAsync<DTOAvaluo>();

            return proyecto.FirstOrDefault();
        }
        public async Task<List<DTOAvaluo>> filtroAvaluo(FiltroAvaluo filtroAvaluo)
        {
            List<DTOAvaluo> listDtoAvaluo = null;

            DynamicParameters parametros = new DynamicParameters();
            parametros.Add("@idUnidadValuacion", filtroAvaluo.claveUnidadEvaluacion);


            var resultado = await this._conexion.QueryMultipleAsync("[seguroCalidad].[SELECT_BITACORA_AVALUO]", parametros, commandType: CommandType.StoredProcedure);
            listDtoAvaluo = resultado.Read<DTOAvaluo>().ToList();

            return listDtoAvaluo;
        }
        public async Task<int> existeUnidadValuacion(int unidadValuacion, int opcion, string idvaluacion)
        {
            string respons;
            DynamicParameters parametros = new DynamicParameters();

            parametros.Add("@idUnidadValuacion", unidadValuacion);
            parametros.Add("@opcion", opcion);
            parametros.Add("@idValuacion", idvaluacion);
            var reader = await this._conexion.ExecuteScalarAsync("[seguroCalidad].[ExisteUnidadvaluacion]", parametros, commandType: CommandType.StoredProcedure);

            var result = reader ?? 0;

            return Convert.ToInt32(result);
        }
        
      



        #region Metodos App

        public async Task<List<DTOAvaluo>> direccionViviendaAvaluo(int idUnidadValuacion)
        {
            DynamicParameters parametros = new DynamicParameters();
            List<DTOAvaluo> listDtoAvaluo = null;
            parametros.Add("@idUnidadValuacion", idUnidadValuacion);
            //var resultado = await this._conexion.QueryMultipleAsync("[seguroCalidad].[DIRECCION_VIVIENDA_AVALUOS]", parametros, commandType: CommandType.StoredProcedure);
            var resultado = await this._conexion.QueryMultipleAsync("[avaluoA].[DIRECCION_VIVIENDA_AVALUOS]", parametros, commandType: CommandType.StoredProcedure);
            listDtoAvaluo = resultado.Read<DTOAvaluo>().ToList();

            return listDtoAvaluo;
        }

        public async Task<int> actualizacionDireccionAvaluoVivienda(DomicilioAvaluo entity)
        {
            DynamicParameters parametros = new DynamicParameters();

            parametros.Add("@fechaAsignacion", entity.fechaAsignacion);
            parametros.Add("@idBitacoraAvaluo", entity.idBitacoraAvaluo);
            parametros.Add("@latitud", entity.latitud);
            parametros.Add("@longitud", entity.longitud);
            parametros.Add("@cp", entity.cp);
            parametros.Add("@nombreasentamiento", entity.nombreasentamiento);
            parametros.Add("@calle", entity.calle);
            parametros.Add("@numeroexterior", entity.numeroexterior);
            parametros.Add("@cve_ent", entity.cve_ent);
            parametros.Add("@nombreEntidad", entity.nombreEntidad);
            parametros.Add("@cve_mun", entity.cve_mun);
            parametros.Add("@nombreMunicipio", entity.nombreMunicipio);
            parametros.Add("@cve_loc", entity.cve_loc);
            parametros.Add("@localidad", entity.localidad);
            parametros.Add("@primeraVialidad", entity.primeraVialidad);
            parametros.Add("@segundaVialidad", entity.segundaVialidad);
            parametros.Add("@vialidadPosterior", entity.vialidadPosterior);
            parametros.Add("@numextalf", entity.numextalf);
            parametros.Add("@numintnum", entity.numintnum);
            parametros.Add("@numintalf", entity.numintalf);

            // var reader = await this._conexion.ExecuteScalarAsync("[seguroCalidad].[BITACORA_DOMICILIO_AVALUOS]", parametros, commandType: CommandType.StoredProcedure);
            var reader = await this._conexion.ExecuteScalarAsync("[avaluoA].[BITACORA_DOMICILIO_AVALUOS]", parametros, commandType: CommandType.StoredProcedure);

            var result = reader ?? 0;

            return Convert.ToInt32(result);
        }

        public async Task<int> existeReporteAvaluo(string cuv)
        {
            DynamicParameters parametros = new DynamicParameters();

            parametros.Add("@cuv", cuv);
            //var reader = await this._conexion.ExecuteScalarAsync("[seguroCalidad].[EXISTE_REPORTE_BITACORA_AVALUO]", parametros, commandType: CommandType.StoredProcedure);
            var reader = await this._conexion.ExecuteScalarAsync("[avaluoA].[EXISTE_REPORTE_BITACORA_AVALUO]", parametros, commandType: CommandType.StoredProcedure);

            var result = reader ?? 0;

            return Convert.ToInt32(result);
        }

        public async Task<int> fechaUpdateReporteAvaluo(DateTime fechaReporte, string idBitacoraAvaluo, string IdPeritoSHF)
        {
            DynamicParameters parametros = new DynamicParameters();

            parametros.Add("@fechaReporte", fechaReporte);
            parametros.Add("@idBitacoraAvaluo", idBitacoraAvaluo);
            parametros.Add("@IdPeritoSHF", IdPeritoSHF);
            //var reader = await this._conexion.ExecuteScalarAsync("[seguroCalidad].[UPDATE_FECHA_BITACORA_AVALUO]", parametros, commandType: CommandType.StoredProcedure);
            var reader = await this._conexion.ExecuteScalarAsync("[avaluoA].[UPDATE_FECHA_BITACORA_AVALUO]", parametros, commandType: CommandType.StoredProcedure);

            var result = reader ?? 0;

            return Convert.ToInt32(result);
        }

        public async Task<int> FotoAvaluo(DocumentoFotoAvaluo entity)
        {
            DynamicParameters parametros = new DynamicParameters();


            parametros.Add("@idBitacoraAvaluo", entity.idBitacoraAvaluo);
            parametros.Add("@idCatalogoFoto", entity.idCatalogoFoto);
            parametros.Add("@fechaCaptura", entity.fechaCaptura);
            parametros.Add("@urlFoto", entity.urlFoto);

            //var reader = await this._conexion.ExecuteScalarAsync("[seguroCalidad].[InsertaFotoAvaluo]", parametros, commandType: CommandType.StoredProcedure);
            var reader = await this._conexion.ExecuteScalarAsync("[avaluoA].[InsertaFotoAvaluo]", parametros, commandType: CommandType.StoredProcedure);
            var result = reader ?? 0;

            return Convert.ToInt32(result);
        }

        public  async Task<List<DocumentoFotoAvaluo>> envioFotosInfonavit(string cuv)
        {
            DynamicParameters parametros = new DynamicParameters();
            List<DocumentoFotoAvaluo> listDtoAvaluo = null;
            parametros.Add("@cuv", cuv);
            //parametros.Add("@fechafin", fechaFin);
            var resultado = await this._conexion.QueryMultipleAsync("[avaluoA].[INFORMACION_FOTO]", parametros, commandType: CommandType.StoredProcedure);
            listDtoAvaluo = resultado.Read<DocumentoFotoAvaluo>().ToList();

            return listDtoAvaluo;
        }
        #endregion

    }





}
