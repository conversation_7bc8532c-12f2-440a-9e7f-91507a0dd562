﻿using RUV.Comun.Utilerias;
using RUV.RUVPP.Entidades.General.Documentos;
using RUV.RUVPP.Generales.Modelo.Documentos;
using RUV.RUVPP.Oferta.Modelo.Mediciones;
using RUV.RUVPP.Oferta.Modelo.OrdenVerificacion.Api;
using RUV.RUVPP.Oferta.Modelo.Proyectos.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace RUV.RUVPP.Oferta.Dominio.OrdenVerificacion
{
    /// <summary>
    /// 
    /// </summary>
    public interface IServicioOrdenVerficacion : IDisposable
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="claveOferta"></param>
        /// <param name="idOrdenVerificacion"></param>
        /// <returns></returns>
        Task<List<Modelo.OrdenVerificacion.Data.OrdenVerificacion>> ObtenerOrdenesVerificacionFiltroAsync(string claveOferta, string idOrdenVerificacion, string idRUVAsis);

        
        /// <param name="idOrdenVerificacion"></param>
        /// <returns></returns>
        Task<Modelo.OrdenVerificacion.Data.OrdenVerificacion> ObtenerOrdenesVerificacionFiltroAsync(string idOrdenVerificacion, int idRuvAsIs);

        Task<ResultadoPaginado<List<PagosOrdenVerificacion>>> ObtenerPagosOrdenVerificacionPaginado(int tamanioPagina, int pagina, string idOrdenVerificacion);
        Task<DocumentoRuv> ObtenerInformacionDocumento(int idDocumento);

        /// <summary>
        /// Obtiene la ultima orden de verificacion de una oferta
        /// </summary>
        /// <param name="claveOferta"></param>
        /// <returns></returns>
        Task<Modelo.OrdenVerificacion.Data.OrdenVerificacion> ObtenerUltimaOrdenVerificacionPorIdOfertaAsync(string claveOferta);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="idVivienda"></param>
        /// <param name="idOrdenVerificacion"></param>
        /// <returns></returns>
        Task<List<Vivienda>> ObtenerViviendasPorOrdenesVerificacionAsync(string idVivienda, string idOrdenVerificacion);



        /// <summary>
        /// 
        /// </summary>
        /// <param name="idOrdenVerificacion"></param>
        /// <returns></returns>
        Task<List<Vivienda>> ObtenerDetalleOrdenVerificacionAsync(string idOrdenVerificacion);

       /// <summary>
       /// 
       /// </summary>
       /// <param name="tamanioPagina"></param>
       /// <param name="pagina"></param>
       /// <param name="claveOferta"></param>
       /// <param name="idOrdenVerificacion"></param>
       /// <param name="idCuv"></param>
       /// <returns></returns>
        Task<ResultadoPaginado<List<Modelo.OrdenVerificacion.Data.OrdenVerificacion>>> ObtenerOrdenesVerificacionPorIdOfertaPaginadorAsync(int tamanioPagina, int pagina, string claveOferta, string idOrdenVerificacion, string idCuv);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="idVivienda"></param>
        /// <param name="idOrdenVerificacion"></param>
        /// <returns></returns>
        Task<ResultadoPaginado<List<Vivienda>>> ObtenerViviendasPorOrdenesVerificacionConPaginadoFiltroAsync(int tamanioPagina, int pagina, string idVivienda, string idOrdenVerificacion);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="cuv"></param>
        /// <returns></returns>
        Task<List<Vivienda>> ObtenerViviendasReportePorOrdenesVerificacionAsync(string cuv);

    }
}
