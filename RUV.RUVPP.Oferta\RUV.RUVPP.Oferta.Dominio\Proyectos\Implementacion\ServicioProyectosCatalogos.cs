﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.ApplicationInsights;
using RUV.Comun.Negocio;
using RUV.RUVPP.Oferta.Datos.Proyectos;
using RUV.RUVPP.Oferta.Modelo.Proyectos.Data;
using RUV.RUVPP.Oferta.Modelo.Proyectos;
using RUV.RUVPP.Oferta.Modelo.Proyectos.Api;

namespace RUV.RUVPP.Oferta.Dominio.Proyectos.Implementacion
{
    public class ServicioProyectosCatalogos : ServicioDominioBase, IServicioProyectosCatalogos
    {
        #region campos

        private readonly ICatalogosProyectosDataMapper _catalogosDM;

        #endregion

        #region Constructor

        public ServicioProyectosCatalogos(ICatalogosProyectosDataMapper catalogosDM)
            : base()
        {
            this._catalogosDM = catalogosDM;
        }



        #endregion

        #region Implementacion de IservicioProyectosCatalogos

        /// <inheritdoc />
        public async Task<List<ZonaRiesgo>> ObtenerZonasDeRiesgoAsync()
        {
            return await this._catalogosDM.ObtenerCatalogoDeZonasDeRiesgoAsync();
        }

        public async Task<List<OpcionCatalogo>> ObtenerCatalogoAsync(CatalogosProyectos catalogo, int? idBusqueda)
        {
            var resultado = await this._catalogosDM.ObtenerCatalogoAsync(catalogo, idBusqueda);

            return resultado.OrderBy(c => c.valor).ToList();
        }

        /// <inheritdoc />
        public async Task<List<OpcionCatalogo>> ObtenerCatalogoPrototiposxEmpresa(int idEmpresa)
        {
            return await this._catalogosDM.ObtenerCatalogoPrototiposxEmpresasync(idEmpresa);
        }

        /// <inheritdoc />
        public async Task<List<OpcionCatalogo>> ObtenerCatalogoProyectosxEmpresa(int idEmpresa)
        {
            return await this._catalogosDM.ObtenerCatalogoProyectosxEmpresasync(idEmpresa);
        }


        /// <inheritdoc />
        public async Task<List<OpcionCatalogo>> ObtenerCatalogoVialidadesFiltradoAsync(string idEstado, string idMunicipio)
        {
            var vialidades = await this._catalogosDM.ObtenerCatalogoVialidadesFiltradoAsync(idEstado, idMunicipio);

            var vialidadesUnicas = vialidades.GroupBy(v => v.valor).Select(g => g.OrderBy(vi => vi.id).First()).ToList();

            return vialidadesUnicas.ToList();
        }

        /// <inheritdoc />
        public async Task<List<OpcionCatalogo>> ObtenerCatalogoasentamientosFiltradoAsync(string idEstado, string idMunicipio)
        {
            var asentamientos = await this._catalogosDM.ObtenerCatalogoasentamientosFiltradoAsync(idEstado, idMunicipio);

            var asentamientosUnicos = asentamientos.GroupBy(a => a.valor).Select(g => g.OrderBy(@as => @as.id).First()).ToList();

            return asentamientosUnicos.ToList();
        }

        /// <inheritdoc />
        public async Task<List<OpcionCatalogo>> ObtenerCatalogoLocalidadesFiltradoAsync(string idEstado, string idMunicipio)
        {
            var localidades = await this._catalogosDM.ObtenerCatalogoLocalidadesFiltradoAsync(idEstado.PadLeft(2, '0'), idMunicipio);

            var localidaesUnicas = localidades.GroupBy(l => l.valor).Select(g => g.OrderBy(lo => lo.id).First()).ToList();

            return localidaesUnicas.ToList();
        }

        /// <inheritdoc />
        public async Task<List<OpcionCatalogo>> ObtenerCatalogoCPFiltradoAsync(string idEstado, string idMunicipio)
        {
            return await this._catalogosDM.ObtenerCodigoPostalXEstadoMunicipio(idEstado, idMunicipio);
        }

        /// <inheritdoc />
        public async Task<List<Vialidad>> ObtenerVialidadesFiltradoAsync(string idEstado, string idMunicipio)
        {
            var vialidades = await this._catalogosDM.ObtenerVialidadesFiltradoAsync(idEstado, idMunicipio);

            var vialidadesUnicas = vialidades.GroupBy(v => v.nombreVialidad).Select(g => g.OrderBy(vi => vi.idVialidad).First()).ToList();

            return vialidadesUnicas.ToList();
        }

        /// <inheritdoc />
        public async Task<List<Asentamiento>> ObtenerAsentamientosFiltradoAsync(string idEstado, string idMunicipio)
        {
            var asentamientos = await this._catalogosDM.ObtenerAsentamientosFiltradoAsync(idEstado, idMunicipio);

            var asentamientosUnicos = asentamientos.GroupBy(a => a.nombreAsentamiento).Select(g => g.OrderBy(@as => @as.idAsentamiento).First()).ToList();

            return asentamientosUnicos.ToList();
        }

        #endregion

        #region Metodos sobreescritos


        protected override void DisposeManagedResources()
        {
            this._catalogosDM.Dispose();
        }
        
        #endregion
    }
}
