﻿using RUV.RUVPP.Oferta.Modelo.Prototipos.Api;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Dominio.Prototipo
{
    /// <summary>
    /// 
    /// </summary>
    public interface IServicioPrototipoCatalogos : IDisposable
    {
        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        Task<List<TipologiaVivienda>> ObtenerCatalogoDeTipologiaViviendaAsync();

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        Task<List<ClasificacionVivienda>> ObtenerCatalogoDeClasificacionViviendaAsync();

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        Task<List<TipoDimensionAreaVivienda>> ObtenerCatalogoTipoDimensionAreaViviendaAsync();

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        Task<List<AreaVivienda>> ObtenerCatalogoAreaViviendaAsync();
    }
}
