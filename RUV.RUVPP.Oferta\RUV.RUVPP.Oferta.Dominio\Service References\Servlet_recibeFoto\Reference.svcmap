<?xml version="1.0" encoding="utf-8"?>
<ReferenceGroup xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" ID="09b4c353-db39-4c24-82b9-3d5cd6f0bdd0" xmlns="urn:schemas-microsoft-com:xml-wcfservicemap">
  <ClientOptions>
    <GenerateAsynchronousMethods>false</GenerateAsynchronousMethods>
    <GenerateTaskBasedAsynchronousMethod>true</GenerateTaskBasedAsynchronousMethod>
    <EnableDataBinding>true</EnableDataBinding>
    <ExcludedTypes />
    <ImportXmlTypes>false</ImportXmlTypes>
    <GenerateInternalTypes>false</GenerateInternalTypes>
    <GenerateMessageContracts>false</GenerateMessageContracts>
    <NamespaceMappings />
    <CollectionMappings />
    <GenerateSerializableTypes>true</GenerateSerializableTypes>
    <Serializer>Auto</Serializer>
    <UseSerializerForFaults>true</UseSerializerForFaults>
    <ReferenceAllAssemblies>true</ReferenceAllAssemblies>
    <ReferencedAssemblies />
    <ReferencedDataContractTypes />
    <ServiceContractMappings />
  </ClientOptions>
  <MetadataSources>
    <MetadataSource Address="https://serviciosweb.infonavit.org.mx:8997/XISOAPAdapter/MessageServlet_recibeFoto_QA?wsdl" Protocol="http" SourceId="1" />
  </MetadataSources>
  <Metadata>
    <MetadataFile FileName="MessageServlet_recibeFoto_QA.wsdl" MetadataType="Wsdl" ID="ec456cad-eb14-4a72-9b40-6a03167deabb" SourceId="1" SourceUrl="https://serviciosweb.infonavit.org.mx:8997/XISOAPAdapter/MessageServlet_recibeFoto_QA?wsdl" />
    <MetadataFile FileName="MessageServlet_recibeFoto_QA.xsd" MetadataType="Schema" ID="dca4bf80-7a87-47b2-9541-8e97629654d0" SourceId="1" SourceUrl="https://serviciosweb.infonavit.org.mx:8997/XISOAPAdapter/MessageServlet_recibeFoto_QA.xsd1.xsd" />
  </Metadata>
  <Extensions>
    <ExtensionFile FileName="configuration91.svcinfo" Name="configuration91.svcinfo" />
    <ExtensionFile FileName="configuration.svcinfo" Name="configuration.svcinfo" />
  </Extensions>
</ReferenceGroup>