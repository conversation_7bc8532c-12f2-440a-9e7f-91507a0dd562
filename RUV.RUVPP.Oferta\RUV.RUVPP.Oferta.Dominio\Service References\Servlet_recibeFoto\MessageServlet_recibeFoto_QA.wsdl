<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:wsp200607="http://www.w3.org/2006/07/ws-policy" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:wsp="http://www.w3.org/ns/ws-policy" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:ns0="http://infonavit.org.mx/Avaluo/sndrecibeFoto" xmlns:wsp200409="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:soap11="http://schemas.xmlsoap.org/wsdl/soap/" targetNamespace="http://infonavit.org.mx/Avaluo/sndrecibeFoto" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types xmlns:xsd="http://www.w3.org/2001/XMLSchema">
    <xsd:schema>
      <xsd:import schemaLocation="MessageServlet_recibeFoto_QA.xsd1.xsd" namespace="http://infonavit.org.mx/Avaluo/sndrecibeFoto" />
    </xsd:schema>
  </wsdl:types>
  <wsdl:message name="MT_recibeFoto_req">
    <wsdl:part xmlns:xsns="http://infonavit.org.mx/Avaluo/sndrecibeFoto" name="MT_recibeFoto_req" element="xsns:MT_recibeFoto_req" />
  </wsdl:message>
  <wsdl:message name="MT_recibeFoto_res">
    <wsdl:part xmlns:xsns="http://infonavit.org.mx/Avaluo/sndrecibeFoto" name="MT_recibeFoto_res" element="xsns:MT_recibeFoto_res" />
  </wsdl:message>
  <wsdl:portType name="SI_recibeFoto_SO">
    <wsdl:operation name="SI_recibeFoto_SO">
      <wsdl:input name="SI_recibeFoto_SORequest" message="ns0:MT_recibeFoto_req" />
      <wsdl:output name="SI_recibeFoto_SOResponse" message="ns0:MT_recibeFoto_res" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="SI_recibeFoto_SOBinding" type="ns0:SI_recibeFoto_SO">
    <soap11:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="SI_recibeFoto_SO">
      <soap11:operation soapAction="http://sap.com/xi/WebService/soap1.1" style="document" />
      <wsdl:input name="SI_recibeFoto_SORequest">
        <soap11:body use="literal" />
      </wsdl:input>
      <wsdl:output name="SI_recibeFoto_SOResponse">
        <soap11:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="SI_recibeFoto_SOService">
    <wsdl:port name="HTTP_Port" binding="ns0:SI_recibeFoto_SOBinding">
      <soap11:address location="https://************:8997/XISOAPAdapter/MessageServlet_recibeFoto_QA" />
    </wsdl:port>
    <wsdl:port name="HTTPS_Port" binding="ns0:SI_recibeFoto_SOBinding">
      <soap11:address location="https://************:8997/XISOAPAdapter/MessageServlet_recibeFotoSec1_QA" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>