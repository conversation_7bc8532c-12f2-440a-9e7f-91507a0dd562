﻿<?xml version="1.0" encoding="utf-8"?>
<edmx:Edmx Version="3.0" xmlns:edmx="http://schemas.microsoft.com/ado/2009/11/edmx">
  <!-- EF Runtime content -->
  <edmx:Runtime>
    <!-- SSDL content -->
    <edmx:StorageModels>
    <Schema Namespace="ruvPlusDesarrolloModel.Store" Provider="System.Data.SqlClient" ProviderManifestToken="2012" Alias="Self" xmlns:store="http://schemas.microsoft.com/ado/2007/12/edm/EntityStoreSchemaGenerator" xmlns:customannotation="http://schemas.microsoft.com/ado/2013/11/edm/customannotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm/ssdl">
        <EntityType Name="DomicilioCamino">
          <Key>
            <PropertyRef Name="idDomicilioGeografico" />
          </Key>
          <Property Name="idDomicilioGeografico" Type="int" Nullable="false" />
          <Property Name="nombreVialidad" Type="varchar" MaxLength="240" Nullable="false" />
          <Property Name="origen" Type="varchar" MaxLength="255" />
          <Property Name="destino" Type="varchar" MaxLength="255" />
          <Property Name="cadenamiento" Type="varchar" MaxLength="255" />
          <Property Name="idTipoCamino" Type="tinyint" Nullable="false" />
          <Property Name="idMargen" Type="tinyint" Nullable="false" />
        </EntityType>
        <EntityType Name="DomicilioCarretera">
          <Key>
            <PropertyRef Name="idDomicilioGeografico" />
          </Key>
          <Property Name="idDomicilioGeografico" Type="int" Nullable="false" />
          <Property Name="nombreVialidad" Type="varchar" MaxLength="255" Nullable="false" />
          <Property Name="origen" Type="varchar" MaxLength="255" />
          <Property Name="destino" Type="varchar" MaxLength="255" />
          <Property Name="cadenamiento" Type="varchar" MaxLength="255" />
          <Property Name="idAdministracion" Type="tinyint" Nullable="false" />
          <Property Name="idDerechoTransito" Type="tinyint" Nullable="false" />
          <Property Name="codigo" Type="varchar" MaxLength="240" />
          <Property Name="idTipoCamino" Type="tinyint" />
        </EntityType>
        <EntityType Name="DomicilioGeografico">
          <Key>
            <PropertyRef Name="idDomicilioGeografico" />
          </Key>
          <Property Name="idDomicilioGeografico" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="idTipoDomicilioINEGI" Type="tinyint" Nullable="false" />
          <Property Name="idEstado" Type="char" MaxLength="2" Nullable="false" />
          <Property Name="idmunicipio" Type="char" MaxLength="3" Nullable="false" />
          <Property Name="idLocalidad" Type="char" MaxLength="4" Nullable="false" />
          <Property Name="idPeriodo" Type="tinyint" Nullable="false" />
          <Property Name="numeroExteriorNumerico" Type="int" />
          <Property Name="numeroExteriorAlfanumerico" Type="varchar" MaxLength="35" />
          <Property Name="numeroExteriorAnt" Type="varchar" MaxLength="35" />
          <Property Name="numeroInteriorNumerico" Type="int" />
          <Property Name="numeroInteriorAlfanumerico" Type="varchar" MaxLength="35" />
          <Property Name="idAsentamiento" Type="varchar" MaxLength="13" Nullable="false" />
          <Property Name="cp" Type="char" MaxLength="5" Nullable="false" />
          <Property Name="idVialidad1" Type="int" Nullable="false" />
          <Property Name="idVialidad2" Type="int" Nullable="false" />
          <Property Name="idVialidad3" Type="int" Nullable="false" />
          <Property Name="descripcion" Type="varchar" MaxLength="255" />
          <Property Name="domicilioGeografico" Type="varchar" MaxLength="255" />
          <Property Name="idVialidadPrincipal" Type="int" Nullable="false" />
          <Property Name="fechaRegistro" Type="datetime" />
          <Property Name="idTipoDomicilioRUV" Type="tinyint" Nullable="false" />
          <Property Name="referenciaPrevia" Type="varchar" MaxLength="16" />
          <Property Name="nombreVialidadPrincipal" Type="varchar" MaxLength="255" Nullable="false" />
          <Property Name="nombreVialidad1" Type="varchar" MaxLength="255" Nullable="false" />
          <Property Name="nombreVialidad2" Type="varchar" MaxLength="255" Nullable="false" />
          <Property Name="nombreVialidad3" Type="varchar" MaxLength="255" Nullable="false" />
          <Property Name="tipoVialidadP" Type="tinyint" />
          <Property Name="tipoVialidad1" Type="tinyint" Nullable="false" />
          <Property Name="tipoVialidad2" Type="tinyint" Nullable="false" />
          <Property Name="tipoVialidad3" Type="tinyint" Nullable="false" />
          <Property Name="latitud" Type="varchar" MaxLength="20" />
          <Property Name="longitud" Type="varchar" MaxLength="20" />
          <Property Name="nombreTipoVialidadPrincipal" Type="varchar" MaxLength="100" Nullable="false" />
          <Property Name="nombreTipoVialidad1" Type="varchar" MaxLength="100" Nullable="false" />
          <Property Name="nombreTipoVialidad2" Type="varchar" MaxLength="100" Nullable="false" />
          <Property Name="nombreTipoVialidad3" Type="varchar" MaxLength="100" Nullable="false" />
          <Property Name="nombreAsentamiento" Type="varchar" MaxLength="255" Nullable="false" />
          <Property Name="xmlSIG" Type="xml" Nullable="false" />
          <Property Name="superManzana" Type="varchar" MaxLength="20" />
          <Property Name="manzana" Type="varchar" MaxLength="20" />
          <Property Name="lote" Type="varchar" MaxLength="20" />
          <Property Name="localidad" Type="varchar" MaxLength="255" />
        </EntityType>
        <!--Errors Found During Generation:
warning 6002: The table/view 'ruvPlusDesarrollo.oferta.DetalleProyecto' does not have a primary key defined. The key has been inferred and the definition was created as a read-only table/view.-->
        <EntityType Name="DetalleProyecto">
          <Key>
            <PropertyRef Name="idProyecto" />
          </Key>
          <Property Name="idProyecto" Type="int" Nullable="false" />
          <Property Name="idConstructor" Type="int" />
          <Property Name="idPromotor" Type="int" />
          <Property Name="idVendedor" Type="int" />
          <Property Name="esZonaDeRiesgo" Type="bit" />
          <Property Name="aceptacionCartaResponsabilidad" Type="bit" />
          <Property Name="fechaRegistro" Type="smalldatetime" />
          <Property Name="fechaActualizacion" Type="smalldatetime" />
          <Property Name="folioAyto" Type="varchar" MaxLength="16" />
          <Property Name="folioSEPLADE" Type="varchar" MaxLength="16" />
          <Property Name="fechaAceptacion" Type="smalldatetime" />
        </EntityType>
        <EntityType Name="DirectorResponsableObra">
          <Key>
            <PropertyRef Name="idDRO" />
          </Key>
          <Property Name="idDRO" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="idIdentificacionOficial" Type="int" />
          <Property Name="idLicencia" Type="int" />
          <Property Name="nombreDRO" Type="varchar" MaxLength="50" Nullable="false" />
          <Property Name="apellidoPaterno" Type="varchar" MaxLength="50" Nullable="false" />
          <Property Name="apellidoMaterno" Type="varchar" MaxLength="50" Nullable="false" />
          <Property Name="numeroPerito" Type="varchar" MaxLength="20" Nullable="false" />
          <Property Name="fechaVigencia" Type="datetime" Nullable="false" />
          <Property Name="fechaRegistro" Type="smalldatetime" Nullable="false" />
          <Property Name="fechaActualizacion" Type="smalldatetime" />
          <Property Name="activo" Type="bit" Nullable="false" />
        </EntityType>
        <EntityType Name="DirectorResponsableObraxProyecto">
          <Key>
            <PropertyRef Name="idDRO" />
            <PropertyRef Name="idProyecto" />
          </Key>
          <Property Name="idDRO" Type="int" Nullable="false" />
          <Property Name="idProyecto" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="DocumentoxProyecto">
          <Key>
            <PropertyRef Name="idProyecto" />
            <PropertyRef Name="idDocumento" />
          </Key>
          <Property Name="idProyecto" Type="int" Nullable="false" />
          <Property Name="idDocumento" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="PromotorExterno">
          <Key>
            <PropertyRef Name="idPromotorExterno" />
          </Key>
          <Property Name="idPromotorExterno" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="numeroRegistroRUV" Type="char" MaxLength="8" />
          <Property Name="nombreRazonSocial" Type="varchar" MaxLength="255" Nullable="false" />
          <Property Name="representanteLegal" Type="varchar" MaxLength="100" />
          <Property Name="lada" Type="varchar" MaxLength="7" />
          <Property Name="numeroTelefono" Type="varchar" MaxLength="15" />
          <Property Name="correoElectronico" Type="varchar" MaxLength="255" Nullable="false" />
          <Property Name="registroPatronal" Type="varchar" MaxLength="11" />
          <Property Name="fechaRegistro" Type="smalldatetime" Nullable="false" />
          <Property Name="fechaActualizacion" Type="smalldatetime" />
          <Property Name="activo" Type="bit" Nullable="false" />
          <Property Name="esVendedor" Type="bit" Nullable="false" />
          <Property Name="rfc" Type="varchar" MaxLength="13" Nullable="false" />
        </EntityType>
        <EntityType Name="PromotorExternoxProyecto">
          <Key>
            <PropertyRef Name="idPromotorExterno" />
            <PropertyRef Name="idProyecto" />
          </Key>
          <Property Name="idPromotorExterno" Type="int" Nullable="false" />
          <Property Name="idProyecto" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="PropietarioTerreno">
          <Key>
            <PropertyRef Name="idPropietarioTerreno" />
          </Key>
          <Property Name="idPropietarioTerreno" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="nombrePropietario" Type="varchar" MaxLength="80" Nullable="false" />
          <Property Name="numeroRPP" Type="varchar" MaxLength="8" Nullable="false" />
          <Property Name="numeroCatastral" Type="varchar" MaxLength="30" Nullable="false" />
          <Property Name="areaTerrenoEscriturado" Type="numeric" Precision="8" Scale="2" Nullable="false" />
          <Property Name="numeroEscritura" Type="varchar" MaxLength="15" Nullable="false" />
          <Property Name="tomo" Type="varchar" MaxLength="10" Nullable="false" />
          <Property Name="volumen" Type="varchar" MaxLength="15" Nullable="false" />
          <Property Name="fechaEscrituracion" Type="smalldatetime" Nullable="false" />
          <Property Name="numeroNotario" Type="int" Nullable="false" />
          <Property Name="fechaRegistro" Type="smalldatetime" Nullable="false" />
          <Property Name="fechaActualizacion" Type="smalldatetime" />
          <Property Name="activo" Type="bit" Nullable="false" />
        </EntityType>
        <EntityType Name="PropietarioTerrenoxProyecto">
          <Key>
            <PropertyRef Name="idPropietarioTerreno" />
            <PropertyRef Name="idProyecto" />
          </Key>
          <Property Name="idPropietarioTerreno" Type="int" Nullable="false" />
          <Property Name="idProyecto" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="Proyecto">
          <Key>
            <PropertyRef Name="idProyecto" />
          </Key>
          <Property Name="idProyecto" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="idEstatusProyecto" Type="tinyint" />
          <Property Name="idEmpresa" Type="int" Nullable="false" />
          <Property Name="nombre" Type="varchar" MaxLength="80" Nullable="false" />
          <Property Name="temporalJSON" Type="nvarchar(max)" />
          <Property Name="fechaRegistro" Type="smalldatetime" />
          <Property Name="fechaActualizacion" Type="smalldatetime" />
          <Property Name="activo" Type="bit" />
        </EntityType>
        <EntityType Name="ProyectoxRiesgoOferta">
          <Key>
            <PropertyRef Name="idRiesgoOferta" />
            <PropertyRef Name="idProyecto" />
          </Key>
          <Property Name="idRiesgoOferta" Type="tinyint" Nullable="false" />
          <Property Name="idProyecto" Type="int" Nullable="false" />
          <Property Name="solucionMitigarRiesgo" Type="varchar" MaxLength="500" Nullable="false" />
          <Property Name="fechaRegistro" Type="smalldatetime" Nullable="false" />
          <Property Name="fechaActualizacion" Type="smalldatetime" />
        </EntityType>
        <EntityType Name="Vivienda">
          <Key>
            <PropertyRef Name="idVivienda" />
          </Key>
          <Property Name="idVivienda" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="idProyecto" Type="int" Nullable="false" />
          <Property Name="idOfertaVivienda" Type="int" />
          <Property Name="idEstatusVivienda" Type="tinyint" />
          <Property Name="idPrototipo" Type="int" Nullable="false" />
          <Property Name="idOrientacionVivienda" Type="tinyint" Nullable="false" />
          <Property Name="idNivelVivienda" Type="tinyint" Nullable="false" />
          <Property Name="idTipoZonaVivienda" Type="tinyint" Nullable="false" />
          <Property Name="idDomicilioGeografico" Type="int" />
          <Property Name="identificadorVivienda" Type="smallint" Nullable="false" />
          <Property Name="numeroCatastralLote" Type="varchar" MaxLength="10" />
          <Property Name="edificio" Type="varchar" MaxLength="60" />
          <Property Name="planta" Type="varchar" MaxLength="15" Nullable="false" />
          <Property Name="costo" Type="numeric" Precision="12" Scale="2" />
          <Property Name="metros2Lote" Type="numeric" Precision="6" Scale="2" />
          <Property Name="metrosFrenteLote" Type="numeric" Precision="6" Scale="2" />
          <Property Name="cuv" Type="varchar" MaxLength="16" />
          <Property Name="fechaRegistro" Type="smalldatetime" Nullable="false" />
          <Property Name="fechaActualizacion" Type="smalldatetime" />
          <Property Name="nombreCondominio" Type="varchar" MaxLength="50" />
          <Property Name="cuvGeografica" Type="varchar" MaxLength="15" />
          <Property Name="idTipoAsentamiento" Type="int" />
          <Property Name="idEstatusJuridicoVivienda" Type="tinyint" />
          <Property Name="idModalidadVivienda" Type="tinyint" />
          <Property Name="idTipoAportacionVivienda" Type="tinyint" />
          <Property Name="idTipoProgramaVivienda" Type="tinyint" />
          <Property Name="featId" Type="int" />
          <Property Name="numeroEstacionamientos" Type="tinyint" />
          <Property Name="idViviendaPlanoSIG" Type="int" />
        </EntityType>
        <Association Name="fk_dirINEGI_DomicilioCamino_DomicilioGeografico">
          <End Role="DomicilioGeografico" Type="Self.DomicilioGeografico" Multiplicity="1" />
          <End Role="DomicilioCamino" Type="Self.DomicilioCamino" Multiplicity="0..1" />
          <ReferentialConstraint>
            <Principal Role="DomicilioGeografico">
              <PropertyRef Name="idDomicilioGeografico" />
            </Principal>
            <Dependent Role="DomicilioCamino">
              <PropertyRef Name="idDomicilioGeografico" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="fk_dirINEGI_DomicilioCarretera_DomicilioGeografico">
          <End Role="DomicilioGeografico" Type="Self.DomicilioGeografico" Multiplicity="1" />
          <End Role="DomicilioCarretera" Type="Self.DomicilioCarretera" Multiplicity="0..1" />
          <ReferentialConstraint>
            <Principal Role="DomicilioGeografico">
              <PropertyRef Name="idDomicilioGeografico" />
            </Principal>
            <Dependent Role="DomicilioCarretera">
              <PropertyRef Name="idDomicilioGeografico" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_dirINEGI_DomicilioGeografico_oferta_Vivienda">
          <End Role="DomicilioGeografico" Type="Self.DomicilioGeografico" Multiplicity="0..1" />
          <End Role="Vivienda" Type="Self.Vivienda" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="DomicilioGeografico">
              <PropertyRef Name="idDomicilioGeografico" />
            </Principal>
            <Dependent Role="Vivienda">
              <PropertyRef Name="idDomicilioGeografico" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_oferta_DirectorResponsableObraxProyecto_DirectorResponsableObra">
          <End Role="DirectorResponsableObra" Type="Self.DirectorResponsableObra" Multiplicity="1" />
          <End Role="DirectorResponsableObraxProyecto" Type="Self.DirectorResponsableObraxProyecto" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="DirectorResponsableObra">
              <PropertyRef Name="idDRO" />
            </Principal>
            <Dependent Role="DirectorResponsableObraxProyecto">
              <PropertyRef Name="idDRO" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_oferta_DocumentoxProyecto_Proyecto">
          <End Role="Proyecto" Type="Self.Proyecto" Multiplicity="1" />
          <End Role="DocumentoxProyecto" Type="Self.DocumentoxProyecto" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Proyecto">
              <PropertyRef Name="idProyecto" />
            </Principal>
            <Dependent Role="DocumentoxProyecto">
              <PropertyRef Name="idProyecto" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_oferta_PromotorExternoxProyecto_PromotorExterno">
          <End Role="PromotorExterno" Type="Self.PromotorExterno" Multiplicity="1" />
          <End Role="PromotorExternoxProyecto" Type="Self.PromotorExternoxProyecto" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="PromotorExterno">
              <PropertyRef Name="idPromotorExterno" />
            </Principal>
            <Dependent Role="PromotorExternoxProyecto">
              <PropertyRef Name="idPromotorExterno" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_oferta_PropietarioTerrenoxProyecto_PropietarioTerreno">
          <End Role="PropietarioTerreno" Type="Self.PropietarioTerreno" Multiplicity="1" />
          <End Role="PropietarioTerrenoxProyecto" Type="Self.PropietarioTerrenoxProyecto" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="PropietarioTerreno">
              <PropertyRef Name="idPropietarioTerreno" />
            </Principal>
            <Dependent Role="PropietarioTerrenoxProyecto">
              <PropertyRef Name="idPropietarioTerreno" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_oferta_Proyecto_DetalleProyecto">
          <End Role="Proyecto" Type="Self.Proyecto" Multiplicity="1" />
          <End Role="DetalleProyecto" Type="Self.DetalleProyecto" Multiplicity="0..1" />
          <ReferentialConstraint>
            <Principal Role="Proyecto">
              <PropertyRef Name="idProyecto" />
            </Principal>
            <Dependent Role="DetalleProyecto">
              <PropertyRef Name="idProyecto" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_oferta_Proyecto_DirectorResponsableObraxProyecto">
          <End Role="Proyecto" Type="Self.Proyecto" Multiplicity="1" />
          <End Role="DirectorResponsableObraxProyecto" Type="Self.DirectorResponsableObraxProyecto" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Proyecto">
              <PropertyRef Name="idProyecto" />
            </Principal>
            <Dependent Role="DirectorResponsableObraxProyecto">
              <PropertyRef Name="idProyecto" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_oferta_Proyecto_PromotorExternoxProyecto">
          <End Role="Proyecto" Type="Self.Proyecto" Multiplicity="1" />
          <End Role="PromotorExternoxProyecto" Type="Self.PromotorExternoxProyecto" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Proyecto">
              <PropertyRef Name="idProyecto" />
            </Principal>
            <Dependent Role="PromotorExternoxProyecto">
              <PropertyRef Name="idProyecto" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_oferta_Proyecto_PropietarioTerrenoxProyecto">
          <End Role="Proyecto" Type="Self.Proyecto" Multiplicity="1" />
          <End Role="PropietarioTerrenoxProyecto" Type="Self.PropietarioTerrenoxProyecto" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Proyecto">
              <PropertyRef Name="idProyecto" />
            </Principal>
            <Dependent Role="PropietarioTerrenoxProyecto">
              <PropertyRef Name="idProyecto" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_oferta_Proyecto_Vivienda">
          <End Role="Proyecto" Type="Self.Proyecto" Multiplicity="1" />
          <End Role="Vivienda" Type="Self.Vivienda" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Proyecto">
              <PropertyRef Name="idProyecto" />
            </Principal>
            <Dependent Role="Vivienda">
              <PropertyRef Name="idProyecto" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_oferta_ProyectoxRiesgoOferta_Proyecto">
          <End Role="Proyecto" Type="Self.Proyecto" Multiplicity="1" />
          <End Role="ProyectoxRiesgoOferta" Type="Self.ProyectoxRiesgoOferta" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Proyecto">
              <PropertyRef Name="idProyecto" />
            </Principal>
            <Dependent Role="ProyectoxRiesgoOferta">
              <PropertyRef Name="idProyecto" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityContainer Name="ruvPlusDesarrolloModelStoreContainer">
          <EntitySet Name="DomicilioCamino" EntityType="Self.DomicilioCamino" Schema="dirINEGI" store:Type="Tables" />
          <EntitySet Name="DomicilioCarretera" EntityType="Self.DomicilioCarretera" Schema="dirINEGI" store:Type="Tables" />
          <EntitySet Name="DomicilioGeografico" EntityType="Self.DomicilioGeografico" Schema="dirINEGI" store:Type="Tables" />
          <EntitySet Name="DirectorResponsableObra" EntityType="Self.DirectorResponsableObra" Schema="oferta" store:Type="Tables" />
          <EntitySet Name="DirectorResponsableObraxProyecto" EntityType="Self.DirectorResponsableObraxProyecto" Schema="oferta" store:Type="Tables" />
          <EntitySet Name="DocumentoxProyecto" EntityType="Self.DocumentoxProyecto" Schema="oferta" store:Type="Tables" />
          <EntitySet Name="PromotorExterno" EntityType="Self.PromotorExterno" Schema="oferta" store:Type="Tables" />
          <EntitySet Name="PromotorExternoxProyecto" EntityType="Self.PromotorExternoxProyecto" Schema="oferta" store:Type="Tables" />
          <EntitySet Name="PropietarioTerreno" EntityType="Self.PropietarioTerreno" Schema="oferta" store:Type="Tables" />
          <EntitySet Name="PropietarioTerrenoxProyecto" EntityType="Self.PropietarioTerrenoxProyecto" Schema="oferta" store:Type="Tables" />
          <EntitySet Name="Proyecto" EntityType="Self.Proyecto" Schema="oferta" store:Type="Tables" />
          <EntitySet Name="ProyectoxRiesgoOferta" EntityType="Self.ProyectoxRiesgoOferta" Schema="oferta" store:Type="Tables" />
          <EntitySet Name="Vivienda" EntityType="Self.Vivienda" Schema="oferta" store:Type="Tables" />
          <EntitySet Name="DetalleProyecto" EntityType="Self.DetalleProyecto" store:Type="Tables" store:Schema="oferta">
            <DefiningQuery>SELECT 
    [DetalleProyecto].[idProyecto] AS [idProyecto], 
    [DetalleProyecto].[idConstructor] AS [idConstructor], 
    [DetalleProyecto].[idPromotor] AS [idPromotor], 
    [DetalleProyecto].[idVendedor] AS [idVendedor], 
    [DetalleProyecto].[esZonaDeRiesgo] AS [esZonaDeRiesgo], 
    [DetalleProyecto].[aceptacionCartaResponsabilidad] AS [aceptacionCartaResponsabilidad], 
    [DetalleProyecto].[fechaRegistro] AS [fechaRegistro], 
    [DetalleProyecto].[fechaActualizacion] AS [fechaActualizacion], 
    [DetalleProyecto].[folioAyto] AS [folioAyto], 
    [DetalleProyecto].[folioSEPLADE] AS [folioSEPLADE], 
    [DetalleProyecto].[fechaAceptacion] AS [fechaAceptacion]
    FROM [oferta].[DetalleProyecto] AS [DetalleProyecto]</DefiningQuery>
          </EntitySet>
          <AssociationSet Name="fk_dirINEGI_DomicilioCamino_DomicilioGeografico" Association="Self.fk_dirINEGI_DomicilioCamino_DomicilioGeografico">
            <End Role="DomicilioGeografico" EntitySet="DomicilioGeografico" />
            <End Role="DomicilioCamino" EntitySet="DomicilioCamino" />
          </AssociationSet>
          <AssociationSet Name="fk_dirINEGI_DomicilioCarretera_DomicilioGeografico" Association="Self.fk_dirINEGI_DomicilioCarretera_DomicilioGeografico">
            <End Role="DomicilioGeografico" EntitySet="DomicilioGeografico" />
            <End Role="DomicilioCarretera" EntitySet="DomicilioCarretera" />
          </AssociationSet>
          <AssociationSet Name="FK_dirINEGI_DomicilioGeografico_oferta_Vivienda" Association="Self.FK_dirINEGI_DomicilioGeografico_oferta_Vivienda">
            <End Role="DomicilioGeografico" EntitySet="DomicilioGeografico" />
            <End Role="Vivienda" EntitySet="Vivienda" />
          </AssociationSet>
          <AssociationSet Name="FK_oferta_DirectorResponsableObraxProyecto_DirectorResponsableObra" Association="Self.FK_oferta_DirectorResponsableObraxProyecto_DirectorResponsableObra">
            <End Role="DirectorResponsableObra" EntitySet="DirectorResponsableObra" />
            <End Role="DirectorResponsableObraxProyecto" EntitySet="DirectorResponsableObraxProyecto" />
          </AssociationSet>
          <AssociationSet Name="FK_oferta_DocumentoxProyecto_Proyecto" Association="Self.FK_oferta_DocumentoxProyecto_Proyecto">
            <End Role="Proyecto" EntitySet="Proyecto" />
            <End Role="DocumentoxProyecto" EntitySet="DocumentoxProyecto" />
          </AssociationSet>
          <AssociationSet Name="FK_oferta_PromotorExternoxProyecto_PromotorExterno" Association="Self.FK_oferta_PromotorExternoxProyecto_PromotorExterno">
            <End Role="PromotorExterno" EntitySet="PromotorExterno" />
            <End Role="PromotorExternoxProyecto" EntitySet="PromotorExternoxProyecto" />
          </AssociationSet>
          <AssociationSet Name="FK_oferta_PropietarioTerrenoxProyecto_PropietarioTerreno" Association="Self.FK_oferta_PropietarioTerrenoxProyecto_PropietarioTerreno">
            <End Role="PropietarioTerreno" EntitySet="PropietarioTerreno" />
            <End Role="PropietarioTerrenoxProyecto" EntitySet="PropietarioTerrenoxProyecto" />
          </AssociationSet>
          <AssociationSet Name="FK_oferta_Proyecto_DetalleProyecto" Association="Self.FK_oferta_Proyecto_DetalleProyecto">
            <End Role="Proyecto" EntitySet="Proyecto" />
            <End Role="DetalleProyecto" EntitySet="DetalleProyecto" />
          </AssociationSet>
          <AssociationSet Name="FK_oferta_Proyecto_DirectorResponsableObraxProyecto" Association="Self.FK_oferta_Proyecto_DirectorResponsableObraxProyecto">
            <End Role="Proyecto" EntitySet="Proyecto" />
            <End Role="DirectorResponsableObraxProyecto" EntitySet="DirectorResponsableObraxProyecto" />
          </AssociationSet>
          <AssociationSet Name="FK_oferta_Proyecto_PromotorExternoxProyecto" Association="Self.FK_oferta_Proyecto_PromotorExternoxProyecto">
            <End Role="Proyecto" EntitySet="Proyecto" />
            <End Role="PromotorExternoxProyecto" EntitySet="PromotorExternoxProyecto" />
          </AssociationSet>
          <AssociationSet Name="FK_oferta_Proyecto_PropietarioTerrenoxProyecto" Association="Self.FK_oferta_Proyecto_PropietarioTerrenoxProyecto">
            <End Role="Proyecto" EntitySet="Proyecto" />
            <End Role="PropietarioTerrenoxProyecto" EntitySet="PropietarioTerrenoxProyecto" />
          </AssociationSet>
          <AssociationSet Name="FK_oferta_Proyecto_Vivienda" Association="Self.FK_oferta_Proyecto_Vivienda">
            <End Role="Proyecto" EntitySet="Proyecto" />
            <End Role="Vivienda" EntitySet="Vivienda" />
          </AssociationSet>
          <AssociationSet Name="FK_oferta_ProyectoxRiesgoOferta_Proyecto" Association="Self.FK_oferta_ProyectoxRiesgoOferta_Proyecto">
            <End Role="Proyecto" EntitySet="Proyecto" />
            <End Role="ProyectoxRiesgoOferta" EntitySet="ProyectoxRiesgoOferta" />
          </AssociationSet>
        </EntityContainer>
      </Schema></edmx:StorageModels>
    <!-- CSDL content -->
    <edmx:ConceptualModels>
      <Schema Namespace="ruvPlusDesarrolloModel" Alias="Self" annotation:UseStrongSpatialTypes="false" xmlns:annotation="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns:customannotation="http://schemas.microsoft.com/ado/2013/11/edm/customannotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm">
        <EntityType Name="DomicilioCamino">
          <Key>
            <PropertyRef Name="idDomicilioGeografico" />
          </Key>
          <Property Name="idDomicilioGeografico" Type="Int32" Nullable="false" />
          <Property Name="nombreVialidad" Type="String" MaxLength="240" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="origen" Type="String" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Name="destino" Type="String" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Name="cadenamiento" Type="String" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Name="idTipoCamino" Type="Byte" Nullable="false" />
          <Property Name="idMargen" Type="Byte" Nullable="false" />
          <NavigationProperty Name="DomicilioGeografico" Relationship="Self.fk_dirINEGI_DomicilioCamino_DomicilioGeografico" FromRole="DomicilioCamino" ToRole="DomicilioGeografico" />
        </EntityType>
        <EntityType Name="DomicilioCarretera">
          <Key>
            <PropertyRef Name="idDomicilioGeografico" />
          </Key>
          <Property Name="idDomicilioGeografico" Type="Int32" Nullable="false" />
          <Property Name="nombreVialidad" Type="String" MaxLength="255" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="origen" Type="String" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Name="destino" Type="String" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Name="cadenamiento" Type="String" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Name="idAdministracion" Type="Byte" Nullable="false" />
          <Property Name="idDerechoTransito" Type="Byte" Nullable="false" />
          <Property Name="codigo" Type="String" MaxLength="240" FixedLength="false" Unicode="false" />
          <Property Name="idTipoCamino" Type="Byte" />
          <NavigationProperty Name="DomicilioGeografico" Relationship="Self.fk_dirINEGI_DomicilioCarretera_DomicilioGeografico" FromRole="DomicilioCarretera" ToRole="DomicilioGeografico" />
        </EntityType>
        <EntityType Name="DomicilioGeografico">
          <Key>
            <PropertyRef Name="idDomicilioGeografico" />
          </Key>
          <Property Name="idDomicilioGeografico" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="idTipoDomicilioINEGI" Type="Byte" Nullable="false" />
          <Property Name="idEstado" Type="String" MaxLength="2" FixedLength="true" Unicode="false" Nullable="false" />
          <Property Name="idmunicipio" Type="String" MaxLength="3" FixedLength="true" Unicode="false" Nullable="false" />
          <Property Name="idLocalidad" Type="String" MaxLength="4" FixedLength="true" Unicode="false" Nullable="false" />
          <Property Name="idPeriodo" Type="Byte" Nullable="false" />
          <Property Name="numeroExteriorNumerico" Type="Int32" />
          <Property Name="numeroExteriorAlfanumerico" Type="String" MaxLength="35" FixedLength="false" Unicode="false" />
          <Property Name="numeroExteriorAnt" Type="String" MaxLength="35" FixedLength="false" Unicode="false" />
          <Property Name="numeroInteriorNumerico" Type="Int32" />
          <Property Name="numeroInteriorAlfanumerico" Type="String" MaxLength="35" FixedLength="false" Unicode="false" />
          <Property Name="idAsentamiento" Type="String" MaxLength="13" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="cp" Type="String" MaxLength="5" FixedLength="true" Unicode="false" Nullable="false" />
          <Property Name="idVialidad1" Type="Int32" Nullable="false" />
          <Property Name="idVialidad2" Type="Int32" Nullable="false" />
          <Property Name="idVialidad3" Type="Int32" Nullable="false" />
          <Property Name="descripcion" Type="String" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Name="domicilioGeografico1" Type="String" MaxLength="255" FixedLength="false" Unicode="false" />
          <Property Name="idVialidadPrincipal" Type="Int32" Nullable="false" />
          <Property Name="fechaRegistro" Type="DateTime" Precision="3" />
          <Property Name="idTipoDomicilioRUV" Type="Byte" Nullable="false" />
          <Property Name="referenciaPrevia" Type="String" MaxLength="16" FixedLength="false" Unicode="false" />
          <Property Name="nombreVialidadPrincipal" Type="String" MaxLength="255" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="nombreVialidad1" Type="String" MaxLength="255" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="nombreVialidad2" Type="String" MaxLength="255" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="nombreVialidad3" Type="String" MaxLength="255" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="tipoVialidadP" Type="Byte" />
          <Property Name="tipoVialidad1" Type="Byte" Nullable="false" />
          <Property Name="tipoVialidad2" Type="Byte" Nullable="false" />
          <Property Name="tipoVialidad3" Type="Byte" Nullable="false" />
          <Property Name="latitud" Type="String" MaxLength="20" FixedLength="false" Unicode="false" />
          <Property Name="longitud" Type="String" MaxLength="20" FixedLength="false" Unicode="false" />
          <Property Name="nombreTipoVialidadPrincipal" Type="String" MaxLength="100" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="nombreTipoVialidad1" Type="String" MaxLength="100" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="nombreTipoVialidad2" Type="String" MaxLength="100" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="nombreTipoVialidad3" Type="String" MaxLength="100" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="nombreAsentamiento" Type="String" MaxLength="255" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="xmlSIG" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="superManzana" Type="String" MaxLength="20" FixedLength="false" Unicode="false" />
          <Property Name="manzana" Type="String" MaxLength="20" FixedLength="false" Unicode="false" />
          <Property Name="lote" Type="String" MaxLength="20" FixedLength="false" Unicode="false" />
          <Property Name="localidad" Type="String" MaxLength="255" FixedLength="false" Unicode="false" />
          <NavigationProperty Name="DomicilioCamino" Relationship="Self.fk_dirINEGI_DomicilioCamino_DomicilioGeografico" FromRole="DomicilioGeografico" ToRole="DomicilioCamino" />
          <NavigationProperty Name="DomicilioCarretera" Relationship="Self.fk_dirINEGI_DomicilioCarretera_DomicilioGeografico" FromRole="DomicilioGeografico" ToRole="DomicilioCarretera" />
          <NavigationProperty Name="Vivienda" Relationship="Self.FK_dirINEGI_DomicilioGeografico_oferta_Vivienda" FromRole="DomicilioGeografico" ToRole="Vivienda" />
        </EntityType>
        <EntityType Name="DirectorResponsableObra">
          <Key>
            <PropertyRef Name="idDRO" />
          </Key>
          <Property Name="idDRO" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="idIdentificacionOficial" Type="Int32" />
          <Property Name="idLicencia" Type="Int32" />
          <Property Name="nombreDRO" Type="String" MaxLength="50" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="apellidoPaterno" Type="String" MaxLength="50" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="apellidoMaterno" Type="String" MaxLength="50" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="numeroPerito" Type="String" MaxLength="20" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="fechaVigencia" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="fechaRegistro" Type="DateTime" Nullable="false" Precision="0" />
          <Property Name="fechaActualizacion" Type="DateTime" Precision="0" />
          <Property Name="activo" Type="Boolean" Nullable="false" />
          <NavigationProperty Name="Proyecto" Relationship="Self.DirectorResponsableObraxProyecto" FromRole="DirectorResponsableObra" ToRole="Proyecto" />
        </EntityType>
        <EntityType Name="DocumentoxProyecto">
          <Key>
            <PropertyRef Name="idProyecto" />
            <PropertyRef Name="idDocumento" />
          </Key>
          <Property Name="idProyecto" Type="Int32" Nullable="false" />
          <Property Name="idDocumento" Type="Int32" Nullable="false" />
          <NavigationProperty Name="Proyecto" Relationship="Self.FK_oferta_DocumentoxProyecto_Proyecto" FromRole="DocumentoxProyecto" ToRole="Proyecto" />
        </EntityType>
        <EntityType Name="PromotorExterno">
          <Key>
            <PropertyRef Name="idPromotorExterno" />
          </Key>
          <Property Name="idPromotorExterno" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="numeroRegistroRUV" Type="String" MaxLength="8" FixedLength="true" Unicode="false" />
          <Property Name="nombreRazonSocial" Type="String" MaxLength="255" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="representanteLegal" Type="String" MaxLength="100" FixedLength="false" Unicode="false" />
          <Property Name="lada" Type="String" MaxLength="7" FixedLength="false" Unicode="false" />
          <Property Name="numeroTelefono" Type="String" MaxLength="15" FixedLength="false" Unicode="false" />
          <Property Name="correoElectronico" Type="String" MaxLength="255" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="registroPatronal" Type="String" MaxLength="11" FixedLength="false" Unicode="false" />
          <Property Name="fechaRegistro" Type="DateTime" Nullable="false" Precision="0" />
          <Property Name="fechaActualizacion" Type="DateTime" Precision="0" />
          <Property Name="activo" Type="Boolean" Nullable="false" />
          <Property Name="esVendedor" Type="Boolean" Nullable="false" />
          <Property Name="rfc" Type="String" MaxLength="13" FixedLength="false" Unicode="false" Nullable="false" />
          <NavigationProperty Name="Proyecto" Relationship="Self.PromotorExternoxProyecto" FromRole="PromotorExterno" ToRole="Proyecto" />
        </EntityType>
        <EntityType Name="PropietarioTerreno">
          <Key>
            <PropertyRef Name="idPropietarioTerreno" />
          </Key>
          <Property Name="idPropietarioTerreno" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="nombrePropietario" Type="String" MaxLength="80" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="numeroRPP" Type="String" MaxLength="8" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="numeroCatastral" Type="String" MaxLength="30" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="areaTerrenoEscriturado" Type="Decimal" Precision="8" Scale="2" Nullable="false" />
          <Property Name="numeroEscritura" Type="String" MaxLength="15" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="tomo" Type="String" MaxLength="10" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="volumen" Type="String" MaxLength="15" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="fechaEscrituracion" Type="DateTime" Nullable="false" Precision="0" />
          <Property Name="numeroNotario" Type="Int32" Nullable="false" />
          <Property Name="fechaRegistro" Type="DateTime" Nullable="false" Precision="0" />
          <Property Name="fechaActualizacion" Type="DateTime" Precision="0" />
          <Property Name="activo" Type="Boolean" Nullable="false" />
          <NavigationProperty Name="Proyecto" Relationship="Self.PropietarioTerrenoxProyecto" FromRole="PropietarioTerreno" ToRole="Proyecto" />
        </EntityType>
        <EntityType Name="Proyecto">
          <Key>
            <PropertyRef Name="idProyecto" />
          </Key>
          <Property Name="idProyecto" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="idEstatusProyecto" Type="Byte" />
          <Property Name="idEmpresa" Type="Int32" Nullable="false" />
          <Property Name="nombre" Type="String" MaxLength="80" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="temporalJSON" Type="String" MaxLength="Max" FixedLength="false" Unicode="true" />
          <Property Name="fechaRegistro" Type="DateTime" Precision="0" />
          <Property Name="fechaActualizacion" Type="DateTime" Precision="0" />
          <Property Name="activo" Type="Boolean" />
          <NavigationProperty Name="DocumentoxProyecto" Relationship="Self.FK_oferta_DocumentoxProyecto_Proyecto" FromRole="Proyecto" ToRole="DocumentoxProyecto" />
          <NavigationProperty Name="DetalleProyecto" Relationship="Self.FK_oferta_Proyecto_DetalleProyecto" FromRole="Proyecto" ToRole="DetalleProyecto" />
          <NavigationProperty Name="Vivienda" Relationship="Self.FK_oferta_Proyecto_Vivienda" FromRole="Proyecto" ToRole="Vivienda" />
          <NavigationProperty Name="ProyectoxRiesgoOferta" Relationship="Self.FK_oferta_ProyectoxRiesgoOferta_Proyecto" FromRole="Proyecto" ToRole="ProyectoxRiesgoOferta" />
          <NavigationProperty Name="DirectorResponsableObra" Relationship="Self.DirectorResponsableObraxProyecto" FromRole="Proyecto" ToRole="DirectorResponsableObra" />
          <NavigationProperty Name="PromotorExterno" Relationship="Self.PromotorExternoxProyecto" FromRole="Proyecto" ToRole="PromotorExterno" />
          <NavigationProperty Name="PropietarioTerreno" Relationship="Self.PropietarioTerrenoxProyecto" FromRole="Proyecto" ToRole="PropietarioTerreno" />
        </EntityType>
        <EntityType Name="ProyectoxRiesgoOferta">
          <Key>
            <PropertyRef Name="idRiesgoOferta" />
            <PropertyRef Name="idProyecto" />
          </Key>
          <Property Name="idRiesgoOferta" Type="Byte" Nullable="false" />
          <Property Name="idProyecto" Type="Int32" Nullable="false" />
          <Property Name="solucionMitigarRiesgo" Type="String" MaxLength="500" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="fechaRegistro" Type="DateTime" Nullable="false" Precision="0" />
          <Property Name="fechaActualizacion" Type="DateTime" Precision="0" />
          <NavigationProperty Name="Proyecto" Relationship="Self.FK_oferta_ProyectoxRiesgoOferta_Proyecto" FromRole="ProyectoxRiesgoOferta" ToRole="Proyecto" />
        </EntityType>
        <EntityType Name="Vivienda">
          <Key>
            <PropertyRef Name="idVivienda" />
          </Key>
          <Property Name="idVivienda" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="idProyecto" Type="Int32" Nullable="false" />
          <Property Name="idOfertaVivienda" Type="Int32" />
          <Property Name="idEstatusVivienda" Type="Byte" />
          <Property Name="idPrototipo" Type="Int32" Nullable="false" />
          <Property Name="idOrientacionVivienda" Type="Byte" Nullable="false" />
          <Property Name="idNivelVivienda" Type="Byte" Nullable="false" />
          <Property Name="idTipoZonaVivienda" Type="Byte" Nullable="false" />
          <Property Name="idDomicilioGeografico" Type="Int32" />
          <Property Name="identificadorVivienda" Type="Int16" Nullable="false" />
          <Property Name="numeroCatastralLote" Type="String" MaxLength="10" FixedLength="false" Unicode="false" />
          <Property Name="edificio" Type="String" MaxLength="60" FixedLength="false" Unicode="false" />
          <Property Name="planta" Type="String" MaxLength="15" FixedLength="false" Unicode="false" Nullable="false" />
          <Property Name="costo" Type="Decimal" Precision="12" Scale="2" />
          <Property Name="metros2Lote" Type="Decimal" Precision="6" Scale="2" />
          <Property Name="metrosFrenteLote" Type="Decimal" Precision="6" Scale="2" />
          <Property Name="cuv" Type="String" MaxLength="16" FixedLength="false" Unicode="false" />
          <Property Name="fechaRegistro" Type="DateTime" Nullable="false" Precision="0" />
          <Property Name="fechaActualizacion" Type="DateTime" Precision="0" />
          <Property Name="nombreCondominio" Type="String" MaxLength="50" FixedLength="false" Unicode="false" />
          <Property Name="cuvGeografica" Type="String" MaxLength="15" FixedLength="false" Unicode="false" />
          <Property Name="idTipoAsentamiento" Type="Int32" />
          <Property Name="idEstatusJuridicoVivienda" Type="Byte" />
          <Property Name="idModalidadVivienda" Type="Byte" />
          <Property Name="idTipoAportacionVivienda" Type="Byte" />
          <Property Name="idTipoProgramaVivienda" Type="Byte" />
          <Property Name="featId" Type="Int32" />
          <Property Name="numeroEstacionamientos" Type="Byte" />
          <Property Name="idViviendaPlanoSIG" Type="Int32" />
          <NavigationProperty Name="DomicilioGeografico" Relationship="Self.FK_dirINEGI_DomicilioGeografico_oferta_Vivienda" FromRole="Vivienda" ToRole="DomicilioGeografico" />
          <NavigationProperty Name="Proyecto" Relationship="Self.FK_oferta_Proyecto_Vivienda" FromRole="Vivienda" ToRole="Proyecto" />
        </EntityType>
        <EntityType Name="DetalleProyecto">
          <Key>
            <PropertyRef Name="idProyecto" />
          </Key>
          <Property Name="idProyecto" Type="Int32" Nullable="false" />
          <Property Name="idConstructor" Type="Int32" />
          <Property Name="idPromotor" Type="Int32" />
          <Property Name="idVendedor" Type="Int32" />
          <Property Name="esZonaDeRiesgo" Type="Boolean" />
          <Property Name="aceptacionCartaResponsabilidad" Type="Boolean" />
          <Property Name="fechaRegistro" Type="DateTime" Precision="0" />
          <Property Name="fechaActualizacion" Type="DateTime" Precision="0" />
          <Property Name="folioAyto" Type="String" MaxLength="16" FixedLength="false" Unicode="false" />
          <Property Name="folioSEPLADE" Type="String" MaxLength="16" FixedLength="false" Unicode="false" />
          <Property Name="fechaAceptacion" Type="DateTime" Precision="0" />
          <NavigationProperty Name="Proyecto" Relationship="Self.FK_oferta_Proyecto_DetalleProyecto" FromRole="DetalleProyecto" ToRole="Proyecto" />
        </EntityType>
        <Association Name="fk_dirINEGI_DomicilioCamino_DomicilioGeografico">
          <End Role="DomicilioGeografico" Type="Self.DomicilioGeografico" Multiplicity="1" />
          <End Role="DomicilioCamino" Type="Self.DomicilioCamino" Multiplicity="0..1" />
          <ReferentialConstraint>
            <Principal Role="DomicilioGeografico">
              <PropertyRef Name="idDomicilioGeografico" />
            </Principal>
            <Dependent Role="DomicilioCamino">
              <PropertyRef Name="idDomicilioGeografico" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="fk_dirINEGI_DomicilioCarretera_DomicilioGeografico">
          <End Role="DomicilioGeografico" Type="Self.DomicilioGeografico" Multiplicity="1" />
          <End Role="DomicilioCarretera" Type="Self.DomicilioCarretera" Multiplicity="0..1" />
          <ReferentialConstraint>
            <Principal Role="DomicilioGeografico">
              <PropertyRef Name="idDomicilioGeografico" />
            </Principal>
            <Dependent Role="DomicilioCarretera">
              <PropertyRef Name="idDomicilioGeografico" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_dirINEGI_DomicilioGeografico_oferta_Vivienda">
          <End Role="DomicilioGeografico" Type="Self.DomicilioGeografico" Multiplicity="0..1" />
          <End Role="Vivienda" Type="Self.Vivienda" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="DomicilioGeografico">
              <PropertyRef Name="idDomicilioGeografico" />
            </Principal>
            <Dependent Role="Vivienda">
              <PropertyRef Name="idDomicilioGeografico" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_oferta_DocumentoxProyecto_Proyecto">
          <End Role="Proyecto" Type="Self.Proyecto" Multiplicity="1" />
          <End Role="DocumentoxProyecto" Type="Self.DocumentoxProyecto" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Proyecto">
              <PropertyRef Name="idProyecto" />
            </Principal>
            <Dependent Role="DocumentoxProyecto">
              <PropertyRef Name="idProyecto" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_oferta_Proyecto_DetalleProyecto">
          <End Role="Proyecto" Type="Self.Proyecto" Multiplicity="1" />
          <End Role="DetalleProyecto" Type="Self.DetalleProyecto" Multiplicity="0..1" />
          <ReferentialConstraint>
            <Principal Role="Proyecto">
              <PropertyRef Name="idProyecto" />
            </Principal>
            <Dependent Role="DetalleProyecto">
              <PropertyRef Name="idProyecto" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_oferta_Proyecto_Vivienda">
          <End Role="Proyecto" Type="Self.Proyecto" Multiplicity="1" />
          <End Role="Vivienda" Type="Self.Vivienda" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Proyecto">
              <PropertyRef Name="idProyecto" />
            </Principal>
            <Dependent Role="Vivienda">
              <PropertyRef Name="idProyecto" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK_oferta_ProyectoxRiesgoOferta_Proyecto">
          <End Role="Proyecto" Type="Self.Proyecto" Multiplicity="1" />
          <End Role="ProyectoxRiesgoOferta" Type="Self.ProyectoxRiesgoOferta" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Proyecto">
              <PropertyRef Name="idProyecto" />
            </Principal>
            <Dependent Role="ProyectoxRiesgoOferta">
              <PropertyRef Name="idProyecto" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="DirectorResponsableObraxProyecto">
          <End Role="DirectorResponsableObra" Type="Self.DirectorResponsableObra" Multiplicity="*" />
          <End Role="Proyecto" Type="Self.Proyecto" Multiplicity="*" />
        </Association>
        <Association Name="PromotorExternoxProyecto">
          <End Role="PromotorExterno" Type="Self.PromotorExterno" Multiplicity="*" />
          <End Role="Proyecto" Type="Self.Proyecto" Multiplicity="*" />
        </Association>
        <Association Name="PropietarioTerrenoxProyecto">
          <End Role="PropietarioTerreno" Type="Self.PropietarioTerreno" Multiplicity="*" />
          <End Role="Proyecto" Type="Self.Proyecto" Multiplicity="*" />
        </Association>
        <EntityContainer Name="ModeloOferta" annotation:LazyLoadingEnabled="true">
          <EntitySet Name="DomicilioCamino" EntityType="Self.DomicilioCamino" />
          <EntitySet Name="DomicilioCarretera" EntityType="Self.DomicilioCarretera" />
          <EntitySet Name="DomicilioGeografico" EntityType="Self.DomicilioGeografico" />
          <EntitySet Name="DirectorResponsableObra" EntityType="Self.DirectorResponsableObra" />
          <EntitySet Name="DocumentoxProyecto" EntityType="Self.DocumentoxProyecto" />
          <EntitySet Name="PromotorExterno" EntityType="Self.PromotorExterno" />
          <EntitySet Name="PropietarioTerreno" EntityType="Self.PropietarioTerreno" />
          <EntitySet Name="Proyecto" EntityType="Self.Proyecto" />
          <EntitySet Name="ProyectoxRiesgoOferta" EntityType="Self.ProyectoxRiesgoOferta" />
          <EntitySet Name="Vivienda" EntityType="Self.Vivienda" />
          <EntitySet Name="DetalleProyecto" EntityType="Self.DetalleProyecto" />
          <AssociationSet Name="fk_dirINEGI_DomicilioCamino_DomicilioGeografico" Association="Self.fk_dirINEGI_DomicilioCamino_DomicilioGeografico">
            <End Role="DomicilioGeografico" EntitySet="DomicilioGeografico" />
            <End Role="DomicilioCamino" EntitySet="DomicilioCamino" />
          </AssociationSet>
          <AssociationSet Name="fk_dirINEGI_DomicilioCarretera_DomicilioGeografico" Association="Self.fk_dirINEGI_DomicilioCarretera_DomicilioGeografico">
            <End Role="DomicilioGeografico" EntitySet="DomicilioGeografico" />
            <End Role="DomicilioCarretera" EntitySet="DomicilioCarretera" />
          </AssociationSet>
          <AssociationSet Name="FK_dirINEGI_DomicilioGeografico_oferta_Vivienda" Association="Self.FK_dirINEGI_DomicilioGeografico_oferta_Vivienda">
            <End Role="DomicilioGeografico" EntitySet="DomicilioGeografico" />
            <End Role="Vivienda" EntitySet="Vivienda" />
          </AssociationSet>
          <AssociationSet Name="FK_oferta_DocumentoxProyecto_Proyecto" Association="Self.FK_oferta_DocumentoxProyecto_Proyecto">
            <End Role="Proyecto" EntitySet="Proyecto" />
            <End Role="DocumentoxProyecto" EntitySet="DocumentoxProyecto" />
          </AssociationSet>
          <AssociationSet Name="FK_oferta_Proyecto_DetalleProyecto" Association="Self.FK_oferta_Proyecto_DetalleProyecto">
            <End Role="Proyecto" EntitySet="Proyecto" />
            <End Role="DetalleProyecto" EntitySet="DetalleProyecto" />
          </AssociationSet>
          <AssociationSet Name="FK_oferta_Proyecto_Vivienda" Association="Self.FK_oferta_Proyecto_Vivienda">
            <End Role="Proyecto" EntitySet="Proyecto" />
            <End Role="Vivienda" EntitySet="Vivienda" />
          </AssociationSet>
          <AssociationSet Name="FK_oferta_ProyectoxRiesgoOferta_Proyecto" Association="Self.FK_oferta_ProyectoxRiesgoOferta_Proyecto">
            <End Role="Proyecto" EntitySet="Proyecto" />
            <End Role="ProyectoxRiesgoOferta" EntitySet="ProyectoxRiesgoOferta" />
          </AssociationSet>
          <AssociationSet Name="DirectorResponsableObraxProyecto" Association="Self.DirectorResponsableObraxProyecto">
            <End Role="DirectorResponsableObra" EntitySet="DirectorResponsableObra" />
            <End Role="Proyecto" EntitySet="Proyecto" />
          </AssociationSet>
          <AssociationSet Name="PromotorExternoxProyecto" Association="Self.PromotorExternoxProyecto">
            <End Role="PromotorExterno" EntitySet="PromotorExterno" />
            <End Role="Proyecto" EntitySet="Proyecto" />
          </AssociationSet>
          <AssociationSet Name="PropietarioTerrenoxProyecto" Association="Self.PropietarioTerrenoxProyecto">
            <End Role="PropietarioTerreno" EntitySet="PropietarioTerreno" />
            <End Role="Proyecto" EntitySet="Proyecto" />
          </AssociationSet>
        </EntityContainer>
      </Schema>
    </edmx:ConceptualModels>
    <!-- C-S mapping content -->
    <edmx:Mappings>
      <Mapping Space="C-S" xmlns="http://schemas.microsoft.com/ado/2009/11/mapping/cs">
        <EntityContainerMapping StorageEntityContainer="ruvPlusDesarrolloModelStoreContainer" CdmEntityContainer="ModeloOferta">
          <EntitySetMapping Name="DomicilioCamino">
            <EntityTypeMapping TypeName="ruvPlusDesarrolloModel.DomicilioCamino">
              <MappingFragment StoreEntitySet="DomicilioCamino">
                <ScalarProperty Name="idDomicilioGeografico" ColumnName="idDomicilioGeografico" />
                <ScalarProperty Name="nombreVialidad" ColumnName="nombreVialidad" />
                <ScalarProperty Name="origen" ColumnName="origen" />
                <ScalarProperty Name="destino" ColumnName="destino" />
                <ScalarProperty Name="cadenamiento" ColumnName="cadenamiento" />
                <ScalarProperty Name="idTipoCamino" ColumnName="idTipoCamino" />
                <ScalarProperty Name="idMargen" ColumnName="idMargen" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="DomicilioCarretera">
            <EntityTypeMapping TypeName="ruvPlusDesarrolloModel.DomicilioCarretera">
              <MappingFragment StoreEntitySet="DomicilioCarretera">
                <ScalarProperty Name="idDomicilioGeografico" ColumnName="idDomicilioGeografico" />
                <ScalarProperty Name="nombreVialidad" ColumnName="nombreVialidad" />
                <ScalarProperty Name="origen" ColumnName="origen" />
                <ScalarProperty Name="destino" ColumnName="destino" />
                <ScalarProperty Name="cadenamiento" ColumnName="cadenamiento" />
                <ScalarProperty Name="idAdministracion" ColumnName="idAdministracion" />
                <ScalarProperty Name="idDerechoTransito" ColumnName="idDerechoTransito" />
                <ScalarProperty Name="codigo" ColumnName="codigo" />
                <ScalarProperty Name="idTipoCamino" ColumnName="idTipoCamino" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="DomicilioGeografico">
            <EntityTypeMapping TypeName="ruvPlusDesarrolloModel.DomicilioGeografico">
              <MappingFragment StoreEntitySet="DomicilioGeografico">
                <ScalarProperty Name="idDomicilioGeografico" ColumnName="idDomicilioGeografico" />
                <ScalarProperty Name="idTipoDomicilioINEGI" ColumnName="idTipoDomicilioINEGI" />
                <ScalarProperty Name="idEstado" ColumnName="idEstado" />
                <ScalarProperty Name="idmunicipio" ColumnName="idmunicipio" />
                <ScalarProperty Name="idLocalidad" ColumnName="idLocalidad" />
                <ScalarProperty Name="idPeriodo" ColumnName="idPeriodo" />
                <ScalarProperty Name="numeroExteriorNumerico" ColumnName="numeroExteriorNumerico" />
                <ScalarProperty Name="numeroExteriorAlfanumerico" ColumnName="numeroExteriorAlfanumerico" />
                <ScalarProperty Name="numeroExteriorAnt" ColumnName="numeroExteriorAnt" />
                <ScalarProperty Name="numeroInteriorNumerico" ColumnName="numeroInteriorNumerico" />
                <ScalarProperty Name="numeroInteriorAlfanumerico" ColumnName="numeroInteriorAlfanumerico" />
                <ScalarProperty Name="idAsentamiento" ColumnName="idAsentamiento" />
                <ScalarProperty Name="cp" ColumnName="cp" />
                <ScalarProperty Name="idVialidad1" ColumnName="idVialidad1" />
                <ScalarProperty Name="idVialidad2" ColumnName="idVialidad2" />
                <ScalarProperty Name="idVialidad3" ColumnName="idVialidad3" />
                <ScalarProperty Name="descripcion" ColumnName="descripcion" />
                <ScalarProperty Name="domicilioGeografico1" ColumnName="domicilioGeografico" />
                <ScalarProperty Name="idVialidadPrincipal" ColumnName="idVialidadPrincipal" />
                <ScalarProperty Name="fechaRegistro" ColumnName="fechaRegistro" />
                <ScalarProperty Name="idTipoDomicilioRUV" ColumnName="idTipoDomicilioRUV" />
                <ScalarProperty Name="referenciaPrevia" ColumnName="referenciaPrevia" />
                <ScalarProperty Name="nombreVialidadPrincipal" ColumnName="nombreVialidadPrincipal" />
                <ScalarProperty Name="nombreVialidad1" ColumnName="nombreVialidad1" />
                <ScalarProperty Name="nombreVialidad2" ColumnName="nombreVialidad2" />
                <ScalarProperty Name="nombreVialidad3" ColumnName="nombreVialidad3" />
                <ScalarProperty Name="tipoVialidadP" ColumnName="tipoVialidadP" />
                <ScalarProperty Name="tipoVialidad1" ColumnName="tipoVialidad1" />
                <ScalarProperty Name="tipoVialidad2" ColumnName="tipoVialidad2" />
                <ScalarProperty Name="tipoVialidad3" ColumnName="tipoVialidad3" />
                <ScalarProperty Name="latitud" ColumnName="latitud" />
                <ScalarProperty Name="longitud" ColumnName="longitud" />
                <ScalarProperty Name="nombreTipoVialidadPrincipal" ColumnName="nombreTipoVialidadPrincipal" />
                <ScalarProperty Name="nombreTipoVialidad1" ColumnName="nombreTipoVialidad1" />
                <ScalarProperty Name="nombreTipoVialidad2" ColumnName="nombreTipoVialidad2" />
                <ScalarProperty Name="nombreTipoVialidad3" ColumnName="nombreTipoVialidad3" />
                <ScalarProperty Name="nombreAsentamiento" ColumnName="nombreAsentamiento" />
                <ScalarProperty Name="xmlSIG" ColumnName="xmlSIG" />
                <ScalarProperty Name="superManzana" ColumnName="superManzana" />
                <ScalarProperty Name="manzana" ColumnName="manzana" />
                <ScalarProperty Name="lote" ColumnName="lote" />
                <ScalarProperty Name="localidad" ColumnName="localidad" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="DirectorResponsableObra">
            <EntityTypeMapping TypeName="ruvPlusDesarrolloModel.DirectorResponsableObra">
              <MappingFragment StoreEntitySet="DirectorResponsableObra">
                <ScalarProperty Name="idDRO" ColumnName="idDRO" />
                <ScalarProperty Name="idIdentificacionOficial" ColumnName="idIdentificacionOficial" />
                <ScalarProperty Name="idLicencia" ColumnName="idLicencia" />
                <ScalarProperty Name="nombreDRO" ColumnName="nombreDRO" />
                <ScalarProperty Name="apellidoPaterno" ColumnName="apellidoPaterno" />
                <ScalarProperty Name="apellidoMaterno" ColumnName="apellidoMaterno" />
                <ScalarProperty Name="numeroPerito" ColumnName="numeroPerito" />
                <ScalarProperty Name="fechaVigencia" ColumnName="fechaVigencia" />
                <ScalarProperty Name="fechaRegistro" ColumnName="fechaRegistro" />
                <ScalarProperty Name="fechaActualizacion" ColumnName="fechaActualizacion" />
                <ScalarProperty Name="activo" ColumnName="activo" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="DocumentoxProyecto">
            <EntityTypeMapping TypeName="ruvPlusDesarrolloModel.DocumentoxProyecto">
              <MappingFragment StoreEntitySet="DocumentoxProyecto">
                <ScalarProperty Name="idProyecto" ColumnName="idProyecto" />
                <ScalarProperty Name="idDocumento" ColumnName="idDocumento" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="PromotorExterno">
            <EntityTypeMapping TypeName="ruvPlusDesarrolloModel.PromotorExterno">
              <MappingFragment StoreEntitySet="PromotorExterno">
                <ScalarProperty Name="idPromotorExterno" ColumnName="idPromotorExterno" />
                <ScalarProperty Name="numeroRegistroRUV" ColumnName="numeroRegistroRUV" />
                <ScalarProperty Name="nombreRazonSocial" ColumnName="nombreRazonSocial" />
                <ScalarProperty Name="representanteLegal" ColumnName="representanteLegal" />
                <ScalarProperty Name="lada" ColumnName="lada" />
                <ScalarProperty Name="numeroTelefono" ColumnName="numeroTelefono" />
                <ScalarProperty Name="correoElectronico" ColumnName="correoElectronico" />
                <ScalarProperty Name="registroPatronal" ColumnName="registroPatronal" />
                <ScalarProperty Name="fechaRegistro" ColumnName="fechaRegistro" />
                <ScalarProperty Name="fechaActualizacion" ColumnName="fechaActualizacion" />
                <ScalarProperty Name="activo" ColumnName="activo" />
                <ScalarProperty Name="esVendedor" ColumnName="esVendedor" />
                <ScalarProperty Name="rfc" ColumnName="rfc" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="PropietarioTerreno">
            <EntityTypeMapping TypeName="ruvPlusDesarrolloModel.PropietarioTerreno">
              <MappingFragment StoreEntitySet="PropietarioTerreno">
                <ScalarProperty Name="idPropietarioTerreno" ColumnName="idPropietarioTerreno" />
                <ScalarProperty Name="nombrePropietario" ColumnName="nombrePropietario" />
                <ScalarProperty Name="numeroRPP" ColumnName="numeroRPP" />
                <ScalarProperty Name="numeroCatastral" ColumnName="numeroCatastral" />
                <ScalarProperty Name="areaTerrenoEscriturado" ColumnName="areaTerrenoEscriturado" />
                <ScalarProperty Name="numeroEscritura" ColumnName="numeroEscritura" />
                <ScalarProperty Name="tomo" ColumnName="tomo" />
                <ScalarProperty Name="volumen" ColumnName="volumen" />
                <ScalarProperty Name="fechaEscrituracion" ColumnName="fechaEscrituracion" />
                <ScalarProperty Name="numeroNotario" ColumnName="numeroNotario" />
                <ScalarProperty Name="fechaRegistro" ColumnName="fechaRegistro" />
                <ScalarProperty Name="fechaActualizacion" ColumnName="fechaActualizacion" />
                <ScalarProperty Name="activo" ColumnName="activo" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Proyecto">
            <EntityTypeMapping TypeName="ruvPlusDesarrolloModel.Proyecto">
              <MappingFragment StoreEntitySet="Proyecto">
                <ScalarProperty Name="idProyecto" ColumnName="idProyecto" />
                <ScalarProperty Name="idEstatusProyecto" ColumnName="idEstatusProyecto" />
                <ScalarProperty Name="idEmpresa" ColumnName="idEmpresa" />
                <ScalarProperty Name="nombre" ColumnName="nombre" />
                <ScalarProperty Name="temporalJSON" ColumnName="temporalJSON" />
                <ScalarProperty Name="fechaRegistro" ColumnName="fechaRegistro" />
                <ScalarProperty Name="fechaActualizacion" ColumnName="fechaActualizacion" />
                <ScalarProperty Name="activo" ColumnName="activo" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ProyectoxRiesgoOferta">
            <EntityTypeMapping TypeName="ruvPlusDesarrolloModel.ProyectoxRiesgoOferta">
              <MappingFragment StoreEntitySet="ProyectoxRiesgoOferta">
                <ScalarProperty Name="idRiesgoOferta" ColumnName="idRiesgoOferta" />
                <ScalarProperty Name="idProyecto" ColumnName="idProyecto" />
                <ScalarProperty Name="solucionMitigarRiesgo" ColumnName="solucionMitigarRiesgo" />
                <ScalarProperty Name="fechaRegistro" ColumnName="fechaRegistro" />
                <ScalarProperty Name="fechaActualizacion" ColumnName="fechaActualizacion" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Vivienda">
            <EntityTypeMapping TypeName="ruvPlusDesarrolloModel.Vivienda">
              <MappingFragment StoreEntitySet="Vivienda">
                <ScalarProperty Name="idVivienda" ColumnName="idVivienda" />
                <ScalarProperty Name="idProyecto" ColumnName="idProyecto" />
                <ScalarProperty Name="idOfertaVivienda" ColumnName="idOfertaVivienda" />
                <ScalarProperty Name="idEstatusVivienda" ColumnName="idEstatusVivienda" />
                <ScalarProperty Name="idPrototipo" ColumnName="idPrototipo" />
                <ScalarProperty Name="idOrientacionVivienda" ColumnName="idOrientacionVivienda" />
                <ScalarProperty Name="idNivelVivienda" ColumnName="idNivelVivienda" />
                <ScalarProperty Name="idTipoZonaVivienda" ColumnName="idTipoZonaVivienda" />
                <ScalarProperty Name="idDomicilioGeografico" ColumnName="idDomicilioGeografico" />
                <ScalarProperty Name="identificadorVivienda" ColumnName="identificadorVivienda" />
                <ScalarProperty Name="numeroCatastralLote" ColumnName="numeroCatastralLote" />
                <ScalarProperty Name="edificio" ColumnName="edificio" />
                <ScalarProperty Name="planta" ColumnName="planta" />
                <ScalarProperty Name="costo" ColumnName="costo" />
                <ScalarProperty Name="metros2Lote" ColumnName="metros2Lote" />
                <ScalarProperty Name="metrosFrenteLote" ColumnName="metrosFrenteLote" />
                <ScalarProperty Name="cuv" ColumnName="cuv" />
                <ScalarProperty Name="fechaRegistro" ColumnName="fechaRegistro" />
                <ScalarProperty Name="fechaActualizacion" ColumnName="fechaActualizacion" />
                <ScalarProperty Name="nombreCondominio" ColumnName="nombreCondominio" />
                <ScalarProperty Name="cuvGeografica" ColumnName="cuvGeografica" />
                <ScalarProperty Name="idTipoAsentamiento" ColumnName="idTipoAsentamiento" />
                <ScalarProperty Name="idEstatusJuridicoVivienda" ColumnName="idEstatusJuridicoVivienda" />
                <ScalarProperty Name="idModalidadVivienda" ColumnName="idModalidadVivienda" />
                <ScalarProperty Name="idTipoAportacionVivienda" ColumnName="idTipoAportacionVivienda" />
                <ScalarProperty Name="idTipoProgramaVivienda" ColumnName="idTipoProgramaVivienda" />
                <ScalarProperty Name="featId" ColumnName="featId" />
                <ScalarProperty Name="numeroEstacionamientos" ColumnName="numeroEstacionamientos" />
                <ScalarProperty Name="idViviendaPlanoSIG" ColumnName="idViviendaPlanoSIG" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="DetalleProyecto">
            <EntityTypeMapping TypeName="ruvPlusDesarrolloModel.DetalleProyecto">
              <MappingFragment StoreEntitySet="DetalleProyecto">
                <ScalarProperty Name="idProyecto" ColumnName="idProyecto" />
                <ScalarProperty Name="idConstructor" ColumnName="idConstructor" />
                <ScalarProperty Name="idPromotor" ColumnName="idPromotor" />
                <ScalarProperty Name="idVendedor" ColumnName="idVendedor" />
                <ScalarProperty Name="esZonaDeRiesgo" ColumnName="esZonaDeRiesgo" />
                <ScalarProperty Name="aceptacionCartaResponsabilidad" ColumnName="aceptacionCartaResponsabilidad" />
                <ScalarProperty Name="fechaRegistro" ColumnName="fechaRegistro" />
                <ScalarProperty Name="fechaActualizacion" ColumnName="fechaActualizacion" />
                <ScalarProperty Name="folioAyto" ColumnName="folioAyto" />
                <ScalarProperty Name="folioSEPLADE" ColumnName="folioSEPLADE" />
                <ScalarProperty Name="fechaAceptacion" ColumnName="fechaAceptacion" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <AssociationSetMapping Name="DirectorResponsableObraxProyecto" TypeName="ruvPlusDesarrolloModel.DirectorResponsableObraxProyecto" StoreEntitySet="DirectorResponsableObraxProyecto">
            <EndProperty Name="DirectorResponsableObra">
              <ScalarProperty Name="idDRO" ColumnName="idDRO" />
            </EndProperty>
            <EndProperty Name="Proyecto">
              <ScalarProperty Name="idProyecto" ColumnName="idProyecto" />
            </EndProperty>
          </AssociationSetMapping>
          <AssociationSetMapping Name="PromotorExternoxProyecto" TypeName="ruvPlusDesarrolloModel.PromotorExternoxProyecto" StoreEntitySet="PromotorExternoxProyecto">
            <EndProperty Name="PromotorExterno">
              <ScalarProperty Name="idPromotorExterno" ColumnName="idPromotorExterno" />
            </EndProperty>
            <EndProperty Name="Proyecto">
              <ScalarProperty Name="idProyecto" ColumnName="idProyecto" />
            </EndProperty>
          </AssociationSetMapping>
          <AssociationSetMapping Name="PropietarioTerrenoxProyecto" TypeName="ruvPlusDesarrolloModel.PropietarioTerrenoxProyecto" StoreEntitySet="PropietarioTerrenoxProyecto">
            <EndProperty Name="PropietarioTerreno">
              <ScalarProperty Name="idPropietarioTerreno" ColumnName="idPropietarioTerreno" />
            </EndProperty>
            <EndProperty Name="Proyecto">
              <ScalarProperty Name="idProyecto" ColumnName="idProyecto" />
            </EndProperty>
          </AssociationSetMapping>
        </EntityContainerMapping>
      </Mapping>
    </edmx:Mappings>
  </edmx:Runtime>
  <!-- EF Designer content (DO NOT EDIT MANUALLY BELOW HERE) -->
  <Designer xmlns="http://schemas.microsoft.com/ado/2009/11/edmx">
    <Connection>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="MetadataArtifactProcessing" Value="EmbedInOutputAssembly" />
      </DesignerInfoPropertySet>
    </Connection>
    <Options>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="ValidateOnBuild" Value="true" />
        <DesignerProperty Name="EnablePluralization" Value="false" />
        <DesignerProperty Name="IncludeForeignKeysInModel" Value="true" />
        <DesignerProperty Name="UseLegacyProvider" Value="false" />
        <DesignerProperty Name="CodeGenerationStrategy" Value="None" />
      </DesignerInfoPropertySet>
    </Options>
    <!-- Diagram content (shape and connector positions) -->
    <Diagrams></Diagrams>
  </Designer>
</edmx:Edmx>