﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Dictaminacion
{
    public class MotivoRechazo
    {
        /** Consecutivo del motivo de rechazo. */
        public int? consecutivo { get; set; }

        /** Textop que representa el camino de secciones de donde se realizo el rechazo. */
        public string secciones { get; set; }

        /** idNombre del elemento donde se realizo el rechazo, si es que hubo uno. */
        public string elemento { get; set; }

        /** Nombre del elemento donde se realizo el rechazo, si es que hubo uno. */
        public string nombreElemento { get; set; }

        /** Iindice del elemento donde se realizo el rechazo, cuando se trata de una lista. */
        public int? indice { get; set; }

        //** Mensaje de rechazo. */
        public string mensajeRechazo { get; set; }

        /** Id de la sección donde se genero el rechzo. */
        public int? idSeccion { get; set; }

        /** Indica si esta actualizado o no el motivo de rechazo. */
        public bool? estActualizado { get; set; }

        /** Indica si el motivo de rechazo se encuentra en la sección seleccionada actual. */
        public bool? estaActivo { get; set; }
    }
}
