﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.AgenteServicio
{
    public class ViviendaASIS
    {
        public string idEstatusVivienda { get; set; }
        public int idProyecto { get; set; }
        public int idPrototipo { get; set; }
        public string idOrientacionVivienda { get; set; }
        public int idDomicilioGeografico { get; set; }
        public string numeroCatastralLote { get; set; }
        public int costo { get; set; }
        public int metros2Lote { get; set; }
        public int metrosFrenteLote { get; set; }
        public string fechaActualizacion { get; set; }
        public string nombreCondominio { get; set; }
        public string edificio { get; set; }
        public string cuv { get; set; }
        public string idOfertaVivienda { get; set; }
        public int identificadorVivienda { get; set; }
        public int idNivelVivienda { get; set; }
    }
}
