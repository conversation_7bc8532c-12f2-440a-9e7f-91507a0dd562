﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{A429AEC1-BA17-4F6F-96C7-D4A52F760F09}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>RUV.RUVPP.Oferta.GeneralesInterop</RootNamespace>
    <AssemblyName>RUV.RUVPP.Oferta.GeneralesInterop</AssemblyName>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <DocumentationFile>bin\Debug\RUV.RUVPP.Oferta.GeneralesInterop.XML</DocumentationFile>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <DocumentationFile>bin\Release\RUV.RUVPP.Oferta.GeneralesInterop.XML</DocumentationFile>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Integracion|AnyCPU'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\Integracion\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DocumentationFile>bin\Debug\RUV.RUVPP.Oferta.GeneralesInterop.XML</DocumentationFile>
    <DebugType>full</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'QA|AnyCPU'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\QA\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DocumentationFile>bin\Debug\RUV.RUVPP.Oferta.GeneralesInterop.XML</DocumentationFile>
    <DebugType>full</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Produccion|AnyCPU'">
    <OutputPath>bin\Produccion\</OutputPath>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Dapper, Version=1.50.2.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Dapper.1.50.2\lib\net451\Dapper.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.RichEdit.v12.2.Core, Version=12.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.Comun.Web.2.6.2.3\lib\net452\DevExpress.RichEdit.v12.2.Core.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="itextsharp, Version=5.5.13.1, Culture=neutral, PublicKeyToken=8354ae6d2174ddca, processorArchitecture=MSIL">
      <HintPath>..\packages\iTextSharp.5.5.13.1\lib\itextsharp.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.AI.Agent.Intercept, Version=2.0.7.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.Agent.Intercept.2.0.7\lib\net45\Microsoft.AI.Agent.Intercept.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.AI.DependencyCollector, Version=2.2.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.DependencyCollector.2.2.0\lib\net45\Microsoft.AI.DependencyCollector.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.AI.PerfCounterCollector, Version=2.2.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.PerfCounterCollector.2.2.0\lib\net45\Microsoft.AI.PerfCounterCollector.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.AI.ServerTelemetryChannel, Version=2.2.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel.2.2.0\lib\net45\Microsoft.AI.ServerTelemetryChannel.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.AI.Web, Version=2.2.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.Web.2.2.0\lib\net45\Microsoft.AI.Web.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.AI.WindowsServer, Version=2.2.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.WindowsServer.2.2.0\lib\net45\Microsoft.AI.WindowsServer.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.ApplicationInsights, Version=2.2.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.2.2.0\lib\net45\Microsoft.ApplicationInsights.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.ApplicationInsights.PersistenceChannel, Version=1.2.3.490, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.PersistenceChannel.1.2.3\lib\net45\Microsoft.ApplicationInsights.PersistenceChannel.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Azure.KeyVault.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Azure.KeyVault.Core.1.0.0\lib\net40\Microsoft.Azure.KeyVault.Core.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Data.Edm, Version=5.6.4.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Data.Edm.5.6.4\lib\net40\Microsoft.Data.Edm.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Data.OData, Version=5.6.4.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Data.OData.5.6.4\lib\net40\Microsoft.Data.OData.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Data.Services.Client, Version=5.6.4.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Data.Services.Client.5.6.4\lib\net40\Microsoft.Data.Services.Client.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Diagnostics.Tracing.EventSource, Version=1.1.28.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Diagnostics.Tracing.EventSource.Redist.1.1.28\lib\net40\Microsoft.Diagnostics.Tracing.EventSource.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Owin, Version=3.0.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.3.0.1\lib\net45\Microsoft.Owin.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Owin.Security, Version=3.0.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.3.0.1\lib\net45\Microsoft.Owin.Security.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Practices.EnterpriseLibrary.TransientFaultHandling, Version=6.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\EnterpriseLibrary.TransientFaultHandling.6.0.1304.0\lib\portable-net45+win+wp8\Microsoft.Practices.EnterpriseLibrary.TransientFaultHandling.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Practices.EnterpriseLibrary.TransientFaultHandling.Data, Version=6.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\EnterpriseLibrary.TransientFaultHandling.Data.6.0.1304.1\lib\NET45\Microsoft.Practices.EnterpriseLibrary.TransientFaultHandling.Data.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Practices.ServiceLocation, Version=1.3.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\CommonServiceLocator.1.3\lib\portable-net4+sl5+netcore45+wpa81+wp8\Microsoft.Practices.ServiceLocation.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Practices.Unity, Version=4.0.0.0, Culture=neutral, PublicKeyToken=6d32ff45e0ccc69f, processorArchitecture=MSIL">
      <HintPath>..\packages\Unity.4.0.1\lib\net45\Microsoft.Practices.Unity.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Practices.Unity.Configuration, Version=4.0.0.0, Culture=neutral, PublicKeyToken=6d32ff45e0ccc69f, processorArchitecture=MSIL">
      <HintPath>..\packages\Unity.4.0.1\lib\net45\Microsoft.Practices.Unity.Configuration.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Practices.Unity.Interception, Version=4.0.0.0, Culture=neutral, PublicKeyToken=6d32ff45e0ccc69f, processorArchitecture=MSIL">
      <HintPath>..\packages\Unity.Interception.4.0.1\lib\Net45\Microsoft.Practices.Unity.Interception.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Practices.Unity.Interception.Configuration, Version=4.0.0.0, Culture=neutral, PublicKeyToken=6d32ff45e0ccc69f, processorArchitecture=MSIL">
      <HintPath>..\packages\Unity.Interception.4.0.1\lib\Net45\Microsoft.Practices.Unity.Interception.Configuration.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Practices.Unity.RegistrationByConvention, Version=4.0.0.0, Culture=neutral, PublicKeyToken=6d32ff45e0ccc69f, processorArchitecture=MSIL">
      <HintPath>..\packages\Unity.4.0.1\lib\net45\Microsoft.Practices.Unity.RegistrationByConvention.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Threading.Tasks, Version=1.0.12.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.Async.1.0.168\lib\net40\Microsoft.Threading.Tasks.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Threading.Tasks.Extensions, Version=1.0.12.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.Async.1.0.168\lib\net40\Microsoft.Threading.Tasks.Extensions.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Threading.Tasks.Extensions.Desktop, Version=1.0.168.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.Async.1.0.168\lib\net40\Microsoft.Threading.Tasks.Extensions.Desktop.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Web.Infrastructure, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Web.Infrastructure.1.0.0.0\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.Configuration, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.WindowsAzure.ConfigurationManager.3.2.1\lib\net40\Microsoft.WindowsAzure.Configuration.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.Storage, Version=7.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\WindowsAzure.Storage.7.0.0\lib\net40\Microsoft.WindowsAzure.Storage.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=9.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.9.0.1\lib\net45\Newtonsoft.Json.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Oracle.ManagedDataAccess, Version=4.121.2.0, Culture=neutral, PublicKeyToken=89b483f429c47342, processorArchitecture=MSIL">
      <HintPath>..\packages\Oracle.ManagedDataAccess.12.1.24160719\lib\net40\Oracle.ManagedDataAccess.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Owin, Version=1.0.0.0, Culture=neutral, PublicKeyToken=f0ebd12fd5e55cc5, processorArchitecture=MSIL">
      <HintPath>..\packages\Owin.1.0\lib\net40\Owin.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RestSharp, Version=10*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RestSharp.105.2.3\lib\net452\RestSharp.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.Comun, Version=2.6.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.Comun.2.6.0\lib\net452\RUV.Comun.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.Comun.Datos, Version=2.6.2.1, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.Comun.Datos.2.6.2.1\lib\net452\RUV.Comun.Datos.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.Comun.Negocio, Version=2.6.0.2, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.Comun.Negocio.2.6.0.2\lib\net452\RUV.Comun.Negocio.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.Comun.Servicios, Version=2.6.1.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.Comun.Servicios.2.6.1\lib\net452\RUV.Comun.Servicios.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.Comun.Utilerias, Version=2.6.2.4, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.Comun.Utilerias.2.6.2.4\lib\net452\RUV.Comun.Utilerias.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.Comun.Web, Version=2.6.2.3, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.Comun.Web.2.6.2.3\lib\net452\RUV.Comun.Web.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.Integracion.Entidades.SAP, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.Empresa.2.5.0.14\lib\net452\RUV.Integracion.Entidades.SAP.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.Integracion.Procesos.SAP, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.Empresa.2.5.0.14\lib\net452\RUV.Integracion.Procesos.SAP.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.Ayuntamiento, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.Empresa.2.5.0.14\lib\net452\RUV.RUVPP.Datos.Ayuntamiento.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.Ayuntamiento.Historico, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.Empresa.2.5.0.14\lib\net452\RUV.RUVPP.Datos.Ayuntamiento.Historico.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.Ayuntamiento.SqlAzure, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.Empresa.2.5.0.14\lib\net452\RUV.RUVPP.Datos.Ayuntamiento.SqlAzure.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.Comun.Contratos, Version=2.5.0.6, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Datos.Comun.Contratos.2.5.0.6\lib\net452\RUV.RUVPP.Datos.Comun.Contratos.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.Comun.Historico, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.55\lib\net452\RUV.RUVPP.Datos.Comun.Historico.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.Comun.SqlAzure, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.55\lib\net452\RUV.RUVPP.Datos.Comun.SqlAzure.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.Empresa, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.55\lib\net452\RUV.RUVPP.Datos.Empresa.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.Empresa.SqlAzure, Version=2.5.0.5, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Datos.Empresa.SqlAzure.2.5.0.5\lib\net452\RUV.RUVPP.Datos.Empresa.SqlAzure.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.Entorno, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.55\lib\net452\RUV.RUVPP.Datos.Entorno.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.Entorno.SqlAzure, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.55\lib\net452\RUV.RUVPP.Datos.Entorno.SqlAzure.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.General, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.55\lib\net452\RUV.RUVPP.Datos.General.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.General.SqlAzure, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.55\lib\net452\RUV.RUVPP.Datos.General.SqlAzure.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.Oferta, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.55\lib\net452\RUV.RUVPP.Datos.Oferta.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.Oferta.Historico, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.55\lib\net452\RUV.RUVPP.Datos.Oferta.Historico.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.Oferta.SqlAzure, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.55\lib\net452\RUV.RUVPP.Datos.Oferta.SqlAzure.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.SAP, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.Empresa.2.5.0.14\lib\net452\RUV.RUVPP.Datos.SAP.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.SAP.SqlAzure, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.Empresa.2.5.0.14\lib\net452\RUV.RUVPP.Datos.SAP.SqlAzure.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.Ubicador, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.55\lib\net452\RUV.RUVPP.Datos.Ubicador.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.Ubicador.SqlAzure, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.Empresa.2.5.0.14\lib\net452\RUV.RUVPP.Datos.Ubicador.SqlAzure.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Entidades.Ayuntamiento, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.55\lib\net452\RUV.RUVPP.Entidades.Ayuntamiento.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Entidades.Comun, Version=1.0.0.9, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Entidades.Comun.1.0.0.9\lib\net452\RUV.RUVPP.Entidades.Comun.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Entidades.Empresa, Version=2.5.0.12, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\RUV.RUVPP.Entidades.Empresa.2.5.0.8\lib\net452\RUV.RUVPP.Entidades.Empresa.dll</HintPath>
    </Reference>
    <Reference Include="RUV.RUVPP.Entidades.Entorno, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.55\lib\net452\RUV.RUVPP.Entidades.Entorno.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Entidades.General.Bam, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.55\lib\net452\RUV.RUVPP.Entidades.General.Bam.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Entidades.General.Documentos, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.55\lib\net452\RUV.RUVPP.Entidades.General.Documentos.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Entidades.General.Indicadores, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.55\lib\net452\RUV.RUVPP.Entidades.General.Indicadores.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Entidades.General.Notificaciones, Version=2.5.1.32, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Entidades.General.Notificaciones.2.5.1.32\lib\net452\RUV.RUVPP.Entidades.General.Notificaciones.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Entidades.General.OrdenServicio, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.55\lib\net452\RUV.RUVPP.Entidades.General.OrdenServicio.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Entidades.General.Seguridad, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.55\lib\net452\RUV.RUVPP.Entidades.General.Seguridad.dll</HintPath>
    </Reference>
    <Reference Include="RUV.RUVPP.Entidades.General.Tareas, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.55\lib\net452\RUV.RUVPP.Entidades.General.Tareas.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Entidades.General.Tarificador, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.55\lib\net452\RUV.RUVPP.Entidades.General.Tarificador.dll</HintPath>
    </Reference>
    <Reference Include="RUV.RUVPP.Entidades.General.Tutoriales, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.55\lib\net452\RUV.RUVPP.Entidades.General.Tutoriales.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Entidades.Oferta, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.55\lib\net452\RUV.RUVPP.Entidades.Oferta.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Entidades.SAP, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.55\lib\net452\RUV.RUVPP.Entidades.SAP.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Entidades.Ubicador, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.55\lib\net452\RUV.RUVPP.Entidades.Ubicador.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Integracion.Procesos.Empresa, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.Empresa.2.5.0.14\lib\net452\RUV.RUVPP.Integracion.Procesos.Empresa.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Negocio.Comun.ProductosServicios, Version=2.5.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.55\lib\net452\RUV.RUVPP.Negocio.Comun.ProductosServicios.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Negocio.Empresa, Version=2.5.0.14, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.Empresa.2.5.0.14\lib\net452\RUV.RUVPP.Negocio.Empresa.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Negocio.General.Bam, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.Empresa.2.5.0.14\lib\net452\RUV.RUVPP.Negocio.General.Bam.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Negocio.General.Documentos, Version=2.5.2.1, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Documentos.2.5.2.1\lib\net452\RUV.RUVPP.Negocio.General.Documentos.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Negocio.General.Notificaciones, Version=2.5.1.23, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Notificaciones.2.5.1.23\lib\net452\RUV.RUVPP.Negocio.General.Notificaciones.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Negocio.General.OrdenServicio, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.Empresa.2.5.0.14\lib\net452\RUV.RUVPP.Negocio.General.OrdenServicio.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Negocio.General.OrdenTrabajo, Version=2.5.0.7, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.OrdenTrabajo.2.5.0.7\lib\net452\RUV.RUVPP.Negocio.General.OrdenTrabajo.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Negocio.General.Seguridad, Version=2.5.0.15, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Seguridad.2.5.0.15\lib\net452\RUV.RUVPP.Negocio.General.Seguridad.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Negocio.General.Tarificador, Version=2.5.2.55, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Negocio.General.Tarificador.dll</HintPath>
    </Reference>
    <Reference Include="StackExchange.Redis, Version=1.0.316.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\StackExchange.Redis.1.0.394\lib\net45\StackExchange.Redis.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Configuration.Abstractions, Version=2.0.2.26, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Configuration.Abstractions.2.0.2.26\lib\net45\System.Configuration.Abstractions.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Core" />
    <Reference Include="System.Net" />
    <Reference Include="System.Net.Http.Formatting, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Client.5.2.3\lib\net45\System.Net.Http.Formatting.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Spatial, Version=5.6.4.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Spatial.5.6.4\lib\net40\System.Spatial.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Transactions" />
    <Reference Include="System.Web.Helpers, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.3\lib\net45\System.Web.Helpers.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Http, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Core.5.2.3\lib\net45\System.Web.Http.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Http.WebHost, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.WebHost.5.2.3\lib\net45\System.Web.Http.WebHost.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Mvc, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Web.Mvc.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Razor, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Razor.3.2.3\lib\net45\System.Web.Razor.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.WebPages, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.3\lib\net45\System.Web.WebPages.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.WebPages.Deployment, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.3\lib\net45\System.Web.WebPages.Deployment.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.WebPages.Razor, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.3\lib\net45\System.Web.WebPages.Razor.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Documentos\DocumentoOferta.cs" />
    <Compile Include="Documentos\DocumentoOfertaDA.cs" />
    <Compile Include="Documentos\Recursos.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Recursos.resx</DependentUpon>
    </Compile>
    <Compile Include="Documentos\ServicioDocumentoOferta.cs" />
    <Compile Include="Modelo\ConfiguracionSeccion\ConfiguracionSeccion.cs" />
    <Compile Include="Modelo\ConfiguracionSeccion\Elemento.cs" />
    <Compile Include="Modelo\ConfiguracionSeccion\Seccion.cs" />
    <Compile Include="Modelo\Dictaminacion\DictaminacionContenedor.cs" />
    <Compile Include="Modelo\Dictaminacion\DictaminacionElemento.cs" />
    <Compile Include="Modelo\Dictaminacion\DictaminacionSeccion.cs" />
    <Compile Include="Modelo\Dictaminacion\MotivoRechazo.cs" />
    <Compile Include="Modelo\Estatus\EstatusElemento.cs" />
    <Compile Include="Modelo\Estatus\EtapaProcedimiento.cs" />
    <Compile Include="Modelo\Estatus\EstatusProcedimiento.cs" />
    <Compile Include="Modelo\Estatus\EstatusSeccion.cs" />
    <Compile Include="Modelo\Reglas\ReglaDependiente.cs" />
    <Compile Include="Modelo\Reglas\ReglaElemento.cs" />
    <Compile Include="Modelo\Reglas\Validacion.cs" />
    <Compile Include="OrdenTrabajo\ServicioOrdenTrabajoOfertaActualizacion.cs" />
    <Compile Include="OrdenTrabajo\ServicioOrdenTrabajoOfertaAlta.cs" />
    <Compile Include="OrdenTrabajo\ServicioOrdenTrabajoProyectoActualizacion.cs" />
    <Compile Include="OrdenTrabajo\ServicioOrdenTrabajoProyectoAlta.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="ApplicationInsights.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="packages.config">
      <SubType>Designer</SubType>
    </None>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Documentos\Recursos.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Recursos.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Recursos\FichaPago.html" />
    <Content Include="Recursos\FichaPago2.html" />
    <Content Include="Resources\ruv.png" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="..\packages\Microsoft.Bcl.Build.1.0.21\build\Microsoft.Bcl.Build.targets" Condition="Exists('..\packages\Microsoft.Bcl.Build.1.0.21\build\Microsoft.Bcl.Build.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Microsoft.Bcl.Build.1.0.21\build\Microsoft.Bcl.Build.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.Bcl.Build.1.0.21\build\Microsoft.Bcl.Build.targets'))" />
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>