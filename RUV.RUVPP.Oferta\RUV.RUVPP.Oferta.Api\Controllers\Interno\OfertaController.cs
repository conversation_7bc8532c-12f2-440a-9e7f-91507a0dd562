﻿using Newtonsoft.Json;
using RUV.Comun.Seguridad.JWT;
using RUV.Comun.Utilerias;
using RUV.Comun.Web.Http;
using RUV.RUVPP.Entidades.Empresa;
using RUV.RUVPP.Entidades.General.Seguridad;
using RUV.RUVPP.Negocio.Empresa.EmpresaConsulta;
using RUV.RUVPP.Oferta.Dominio.Empresa;
using RUV.RUVPP.Oferta.Dominio.Excepciones;
using RUV.RUVPP.Oferta.Dominio.Oferta;
using RUV.RUVPP.Oferta.Modelo.Dictaminacion;
using RUV.RUVPP.Oferta.Modelo.Historico.Data;
using RUV.RUVPP.Oferta.Modelo.Oferta.Api;
using RUV.RUVPP.Oferta.Modelo.Oferta.Data;
using RUV.RUVPP.Oferta.Modelo.Proyectos.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;
using System.Web.Http.Description;
using System.Web.WebPages.Html;

namespace RUV.RUVPP.Oferta.Api.Controllers.Interno
{
    [RoutePrefix("interno/api/ofertas")]
    public class OfertaController : ApiControllerBase
    {
        private readonly IServicioOferta _servicioOferta;
        private readonly IServicioEmpresaConsulta _servicioEmpresaConsulta;
        private readonly IServicioEmpresa _servicioEmpresa;

        #region Oferta

        /// <summary>
        /// Constructor del Controlador
        /// </summary>
        /// <param name="clienteTelemetria">Instancia de Cliente Telemetria</param>
        /// <param name="_servicioOferta">Instancia de Servicio Oferta</param>
        public OfertaController(IServicioOferta _servicioOferta,
            IServicioEmpresaConsulta servicioEmpresaConsulta, IServicioEmpresa servicioEmpresa)
            : base()
        {
            this._servicioOferta = _servicioOferta;
            this._servicioEmpresaConsulta = servicioEmpresaConsulta;
            this._servicioEmpresa = servicioEmpresa;
        }

        //TASK: Parece repetido con EmpresaController.ConsultaEmpresaPorNRRUV
        [HttpGet, Route("empresa/consulta")]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerDatosEmpresaConsulta(string NRRuv)
        {
            var resultado = this._servicioEmpresaConsulta.ObtenerEmpresasConsulta(new ConsultaEmpresas { NRRuv = NRRuv }, new Entidades.Comun.PaginadorDto { pagina = 1 });

            return Request.CreateResponse(HttpStatusCode.OK, resultado.ListaEntidad.FirstOrDefault());
        }

        //TASK: Repetido con ProyectosController.ObtenerProyectosAsync
        [HttpGet, Route("proyectos/empresa/{idEmpresa}")]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerProyectosEmpresaAsync(int idEmpresa)
        {
            Dictionary<int, string> proyectos = new Dictionary<int, string>();
            proyectos = await this._servicioOferta.ObtenerProyectosEmpresaAsync(idEmpresa);

            return Request.CreateResponse(HttpStatusCode.OK, proyectos);
        }

        //TASK: Mover a EpresaController
        [HttpGet, Route("Empresa/{idEmpresa}")]
        [ResponseType(typeof(DatosCPV))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerDatosEmpresaPorIdEmpresa(int idEmpresa)
        {
            Entidades.Empresa.EmpresaDto empresa = this._servicioEmpresaConsulta.ObtenerEmpresaDto(idEmpresa);
            DatosCPV datosEmpresa = await this._servicioEmpresa.ConsultaEmpresaPorNRRUV(empresa.idEmpresa, empresa.IdEmpresaInst);
            return Request.CreateResponse(HttpStatusCode.OK, datosEmpresa);
        }

        [HttpGet, Route("Empresa/{idProyecto}/{idOferta}/{cuv}")]
        [ResponseType(typeof(DatosCPV))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerDatosEmpresaInterno(int idProyecto, int idOferta, string cuv)
        {
            DatosCPV datosEmpresa = await this._servicioEmpresa.ConsultaEmpresaInterno(idProyecto, idOferta, cuv);
            datosEmpresa = await this._servicioEmpresa.ConsultaEmpresaPorNRRUV(datosEmpresa.idEmpresa, null);
            return Request.CreateResponse(HttpStatusCode.OK, datosEmpresa);
        }

        //TASK: Revisar
        [HttpGet, Route("viviendasprototipos/{idProyecto}")]
        [ResponseType(typeof(List<ViviendaPrototipo>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerViviendasPrototiposProyectoAsync(int idProyecto, int? idOferta = null)
        {
            var idEstatusOferta = (int)EstatusOferta.EnRegistro;

            if (idOferta != null)
            {
                var oferta = await this._servicioOferta.ObtenerOfertaAsync(idOferta.Value);
                idEstatusOferta = oferta.ofertaVivienda.idEstatusOfertaVivienda;
            }

            var result = await this._servicioOferta.ObtenerViviendasPrototiposAsync(idProyecto, idOferta, idEstatusOferta);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        //MOver a controlador de viviendas
        [HttpGet, Route("estatus/vivienda/{cuv}")]
        [ResponseType(typeof(int))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerEstatusViviendas(string cuv)
        {
            int result = await this._servicioOferta.ObtenerEstatusViviendaAsIs(cuv);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        //TASK: Repetido con ProyectosController.ObtenerViviendasPorProyectoAsync
        [HttpGet, Route("viviendas/{idProyecto}")]
        [ResponseType(typeof(List<ViviendaPrototipo>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerViviendasPorIdProyectoAsync(int idProyecto)
        {
            var result = await this._servicioOferta.ObtenerViviendasPorIdProyectoAsync(idProyecto);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        //TASK: Mover a controlador de viviendas
        [HttpPost, Route("detalle/vivienda/asis")]
        [ResponseType(typeof(Vivienda))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerDetalleViviendaAsisAsync(Vivienda vivienda)
        {
            var result = await this._servicioOferta.ObtenerDetalleViviendaAsisAsync(vivienda);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("catalogos/estatus-oferta")]
        [ResponseType(typeof(List<EstatusOferta>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerEstatusOferta()
        {
            var lista = Enum.GetValues(typeof(EstatusOferta)).Cast<EstatusOferta>().Select(v => new SelectListItem
            {
                Text = v.ToString(),
                Value = ((int)v).ToString()
            }).ToList();

            return Request.CreateResponse(HttpStatusCode.OK, lista);
        }

        //Mover a controlador de viviendas
        [HttpGet, Route("viviendas/sin-asignar/tamanioPagina/{tamanioPagina}/pagina/{pagina}")]
        [ResponseType(typeof(ResultadoPaginado<ViviendaSinAsignar>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerViviendasSinAsignarOfertaAsync(int tamanioPagina, int pagina, int? idOferta = null, int? idProyecto = null)
        {
            var idProyectoOferta = idProyecto;
            var idEstatusOferta = (int)EstatusOferta.EnRegistro;

            if (idOferta != null)
            {
                var oferta = await this._servicioOferta.ObtenerOfertaAsync(idOferta.Value);
                idEstatusOferta = oferta.ofertaVivienda.idEstatusOfertaVivienda;
                idProyectoOferta = oferta.ofertaVivienda.idProyecto;
            }

            var result = await this._servicioOferta.ObtenerViviendasSinAsingarOfertaAsync(idProyectoOferta.Value, idEstatusOferta, tamanioPagina, pagina, idOferta);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpPost, Route("")]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> GuardarOfertaAsync(Modelo.Oferta.Api.Oferta oferta)
        {
            var usuario = (CustomUserRuv)User;
            var result = oferta.ofertaVivienda.idOfertaVivienda > 0 ?
            await this._servicioOferta.ActualizarOfertaAsync(oferta.ofertaVivienda, JsonConvert.SerializeObject(oferta), oferta.fueronModificadosDatosSensibles) :
            await this._servicioOferta.GuardarOfertaAsync(oferta.ofertaVivienda.idProyecto, oferta.ofertaVivienda.nombreFrente, JsonConvert.SerializeObject(oferta), usuario);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpPost, Route("detalle")]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> GuardarOfertaDetalleAsync(Modelo.Oferta.Api.Oferta oferta)
        {
            var usuario = (CustomUserRuv)User;
            bool existeProyectoDetalle = await this._servicioOferta.ObtenerOfertaDetalleAsync(oferta.ofertaVivienda.idOfertaVivienda) != null;

            var result = 0;

            int contadorIntentos = 1;
            try
            {
                 result = existeProyectoDetalle ?
                 await this._servicioOferta.ActualizarOfertaDetalleAsync(oferta, usuario) :
                 await this._servicioOferta.GuardarOfertaDetalleAsync(oferta, usuario);
            }
            catch (Exception e)
            {
                if (e.Message.Contains("ORA-12571"))
                {
                    if (contadorIntentos > 1)
                    {
                        throw new ExcepcionSig("Error en la conexión al actualizar sembrado, favor de volver a intentar.", e);
                    }
                    else
                    {
                        contadorIntentos++;
                         result = existeProyectoDetalle ?
                        await this._servicioOferta.ActualizarOfertaDetalleAsync(oferta, usuario) :
                        await this._servicioOferta.GuardarOfertaDetalleAsync(oferta, usuario);
                    }
                }
                else
                {
                    throw new ExcepcionSig("Error en la conexión al actualizar sembrado, favor de volver a intentar.", e);
                }
            }

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("{idOferta}/parcial")]
        [ResponseType(typeof(bool))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> EliminarOfertaParcialAsync(int idOferta)
        {
            await this._servicioOferta.EliminarOfertaParcialAsync(idOferta);

            return Request.CreateResponse(HttpStatusCode.OK, true);
        }

        [HttpDelete, Route("{idOferta}")]
        [ResponseType(typeof(bool))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> EliminarOfertaAsync(int idOferta)
        {
            var result = await this._servicioOferta.EliminarOfertaAsync(idOferta);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="claveOferta"></param>
        /// <param name="nombreProyecto"></param>
        /// <param name="idEmpresa"></param>
        /// <returns></returns>
        [HttpGet, Route("tamanioPagina/{tamanioPagina}/pagina/{pagina}")]
        [ResponseType(typeof(ResultadoPaginado<List<Modelo.Oferta.Api.Oferta>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerOfertaFiltradoAsync(int tamanioPagina, int pagina, string claveOferta = null, string nombreProyecto = null, int? idEmpresa = null)
        {
            var result = await this._servicioOferta.ObtenerOfertasFiltradoAsync(tamanioPagina, pagina, claveOferta, nombreProyecto, idEmpresa);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("proyectos/{idProyecto}")]
        [ResponseType(typeof(Modelo.Oferta.Api.Oferta))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerOfertaPorProyectoAsync(int idProyecto)
        {
            var result = await this._servicioOferta.ObtenerOfertaxProyectoAsync(idProyecto);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("{idOferta}/datosGenerales")]
        [ResponseType(typeof(ResultadoPaginado<Modelo.Oferta.Api.Oferta>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerDatosGeneralesAsync(int idOferta)
        {
            var result = await this._servicioOferta.ObtenerDatosGeneralesAsync(idOferta);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("{idOferta}")]
        [ResponseType(typeof(ResultadoPaginado<Modelo.Oferta.Api.Oferta>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerOfertaAsync(int idOferta)
        {
            var usuario = (CustomUserRuv)User;
            var result = await this._servicioOferta.ObtenerOfertaAsync(idOferta);
            var idOfertaVivienda = result.ofertaVivienda.idOfertaVivienda;

            if (usuario.IdEmpresa == 1 || usuario.IdEmpresa == result.idEmpresa || usuario.IdEntidad == 15)
            {
                result.esAccesible = true;
            }
            else
            {
                result = new Modelo.Oferta.Api.Oferta();
                result.esAccesible = false;
                result.ofertaVivienda = new OfertaVivienda();
                result.ofertaVivienda.idOfertaVivienda = idOfertaVivienda;
            }

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("{idOferta}/documentos")]
        [ResponseType(typeof(ResultadoPaginado<Modelo.Oferta.Api.Oferta>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerDocumentosOfertaAsync(int idOferta, int pagina, int tamPag, int num)
        {
            var result = await this._servicioOferta.ObtenerDocumentosOfertaAsync(idOferta, pagina, tamPag, num);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        //TASK: Unificar con ObtenerOfertaFiltradoAsync
        [HttpGet, Route("especializada/filtro")]
        [ResponseType(typeof(Modelo.Oferta.Api.Oferta))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerOfertasFiltroAsync(int? idEmpresa = null, string noRuv = null, string nombreProyecto = null, int? idProyecto = null, int? idOferta = null, string claveOferta = null)
        {
            var result = await this._servicioOferta.ObtenerOfertasFiltroAsync(idEmpresa, noRuv, nombreProyecto, idProyecto, idOferta, claveOferta);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        //TASK: Mover a controlador de vivienda
        [HttpGet, Route("vivienda/filtro")]
        [ResponseType(typeof(List<Vivienda>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerViviendasFiltroAsync(int? idEmpresa = null, string noRuv = null, string nombreProyecto = null, int? idProyecto = null, int? idOferta = null, string claveOferta = null, int? idVivienda = null, string cuv = null)
        {
            var result = await this._servicioOferta.ObtenerViviendasFiltroAsync(idEmpresa, noRuv, nombreProyecto, idProyecto, idOferta, claveOferta, idVivienda, cuv);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        //TASK: Unificar con ObtenerOfertaFiltradoAsync
        [HttpGet, Route("especializada/filtroconpaginado")]
        [ResponseType(typeof(ResultadoPaginado<List<Modelo.Oferta.Api.Oferta>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerOfertasFiltroConPaginadoAsync(int tamanioPagina, int pagina, int? idEmpresa = null, string noRuv = null, string nombreProyecto = null, int? idProyecto = null, int? idOferta = null, int? idEstatus = null)
        {
            var result = await this._servicioOferta.ObtenerOfertasFiltroConPaginadoAsync(tamanioPagina, pagina, idEmpresa, noRuv, nombreProyecto, idProyecto, idOferta, idEstatus);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        //TASK: Mover a controlador de viviendas
        [HttpGet, Route("vivienda/filtroconpaginado")]
        [ResponseType(typeof(ResultadoPaginado<List<Vivienda>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerViviendasFiltroConPaginadoAsync(int tamanioPagina, int pagina, int? idEmpresa = null, string noRuv = null, string nombreProyecto = null, int? idProyecto = null, int? idOferta = null, int? idVivienda = null, string cuv = null)
        {
            var result = await this._servicioOferta.ObtenerViviendasFiltroConPaginadoAsync(tamanioPagina, pagina, idEmpresa, noRuv, nombreProyecto, idProyecto, idOferta, idVivienda, cuv);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        //TASK: Mover a controlador de prototipos
        [HttpGet, Route("catalogos/prototipos")]
        [ResponseType(typeof(Modelo.Oferta.Data.PrototipoCatalogo))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerPrototiposAsync()
        {
            var resultado = await this._servicioOferta.ObtenerCatalogoPrototipoAsyn();
            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }

        [HttpGet, Route("catalogos/documentos-complementarios")]
        [ResponseType(typeof(Modelo.Oferta.Data.Documento))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerDocumentosComplementariosAsync()
        {
            var resultado = await this._servicioOferta.ObtenerCatalogoDocumentosAsyn();
            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }

        /// <summary>
        /// Obtiene el historico de validaciones(lista de ordenes de trabajo de una oferta con paginacion)
        /// </summary>
        /// <param name="idOfertaVivienda">Identificador de la oferta</param>
        /// <param name="tamanioPagina">Tamaño de pagina</param>
        /// <param name="pagina">Pagina consultada</param>
        /// <returns></returns>
        [HttpGet, Route("{idOfertaVivienda}/ordenes-trabajo")]
        [ResponseType(typeof(ResultadoPaginado<List<OrdenTrabajoHistoricoValidacion>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerOrdenesTrabajoPorOfertaPaginadoAsync(int idOfertaVivienda, int tamanioPagina, int pagina)
        {
            var result = await this._servicioOferta.ObtenerOrdenesTrabajoPorOfertaPaginadoAsync(idOfertaVivienda, tamanioPagina, pagina);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        //TASK: Unificar con ObtenerViviendasPorOfertaPaginadoAsync
        [HttpGet, Route("{idOferta}/viviendas")]
        [ResponseType(typeof(List<Vivienda>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerViviendasPorOfertaViviendaAsync(int idOferta)
        {
            var resultado = await this._servicioOferta.ObtenerViviendasPorOfertaVivienda(idOferta);
            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }

        [HttpGet, Route("{idOferta}/viviendas/reporte")]
        [ResponseType(typeof(List<Vivienda>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerViviendasReportePorOfertaViviendaAsync(int idOferta)
        {
            var resultado = await this._servicioOferta.ObtenerViviendasReportePorOfertaVivienda(idOferta);
            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }

        /// <summary>
        /// Obtiene el listado de viviendas por oferta paginado
        /// </summary>
        /// <param name="idProyecto">Identificador del proyecto</param>
        /// <param name="tamanioPagina">Tamaño de pagina</param>
        /// <param name="pagina">Pagina consultada</param>
        /// <returns></returns>
        [HttpGet, Route("{idOferta}/viviendas-paginado")]
        [ResponseType(typeof(ResultadoPaginado<List<Vivienda>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerViviendasPorOfertaPaginadoAsync(int idOferta, int tamanioPagina, int pagina)
        {
            var result = await this._servicioOferta.ObtenerViviendasOfertaPaginadoAsync(idOferta, tamanioPagina, pagina);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// Obtiene el catalogo de estatus de vivienda
        /// </summary>
        [HttpGet, Route("catalogos/estatus-vivienda")]
        [ResponseType(typeof(Modelo.Oferta.Data.EstatusVivienda))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerCatalogoEstatusViviendaAsync()
        {
            var resultado = await this._servicioOferta.ObtenerCatalogoEstatusViviendaAsync();
            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }

        //TASK: mover a controlador de proyectos
        /// <summary>
        /// Obtiene los ids de oferta por proyecto
        /// </summary>
        /// <param name="idProyecto">Identificador del proyecto</param>
        [HttpGet, Route("proyectos/{idProyecto}/identificadores/ofertas/aceptadas")]
        [ResponseType(typeof(Modelo.Oferta.Data.EstatusVivienda))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerIdsOfertasPorProyectoAsync(int idProyecto)
        {
            var resultado = await this._servicioOferta.ObtenerIdsOfertaPorProyectoAsync(idProyecto, (int)EstatusOferta.Aceptada);
            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }

        //TASK: Validar funcionamiento
        [HttpGet, Route("proyectos/consulta")]
        [ResponseType(typeof(List<PrototipoVivienda>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerPrototiposConsultaGeneralsync(int? idProyecto = null, int? idOferta = null, string cuv = null)
        {
            var resultado = await this._servicioOferta.ObtenerPrototipoConsultaGeneral(idProyecto, idOferta, cuv);
            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }


        [HttpGet, Route("notificarOfertaAsIs")]
        [ResponseType(typeof(List<PrototipoVivienda>))]
        public async Task<HttpResponseMessage> notificarOfertaAsIs(int idProyecto, int idOferta)
        {
            await this._servicioOferta.NotificarOfertaNuevaAsIs(new Modelo.Oferta.Api.Oferta() { idOferta = idOferta, idProyecto = idProyecto });
            return Request.CreateResponse(HttpStatusCode.OK, true);
        }

        #endregion

        #region DictaminarOferta

        //TASK: Validar repetición con metodos de EmpresaController
        [HttpGet, Route("{idRegistro}/Empresa")]
        [ResponseType(typeof(DatosCPV))]
        public async Task<HttpResponseMessage> ObtenerDatosEmpresa(int idRegistro)
        {
            Oferta.Modelo.Oferta.Api.Oferta oferta = await this._servicioOferta.ObtenerOfertaPorIdAsync(idRegistro);
            Entidades.Empresa.EmpresaDto empresa = this._servicioEmpresaConsulta.ObtenerEmpresaDto(oferta.idEmpresa.Value);
            DatosCPV datosEmpresa = await this._servicioEmpresa.ConsultaEmpresaPorNRRUV(empresa.idEmpresa, empresa.IdEmpresaInst);
            return Request.CreateResponse(HttpStatusCode.OK, datosEmpresa);
        }

        [HttpPost, Route("{idOferta}/dictaminaciones")]
        [ResponseType(typeof(bool))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> EnviarDictaminacionOfertaVivienda(int idOferta, int idOrdenTrabajo, short idServicio, [FromBody]ParametroBodyDictaminacion parametroBodyDictaminacion)
        {
            var usuario = (CustomUserRuv)User;
            var result = await this._servicioOferta.EnviarDictaminacionOfertaVivienda(idOrdenTrabajo, idServicio, idOferta, parametroBodyDictaminacion, usuario);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        #endregion DictaminarOferta

        #region Pago Oferta

        /// <summary>
        /// Metodo para confirmar el pago en SAP una vez que éste sea pagado
        /// </summary>
        /// <param name="fichaPagoDTO">id de la oferta de vivienda</param>
        /// <returns>Regresa true si el proceso se ejecuta correctamente</returns>
        [HttpPost, Route("confirmarpagosap")]
        [ResponseType(typeof(bool))]
        public async Task<HttpResponseMessage> ConfirmarPagoSapAsync(FichaPagoDTO fichaPagoDTO)
        {
            var result = await this._servicioOferta.ConfirmarPagoPorIdAsync(fichaPagoDTO);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        //TASK: utilizar id oferta y cambiar ruta
        /// <summary>
        ///Metodo para calcular el monto de las viviendas seleccionadas
        /// </summary>
        /// <param name="viviendasProyecto">Viviendas del proyecto</param>
        /// /// <param name="idProyecto">Id del proyecto donde se va a generar la oferta de vivienda</param>
        /// <returns>Regresa el monto total de la oferta</returns>
        [HttpPost, Route("{idProyecto}/montooferta")]
        [ResponseType(typeof(ResultadoPaginado<Modelo.Oferta.Api.Oferta>))]
        public async Task<HttpResponseMessage> ObtenerMontoAsync(int idProyecto, List<ViviendaPrototipo> viviendasProyecto)
        {
            var result = await this._servicioOferta.ObtenerMontoOfertaAsync(viviendasProyecto, idProyecto);
            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        #endregion Pago Oferta

        #region Disposible

        protected override void Dispose(bool disposing)
        {
            base.Dispose(disposing);

            this._servicioEmpresa.Dispose();
            this._servicioOferta.Dispose();
        }

        #endregion Disposible

        #region Consulta Suspension

        [HttpGet, Route("consulta/suspension-orden")]
        [ResponseType(typeof(ResultadoPaginado<Modelo.Oferta.Api.DatosSuspension>))]
        public async Task<HttpResponseMessage> ObtenerDatosSuspensionAsync(int tamanioPagina, int pagina, string ordenVerificacion = null)
        {
            var result = await this._servicioOferta.ObtenerDatosSuspensionAsync(tamanioPagina, pagina, ordenVerificacion);
            return Request.CreateResponse(HttpStatusCode.OK, result);
        }
        #endregion Consulta Suspension

        #region Documento Cuvs Oferta
        /// <summary>
        /// Servicio que Genera un PDF de las Cuvs de la Oferta
        /// </summary>
        /// <returns></returns>
        [HttpGet, Route("generar-pdf-cuvs-oferta")]
        [ResponseType(typeof(string))]
        public async Task<HttpResponseMessage> GenerarPDFCuvs(string idOfertaVivienda, bool cuvsValidadas, int idEmpresa)
        {
            var result = await this._servicioOferta.GenerarPDFCuvs(idOfertaVivienda, cuvsValidadas, idEmpresa);

            return Request.CreateResponse(HttpStatusCode.OK, result);

        }
        #endregion
    }
}