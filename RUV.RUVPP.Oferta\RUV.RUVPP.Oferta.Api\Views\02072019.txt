#config-version=FG200D-5.04-FW-build1111-161220:opmode=0:vdom=0:user=admin
#conf_file_ver=0
#buildno=1111
#global_vdom=1
config system global
    set admintimeout 15
    set alias "FG200D3916821220"
    set compliance-check disable
    set disk-usage wanopt
    set fgd-alert-subscription advisory latest-threat
    set hostname "FG200D3916821220"
    set switch-controller enable
    set timezone 09
end
config system accprofile
    edit "prof_admin"
        set mntgrp read-write
        set admingrp read-write
        set updategrp read-write
        set authgrp read-write
        set sysgrp read-write
        set netgrp read-write
        set loggrp read-write
        set routegrp read-write
        set fwgrp read-write
        set vpngrp read-write
        set utmgrp read-write
        set wanoptgrp read-write
        set endpoint-control-grp read-write
        set wifi read-write
    next
end
config system interface
    edit "mgmt"
        set vdom "root"
        set ip ************ *************
        set allowaccess ping https http fgfm
        set type physical
        set dedicated-to management
        set role lan
        set snmp-index 1
    next
    edit "wan1"
        set vdom "root"
        set ip ************** ***************
        set allowaccess ping
        set type physical
        set alias "Internet"
        set role wan
        set snmp-index 2
    next
    edit "wan2"
        set vdom "root"
        set mode dhcp
        set allowaccess ping fgfm
        set type physical
        set role wan
        set snmp-index 3
    next
    edit "dmz1"
        set vdom "root"
        set ip ********** *************
        set allowaccess ping https http fgfm capwap
        set type physical
        set role dmz
        set snmp-index 4
    next
    edit "dmz2"
        set vdom "root"
        set allowaccess ping fgfm capwap
        set type physical
        set role dmz
        set snmp-index 5
    next
    edit "modem"
        set vdom "root"
        set mode pppoe
        set type physical
        set snmp-index 6
    next
    edit "ssl.root"
        set vdom "root"
        set type tunnel
        set alias "SSL VPN interface"
        set snmp-index 7
    next
    edit "port2"
        set vdom "root"
        set ip ********* *************
        set allowaccess ping https ssh
        set type physical
        set alias "Internal"
        set role lan
        set snmp-index 9
    next
    edit "port3"
        set vdom "root"
        set type physical
        set snmp-index 10
    next
    edit "port4"
        set vdom "root"
        set type physical
        set snmp-index 11
    next
    edit "port5"
        set vdom "root"
        set type physical
        set snmp-index 12
    next
    edit "port6"
        set vdom "root"
        set type physical
        set snmp-index 13
    next
    edit "port7"
        set vdom "root"
        set type physical
        set snmp-index 14
    next
    edit "port8"
        set vdom "root"
        set type physical
        set snmp-index 15
    next
    edit "port9"
        set vdom "root"
        set type physical
        set snmp-index 16
    next
    edit "port10"
        set vdom "root"
        set type physical
        set snmp-index 17
    next
    edit "port11"
        set vdom "root"
        set type physical
        set snmp-index 18
    next
    edit "port12"
        set vdom "root"
        set type physical
        set snmp-index 19
    next
    edit "port13"
        set vdom "root"
        set type physical
        set snmp-index 20
    next
    edit "port14"
        set vdom "root"
        set type physical
        set snmp-index 21
    next
    edit "port15"
        set vdom "root"
        set type physical
        set snmp-index 22
    next
    edit "port16"
        set vdom "root"
        set type physical
        set snmp-index 23
    next
    edit "lan"
        set vdom "root"
        set ip ********* *************
        set allowaccess ping https ssh
        set type hard-switch
        set stp enable
        set role lan
        set snmp-index 8
    next
    edit "ToAzureVPN"
        set vdom "root"
        set type tunnel
        set snmp-index 24
        set interface "wan1"
    next
    edit "ToAzureVPN2"
        set vdom "root"
        set type tunnel
        set snmp-index 25
        set interface "wan1"
    next
    edit "vpn-usuarios"
        set vdom "root"
        set ip *********** ***************
        set type tunnel
        set remote-ip ***********
        set fortiheartbeat enable
        set snmp-index 26
        set interface "wan1"
    next
    edit "HSBC"
        set vdom "root"
        set type tunnel
        set snmp-index 27
        set interface "wan1"
    next
end
config system physical-switch
    edit "sw0"
        set age-val 0
    next
end
config system virtual-switch
    edit "lan"
        set physical-switch "sw0"
        config port
            edit "port1"
            next
        end
    next
end
config system custom-language
    edit "en"
        set filename "en"
    next
    edit "fr"
        set filename "fr"
    next
    edit "sp"
        set filename "sp"
    next
    edit "pg"
        set filename "pg"
    next
    edit "x-sjis"
        set filename "x-sjis"
    next
    edit "big5"
        set filename "big5"
    next
    edit "GB2312"
        set filename "GB2312"
    next
    edit "euc-kr"
        set filename "euc-kr"
    next
end
config system admin
    edit "admin"
        set accprofile "super_admin"
        set vdom "root"
        config dashboard
            edit 1
                set column 1
            next
            edit 2
                set widget-type licinfo
                set column 1
            next
            edit 3
                set widget-type jsconsole
                set column 1
            next
            edit 5
                set widget-type alert
                set column 2
                set top-n 10
                set status close
            next
            edit 4
                set widget-type sysres
                set column 2
            next
        end
        set password ENC SH2+qIOLo+dztRILTBkXpacdcg/QPLZ4/wmsfydc5IkJrVZQY2n050SQ7c/A08=
    next
end
config system ha
    set override disable
end
config system storage
    edit "Internal"
        set partition "WANOPTXX26AD59F8"
        set media-type "scsi"
        set device "/dev/sdb1"
        set size 60092
    next
end
config system dns
    set primary 208.91.112.53
    set secondary 208.91.112.52
end
config system replacemsg-image
    edit "logo_fnet"
        set image-type gif
        set image-base64 ''
    next
    edit "logo_fguard_wf"
        set image-type gif
        set image-base64 ''
    next
    edit "logo_fw_auth"
        set image-type png
        set image-base64 ''
    next
    edit "logo_v2_fnet"
        set image-type png
        set image-base64 ''
    next
    edit "logo_v2_fguard_wf"
        set image-type png
        set image-base64 ''
    next
    edit "logo_v2_fguard_app"
        set image-type png
        set image-base64 ''
    next
end
config system replacemsg mail "email-block"
end
config system replacemsg mail "email-dlp-subject"
end
config system replacemsg mail "email-dlp-ban"
end
config system replacemsg mail "email-filesize"
end
config system replacemsg mail "partial"
end
config system replacemsg mail "smtp-block"
end
config system replacemsg mail "smtp-filesize"
end
config system replacemsg http "bannedword"
end
config system replacemsg http "url-block"
end
config system replacemsg http "urlfilter-err"
end
config system replacemsg http "infcache-block"
end
config system replacemsg http "http-block"
end
config system replacemsg http "http-filesize"
end
config system replacemsg http "http-dlp-ban"
end
config system replacemsg http "http-archive-block"
end
config system replacemsg http "http-contenttypeblock"
end
config system replacemsg http "https-invalid-cert-block"
end
config system replacemsg http "http-client-block"
end
config system replacemsg http "http-client-filesize"
end
config system replacemsg http "http-client-bannedword"
end
config system replacemsg http "http-post-block"
end
config system replacemsg http "http-client-archive-block"
end
config system replacemsg http "switching-protocols-block"
end
config system replacemsg webproxy "deny"
end
config system replacemsg webproxy "user-limit"
end
config system replacemsg webproxy "auth-challenge"
end
config system replacemsg webproxy "auth-login-fail"
end
config system replacemsg webproxy "auth-authorization-fail"
end
config system replacemsg webproxy "http-err"
end
config system replacemsg webproxy "auth-ip-blackout"
end
config system replacemsg ftp "ftp-dl-blocked"
end
config system replacemsg ftp "ftp-dl-filesize"
end
config system replacemsg ftp "ftp-dl-dlp-ban"
end
config system replacemsg ftp "ftp-explicit-banner"
end
config system replacemsg ftp "ftp-dl-archive-block"
end
config system replacemsg nntp "nntp-dl-blocked"
end
config system replacemsg nntp "nntp-dl-filesize"
end
config system replacemsg nntp "nntp-dlp-subject"
end
config system replacemsg nntp "nntp-dlp-ban"
end
config system replacemsg fortiguard-wf "ftgd-block"
end
config system replacemsg fortiguard-wf "http-err"
end
config system replacemsg fortiguard-wf "ftgd-ovrd"
end
config system replacemsg fortiguard-wf "ftgd-quota"
end
config system replacemsg fortiguard-wf "ftgd-warning"
end
config system replacemsg spam "ipblocklist"
end
config system replacemsg spam "smtp-spam-dnsbl"
end
config system replacemsg spam "smtp-spam-feip"
end
config system replacemsg spam "smtp-spam-helo"
end
config system replacemsg spam "smtp-spam-emailblack"
end
config system replacemsg spam "smtp-spam-mimeheader"
end
config system replacemsg spam "reversedns"
end
config system replacemsg spam "smtp-spam-bannedword"
end
config system replacemsg spam "smtp-spam-ase"
end
config system replacemsg spam "submit"
end
config system replacemsg alertmail "alertmail-virus"
end
config system replacemsg alertmail "alertmail-block"
end
config system replacemsg alertmail "alertmail-nids-event"
end
config system replacemsg alertmail "alertmail-crit-event"
end
config system replacemsg alertmail "alertmail-disk-full"
end
config system replacemsg admin "pre_admin-disclaimer-text"
end
config system replacemsg admin "post_admin-disclaimer-text"
end
config system replacemsg auth "auth-disclaimer-page-1"
end
config system replacemsg auth "auth-disclaimer-page-2"
end
config system replacemsg auth "auth-disclaimer-page-3"
end
config system replacemsg auth "auth-reject-page"
end
config system replacemsg auth "auth-login-page"
end
config system replacemsg auth "auth-login-failed-page"
end
config system replacemsg auth "auth-token-login-page"
end
config system replacemsg auth "auth-token-login-failed-page"
end
config system replacemsg auth "auth-success-msg"
end
config system replacemsg auth "auth-challenge-page"
end
config system replacemsg auth "auth-keepalive-page"
end
config system replacemsg auth "auth-portal-page"
end
config system replacemsg auth "auth-password-page"
end
config system replacemsg auth "auth-fortitoken-page"
end
config system replacemsg auth "auth-next-fortitoken-page"
end
config system replacemsg auth "auth-email-token-page"
end
config system replacemsg auth "auth-sms-token-page"
end
config system replacemsg auth "auth-email-harvesting-page"
end
config system replacemsg auth "auth-email-failed-page"
end
config system replacemsg auth "auth-cert-passwd-page"
end
config system replacemsg auth "auth-guest-print-page"
end
config system replacemsg auth "auth-guest-email-page"
end
config system replacemsg auth "auth-success-page"
end
config system replacemsg auth "auth-block-notification-page"
end
config system replacemsg sslvpn "sslvpn-login"
end
config system replacemsg sslvpn "sslvpn-header"
end
config system replacemsg sslvpn "sslvpn-limit"
end
config system replacemsg sslvpn "hostcheck-error"
end
config system replacemsg ec "endpt-download-portal"
end
config system replacemsg ec "endpt-download-portal-mac"
end
config system replacemsg ec "endpt-download-portal-ios"
end
config system replacemsg ec "endpt-download-portal-aos"
end
config system replacemsg ec "endpt-download-portal-other"
end
config system replacemsg ec "endpt-quarantine-portal"
end
config system replacemsg device-detection-portal "device-detection-failure"
end
config system replacemsg nac-quar "nac-quar-virus"
end
config system replacemsg nac-quar "nac-quar-dos"
end
config system replacemsg nac-quar "nac-quar-ips"
end
config system replacemsg nac-quar "nac-quar-dlp"
end
config system replacemsg nac-quar "nac-quar-admin"
end
config system replacemsg nac-quar "nac-quar-app"
end
config system replacemsg traffic-quota "per-ip-shaper-block"
end
config system replacemsg utm "virus-html"
end
config system replacemsg utm "client-virus-html"
end
config system replacemsg utm "virus-text"
end
config system replacemsg utm "dlp-html"
end
config system replacemsg utm "dlp-text"
end
config system replacemsg utm "appblk-html"
end
config system replacemsg utm "ipsblk-html"
end
config system replacemsg utm "exe-text"
end
config system replacemsg utm "waf-html"
end
config system central-management
    set type fortiguard
end
config user device-category
    edit "android-phone"
    next
    edit "android-tablet"
    next
    edit "blackberry-phone"
    next
    edit "blackberry-playbook"
    next
    edit "forticam"
    next
    edit "fortifone"
    next
    edit "fortinet-device"
    next
    edit "gaming-console"
    next
    edit "ip-phone"
    next
    edit "ipad"
    next
    edit "iphone"
    next
    edit "linux-pc"
    next
    edit "mac"
    next
    edit "media-streaming"
    next
    edit "printer"
    next
    edit "router-nat-device"
    next
    edit "windows-pc"
    next
    edit "windows-phone"
    next
    edit "windows-tablet"
    next
    edit "other-network-device"
    next
    edit "collected-emails"
    next
    edit "all"
    next
end
config wanopt storage
    edit "Internal"
        set size 48073
    next
end
config system cluster-sync
end
config system fortiguard
    set service-account-id "<EMAIL>"
    set sdns-server-ip "**************" 
end
config ips global
    set default-app-cat-mask 18446744073642442751
end
config ips dbinfo
    set version 1
end
config system email-server
    set server "notification.fortinet.net"
    set port 465
    set security smtps
end
config gui console
    unset preferences
end
config system session-helper
    edit 1
        set name pptp
        set protocol 6
        set port 1723
    next
    edit 2
        set name h323
        set protocol 6
        set port 1720
    next
    edit 3
        set name ras
        set protocol 17
        set port 1719
    next
    edit 4
        set name tns
        set protocol 6
        set port 1521
    next
    edit 5
        set name tftp
        set protocol 17
        set port 69
    next
    edit 6
        set name rtsp
        set protocol 6
        set port 554
    next
    edit 7
        set name rtsp
        set protocol 6
        set port 7070
    next
    edit 8
        set name rtsp
        set protocol 6
        set port 8554
    next
    edit 9
        set name ftp
        set protocol 6
        set port 21
    next
    edit 10
        set name mms
        set protocol 6
        set port 1863
    next
    edit 11
        set name pmap
        set protocol 6
        set port 111
    next
    edit 12
        set name pmap
        set protocol 17
        set port 111
    next
    edit 13
        set name sip
        set protocol 17
        set port 5060
    next
    edit 14
        set name dns-udp
        set protocol 17
        set port 53
    next
    edit 15
        set name rsh
        set protocol 6
        set port 514
    next
    edit 16
        set name rsh
        set protocol 6
        set port 512
    next
    edit 17
        set name dcerpc
        set protocol 6
        set port 135
    next
    edit 18
        set name dcerpc
        set protocol 17
        set port 135
    next
    edit 19
        set name mgcp
        set protocol 17
        set port 2427
    next
    edit 20
        set name mgcp
        set protocol 17
        set port 2727
    next
end
config system auto-install
    set auto-install-config enable
    set auto-install-image enable
end
config system ntp
    set ntpsync enable
    set syncinterval 60
end
config system settings
end
config system replacemsg-group
    edit "web-filter-default"
        set comment "System Generated"
        set group-type utm
    next
end
config system dhcp server
    edit 2
        set dns-service default
        set default-gateway ************
        set netmask *************
        set interface "mgmt"
        config ip-range
            edit 1
                set start-ip *************
                set end-ip *************
            next
        end
    next
end
config firewall address
    edit "SSLVPN_TUNNEL_ADDR1"
        set uuid f48f2a38-2687-51e7-fafa-dedf889a7e70
        set type iprange
        set associated-interface "ssl.root"
        set start-ip **************
        set end-ip **************
    next
    edit "all"
        set uuid f6b3191e-2687-51e7-cc07-8bf990bbc985
    next
    edit "none"
        set uuid f6b32224-2687-51e7-131d-414cf67ad9ad
        set subnet 0.0.0.0 ***************
    next
    edit "adobe"
        set uuid f6b32a1c-2687-51e7-bcc1-1bad611ddc95
        set type wildcard-fqdn
        set wildcard-fqdn "*.adobe.com"
    next
    edit "Adobe Login"
        set uuid f6b33264-2687-51e7-ffba-910c7a451dc3
        set type wildcard-fqdn
        set wildcard-fqdn "*.adobelogin.com"
    next
    edit "android"
        set uuid f6b33a84-2687-51e7-2615-ea39c8849598
        set type wildcard-fqdn
        set wildcard-fqdn "*.android.com"
    next
    edit "apple"
        set uuid f6b34290-2687-51e7-bf16-c5ab0223125a
        set type wildcard-fqdn
        set wildcard-fqdn "*.apple.com"
    next
    edit "appstore"
        set uuid f6b34a88-2687-51e7-2ceb-62753c76358c
        set type wildcard-fqdn
        set wildcard-fqdn "*.appstore.com"
    next
    edit "auth.gfx.ms"
        set uuid f6b352a8-2687-51e7-a7c0-cbcc939080b3
        set type fqdn
        set fqdn "auth.gfx.ms"
    next
    edit "autoupdate.opera.com"
        set uuid f6b35ae6-2687-51e7-b5d9-324f65c8d229
        set type fqdn
        set fqdn "autoupdate.opera.com"
    next
    edit "citrix"
        set uuid f6b36310-2687-51e7-c598-603f491fa996
        set type wildcard-fqdn
        set wildcard-fqdn "*.citrixonline.com"
    next
    edit "dropbox.com"
        set uuid f6b36b08-2687-51e7-7ebf-a96bb5ba256d
        set type wildcard-fqdn
        set wildcard-fqdn "*.dropbox.com"
    next
    edit "eease"
        set uuid f6b372f6-2687-51e7-f47a-7ddbf6c05bb2
        set type wildcard-fqdn
        set wildcard-fqdn "*.eease.com"
    next
    edit "firefox update server"
        set uuid f6b37aee-2687-51e7-a817-c2c3fb6e2c81
        set type wildcard-fqdn
        set wildcard-fqdn "aus*.mozilla.org"
    next
    edit "fortinet"
        set uuid f6b382e6-2687-51e7-4545-abb69e3d8c8d
        set type wildcard-fqdn
        set wildcard-fqdn "*.fortinet.com"
    next
    edit "googleapis.com"
        set uuid f6b38ad4-2687-51e7-ce6c-19a61f2370b8
        set type wildcard-fqdn
        set wildcard-fqdn "*.googleapis.com"
    next
    edit "google-drive"
        set uuid f6b392cc-2687-51e7-bab5-a29358437a11
        set type wildcard-fqdn
        set wildcard-fqdn "*drive.google.com"
    next
    edit "google-play"
        set uuid f6b39aba-2687-51e7-dcb1-8db5942f874f
        set type fqdn
        set fqdn "play.google.com"
    next
    edit "google-play2"
        set uuid f6b3a2d0-2687-51e7-41fa-1c89e0995300
        set type wildcard-fqdn
        set wildcard-fqdn "*.ggpht.com"
    next
    edit "google-play3"
        set uuid f6b3aabe-2687-51e7-bf50-251c36a30210
        set type wildcard-fqdn
        set wildcard-fqdn "*.books.google.com"
    next
    edit "Gotomeeting"
        set uuid f6b3b338-2687-51e7-5773-b667f3ce45b3
        set type wildcard-fqdn
        set wildcard-fqdn "*.gotomeeting.com"
    next
    edit "icloud"
        set uuid f6b3bb1c-2687-51e7-f79c-443f424d6bd8
        set type wildcard-fqdn
        set wildcard-fqdn "*.icloud.com"
    next
    edit "itunes"
        set uuid f6b3c300-2687-51e7-fd8a-fa54519fd419
        set type wildcard-fqdn
        set wildcard-fqdn "*itunes.apple.com"
    next
    edit "microsoft"
        set uuid f6b3cae4-2687-51e7-19e3-c3ba00bf877d
        set type wildcard-fqdn
        set wildcard-fqdn "*.microsoft.com"
    next
    edit "skype"
        set uuid f6b3d2c8-2687-51e7-1793-053c8b8a8264
        set type wildcard-fqdn
        set wildcard-fqdn "*.messenger.live.com"
    next
    edit "softwareupdate.vmware.com"
        set uuid f6b3daca-2687-51e7-7efd-90f53e5d8dfd
        set type fqdn
        set fqdn "softwareupdate.vmware.com"
    next
    edit "swscan.apple.com"
        set uuid f6b3e2fe-2687-51e7-c45d-d60f594b0976
        set type fqdn
        set fqdn "swscan.apple.com"
    next
    edit "update.microsoft.com"
        set uuid f6b3eb28-2687-51e7-df77-f4607dc23ed3
        set type fqdn
        set fqdn "update.microsoft.com"
    next
    edit "verisign"
        set uuid f6b3f348-2687-51e7-882a-8e08cc226658
        set type wildcard-fqdn
        set wildcard-fqdn "*.verisign.com"
    next
    edit "Windows update 2"
        set uuid f6b3fb36-2687-51e7-a33e-6343a7914c6d
        set type wildcard-fqdn
        set wildcard-fqdn "*.windowsupdate.com"
    next
    edit "live.com"
        set uuid f6b403e2-2687-51e7-0b26-2f66cd7fa400
        set type wildcard-fqdn
        set wildcard-fqdn "*.live.com"
    next
    edit "AzureNetwork"
        set uuid 93f27f14-797e-51e7-d18d-12afc174cc65
        set associated-interface "ToAzureVPN"
        set subnet ********** *************
    next
    edit "AzureNetwork2"
        set uuid f23b1d74-79a1-51e7-4234-c540af5a531f
        set associated-interface "ToAzureVPN2"
        set subnet ********** *************
    next
    edit "vpn-usuarios_range"
        set uuid 1ec2fa26-a47f-51e7-3da8-24a9530d049c
        set type iprange
        set comment "VPN: vpn-usuarios (Created by VPN wizard)"
        set start-ip ********
        set end-ip ********00
    next
    edit "SAP_produccion"
        set uuid 61d041d4-4ee3-51e8-3c9a-d5c34bf6d0ef
        set associated-interface "port2"
        set subnet *********** ***************
    next
    edit "HSBC_local_subnet_1"
        set uuid 20ba559c-4ef0-51e8-ed48-5e134bca5dbd
        set subnet *********** ***************
    next
    edit "HSBC_remote_subnet_1"
        set uuid 20be6da8-4ef0-51e8-ae05-1ee27846e5ec
        set subnet ************ ***************
    next
    edit "QA P1"
        set uuid c72a1536-3935-51e9-6b20-8eff97231718
        set comment "aplicacion externo QAP1"
        set associated-interface "port2"
        set subnet ************ ***************
    next
end
config firewall multicast-address
    edit "all"
        set start-ip *********
        set end-ip ***************
    next
    edit "all_hosts"
        set start-ip *********
        set end-ip *********
    next
    edit "all_routers"
        set start-ip *********
        set end-ip *********
    next
    edit "Bonjour"
        set start-ip *********51
        set end-ip *********51
    next
    edit "EIGRP"
        set start-ip *********0
        set end-ip *********0
    next
    edit "OSPF"
        set start-ip *********
        set end-ip *********
    next
end
config firewall address6
    edit "SSLVPN_TUNNEL_IPv6_ADDR1"
        set uuid f48f2cf4-2687-51e7-9b74-d17d3f5b8a38
        set ip6 fdff:ffff::/120
    next
    edit "all"
        set uuid f6b40cde-2687-51e7-bd1c-3086ef7aa43f
    next
    edit "none"
        set uuid f6b4140e-2687-51e7-833e-af0c2cb88722
        set ip6 ::/128
    next
end
config firewall multicast-address6
    edit "all"
        set ip6 ff00::/8
        set visibility disable
    next
end
config firewall addrgrp
    edit "HSBC_local"
        set uuid 20bc3c0e-4ef0-51e8-12df-64b332d2515c
        set member "HSBC_local_subnet_1"
        set comment "VPN: HSBC (Created by VPN wizard)"
    next
    edit "HSBC_remote"
        set uuid 20c002b2-4ef0-51e8-e39a-2f7d50e6bcc2
        set member "HSBC_remote_subnet_1"
        set comment "VPN: HSBC (Created by VPN wizard)"
    next
end
config firewall service category
    edit "General"
        set comment "General services."
    next
    edit "Web Access"
        set comment "Web access."
    next
    edit "File Access"
        set comment "File access."
    next
    edit "Email"
        set comment "Email services."
    next
    edit "Network Services"
        set comment "Network services."
    next
    edit "Authentication"
        set comment "Authentication service."
    next
    edit "Remote Access"
        set comment "Remote access."
    next
    edit "Tunneling"
        set comment "Tunneling service."
    next
    edit "VoIP, Messaging & Other Applications"
        set comment "VoIP, messaging, and other applications."
    next
    edit "Web Proxy"
        set comment "Explicit web proxy."
    next
end
config firewall service custom
    edit "ALL"
        set category "General"
        set protocol IP
    next
    edit "ALL_TCP"
        set category "General"
        set tcp-portrange 1-65535
    next
    edit "ALL_UDP"
        set category "General"
        set udp-portrange 1-65535
    next
    edit "ALL_ICMP"
        set category "General"
        set protocol ICMP
        unset icmptype
    next
    edit "ALL_ICMP6"
        set category "General"
        set protocol ICMP6
        unset icmptype
    next
    edit "GRE"
        set category "Tunneling"
        set protocol IP
        set protocol-number 47
    next
    edit "AH"
        set category "Tunneling"
        set protocol IP
        set protocol-number 51
    next
    edit "ESP"
        set category "Tunneling"
        set protocol IP
        set protocol-number 50
    next
    edit "AOL"
        set visibility disable
        set tcp-portrange 5190-5194
    next
    edit "BGP"
        set category "Network Services"
        set tcp-portrange 179
    next
    edit "DHCP"
        set category "Network Services"
        set udp-portrange 67-68
    next
    edit "DNS"
        set category "Network Services"
        set tcp-portrange 53
        set udp-portrange 53
    next
    edit "FINGER"
        set visibility disable
        set tcp-portrange 79
    next
    edit "FTP"
        set category "File Access"
        set tcp-portrange 21
    next
    edit "FTP_GET"
        set category "File Access"
        set tcp-portrange 21
    next
    edit "FTP_PUT"
        set category "File Access"
        set tcp-portrange 21
    next
    edit "GOPHER"
        set visibility disable
        set tcp-portrange 70
    next
    edit "H323"
        set category "VoIP, Messaging & Other Applications"
        set tcp-portrange 1720 1503
        set udp-portrange 1719
    next
    edit "HTTP"
        set category "Web Access"
        set tcp-portrange 80
    next
    edit "HTTPS"
        set category "Web Access"
        set tcp-portrange 443
    next
    edit "IKE"
        set category "Tunneling"
        set udp-portrange 500 4500
    next
    edit "IMAP"
        set category "Email"
        set tcp-portrange 143
    next
    edit "IMAPS"
        set category "Email"
        set tcp-portrange 993
    next
    edit "Internet-Locator-Service"
        set visibility disable
        set tcp-portrange 389
    next
    edit "IRC"
        set category "VoIP, Messaging & Other Applications"
        set tcp-portrange 6660-6669
    next
    edit "L2TP"
        set category "Tunneling"
        set tcp-portrange 1701
        set udp-portrange 1701
    next
    edit "LDAP"
        set category "Authentication"
        set tcp-portrange 389
    next
    edit "NetMeeting"
        set visibility disable
        set tcp-portrange 1720
    next
    edit "NFS"
        set category "File Access"
        set tcp-portrange 111 2049
        set udp-portrange 111 2049
    next
    edit "NNTP"
        set visibility disable
        set tcp-portrange 119
    next
    edit "NTP"
        set category "Network Services"
        set tcp-portrange 123
        set udp-portrange 123
    next
    edit "OSPF"
        set category "Network Services"
        set protocol IP
        set protocol-number 89
    next
    edit "PC-Anywhere"
        set category "Remote Access"
        set tcp-portrange 5631
        set udp-portrange 5632
    next
    edit "PING"
        set category "Network Services"
        set protocol ICMP
        set icmptype 8
        unset icmpcode
    next
    edit "TIMESTAMP"
        set protocol ICMP
        set visibility disable
        set icmptype 13
        unset icmpcode
    next
    edit "INFO_REQUEST"
        set protocol ICMP
        set visibility disable
        set icmptype 15
        unset icmpcode
    next
    edit "INFO_ADDRESS"
        set protocol ICMP
        set visibility disable
        set icmptype 17
        unset icmpcode
    next
    edit "ONC-RPC"
        set category "Remote Access"
        set tcp-portrange 111
        set udp-portrange 111
    next
    edit "DCE-RPC"
        set category "Remote Access"
        set tcp-portrange 135
        set udp-portrange 135
    next
    edit "POP3"
        set category "Email"
        set tcp-portrange 110
    next
    edit "POP3S"
        set category "Email"
        set tcp-portrange 995
    next
    edit "PPTP"
        set category "Tunneling"
        set tcp-portrange 1723
    next
    edit "QUAKE"
        set visibility disable
        set udp-portrange 26000 27000 27910 27960
    next
    edit "RAUDIO"
        set visibility disable
        set udp-portrange 7070
    next
    edit "REXEC"
        set visibility disable
        set tcp-portrange 512
    next
    edit "RIP"
        set category "Network Services"
        set udp-portrange 520
    next
    edit "RLOGIN"
        set visibility disable
        set tcp-portrange 513:512-1023
    next
    edit "RSH"
        set visibility disable
        set tcp-portrange 514:512-1023
    next
    edit "SCCP"
        set category "VoIP, Messaging & Other Applications"
        set tcp-portrange 2000
    next
    edit "SIP"
        set category "VoIP, Messaging & Other Applications"
        set tcp-portrange 5060
        set udp-portrange 5060
    next
    edit "SIP-MSNmessenger"
        set category "VoIP, Messaging & Other Applications"
        set tcp-portrange 1863
    next
    edit "SAMBA"
        set category "File Access"
        set tcp-portrange 139
    next
    edit "SMTP"
        set category "Email"
        set tcp-portrange 25
    next
    edit "SMTPS"
        set category "Email"
        set tcp-portrange 465
    next
    edit "SNMP"
        set category "Network Services"
        set tcp-portrange 161-162
        set udp-portrange 161-162
    next
    edit "SSH"
        set category "Remote Access"
        set tcp-portrange 22
    next
    edit "SYSLOG"
        set category "Network Services"
        set udp-portrange 514
    next
    edit "TALK"
        set visibility disable
        set udp-portrange 517-518
    next
    edit "TELNET"
        set category "Remote Access"
        set tcp-portrange 23
    next
    edit "TFTP"
        set category "File Access"
        set udp-portrange 69
    next
    edit "MGCP"
        set visibility disable
        set udp-portrange 2427 2727
    next
    edit "UUCP"
        set visibility disable
        set tcp-portrange 540
    next
    edit "VDOLIVE"
        set visibility disable
        set tcp-portrange 7000-7010
    next
    edit "WAIS"
        set visibility disable
        set tcp-portrange 210
    next
    edit "WINFRAME"
        set visibility disable
        set tcp-portrange 1494 2598
    next
    edit "X-WINDOWS"
        set category "Remote Access"
        set tcp-portrange 6000-6063
    next
    edit "PING6"
        set protocol ICMP6
        set visibility disable
        set icmptype 128
        unset icmpcode
    next
    edit "MS-SQL"
        set category "VoIP, Messaging & Other Applications"
        set tcp-portrange 1433 1434
    next
    edit "MYSQL"
        set category "VoIP, Messaging & Other Applications"
        set tcp-portrange 3306
    next
    edit "RDP"
        set category "Remote Access"
        set tcp-portrange 3389
    next
    edit "VNC"
        set category "Remote Access"
        set tcp-portrange 5900
    next
    edit "DHCP6"
        set category "Network Services"
        set udp-portrange 546 547
    next
    edit "SQUID"
        set category "Tunneling"
        set tcp-portrange 3128
    next
    edit "SOCKS"
        set category "Tunneling"
        set tcp-portrange 1080
        set udp-portrange 1080
    next
    edit "WINS"
        set category "Remote Access"
        set tcp-portrange 1512
        set udp-portrange 1512
    next
    edit "RADIUS"
        set category "Authentication"
        set udp-portrange 1812 1813
    next
    edit "RADIUS-OLD"
        set visibility disable
        set udp-portrange 1645 1646
    next
    edit "CVSPSERVER"
        set visibility disable
        set tcp-portrange 2401
        set udp-portrange 2401
    next
    edit "AFS3"
        set category "File Access"
        set tcp-portrange 7000-7009
        set udp-portrange 7000-7009
    next
    edit "TRACEROUTE"
        set category "Network Services"
        set udp-portrange 33434-33535
    next
    edit "RTSP"
        set category "VoIP, Messaging & Other Applications"
        set tcp-portrange 554 7070 8554
        set udp-portrange 554
    next
    edit "MMS"
        set visibility disable
        set tcp-portrange 1755
        set udp-portrange 1024-5000
    next
    edit "KERBEROS"
        set category "Authentication"
        set tcp-portrange 88 464
        set udp-portrange 88 464
    next
    edit "LDAP_UDP"
        set category "Authentication"
        set udp-portrange 389
    next
    edit "SMB"
        set category "File Access"
        set tcp-portrange 445
    next
    edit "NONE"
        set visibility disable
        set tcp-portrange 0
    next
    edit "webproxy"
        set explicit-proxy enable
        set category "Web Proxy"
        set protocol ALL
        set tcp-portrange 0-65535:0-65535
    next
    edit "SFTP_HSBC"
        set tcp-portrange 1-65535:10039
    next
end
config firewall service group
    edit "Email Access"
        set member "DNS" "IMAP" "IMAPS" "POP3" "POP3S" "SMTP" "SMTPS"
    next
    edit "Web Access"
        set member "DNS" "HTTP" "HTTPS"
    next
    edit "Windows AD"
        set member "DCE-RPC" "DNS" "KERBEROS" "LDAP" "LDAP_UDP" "SAMBA" "SMB"
    next
    edit "Exchange Server"
        set member "DCE-RPC" "DNS" "HTTPS"
    next
end
config webfilter ftgd-local-cat
    edit "custom1"
        set id 140
    next
    edit "custom2"
        set id 141
    next
end
config ips sensor
    edit "sniffer-profile"
        set comment "Monitor IPS attacks."
        config entries
            edit 1
                set severity high critical 
            next
        end
    next
    edit "default"
        set comment "Prevent critical attacks."
        config entries
            edit 1
                set severity medium high critical 
            next
        end
    next
    edit "all_default"
        set comment "All predefined signatures with default setting."
        config entries
            edit 1
            next
        end
    next
    edit "all_default_pass"
        set comment "All predefined signatures with PASS action."
        config entries
            edit 1
                set action pass
            next
        end
    next
    edit "protect_http_server"
        set comment "Protect against HTTP server-side vulnerabilities."
        config entries
            edit 1
                set location server 
                set protocol HTTP 
            next
        end
    next
    edit "protect_email_server"
        set comment "Protect against email server-side vulnerabilities."
        config entries
            edit 1
                set location server 
                set protocol SMTP POP3 IMAP 
            next
        end
    next
    edit "protect_client"
        set comment "Protect against client-side vulnerabilities."
        config entries
            edit 1
                set location client 
            next
        end
    next
    edit "high_security"
        set comment "Blocks all Critical/High/Medium and some Low severity vulnerabilities"
        set block-malicious-url enable
        config entries
            edit 1
                set severity medium high critical 
                set status enable
                set action block
            next
            edit 2
                set severity low 
            next
        end
    next
end
config firewall shaper traffic-shaper
    edit "high-priority"
        set maximum-bandwidth 1048576
        set per-policy enable
    next
    edit "medium-priority"
        set maximum-bandwidth 1048576
        set priority medium
        set per-policy enable
    next
    edit "low-priority"
        set maximum-bandwidth 1048576
        set priority low
        set per-policy enable
    next
    edit "guarantee-100kbps"
        set guaranteed-bandwidth 100
        set maximum-bandwidth 1048576
        set per-policy enable
    next
    edit "shared-1M-pipe"
        set maximum-bandwidth 1024
    next
end
config web-proxy global
    set proxy-fqdn "default.fqdn"
end
config application list
    edit "sniffer-profile"
        set comment "Monitor all applications."
        unset options
        config entries
            edit 1
                set action pass
            next
        end
    next
    edit "default"
        set comment "Monitor all applications."
        config entries
            edit 1
                set action pass
            next
        end
    next
    edit "block-botnet"
        config entries
            edit 1
                set category 19
            next
        end
    next
    edit "block-high-risk"
        config entries
            edit 1
                set category 2 6 19
            next
            edit 2
                set action pass
            next
        end
    next
end
config application casi profile
    edit "sniffer-profile"
        set comment "Monitor all applications."
        config entries
            edit 1
                set action pass
            next
        end
    next
    edit "default"
        set comment "Monitor all applications."
        config entries
            edit 1
                set action pass
            next
        end
    next
end
config dlp filepattern
    edit 1
        set name "builtin-patterns"
        config entries
            edit "*.bat"
            next
            edit "*.com"
            next
            edit "*.dll"
            next
            edit "*.doc"
            next
            edit "*.exe"
            next
            edit "*.gz"
            next
            edit "*.hta"
            next
            edit "*.ppt"
            next
            edit "*.rar"
            next
            edit "*.scr"
            next
            edit "*.tar"
            next
            edit "*.tgz"
            next
            edit "*.vb?"
            next
            edit "*.wps"
            next
            edit "*.xl?"
            next
            edit "*.zip"
            next
            edit "*.pif"
            next
            edit "*.cpl"
            next
        end
    next
    edit 2
        set name "all_executables"
        config entries
            edit "bat"
                set filter-type type
                set file-type bat
            next
            edit "exe"
                set filter-type type
                set file-type exe
            next
            edit "elf"
                set filter-type type
                set file-type elf
            next
            edit "hta"
                set filter-type type
                set file-type hta
            next
        end
    next
end
config dlp fp-sensitivity
    edit "Private"
    next
    edit "Critical"
    next
    edit "Warning"
    next
end
config dlp sensor
    edit "sniffer-profile"
        set comment "Log a summary of email and web traffic."
        set flow-based enable
        set summary-proto smtp pop3 imap http-get http-post
    next
    edit "default"
        set comment "Default sensor."
    next
    edit "Content_Summary"
        set summary-proto smtp pop3 imap http-get http-post ftp nntp mapi
    next
    edit "Content_Archive"
        set full-archive-proto smtp pop3 imap http-get http-post ftp nntp mapi
        set summary-proto smtp pop3 imap http-get http-post ftp nntp mapi
    next
    edit "Large-File"
        config filter
            edit 1
                set name "Large-File-Filter"
                set proto smtp pop3 imap http-get http-post mapi
                set filter-by file-size
                set file-size 5120
                set action log-only
            next
        end
    next
    edit "Credit-Card"
        config filter
            edit 1
                set name "Credit-Card-Filter"
                set severity high
                set proto smtp pop3 imap http-get http-post mapi
                set action log-only
            next
            edit 2
                set name "Credit-Card-Filter"
                set severity high
                set type message
                set proto smtp pop3 imap http-post mapi
                set action log-only
            next
        end
    next
    edit "SSN-Sensor"
        set comment "Match SSN numbers but NOT WebEx invite emails."
        config filter
            edit 1
                set name "SSN-Sensor-Filter"
                set severity high
                set type message
                set proto smtp pop3 imap mapi
                set filter-by regexp
                set regexp "WebEx"
            next
            edit 2
                set name "SSN-Sensor-Filter"
                set severity high
                set type message
                set proto smtp pop3 imap mapi
                set filter-by ssn
                set action log-only
            next
            edit 3
                set name "SSN-Sensor-Filter"
                set severity high
                set proto smtp pop3 imap http-get http-post ftp mapi
                set filter-by ssn
                set action log-only
            next
        end
    next
end
config log threat-weight
    config web
        edit 1
            set category 26
            set level high
        next
        edit 2
            set category 61
            set level high
        next
        edit 3
            set category 86
            set level high
        next
        edit 4
            set category 1
            set level medium
        next
        edit 5
            set category 3
            set level medium
        next
        edit 6
            set category 4
            set level medium
        next
        edit 7
            set category 5
            set level medium
        next
        edit 8
            set category 6
            set level medium
        next
        edit 9
            set category 12
            set level medium
        next
        edit 10
            set category 59
            set level medium
        next
        edit 11
            set category 62
            set level medium
        next
        edit 12
            set category 83
            set level medium
        next
        edit 13
            set category 72
        next
        edit 14
            set category 14
        next
    end
    config application
        edit 1
            set category 2
        next
        edit 2
            set category 6
            set level medium
        next
        edit 3
            set category 19
            set level critical
        next
    end
end
config icap profile
    edit "default"
    next
end
config vpn certificate ca
end
config vpn certificate local
    edit "Fortinet_CA_SSL"
        set password ENC 3cynqUFH0YQ35J9a2AqqSKOyI7rdXWTJs4fKQdVDurM6Z0PHMGdlgdZym/NnKWBWynmq81Aq8U6/jtnIyvoxFydwxQ7CjsrkNMLKWk/jW3KsLT4A6tycHi+vOriV/xiDcOTMhFpjXvn9ZkUJiK84DmCFnJLRF3cx3xOth+56Akxxl0isETs3Kf4YioKGSt1Q3U0V/A==
        set comments "This is the default CA certificate the SSL Inspection will use when generating new server certificates."
        set private-key "-----BEGIN ENCRYPTED PRIVATE KEY-----
MIIFDjBABgkqhkiG9w0BBQ0wMzAbBgkqhkiG9w0BBQwwDgQI+llxOLQfHo4CAggA
MBQGCCqGSIb3DQMHBAhHIF4tqJqEowSCBMhkzGjmofkJHKEM56wYTaul2sNgQTXu
zsTNytanTVXQpVhBRUIo+/FHwgC5MzmZ13YdmSJLn1BpMEP1j0HxncPlxzNRpXf6
insNzrBX3O+NoJYkDLvbVTq85/+1QS33s6QeS1uzNw91XmAioe+41qBHL7L/WHzq
58WMmWecgFEj5Bou59B4wpHVF30bL0qoADHV5god/WG5HcdbRJmtpPK9aEFKBOv1
fF48aRqkXe/GRLbycEZujaMA0AEfWge6vI8o0xLa37T5Fw6dWoTdPOHLi+5mRniX
J517VLfYSC7NvY8VZJQHXdYCdvXN8BlkOo+pm0Nu4dRS0MZEdT9gFSbI2RIL9HjY
32gouZracm7Ngzd/UOQUtjMJX2b1JOZoxQYOtKvSCBsa0bdSTY5CPjLADs8gUjWj
QH5iye836faYNbfihJERYRJfBfVOG3P4pdoZbAmNaIVghip1ofBI223zolRRXWeV
7G0pl4xEk99rMBQrY0XsHaCbK9t6AORDN5/d9gTM6qAEl0ImjHzJ+qJerwweANQv
ltWbDswvMXi27daTjDuhOSi1y0pYd9wKOKVval5pXM1enbO6cGKY/g/YNxzkEhSG
II+oD7sOqb1HCowF+/RyacZCmD/eYQSNUOchiPEPjkQ+9NiGY3AxGgxE4W5b68dQ
CZ5cy+7AkCu7t4NXr1quUvGm9MbXHuy6uY5WxMqZecL5bbuuwSxGDp9QWt9k1Kr5
fyUYbk/bkHK8W6fLHaYTYNCXidAWPamamrpnh4Y55KuslOj57OiFIRiwr/vikyqt
CEDbljpAGhvCAJ50qnx75BYhe6G5XxHUrk4HFGh+EkKcefuzfrGlMfWFUMFPWzlr
DRl7KZ4XhjCiebFtQHjT00muayttx2Jz/KOrnatLBDM2yaVuarn1jVVcvB9n/bAg
bIEpoipF3tDgZ/tOkHrO2Qnmc0Cl60im53vwNjxtZK3mvOcUT5k/KGZfx0ApUit+
JCbcRyN8Am5qVeseWLw0GCdOvoR2qfamIWwTfWwIB0zwx6LW7Uu3jfpSD53mCETh
/VJkNP+e1DwXSlQHf+ZehA1izA6aRliJV8hJbS21W+iBOFtAwNQrBN04ysBjCJWi
SXSpD3JZCMpiXRUKvELU9Bv3lVUhLxXutO2DqvkixxGFeletloV8OS0r6SMmn0We
Om4gllCR7Rno5tJYuBgfG5p9dd8fr2to/U/DSrJ4sL+wTiunrwMKPw5zVQ7N98FE
Npuq1By+KYaV0AQP6NFslVpd5Q/wdSXppuoq9mRK/wIGeGK7FnAe62TqFpFguJfv
sSlwtTp7bRjCmnfZLBAfv0cRitl24CgH8o2lsF/qlU2bkse5hBsdOODObZy2lS8g
b501h1Zs3QnRMukUWWz8IrIyz0UunBUclQUy6t3GZGQwAwsBbw7j3XfHW74fWpds
faXoMLYQ+UlclQ06xEgZwTFc+AdtMtolSEv7CuRGAnzPANAvL+uGgg7PHm6AY+Ei
91bhB3luG6UYPx406edKwrZDgBOVWEQBbzLveprYbcUSiBUBhWCrufB1ICd0zZNL
3x7lJdebU3KBCSQ6YA7lc67BZi6duHsVjoSHBbB8GvRedpytVANAoPT83DkWn2mj
uFo=
-----END ENCRYPTED PRIVATE KEY-----"
        set certificate "-----BEGIN CERTIFICATE-----
MIID5jCCAs6gAwIBAgIIV6AKb78XYbkwDQYJKoZIhvcNAQELBQAwgakxCzAJBgNV
BAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQHDAlTdW5ueXZhbGUx
ETAPBgNVBAoMCEZvcnRpbmV0MR4wHAYDVQQLDBVDZXJ0aWZpY2F0ZSBBdXRob3Jp
dHkxGTAXBgNVBAMMEEZHMjAwRDM5MTY4MjEyMjAxIzAhBgkqhkiG9w0BCQEWFHN1
cHBvcnRAZm9ydGluZXQuY29tMB4XDTE3MDQyMTExNDUwMVoXDTI3MDQyMjExNDUw
MVowgakxCzAJBgNVBAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQH
DAlTdW5ueXZhbGUxETAPBgNVBAoMCEZvcnRpbmV0MR4wHAYDVQQLDBVDZXJ0aWZp
Y2F0ZSBBdXRob3JpdHkxGTAXBgNVBAMMEEZHMjAwRDM5MTY4MjEyMjAxIzAhBgkq
hkiG9w0BCQEWFHN1cHBvcnRAZm9ydGluZXQuY29tMIIBIjANBgkqhkiG9w0BAQEF
AAOCAQ8AMIIBCgKCAQEAwEIrGeP0PW/2Sf3ut7YK8Ofjzcg77GSGmUptWzIYQ+Gf
G4ufreq4D22cNgcrIudAYdejTqNvGBs3eiRsZ+n+e3p46Qoc/i+vKIZ0SumdHq1V
xsLVLdUK853ZO2tNgXd+wVqfeibHIIOM2lK/bMzXlR+0tIpjtfseOprMGebEDIJt
k0cAgzRYiHCKUluPJ2RU/jwbzsu8ChsIhuk1kPnQhHsaWrtezARN+KFokkB30lYC
07Ge0BkeFZma4tB4jW3BH9UA3w1l+KzHKKFPvpv9VFu9+yMmp1k4JJiNzjUuncPv
kFQt2J7X57/3wm3mEmqxyEy9y63xrKdKYrHgJe0dRQIDAQABoxAwDjAMBgNVHRME
BTADAQH/MA0GCSqGSIb3DQEBCwUAA4IBAQAI3r97gudlwupLvw6TT1flAZBv4PnN
zJPonrqy5uy6wo1mshiQrVIhjl83YtmokLyDlyqxR+wKIMx0yE0eyB7Qwog2EjZR
mtvaIjywCV1YAfqCPThEa0JJl+3ttRyCudPlWVPCrwmgsjIiftP7v0A1T4ZrZM1V
SnyJiqNAyu9pqKB77evtkYVk5w/Ez3uD9NF1bdc7jQp6Uy0HQGrM8ZBUQ7pCKWj7
DG9KMWvXgtPyfnxDK1240Gsimsvb9avvOTEMJTgiF/TcCU5M/RmG6KxQWJr7gB5f
ilqw+GYar21oyp0CxRoiA6GlqpgFPtFBdL+SECrBQn6zy98a6m0VMvRl
-----END CERTIFICATE-----"
        set range global
        set source factory
    next
    edit "Fortinet_CA_Untrusted"
        set password ENC Jdpz0ilWr5tt8RJbwSXJ+AHHbWv6DzvYJNEYk4diB5IEH6XwsAlxQLLmIFxMHo9Hfzidu1t5n0C5nJlFq4c7JlVaHhJmDaD8UqbTSETduxSoXTvG0hqWnKVyMhc9cE3bPJR4BFodm+4D6kZAFoiUaY/JNO9nAqDaxw9yllmVOUTc79nKLHP5eBLL1Zap6TwurQTemA==
        set comments "This is the default CA certificate the SSL Inspection will use when generating new server certificates."
        set private-key "-----BEGIN ENCRYPTED PRIVATE KEY-----
MIIFDjBABgkqhkiG9w0BBQ0wMzAbBgkqhkiG9w0BBQwwDgQIDHSY9mmD84cCAggA
MBQGCCqGSIb3DQMHBAiqCddjdY7C9gSCBMjGskasOQNOuzx7I/X2xC9Y4pcYgeEC
PlT0b09mB9BHbYNngvaMCI7JLbhlw2WEyHPVZmSoOPK6mE7GpdzEW7Hd9Be/zY88
0eUy2ztVOjFW0GZWCASPZye57K/NlP3JZwqv4JFEDBJKHY188etlzoTYKIs5FHja
ylzBvKAa33mD/EdEsfsRNBxzWL4BszLOu6BRMfhz6IAtKegop3NkbjqlFedSViRc
sCXqrWM5BCmE1acdhEqYvueP4nM/0MReSdoryQN5uQiwax59Ow9UXtG5QfhRbhE8
8U/mk8IxfjFT2J5v55jz+HBhoTvPcNOivvtIt19R/5ua7udd3+82UN9gcZ4ZHYKe
va4sLUK9isaj1uDa4k/ABi9y8KiNSkPl5Fr4VN3GisKWQkNEF6E1N0XEkP7sVili
XNkCYxBGGRuLryFzTGfTxyraqxqFTCvC3g0GUjpLG3fjvBEvw5ykbLKBzS5Inn+g
HmMhFRYY2FaAABYTsCMzWMQW9yQxxQ7z8cm26YqAUE2uv3dYHYrdoRCrNtOMPzXg
9a0QZicLIykMjgJCCN8VZrgfzvh2ujmUcVYi/gQPYlhez/9vpGHTmw25D88s6e3B
08mnOx2ZflOpOanzLMfWXc6mUTQxzLAaezuSUNtQk6wPGQ8AF1VasEtxPuOHQWdZ
GtcqueEW0WYSa5ga4X/rvnAQfkPFLoSzh5nk984Cxdf3B/hTnBC8W1RS5yOF8KIi
CLElvHH925FHY4Q9kNxkmhe4mLcZ6tGeFnSMt5bVs2k5zFK1XHdTRUZxO6ljafiI
WGfWG0ZZvE/P2iWO1NPMWukQ9uqbM0tpyhGzSNge9lEtnT/3nCArNdfPS7COYqFe
iZ4x7vReGkhZOkLH52tHxkBqk3oVNtl/qb1ray43UqusDiZUZBhJX06QCU0AC+gL
3aUy9M01r5wlWj7BsP9EZ+Hx1cjrSjMKxHkGjns88YHhLlfogI9ThrGg0EY3xz82
EQBW8n7MhNge+bgVh0Q6KyLx/0UbuCJQsX3PD84TXytZITo1qMkI7TrtfEVI1/hh
dPUzfOzyQZdMWML08XGRYYgKWDuapiiOtyHLskZZ2shiL5HXHhTIc5uxQZypt1gX
xwiHJY8AgQU61CI63JHEvVMJDGkonH3stny/jykTOmLXCvW2iD4fxi25R3QJGJQd
4U6GAhPER4O0OpJRbF1E3Me7AxJ4rvE2E+H+2ywrVy4xwIT0e0vbq0tnd433r7MY
TBG4bNGC4uFfWtQnPuYL/QmZzv9eM46P8cAGlTLngG6d/r7/P0lJ/aIxQOqtiG+r
TshFwXCpq4b6uBa+8z13vk0zCFQsXazU+OrNDgDp/sfP6qfEB+Du+k01nvH6yKVB
N0jXXmAmYBTIe1Rhd19kSymJmiJKtRSKparCrNL3aHyAfQ6dQ3CtIS4hFVC7SUka
R0RGHeqW5IZXZfPmQiekBytD3iJdBX6COYdYC5nf2wh5dFyJZUt/K32fGahMegfe
OpaHwx24X9Kq2kbJl+YxnvqKmV//zoBBUChe6zNfentu76brbgvr8Lj5R1hcVj+G
LE22BkCQq0WtxTSVxMQw9orcFdvF1G83ngTt9EccVY0g7xbwbKaQ+NurHQ7qMVPp
UPU=
-----END ENCRYPTED PRIVATE KEY-----"
        set certificate "-----BEGIN CERTIFICATE-----
MIID8DCCAtigAwIBAgIIb04zDSAR9IgwDQYJKoZIhvcNAQELBQAwga4xCzAJBgNV
BAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQHDAlTdW5ueXZhbGUx
ETAPBgNVBAoMCEZvcnRpbmV0MR4wHAYDVQQLDBVDZXJ0aWZpY2F0ZSBBdXRob3Jp
dHkxHjAcBgNVBAMMFUZvcnRpbmV0IFVudHJ1c3RlZCBDQTEjMCEGCSqGSIb3DQEJ
ARYUc3VwcG9ydEBmb3J0aW5ldC5jb20wHhcNMTcwNDIxMTE0NTAxWhcNMjcwNDIy
MTE0NTAxWjCBrjELMAkGA1UEBhMCVVMxEzARBgNVBAgMCkNhbGlmb3JuaWExEjAQ
BgNVBAcMCVN1bm55dmFsZTERMA8GA1UECgwIRm9ydGluZXQxHjAcBgNVBAsMFUNl
cnRpZmljYXRlIEF1dGhvcml0eTEeMBwGA1UEAwwVRm9ydGluZXQgVW50cnVzdGVk
IENBMSMwIQYJKoZIhvcNAQkBFhRzdXBwb3J0QGZvcnRpbmV0LmNvbTCCASIwDQYJ
KoZIhvcNAQEBBQADggEPADCCAQoCggEBAK2/BQViXf9byBKTgELH1iiJYc8ag1m8
njfNrU5MrLwpzPmN/KuTD7ljaqT8Fn0DckRY1225ZqEvwZS99PwL+f6JyUjOIrTw
DnTd6tz9xnmQWB8kUEC3Hk2vyzwOn2Go4FvKG0/1FlrdFLSKYta4BMEMOex4rpH8
amn0ZBE9szrO55GN8584a+vCEbUdwr338yPPfE/cFsC8kD3XBswSnmoMnc6nCiXT
KleeGp/Q3pCjmFS5AvXJedslRwrwg2bLsDKrEng8rEfcSBPBmLBo3cHeq/mZo2gq
Cwl7EhsjaqzdoEgQ7kviII8n4Ttsb7rrAMVZMuCz0WPYY2V0hsdW9wUCAwEAAaMQ
MA4wDAYDVR0TBAUwAwEB/zANBgkqhkiG9w0BAQsFAAOCAQEANpEzTrjObKn5ZggR
7olUPRMfhkn5GAIr1cOH6Z4U0sws30nWHtYZCHJ0dR7LS4Bgdzshrj1qOLcShSiT
tBg0HZzQFiY0AZrjXyE1hSqj+sok0eKYwjlm44rVUzfS4qt/JGB9eDlPebAR6VGO
2LN0cz7OC1cMNr0vXQ3Mn5bXCmP/SZStw1M11EjqpOD7L4mrAzM9qcSAWlHvEgh5
5AMZfTesndp0lil7FGm6fyBACTFUUkNcHN8pHAVpDuywM8di3c5hmTU1HUtW2qLk
WCxi96buZMLFQ3f2pc0H4QIfbwn8M4FxCJGqzS/tcwsqEsnyaOSOEwAjv1Aial6Y
H3d2Jg==
-----END CERTIFICATE-----"
        set range global
        set source factory
    next
    edit "Fortinet_SSL"
        set password ENC tcKCLbTLM02EovYLmMDPW2h5g1FD3Gt4i+Zvrhbmy/D5oS+IwlMCGYND/A6reEnu6gFb6cW5deR0dGTZh/9RT883K+GLgpmVFUk87bFw2cnbovxZUO4pT0yh0o5tVPLj+4Ddhdpjbgqot10Ogb0iAm3MiBZ+YrdbmbPPc2ARAoBvn83jxR6eL4oJ18Q7mtf29fIhvg==
        set comments "This certificate is embedded in the hardware at the factory and is unique to this unit. "
        set private-key "-----BEGIN ENCRYPTED PRIVATE KEY-----
MIIFDjBABgkqhkiG9w0BBQ0wMzAbBgkqhkiG9w0BBQwwDgQIOwJFxJsNrJ8CAggA
MBQGCCqGSIb3DQMHBAjovLq3QJZBcwSCBMi/K/bfS+UQvb3tvLxKhEhPvamcAsU3
qxQlLY+hL9VPELvAFFwWoWdFCNQCXsfQnqEe5rb0/89eMphIrYq3MKG3yDgPQFV+
C/qGPsk0FIJu7nim3kBJFcYhjNVjaFlxSnVTOKgCyjIQYLn4AhYNnxYMTl33jL3c
nKyRPMUsWwW7Byh79prrKolvXsAva7Voe69dRjsZHMuz4qsYBvDvsmyNE0r6uJqo
gHpb28YW8xI1ZokacADCOyGlYqHpOWhGgnVfWRdsnnnTNV6vtU6y0Vry3FJJE4zw
J+S+7BLWhcrbTUdwCLxiadN17axigUwXxgG/Caau/Q69VatRE9C+iMgHVGu4Aqf/
71eQFZT5bx8VAqncddRY8jXpCN7Zd+NNXS8yN3JYEnV64AHBLdmlLrcZNOBca8bB
b+pjBb0E58iv5WjUMon3bAju24mCsnMk9jcCdb+stt22dLgKyWYp3ciZ8alAEqOn
LE+iE5I5Z+9jVb5C1nouyuvL/ZrpkK6v1N8k9ZA0GK8FVNY7+E/n274cUKC2UBTu
rWG9/jTdPAvyIRGV+0xD8xjqCt8ZCeunf5UumXRXZxtg6cnctPwfDcJiC0ZkDH7f
TCC+PKCCf8Cu0f/d2C60ULimW/MnmXNvv2kuq0Mzw6XsylWEjTlBI14pB5ri+lIl
mHW6Fa5TnAMUHlgEQlR8mawqQTbxW7fv6j99xNV0SF8EA+YdxgX5xvmCasDPO6FL
jaj/6+T7jovidi9EZPVof9Sb6v05VuDLRC17KdKPXIQi/QOq7sKv9ItcHS0LWx9M
Q+OXoF+3r9OjGzMy6VMs5cpm9o4kAX9drphd1ozAbZSjMVIPbU8K3EQeR9xm72hM
ASeDUvjU9IsJYK1gp8zP4Jysva70wJUOx40vn23ZUy04Z2EuRSH8aJKTY9MO6IiG
h8rIeu25Wvw/DzKX1OuTMhdWVzBfI/ujFjHe4ImUHWu5KleXAIP7C7NULxyZ4WhI
KWxB0qSEJTmz4zhkOccv4zuBaXQlHAGseeDB6Jjk+UMZJCE8YAZdrmMM2td6Rtm6
qQMwDq+UIuDgF5pgXVs2ln6jOCpQufCoIygW9LD85izDq1UEgQWlzich4YtqsBeb
TLEm4vTo34aWPueXg9X30YYcD4sKJ35+V/yveK+KpczJFUeRaoWWfZPbkoL032Kv
YBvWaCYOX1EJNUnL4DNFQW+N5KAivEFhhSsJ9HFe1pKwVjdo2h+NL+k0tGQLHgQf
qTyiyWfeuUgA0lHxevEYJP7UfQEcyCqn/vVq3Pq3PrL8Geh5/7PNWA/++pMxeKxk
MAUedx/acNdaDSRDGEHzpSKsxkSm8XXlDWQ2KehP1uS/YmaCdB+1JiuUy9Nh4s6l
mqeeFAOEzp1gRxwuINS3BiVOg9meNzW8tgl5HTLJJWjcsRADWNoHTpPt+bZmxA/3
CDotb/hRlm73pvFOjMoZPj+T0R5/wKAAMkxMxJLgl2yHcvCRX1Nim665F9m8Dium
dFlFBH9nE2H5RWFjNTmUxt5JYA43VPK+xp82F+68L3WMLbuaKsnuSKMAd0Col5p1
GWh/pl2VSC8v6LSjqRTEVjogeaCfxzNJngInY9mUh1CgjdZg29X8stX74ucAn22W
paY=
-----END ENCRYPTED PRIVATE KEY-----"
        set certificate "-----BEGIN CERTIFICATE-----
MIIDyzCCArOgAwIBAgIIbL/ipxE7CKowDQYJKoZIhvcNAQELBQAwgZ0xCzAJBgNV
BAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQHDAlTdW5ueXZhbGUx
ETAPBgNVBAoMCEZvcnRpbmV0MRIwEAYDVQQLDAlGb3J0aUdhdGUxGTAXBgNVBAMM
EEZHMjAwRDM5MTY4MjEyMjAxIzAhBgkqhkiG9w0BCQEWFHN1cHBvcnRAZm9ydGlu
ZXQuY29tMB4XDTE3MDQyMTExNDUwMVoXDTI3MDQyMjExNDUwMVowgZ0xCzAJBgNV
BAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRIwEAYDVQQHDAlTdW5ueXZhbGUx
ETAPBgNVBAoMCEZvcnRpbmV0MRIwEAYDVQQLDAlGb3J0aUdhdGUxGTAXBgNVBAMM
EEZHMjAwRDM5MTY4MjEyMjAxIzAhBgkqhkiG9w0BCQEWFHN1cHBvcnRAZm9ydGlu
ZXQuY29tMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA4DUaDRioGxRa
bhCMD6/FMhuWIZeI1pPnE2Vgl921Hhe811vBeyxsVQM6tUHavCFup926Xpsnd844
noB1BeiaqWWBYcyiULXZ6Im7duuqgrmzv5jE9tqu9kjPqtozmo2tM3oRwg5Dzu6F
asAjLsESD/toulNbTSWYS8TbF7nyA8bR9PvYuKz4gYOwaJGnGviqd90NB+TWgKWj
HbP/HhFohVhu09c+yp8c6wpApI6chd28TkFZrerrJdcmPsQKnoUVUuQXhzLF+Sq9
jQTnSQ247DTR58xxIYvRGkBLXzEYANqyKUdgb9cycHyF5CilAEMRETBKtRHdFfgG
F/cunJ2KDwIDAQABow0wCzAJBgNVHRMEAjAAMA0GCSqGSIb3DQEBCwUAA4IBAQAA
qRxi2bbla3kM1n0aMjgVL7M0NOAzeUGFFKmUMbWZyGjdo1lHxmczo4idM1dF3o4g
EIMRukQfPeX4MxGDaTVHeozSu/BUfNhQrVCzZSm5V5hADJ8NhUuIRa0RTBEFsn3x
eFwODrDITu8gjwT/nvoO03ZJomrllP80hmkGrRowdPbD+wA//JksRHPElkf/I2OZ
iS2M00jLy25312SN7lUucFjg1JXL9JxAYDy863Ny4kM1EsVw5fifDsD1IEk/lCD5
v9t7APi3CpPzr6SOjmxUsQ/XI/4RM7nCix4EkiIQPFRugenBnR0vc3QENQasENRt
muo2NPfLoq/LLstwty9J
-----END CERTIFICATE-----"
        set range global
        set source factory
    next
end
config user fortitoken
    edit "FTKMOB7846FD581E"
        set license "FTMTRIAL01907026"
    next
    edit "FTKMOB7887F0826C"
        set license "FTMTRIAL01907026"
    next
end
config user local
    edit "soluciones"
        set type password
        set passwd-time 2019-03-15 20:40:15
        set passwd ENC zFg02tf7mZWuaPZjui352XN/AJZaIKcOSGYETkD/e6yCXd2LJrtljD9AbhL+jRgI7hB3HhPHK+0pGTGCYSQsiMex6+P7GdeFu3Th/mTPXXnyS3rKScge367qyVpbOSUW68SGYIfic4e6VLOP6Za5Du1y/Mo1krCWP+rrof+b1/LI/XqUCP/J6WSsUuyZk8lNUIa0/Q==
    next
    edit "armand"
        set type password
        set passwd-time 2019-03-15 22:11:09
        set passwd ENC X46fSe0GRUGzNa3zb/vbnEjAsT1dWct7AekTWJ4iB63X6VmCLobT4xaq2+LbPGWWK2SdyIdMgc70p1XV4m9sSVEIx9o2ro+WDOQrA/YMDk+4tQ1zpSH7+AAgVsHukp6lPFh68hYDuZNUsut39IhbozHnHQWXtSX23Iw0EgobsRHsuzAHo9BfTiC5AnBuzBrlFyIWdA==
    next
end
config user setting
    set auth-cert "Fortinet_Factory"
    set auth-timeout 15
end
config user group
    edit "SSO_Guest_Users"
    next
    edit "VPN-USERS"
        set member "soluciones" "armand"
    next
end
config user device-group
    edit "Mobile Devices"
        set member "android-phone" "android-tablet" "blackberry-phone" "blackberry-playbook" "ipad" "iphone" "windows-phone" "windows-tablet"
        set comment "Phones, tablets, etc."
    next
    edit "Network Devices"
        set member "fortinet-device" "other-network-device" "router-nat-device"
        set comment "Routers, firewalls, gateways, etc."
    next
    edit "Others"
        set member "gaming-console" "media-streaming"
        set comment "Other devices."
    next
end
config vpn ssl web host-check-software
    edit "FortiClient-AV"
        set guid "C86EC76D-5A4C-40E7-BD94-59358E544D81"
    next
    edit "FortiClient-FW"
        set type fw
        set guid "528CB157-D384-4593-AAAA-E42DFF111CED"
    next
    edit "FortiClient-AV-Vista-Win7"
        set guid "385618A6-2256-708E-3FB9-7E98B93F91F9"
    next
    edit "FortiClient-FW-Vista-Win7"
        set type fw
        set guid "006D9983-6839-71D6-14E6-D7AD47ECD682"
    next
    edit "AVG-Internet-Security-AV"
        set guid "17DDD097-36FF-435F-9E1B-52D74245D6BF"
    next
    edit "AVG-Internet-Security-FW"
        set type fw
        set guid "8DECF618-9569-4340-B34A-D78D28969B66"
    next
    edit "AVG-Internet-Security-AV-Vista-Win7"
        set guid "0C939084-9E57-CBDB-EA61-0B0C7F62AF82"
    next
    edit "AVG-Internet-Security-FW-Vista-Win7"
        set type fw
        set guid "34A811A1-D438-CA83-C13E-A23981B1E8F9"
    next
    edit "CA-Anti-Virus"
        set guid "17CFD1EA-56CF-40B5-A06B-BD3A27397C93"
    next
    edit "CA-Internet-Security-AV"
        set guid "6B98D35F-BB76-41C0-876B-A50645ED099A"
    next
    edit "CA-Internet-Security-FW"
        set type fw
        set guid "38102F93-1B6E-4922-90E1-A35D8DC6DAA3"
    next
    edit "CA-Internet-Security-AV-Vista-Win7"
        set guid "3EED0195-0A4B-4EF3-CC4F-4F401BDC245F"
    next
    edit "CA-Internet-Security-FW-Vista-Win7"
        set type fw
        set guid "06D680B0-4024-4FAB-E710-E675E50F6324"
    next
    edit "CA-Personal-Firewall"
        set type fw
        set guid "14CB4B80-8E52-45EA-905E-67C1267B4160"
    next
    edit "F-Secure-Internet-Security-AV"
        set guid "E7512ED5-4245-4B4D-AF3A-382D3F313F15"
    next
    edit "F-Secure-Internet-Security-FW"
        set type fw
        set guid "D4747503-0346-49EB-9262-997542F79BF4"
    next
    edit "F-Secure-Internet-Security-AV-Vista-Win7"
        set guid "15414183-282E-D62C-CA37-EF24860A2F17"
    next
    edit "F-Secure-Internet-Security-FW-Vista-Win7"
        set type fw
        set guid "2D7AC0A6-6241-D774-E168-461178D9686C"
    next
    edit "Kaspersky-AV"
        set guid "2C4D4BC6-0793-4956-A9F9-E252435469C0"
    next
    edit "Kaspersky-FW"
        set type fw
        set guid "2C4D4BC6-0793-4956-A9F9-E252435469C0"
    next
    edit "Kaspersky-AV-Vista-Win7"
        set guid "AE1D740B-8F0F-D137-211D-873D44B3F4AE"
    next
    edit "Kaspersky-FW-Vista-Win7"
        set type fw
        set guid "9626F52E-C560-D06F-0A42-2E08BA60B3D5"
    next
    edit "McAfee-Internet-Security-Suite-AV"
        set guid "84B5EE75-6421-4CDE-A33A-DD43BA9FAD83"
    next
    edit "McAfee-Internet-Security-Suite-FW"
        set type fw
        set guid "94894B63-8C7F-4050-BDA4-813CA00DA3E8"
    next
    edit "McAfee-Internet-Security-Suite-AV-Vista-Win7"
        set guid "*************-3EA7-ABB3-1B136EB04637"
    next
    edit "McAfee-Internet-Security-Suite-FW-Vista-Win7"
        set type fw
        set guid "BE0ED752-0A0B-3FFF-80EC-B2269063014C"
    next
    edit "McAfee-Virus-Scan-Enterprise"
        set guid "918A2B0B-2C60-4016-A4AB-E868DEABF7F0"
    next
    edit "Norton-360-2.0-AV"
        set guid "A5F1BC7C-EA33-4247-961C-0217208396C4"
    next
    edit "Norton-360-2.0-FW"
        set type fw
        set guid "371C0A40-5A0C-4AD2-A6E5-69C02037FBF3"
    next
    edit "Norton-360-3.0-AV"
        set guid "E10A9785-9598-4754-B552-92431C1C35F8"
    next
    edit "Norton-360-3.0-FW"
        set type fw
        set guid "7C21A4C9-F61F-4AC4-B722-A6E19C16F220"
    next
    edit "Norton-Internet-Security-AV"
        set guid "E10A9785-9598-4754-B552-92431C1C35F8"
    next
    edit "Norton-Internet-Security-FW"
        set type fw
        set guid "7C21A4C9-F61F-4AC4-B722-A6E19C16F220"
    next
    edit "Norton-Internet-Security-AV-Vista-Win7"
        set guid "88C95A36-8C3B-2F2C-1B8B-30FCCFDC4855"
    next
    edit "Norton-Internet-Security-FW-Vista-Win7"
        set type fw
        set guid "B0F2DB13-C654-2E74-30D4-99C9310F0F2E"
    next
    edit "Symantec-Endpoint-Protection-AV"
        set guid "FB06448E-52B8-493A-90F3-E43226D3305C"
    next
    edit "Symantec-Endpoint-Protection-FW"
        set type fw
        set guid "BE898FE3-CD0B-4014-85A9-03DB9923DDB6"
    next
    edit "Symantec-Endpoint-Protection-AV-Vista-Win7"
        set guid "88C95A36-8C3B-2F2C-1B8B-30FCCFDC4855"
    next
    edit "Symantec-Endpoint-Protection-FW-Vista-Win7"
        set type fw
        set guid "B0F2DB13-C654-2E74-30D4-99C9310F0F2E"
    next
    edit "Panda-Antivirus+Firewall-2008-AV"
        set guid "EEE2D94A-D4C1-421A-AB2C-2CE8FE51747A"
    next
    edit "Panda-Antivirus+Firewall-2008-FW"
        set type fw
        set guid "7B090DC0-8905-4BAF-8040-FD98A41C8FB8"
    next
    edit "Panda-Internet-Security-AV"
        set guid "4570FB70-5C9E-47E9-B16C-A3A6A06C4BF0"
    next
    edit "Panda-Internet-Security-2006~2007-FW"
        set type fw
        set guid "4570FB70-5C9E-47E9-B16C-A3A6A06C4BF0"
    next
    edit "Panda-Internet-Security-2008~2009-FW"
        set type fw
        set guid "7B090DC0-8905-4BAF-8040-FD98A41C8FB8"
    next
    edit "Sophos-Anti-Virus"
        set guid "3F13C776-3CBE-4DE9-8BF6-09E5183CA2BD"
    next
    edit "Sophos-Enpoint-Secuirty-and-Control-FW"
        set type fw
        set guid "0786E95E-326A-4524-9691-41EF88FB52EA"
    next
    edit "Sophos-Enpoint-Secuirty-and-Control-AV-Vista-Win7"
        set guid "479CCF92-4960-B3E0-7373-BF453B467D2C"
    next
    edit "Sophos-Enpoint-Secuirty-and-Control-FW-Vista-Win7"
        set type fw
        set guid "7FA74EB7-030F-B2B8-582C-1670C5953A57"
    next
    edit "Trend-Micro-AV"
        set guid "7D2296BC-32CC-4519-917E-52E652474AF5"
    next
    edit "Trend-Micro-FW"
        set type fw
        set guid "3E790E9E-6A5D-4303-A7F9-185EC20F3EB6"
    next
    edit "Trend-Micro-AV-Vista-Win7"
        set guid "48929DFC-7A52-A34F-8351-C4DBEDBD9C50"
    next
    edit "Trend-Micro-FW-Vista-Win7"
        set type fw
        set guid "70A91CD9-303D-A217-A80E-6DEE136EDB2B"
    next
    edit "ZoneAlarm-AV"
        set guid "5D467B10-818C-4CAB-9FF7-6893B5B8F3CF"
    next
    edit "ZoneAlarm-FW"
        set type fw
        set guid "829BDA32-94B3-44F4-8446-F8FCFF809F8B"
    next
    edit "ZoneAlarm-AV-Vista-Win7"
        set guid "D61596DF-D219-341C-49B3-AD30538CBC5B"
    next
    edit "ZoneAlarm-FW-Vista-Win7"
        set type fw
        set guid "EE2E17FA-9876-3544-62EC-0405AD5FFB20"
    next
    edit "ESET-Smart-Security-AV"
        set guid "19259FAE-8396-A113-46DB-15B0E7DFA289"
    next
    edit "ESET-Smart-Security-FW"
        set type fw
        set guid "211E1E8B-C9F9-A04B-6D84-BC85190CE5F2"
    next
end
config vpn ssl web portal
    edit "full-access"
        set tunnel-mode enable
        set ipv6-tunnel-mode enable
        set web-mode enable
        set ip-pools "SSLVPN_TUNNEL_ADDR1"
        set ipv6-pools "SSLVPN_TUNNEL_IPv6_ADDR1"
    next
    edit "web-access"
        set web-mode enable
    next
    edit "tunnel-access"
        set tunnel-mode enable
        set keep-alive enable
        set ip-pools "SSLVPN_TUNNEL_ADDR1"
    next
end
config vpn ssl settings
    set servercert "Fortinet_Factory"
    set port 443
end
config voip profile
    edit "default"
        set comment "Default VoIP profile."
    next
    edit "strict"
        config sip
            set malformed-request-line discard
            set malformed-header-via discard
            set malformed-header-from discard
            set malformed-header-to discard
            set malformed-header-call-id discard
            set malformed-header-cseq discard
            set malformed-header-rack discard
            set malformed-header-rseq discard
            set malformed-header-contact discard
            set malformed-header-record-route discard
            set malformed-header-route discard
            set malformed-header-expires discard
            set malformed-header-content-type discard
            set malformed-header-content-length discard
            set malformed-header-max-forwards discard
            set malformed-header-allow discard
            set malformed-header-p-asserted-identity discard
            set malformed-header-sdp-v discard
            set malformed-header-sdp-o discard
            set malformed-header-sdp-s discard
            set malformed-header-sdp-i discard
            set malformed-header-sdp-c discard
            set malformed-header-sdp-b discard
            set malformed-header-sdp-z discard
            set malformed-header-sdp-k discard
            set malformed-header-sdp-a discard
            set malformed-header-sdp-t discard
            set malformed-header-sdp-r discard
            set malformed-header-sdp-m discard
        end
    next
end
config webfilter profile
    edit "sniffer-profile"
        set comment "Monitor web traffic."
        set inspection-mode flow-based
        config ftgd-wf
            config filters
                edit 1
                next
                edit 2
                    set category 1
                next
                edit 3
                    set category 2
                next
                edit 4
                    set category 3
                next
                edit 5
                    set category 4
                next
                edit 6
                    set category 5
                next
                edit 7
                    set category 6
                next
                edit 8
                    set category 7
                next
                edit 9
                    set category 8
                next
                edit 10
                    set category 9
                next
                edit 11
                    set category 11
                next
                edit 12
                    set category 12
                next
                edit 13
                    set category 13
                next
                edit 14
                    set category 14
                next
                edit 15
                    set category 15
                next
                edit 16
                    set category 16
                next
                edit 17
                    set category 17
                next
                edit 18
                    set category 18
                next
                edit 19
                    set category 19
                next
                edit 20
                    set category 20
                next
                edit 21
                    set category 23
                next
                edit 22
                    set category 24
                next
                edit 23
                    set category 25
                next
                edit 24
                    set category 26
                next
                edit 25
                    set category 28
                next
                edit 26
                    set category 29
                next
                edit 27
                    set category 30
                next
                edit 28
                    set category 31
                next
                edit 29
                    set category 33
                next
                edit 30
                    set category 34
                next
                edit 31
                    set category 35
                next
                edit 32
                    set category 36
                next
                edit 33
                    set category 37
                next
                edit 34
                    set category 38
                next
                edit 35
                    set category 39
                next
                edit 36
                    set category 40
                next
                edit 37
                    set category 41
                next
                edit 38
                    set category 42
                next
                edit 39
                    set category 43
                next
                edit 40
                    set category 44
                next
                edit 41
                    set category 46
                next
                edit 42
                    set category 47
                next
                edit 43
                    set category 48
                next
                edit 44
                    set category 49
                next
                edit 45
                    set category 50
                next
                edit 46
                    set category 51
                next
                edit 47
                    set category 52
                next
                edit 48
                    set category 53
                next
                edit 49
                    set category 54
                next
                edit 50
                    set category 55
                next
                edit 51
                    set category 56
                next
                edit 52
                    set category 57
                next
                edit 53
                    set category 58
                next
                edit 54
                    set category 59
                next
                edit 55
                    set category 61
                next
                edit 56
                    set category 62
                next
                edit 57
                    set category 63
                next
                edit 58
                    set category 64
                next
                edit 59
                    set category 65
                next
                edit 60
                    set category 66
                next
                edit 61
                    set category 67
                next
                edit 62
                    set category 68
                next
                edit 63
                    set category 69
                next
                edit 64
                    set category 70
                next
                edit 65
                    set category 71
                next
                edit 66
                    set category 72
                next
                edit 67
                    set category 75
                next
                edit 68
                    set category 76
                next
                edit 69
                    set category 77
                next
                edit 70
                    set category 78
                next
                edit 71
                    set category 79
                next
                edit 72
                    set category 80
                next
                edit 73
                    set category 81
                next
                edit 74
                    set category 82
                next
                edit 75
                    set category 83
                next
                edit 76
                    set category 84
                next
                edit 77
                    set category 85
                next
                edit 78
                    set category 86
                next
                edit 79
                    set category 87
                next
                edit 80
                    set category 88
                next
                edit 81
                    set category 89
                next
                edit 82
                    set category 140
                next
                edit 83
                    set category 141
                next
            end
        end
    next
    edit "default"
        set comment "Default web filtering."
        config web
            set blacklist enable
        end
        config ftgd-wf
            unset options
            set category-override 140 141
            config filters
                edit 1
                    set category 2
                    set action block
                next
                edit 2
                    set category 7
                    set action block
                next
                edit 3
                    set category 8
                    set action block
                next
                edit 4
                    set category 9
                    set action block
                next
                edit 6
                    set category 12
                    set action warning
                next
                edit 7
                    set category 13
                    set action block
                next
                edit 8
                    set category 14
                    set action block
                next
                edit 9
                    set category 15
                    set action block
                next
                edit 10
                    set category 16
                    set action block
                next
                edit 12
                    set category 57
                    set action block
                next
                edit 13
                    set category 63
                    set action block
                next
                edit 14
                    set category 64
                    set action block
                next
                edit 15
                    set category 65
                    set action block
                next
                edit 16
                    set category 66
                    set action block
                next
                edit 17
                    set category 67
                    set action block
                next
                edit 18
                    set category 26
                    set action block
                next
                edit 19
                    set category 61
                    set action block
                next
                edit 20
                    set category 86
                    set action block
                next
                edit 21
                    set category 88
                    set action block
                next
                edit 23
                    set category 75
                    set action block
                next
            end
        end
    next
    edit "monitor-all"
        set comment "Monitor and log all visited URLs, proxy-based."
        config ftgd-wf
            unset options
            config filters
                edit 1
                    set category 1
                next
                edit 2
                    set category 3
                next
                edit 3
                    set category 4
                next
                edit 4
                    set category 5
                next
                edit 5
                    set category 6
                next
                edit 6
                    set category 12
                next
                edit 7
                    set category 59
                next
                edit 8
                    set category 62
                next
                edit 9
                    set category 83
                next
                edit 10
                    set category 2
                next
                edit 11
                    set category 7
                next
                edit 12
                    set category 8
                next
                edit 13
                    set category 9
                next
                edit 14
                    set category 11
                next
                edit 15
                    set category 13
                next
                edit 16
                    set category 14
                next
                edit 17
                    set category 15
                next
                edit 18
                    set category 16
                next
                edit 19
                    set category 57
                next
                edit 20
                    set category 63
                next
                edit 21
                    set category 64
                next
                edit 22
                    set category 65
                next
                edit 23
                    set category 66
                next
                edit 24
                    set category 67
                next
                edit 25
                    set category 19
                next
                edit 26
                    set category 24
                next
                edit 27
                    set category 25
                next
                edit 28
                    set category 72
                next
                edit 29
                    set category 75
                next
                edit 30
                    set category 76
                next
                edit 31
                    set category 26
                next
                edit 32
                    set category 61
                next
                edit 33
                    set category 86
                next
                edit 34
                    set category 17
                next
                edit 35
                    set category 18
                next
                edit 36
                    set category 20
                next
                edit 37
                    set category 23
                next
                edit 38
                    set category 28
                next
                edit 39
                    set category 29
                next
                edit 40
                    set category 30
                next
                edit 41
                    set category 33
                next
                edit 42
                    set category 34
                next
                edit 43
                    set category 35
                next
                edit 44
                    set category 36
                next
                edit 45
                    set category 37
                next
                edit 46
                    set category 38
                next
                edit 47
                    set category 39
                next
                edit 48
                    set category 40
                next
                edit 49
                    set category 42
                next
                edit 50
                    set category 44
                next
                edit 51
                    set category 46
                next
                edit 52
                    set category 47
                next
                edit 53
                    set category 48
                next
                edit 54
                    set category 54
                next
                edit 55
                    set category 55
                next
                edit 56
                    set category 58
                next
                edit 57
                    set category 68
                next
                edit 58
                    set category 69
                next
                edit 59
                    set category 70
                next
                edit 60
                    set category 71
                next
                edit 61
                    set category 77
                next
                edit 62
                    set category 78
                next
                edit 63
                    set category 79
                next
                edit 64
                    set category 80
                next
                edit 65
                    set category 82
                next
                edit 66
                    set category 85
                next
                edit 67
                    set category 87
                next
                edit 68
                    set category 31
                next
                edit 69
                    set category 41
                next
                edit 70
                    set category 43
                next
                edit 71
                    set category 49
                next
                edit 72
                    set category 50
                next
                edit 73
                    set category 51
                next
                edit 74
                    set category 52
                next
                edit 75
                    set category 53
                next
                edit 76
                    set category 56
                next
                edit 77
                    set category 81
                next
                edit 78
                    set category 84
                next
                edit 79
                next
            end
        end
        set log-all-url enable
        set web-content-log disable
        set web-filter-activex-log disable
        set web-filter-command-block-log disable
        set web-filter-cookie-log disable
        set web-filter-applet-log disable
        set web-filter-jscript-log disable
        set web-filter-js-log disable
        set web-filter-vbs-log disable
        set web-filter-unknown-log disable
        set web-filter-referer-log disable
        set web-filter-cookie-removal-log disable
        set web-url-log disable
        set web-invalid-domain-log disable
        set web-ftgd-err-log disable
        set web-ftgd-quota-usage disable
    next
end
config webfilter ftgd-local-rating
    edit "ruvpp-portal-externo-qa.azurewebsites.net"
        set rating 52
    next
    edit "ruvpp-oferta-web-qa.azurewebsites.net"
        set rating 52
    next
    edit "ruvpp-oferta-api-qa.azurewebsites.net"
        set rating 52
    next
    edit "geoespacial.ruv.org.mx"
        set rating 52
    next
    edit "geo.ruv.org.mx:8080/demo"
        set rating 52
    next
    edit "geo.ruv.org.mx"
        set rating 52
    next
end
config webfilter search-engine
    edit "google"
        set hostname ".*\\.google\\..*"
        set url "^\\/((custom|search|images|videosearch|webhp)\\?)"
        set query "q="
        set safesearch url
        set safesearch-str "&safe=active"
    next
    edit "yahoo"
        set hostname ".*\\.yahoo\\..*"
        set url "^\\/search(\\/video|\\/images){0,1}(\\?|;)"
        set query "p="
        set safesearch url
        set safesearch-str "&vm=r"
    next
    edit "bing"
        set hostname ".*\\.bing\\..*"
        set url "^(\\/images|\\/videos)?(\\/search|\\/async|\\/asyncv2)\\?"
        set query "q="
        set safesearch header
    next
    edit "yandex"
        set hostname "yandex\\..*"
        set url "^\\/((yand|images\\/|video\\/)(search)|search\\/)\\?"
        set query "text="
        set safesearch url
        set safesearch-str "&family=yes"
    next
    edit "youtube"
        set hostname ".*\\.youtube\\..*"
        set safesearch header
    next
    edit "baidu"
        set hostname ".*\\.baidu\\.com"
        set url "^\\/s?\\?"
        set query "wd="
    next
    edit "baidu2"
        set hostname ".*\\.baidu\\.com"
        set url "^\\/(ns|q|m|i|v)\\?"
        set query "word="
    next
    edit "baidu3"
        set hostname "tieba\\.baidu\\.com"
        set url "^\\/f\\?"
        set query "kw="
    next
end
config vpn ipsec phase1-interface
    edit "ToAzureVPN"
        set interface "wan1"
        set ike-version 2
        set keylife 28800
        set peertype any
        set proposal aes128-sha256
        set dpd on-idle
        set dhgrp 2
        set nattraversal disable
        set remote-gw *************
        set psksecret ENC F5QdgEJ1YC8qHNgIwvS/oBGIg/6ka+SofLPMZ2dwD5vYDPTuUlDMlwUcdPTGMATMqyZcym3eIbEuqMdVYwRtK/VhxaK8C53NreEPOt0NnRMEsZUS1uAnItJZmfv9H+FI3PVqaMqLAm6PfJkrsFoZ7rKTbvuXLRnjJweeSIC0I4jYZcGDiY08g8rm3UKPI5mrdl1Bmw==
    next
    edit "ToAzureVPN2"
        set interface "wan1"
        set ike-version 2
        set keylife 28800
        set peertype any
        set proposal aes256-sha1 3des-sha1 aes256-sha256
        set dpd on-idle
        set dhgrp 2
        set nattraversal disable
        set remote-gw *************
        set psksecret ENC YXm33WdOXMDk9iIVSEwejcJ8nCSdsON0q3PAG9KQT33ar08UkCzCQcGl0R3rTTwkM2I8VDGPFI/RNQwg7wBK5tsUDA0svCzSCiRj4ihm7hcAklxORkzy5EOVPwQk+wltOZwLO6ImwFK4bMeIXFjuLUz3VQZWDmy80fmXhPP+Bd0N0kPBVHse8cBZ1kobSGl0DrgkHA==
    next
    edit "vpn-usuarios"
        set type dynamic
        set interface "wan1"
        set mode aggressive
        set peertype any
        set mode-cfg enable
        set comments "VPN: vpn-usuarios (Created by VPN wizard)"
        set wizard-type dialup-forticlient
        set xauthtype auto
        set authusrgrp "VPN-USERS"
        set ipv4-start-ip ********
        set ipv4-end-ip ********00
        set dns-mode auto
        set save-password enable
        set psksecret ENC G4T+r8Has/QRhzA4RiDp8u0mbVDqFUmb5UPRpIUekOZMpqJ072VFdEBLj8XEiOqYjgsq99r460TuA89rFpXxTp8KKryhi4iX1ZL3W2dJFJ7ZH2Y7Fr1ASTXEPYqTyQexgxwgPgn7yLfm3dKVlQqgTuVCZK0hwELRx1Mb0mzYxmt2vP8xZk9kzoznBuB93dROfZzmqw==
    next
    edit "HSBC"
        set interface "wan1"
        set peertype any
        set proposal aes256-sha1
        set comments "VPN: HSBC (Created by VPN wizard)"
        set dhgrp 2
        set remote-gw *************
        set psksecret ENC YzD0HUjWgut18ySQnks0HLDgS2m+ZBQfFjDuO0IvOnWVc4EGxH+X9FLnaqKf6yzDSqGhbMnp7JatXmCDVgW8pNE9RWZbLZ99ZsszOgPgVrRhLHDAd5gXCscuoM591NbKWYVqjJqvMEbQAoIrUNBazph4X9MHHKE0RpC9StvPTCvorHbGSVOK1cuptojEtWdGO/rGog==
    next
end
config vpn ipsec phase2-interface
    edit "ToAzureVPN"
        set phase1name "ToAzureVPN"
        set proposal aes128-sha256
        set pfs disable
        set replay disable
        set keylifeseconds 27000
        set src-subnet ********* *************
        set dst-subnet ********** *************
    next
    edit "ToAzureVPN2"
        set phase1name "ToAzureVPN2"
        set pfs disable
        set src-subnet ********* *************
        set dst-subnet ********** *************
    next
    edit "vpn-usuarios"
        set phase1name "vpn-usuarios"
        set comments "VPN: vpn-usuarios (Created by VPN wizard)"
    next
    edit "HSBC"
        set phase1name "HSBC"
        set proposal aes256-sha1
        set pfs disable
        set replay disable
        set auto-negotiate enable
        set comments "VPN: HSBC (Created by VPN wizard)"
        set keylifeseconds 3600
        set src-subnet *********** ***************
        set dst-subnet ************ ***************
    next
end
config vpn l2tp
    set eip **************
    set sip **************
    set status enable
    set usrgrp "VPN-USERS"
end
config dnsfilter profile
    edit "default"
        set comment "Default dns filtering."
        config ftgd-dns
            config filters
                edit 1
                    set category 2
                next
                edit 2
                    set category 7
                next
                edit 3
                    set category 8
                next
                edit 4
                    set category 9
                next
                edit 5
                    set category 11
                next
                edit 6
                    set category 12
                next
                edit 7
                    set category 13
                next
                edit 8
                    set category 14
                next
                edit 9
                    set category 15
                next
                edit 10
                    set category 16
                next
                edit 11
                next
                edit 12
                    set category 57
                next
                edit 13
                    set category 63
                next
                edit 14
                    set category 64
                next
                edit 15
                    set category 65
                next
                edit 16
                    set category 66
                next
                edit 17
                    set category 67
                next
                edit 18
                    set category 26
                    set action block
                next
                edit 19
                    set category 61
                    set action block
                next
                edit 20
                    set category 86
                    set action block
                next
                edit 21
                    set category 88
                    set action block
                next
            end
        end
        set block-botnet enable
    next
end
config antivirus settings
    set grayware enable
end
config antivirus profile
    edit "sniffer-profile"
        set comment "Scan files and monitor viruses."
        config http
            set options scan
        end
        config ftp
            set options scan
        end
        config imap
            set options scan
            set executables virus
        end
        config pop3
            set options scan
            set executables virus
        end
        config smtp
            set options scan
            set executables virus
        end
    next
    edit "default"
        set comment "Scan files and block viruses."
        set inspection-mode proxy
        config http
            set options scan
        end
        config ftp
            set options scan
        end
        config imap
            set options scan
            set executables virus
        end
        config pop3
            set options scan
            set executables virus
        end
        config smtp
            set options scan
            set executables virus
        end
        config mapi
            set executables virus
        end
    next
end
config spamfilter profile
    edit "sniffer-profile"
        set comment "Malware and phishing URL monitoring."
        set flow-based enable
    next
    edit "default"
        set comment "Malware and phishing URL filtering."
    next
end
config wanopt settings
    set host-id "default-id"
end
config wanopt profile
    edit "default"
        set comments "Default WANopt profile."
    next
end
config firewall schedule recurring
    edit "always"
        set day sunday monday tuesday wednesday thursday friday saturday
    next
    edit "none"
    next
end
config firewall vip
    edit "MOODLE"
        set uuid fb77cfe0-7979-51e7-eaaa-1a59d6a717b4
        set comment "IP VIRTUAL MOODLE"
        set extip **************
        set extintf "wan1"
        set portforward enable
        set mappedip "***********"
        set extport 80
        set mappedport 80
    next
    edit "SAPQA"
        set uuid 39b1ebe4-1b3f-51e8-122f-d5b22e3bfdd7
        set extip **************
        set extintf "wan1"
        set portforward enable
        set mappedip "***********"
        set extport 80
        set mappedport 80
    next
end
config firewall profile-protocol-options
    edit "default"
        set comment "All default services."
        config http
            set ports 80
            unset options
            unset post-lang
        end
        config ftp
            set ports 21
            set options splice
        end
        config imap
            set ports 143
            set options fragmail
        end
        config mapi
            set ports 135
            set options fragmail
        end
        config pop3
            set ports 110
            set options fragmail
        end
        config smtp
            set ports 25
            set options fragmail splice
        end
        config nntp
            set ports 119
            set options splice
        end
        config dns
            set ports 53
        end
    next
end
config firewall ssl-ssh-profile
    edit "deep-inspection"
        set comment "Deep inspection."
        config https
            set ports 443
        end
        config ftps
            set ports 990
        end
        config imaps
            set ports 993
        end
        config pop3s
            set ports 995
        end
        config smtps
            set ports 465
        end
        config ssh
            set ports 22
        end
        config ssl-exempt
            edit 1
                set fortiguard-category 31
            next
            edit 2
                set fortiguard-category 33
            next
            edit 3
                set type address
                set address "android"
            next
            edit 4
                set type address
                set address "apple"
            next
            edit 5
                set type address
                set address "appstore"
            next
            edit 6
                set type address
                set address "citrix"
            next
            edit 7
                set type address
                set address "eease"
            next
            edit 8
                set type address
                set address "google-drive"
            next
            edit 9
                set type address
                set address "google-play"
            next
            edit 10
                set type address
                set address "google-play2"
            next
            edit 11
                set type address
                set address "google-play3"
            next
            edit 12
                set type address
                set address "Gotomeeting"
            next
            edit 13
                set type address
                set address "microsoft"
            next
            edit 14
                set type address
                set address "update.microsoft.com"
            next
            edit 15
                set type address
                set address "adobe"
            next
            edit 16
                set type address
                set address "Adobe Login"
            next
            edit 17
                set type address
                set address "dropbox.com"
            next
            edit 18
                set type address
                set address "fortinet"
            next
            edit 19
                set type address
                set address "googleapis.com"
            next
            edit 20
                set type address
                set address "icloud"
            next
            edit 21
                set type address
                set address "itunes"
            next
            edit 22
                set type address
                set address "skype"
            next
            edit 23
                set type address
                set address "swscan.apple.com"
            next
            edit 24
                set type address
                set address "verisign"
            next
            edit 25
                set type address
                set address "Windows update 2"
            next
            edit 26
                set type address
                set address "auth.gfx.ms"
            next
            edit 27
                set type address
                set address "autoupdate.opera.com"
            next
            edit 28
                set type address
                set address "softwareupdate.vmware.com"
            next
            edit 29
                set type address
                set address "firefox update server"
            next
        end
    next
    edit "certificate-inspection"
        set comment "SSL handshake inspection."
        config https
            set ports 443
            set status certificate-inspection
        end
        config ftps
            set ports 990
            set status disable
        end
        config imaps
            set ports 993
            set status disable
        end
        config pop3s
            set ports 995
            set status disable
        end
        config smtps
            set ports 465
            set status disable
        end
        config ssh
            set ports 22
            set status disable
        end
    next
end
config waf profile
    edit "default"
        config signature
            config main-class 100000000
                set action block
                set severity high
            end
            config main-class 20000000
            end
            config main-class 30000000
                set status enable
                set action block
                set severity high
            end
            config main-class 40000000
            end
            config main-class 50000000
                set status enable
                set action block
                set severity high
            end
            config main-class 60000000
            end
            config main-class 70000000
                set status enable
                set action block
                set severity high
            end
            config main-class 80000000
                set status enable
                set severity low
            end
            config main-class 110000000
                set status enable
                set severity high
            end
            config main-class 90000000
                set status enable
                set action block
                set severity high
            end
            set disabled-signature 80080005 80200001 60030001 60120001 80080003 90410001 90410002
        end
        config constraint
            config header-length
                set status enable
                set log enable
                set severity low
            end
            config content-length
                set status enable
                set log enable
                set severity low
            end
            config param-length
                set status enable
                set log enable
                set severity low
            end
            config line-length
                set status enable
                set log enable
                set severity low
            end
            config url-param-length
                set status enable
                set log enable
                set severity low
            end
            config version
                set log enable
            end
            config method
                set action block
                set log enable
            end
            config hostname
                set action block
                set log enable
            end
            config malformed
                set log enable
            end
            config max-cookie
                set status enable
                set log enable
                set severity low
            end
            config max-header-line
                set status enable
                set log enable
                set severity low
            end
            config max-url-param
                set status enable
                set log enable
                set severity low
            end
            config max-range-segment
                set status enable
                set log enable
                set severity high
            end
        end
    next
end
config firewall policy
    edit 1
        set name "LanWan"
        set uuid f6cb56f0-2687-51e7-cd4f-b209c7555646
        set srcintf "port2"
        set dstintf "wan1"
        set srcaddr "all"
        set dstaddr "all"
        set action accept
        set schedule "always"
        set service "ALL"
        set utm-status enable
        set logtraffic all
        set webfilter-profile "default"
        set profile-protocol-options "default"
        set nat enable
    next
    edit 2
        set name "SAPNAT"
        set uuid 9a07507c-797a-51e7-a585-3eb392199df3
        set srcintf "wan1"
        set dstintf "port2"
        set srcaddr "all"
        set dstaddr "MOODLE"
        set action accept
        set schedule "always"
        set service "HTTP"
        set nat enable
    next
    edit 3
        set name "ToAzureVPN"
        set uuid 00cf180e-797f-51e7-5f41-fa505dcd96db
        set srcintf "port2"
        set dstintf "ToAzureVPN"
        set srcaddr "all"
        set dstaddr "AzureNetwork"
        set action accept
        set schedule "always"
        set service "ALL"
    next
    edit 4
        set name "FromAzureVPN"
        set uuid 55fd8662-79a2-51e7-8de2-7db60b2265a0
        set srcintf "ToAzureVPN"
        set dstintf "port2"
        set srcaddr "AzureNetwork"
        set dstaddr "all"
        set action accept
        set schedule "always"
        set service "ALL"
    next
    edit 5
        set name "ToAzureVPN2"
        set uuid c5959118-79a2-51e7-5364-9ecaa92b2ab0
        set srcintf "port2"
        set dstintf "ToAzureVPN2"
        set srcaddr "all"
        set dstaddr "AzureNetwork2"
        set action accept
        set schedule "always"
        set service "ALL"
    next
    edit 6
        set name "FromAzureVPN2"
        set uuid dc0730fa-79a2-51e7-9154-44c70d3d219c
        set srcintf "ToAzureVPN2"
        set dstintf "port2"
        set srcaddr "AzureNetwork2"
        set dstaddr "all"
        set action accept
        set schedule "always"
        set service "ALL"
        set logtraffic all
    next
    edit 7
        set name "vpn_vpn-usuarios_remote"
        set uuid 1ec72768-a47f-51e7-9726-603e8019e147
        set srcintf "vpn-usuarios"
        set dstintf "port2"
        set srcaddr "vpn-usuarios_range"
        set dstaddr "all"
        set action accept
        set schedule "always"
        set service "ALL"
        set comments "VPN: vpn-usuarios (Created by VPN wizard)"
        set nat enable
    next
    edit 8
        set name "vpn-usuarios"
        set uuid 4a8dc216-a480-51e7-aa2a-7e051c262309
        set srcintf "vpn-usuarios"
        set dstintf "wan1"
        set srcaddr "vpn-usuarios_range"
        set dstaddr "all"
        set action accept
        set schedule "always"
        set service "ALL"
        set nat enable
    next
    edit 9
        set name "SAPNATQA"
        set uuid 99220474-1b3f-51e8-a3bb-dead83b7f3e0
        set srcintf "wan1"
        set dstintf "port2"
        set srcaddr "all"
        set dstaddr "SAPQA"
        set action accept
        set schedule "always"
        set service "HTTP"
        set nat enable
    next
    edit 10
        set name "vpn_HSBC_local"
        set uuid 20c1a0ea-4ef0-51e8-7114-a61b7aee2db7
        set srcintf "port2"
        set dstintf "HSBC"
        set srcaddr "HSBC_local"
        set dstaddr "HSBC_remote"
        set action accept
        set schedule "always"
        set service "ALL"
        set comments "VPN: HSBC (Created by VPN wizard)"
    next
    edit 11
        set name "vpn_HSBC_remote"
        set uuid 20c3758c-4ef0-51e8-ac30-b9fa575db669
        set srcintf "HSBC"
        set dstintf "port2"
        set srcaddr "HSBC_remote"
        set dstaddr "HSBC_local"
        set action accept
        set schedule "always"
        set service "ALL"
        set comments "VPN: HSBC (Created by VPN wizard)"
    next
    edit 12
        set name "VPN_usuarios-AZURE"
        set uuid c79fd220-62c2-51e8-4206-2b924bd3b7e5
        set srcintf "vpn-usuarios"
        set dstintf "ToAzureVPN"
        set srcaddr "vpn-usuarios_range"
        set dstaddr "all"
        set action accept
        set schedule "always"
        set service "ALL"
        set logtraffic all
    next
    edit 13
        set name "VPN_usuarios-AZURE2"
        set uuid 5b8f7a08-62c3-51e8-54d9-f4e64eefeaa9
        set srcintf "vpn-usuarios"
        set dstintf "ToAzureVPN2"
        set srcaddr "vpn-usuarios_range"
        set dstaddr "all"
        set action accept
        set schedule "always"
        set service "ALL"
        set logtraffic all
        set comments "Clone of VPN_usuarios-AZURE"
    next
    edit 14
        set name "Azure-VPN_usuarios"
        set uuid d466e5c6-6424-51e8-6274-83b2db8f0a62
        set srcintf "ToAzureVPN"
        set dstintf "vpn-usuarios"
        set srcaddr "AzureNetwork"
        set dstaddr "all"
        set action accept
        set schedule "always"
        set service "ALL"
        set nat enable
    next
    edit 15
        set name "Azure2-VPN_usuarios"
        set uuid 0a67d77a-6425-51e8-5903-66cebd67f92f
        set srcintf "ToAzureVPN2"
        set dstintf "vpn-usuarios"
        set srcaddr "AzureNetwork2"
        set dstaddr "all"
        set action accept
        set schedule "always"
        set service "ALL"
    next
    edit 16
        set name "LAN_to_VPNusuarios"
        set uuid 92e4fe8e-642f-51e8-5f7e-0407eca120de
        set srcintf "port2"
        set dstintf "vpn-usuarios"
        set srcaddr "all"
        set dstaddr "all"
        set action accept
        set schedule "always"
        set service "ALL"
    next
end
config firewall sniffer
    edit 1
        set status disable
        set logtraffic disable
        set interface "ssl.root"
    next
end
config endpoint-control profile
    edit "default"
        config forticlient-winmac-settings
        end
        config forticlient-android-settings
        end
        config forticlient-ios-settings
        end
    next
end
config switch-controller switch-profile
    edit "default"
    next
end
config wireless-controller wids-profile
    edit "default"
        set comment "Default WIDS profile."
        set ap-scan enable
        set wireless-bridge enable
        set deauth-broadcast enable
        set null-ssid-probe-resp enable
        set long-duration-attack enable
        set invalid-mac-oui enable
        set weak-wep-iv enable
        set auth-frame-flood enable
        set assoc-frame-flood enable
        set spoofed-deauth enable
        set asleap-attack enable
        set eapol-start-flood enable
        set eapol-logoff-flood enable
        set eapol-succ-flood enable
        set eapol-fail-flood enable
        set eapol-pre-succ-flood enable
        set eapol-pre-fail-flood enable
    next
    edit "default-wids-apscan-enabled"
        set ap-scan enable
    next
end
config wireless-controller wtp-profile
    edit "FAP423E-default"
        config platform
            set type 423E
        end
        set ap-country US
        config radio-1
            set band 802.11n
        end
        config radio-2
            set band 802.11ac
        end
    next
    edit "FAP421E-default"
        config platform
            set type 421E
        end
        set ap-country US
        config radio-1
            set band 802.11n
        end
        config radio-2
            set band 802.11ac
        end
    next
    edit "FAPS423E-default"
        config platform
            set type S423E
        end
        set ap-country US
        config radio-1
            set band 802.11n
        end
        config radio-2
            set band 802.11ac
        end
    next
    edit "FAPS422E-default"
        config platform
            set type S422E
        end
        set ap-country US
        config radio-1
            set band 802.11n
        end
        config radio-2
            set band 802.11ac
        end
    next
    edit "FAPS421E-default"
        config platform
            set type S421E
        end
        set ap-country US
        config radio-1
            set band 802.11n
        end
        config radio-2
            set band 802.11ac
        end
    next
    edit "FAPS323CR-default"
        config platform
            set type S323CR
        end
        set ap-country US
        config radio-1
            set band 802.11n
        end
        config radio-2
            set band 802.11ac
        end
    next
    edit "FAPS322CR-default"
        config platform
            set type S322CR
        end
        set ap-country US
        config radio-1
            set band 802.11n
        end
        config radio-2
            set band 802.11ac
        end
    next
    edit "FAPS321CR-default"
        config platform
            set type S321CR
        end
        set ap-country US
        config radio-1
            set band 802.11n
        end
        config radio-2
            set band 802.11ac
        end
    next
    edit "FAPS313C-default"
        config platform
            set type S313C
        end
        set ap-country US
        config radio-1
            set band 802.11ac
        end
        config radio-2
            set mode disabled
        end
    next
    edit "FAPS311C-default"
        config platform
            set type S311C
        end
        set ap-country US
        config radio-1
            set band 802.11ac
        end
        config radio-2
            set mode disabled
        end
    next
    edit "FAPS323C-default"
        config platform
            set type S323C
        end
        set ap-country US
        config radio-1
            set band 802.11n
        end
        config radio-2
            set band 802.11ac
        end
    next
    edit "FAPS322C-default"
        config platform
            set type S322C
        end
        set ap-country US
        config radio-1
            set band 802.11n
        end
        config radio-2
            set band 802.11ac
        end
    next
    edit "FAPS321C-default"
        config platform
            set type S321C
        end
        set ap-country US
        config radio-1
            set band 802.11n
        end
        config radio-2
            set band 802.11ac
        end
    next
    edit "FAP321C-default"
        config platform
            set type 321C
        end
        set ap-country US
        config radio-1
            set band 802.11n
        end
        config radio-2
            set band 802.11ac
        end
    next
    edit "FAP223C-default"
        config platform
            set type 223C
        end
        set ap-country US
        config radio-1
            set band 802.11n
        end
        config radio-2
            set band 802.11ac
        end
    next
    edit "FAP112D-default"
        config platform
            set type 112D
        end
        set ap-country US
        config radio-1
            set band 802.11n
        end
        config radio-2
            set mode disabled
        end
    next
    edit "FAP24D-default"
        config platform
            set type 24D
        end
        set ap-country US
        config radio-1
            set band 802.11n
        end
        config radio-2
            set mode disabled
        end
    next
    edit "FAP21D-default"
        config platform
            set type 21D
        end
        set ap-country US
        config radio-1
            set band 802.11n
        end
        config radio-2
            set mode disabled
        end
    next
    edit "FK214B-default"
        config platform
            set type 214B
        end
        set ap-country US
        config radio-1
            set band 802.11n
        end
        config radio-2
            set mode disabled
        end
    next
    edit "FAP224D-default"
        config platform
            set type 224D
        end
        set ap-country US
        config radio-1
            set band 802.11n-5G
        end
        config radio-2
            set band 802.11n
        end
    next
    edit "FAP222C-default"
        config platform
            set type 222C
        end
        set ap-country US
        config radio-1
            set band 802.11n
        end
        config radio-2
            set band 802.11ac
        end
    next
    edit "FAP25D-default"
        config platform
            set type 25D
        end
        set ap-country US
        config radio-1
            set band 802.11n
        end
        config radio-2
            set mode disabled
        end
    next
    edit "FAP221C-default"
        config platform
            set type 221C
        end
        set ap-country US
        config radio-1
            set band 802.11n
        end
        config radio-2
            set band 802.11ac
        end
    next
    edit "FAP320C-default"
        config platform
            set type 320C
        end
        set ap-country US
        config radio-1
            set band 802.11n
        end
        config radio-2
            set band 802.11ac
        end
    next
    edit "FAP28C-default"
        config platform
            set type 28C
        end
        set ap-country US
        config radio-1
            set band 802.11n
        end
        config radio-2
            set mode disabled
        end
    next
    edit "FAP223B-default"
        config platform
            set type 223B
        end
        set ap-country US
        config radio-1
            set band 802.11n-5G
        end
        config radio-2
            set band 802.11n
        end
    next
    edit "FAP14C-default"
        config platform
            set type 14C
        end
        set ap-country US
        config radio-1
            set band 802.11n
        end
        config radio-2
            set mode disabled
        end
    next
    edit "FAP11C-default"
        config platform
            set type 11C
        end
        set ap-country US
        config radio-1
            set band 802.11n
        end
        config radio-2
            set mode disabled
        end
    next
    edit "FAP320B-default"
        config platform
            set type 320B
        end
        set ap-country US
        config radio-1
            set band 802.11n-5G
        end
        config radio-2
            set band 802.11n
        end
    next
    edit "FAP112B-default"
        config platform
            set type 112B
        end
        set ap-country US
        config radio-1
            set band 802.11n
        end
        config radio-2
            set mode disabled
        end
    next
    edit "FAP222B-default"
        config platform
            set type 222B
        end
        set ap-country US
        config radio-1
            set band 802.11n
        end
        config radio-2
            set band 802.11n-5G
        end
    next
    edit "FAP210B-default"
        config platform
            set type 210B
        end
        set ap-country US
        config radio-1
            set band 802.11n
        end
        config radio-2
            set mode disabled
        end
    next
    edit "FAP220B-default"
        set ap-country US
        config radio-1
            set band 802.11n-5G
        end
        config radio-2
            set band 802.11n
        end
    next
    edit "AP-11N-default"
        config platform
            set type AP-11N
        end
        set ap-country US
        config radio-1
            set band 802.11n
        end
        config radio-2
            set mode disabled
        end
    next
end
config log memory setting
    set status enable
end
config log null-device setting
    set status disable
end
config log setting
    set local-in-allow enable
    set local-in-deny-unicast enable
    set local-in-deny-broadcast enable
    set local-out enable
end
config log gui-display
    set fortiview-local-traffic enable
end
config router rip
    config redistribute "connected"
    end
    config redistribute "static"
    end
    config redistribute "ospf"
    end
    config redistribute "bgp"
    end
    config redistribute "isis"
    end
end
config router ripng
    config redistribute "connected"
    end
    config redistribute "static"
    end
    config redistribute "ospf"
    end
    config redistribute "bgp"
    end
    config redistribute "isis"
    end
end
config router static
    edit 1
        set gateway **************
        set device "wan1"
    next
    edit 2
        set dst ********* *************
        set gateway *********
        set device "port2"
    next
    edit 3
        set dst ********** *************
        set distance 2
        set device "ToAzureVPN"
    next
    edit 4
        set dst ********** *************
        set distance 3
        set device "ToAzureVPN2"
    next
    edit 5
        set dst ************ ***************
        set device "HSBC"
        set comment "VPN: HSBC (Created by VPN wizard)"
    next
end
config router ospf
    config redistribute "connected"
    end
    config redistribute "static"
    end
    config redistribute "rip"
    end
    config redistribute "bgp"
    end
    config redistribute "isis"
    end
end
config router ospf6
    config redistribute "connected"
    end
    config redistribute "static"
    end
    config redistribute "rip"
    end
    config redistribute "bgp"
    end
    config redistribute "isis"
    end
end
config router bgp
    config redistribute "connected"
    end
    config redistribute "rip"
    end
    config redistribute "ospf"
    end
    config redistribute "static"
    end
    config redistribute "isis"
    end
    config redistribute6 "connected"
    end
    config redistribute6 "rip"
    end
    config redistribute6 "ospf"
    end
    config redistribute6 "static"
    end
    config redistribute6 "isis"
    end
end
config router isis
    config redistribute "connected"
    end
    config redistribute "rip"
    end
    config redistribute "ospf"
    end
    config redistribute "bgp"
    end
    config redistribute "static"
    end
end
config router multicast
end
