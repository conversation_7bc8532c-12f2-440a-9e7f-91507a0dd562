﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.Promotor.Api
{
    /// <summary>
    /// 
    /// </summary>
    public class OfertaxPromotor
    {
        public int? idPromotor { get; set; }
        public string idOfertaVivienda { get; set; }
        public bool? activo { get; set; }
        public DateTime? fechaRegistro { get; set; }
        public DateTime? fechaActualizacion { get; set; }

    }

    /// <summary>
    /// 
    /// </summary>
    public class OfertaxPromotorDTO : OfertaxPromotor
    {
      //  public string idOfertaVivienda { get; set; }
        public int[] idPromotores { get; set; }
        public int? idEmpresa { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class OfertaxPromotorPaginado
    {

        public int tamanioPagina { get; set; }
        public int pagina { get; set; }
        public int idOferente { get; set; }
        public string[] idOfertaVivienda { get; set; }
        public string nombre { get; set; }
        public string rfc { get; set; }

    }
}
