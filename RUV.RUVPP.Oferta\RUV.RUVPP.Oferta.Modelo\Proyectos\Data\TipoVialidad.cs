﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.Proyectos.Data
{
    public enum TipoVialidad
    {
        AMPLIACIÓN = 1,
        ANDADOR = 2,
        AVENIDA = 3,
        BOULEVARD = 4,
        CALLE = 5,
        CALLEJÓN = 6,
        CALZADA = 7,
        CERRADA = 8,
        CIRCUITO = 9,
        CIRCUNVALACIÓN = 10,
        CONTINUACIÓN = 11,
        CORREDOR = 12,
        DIAGONAL = 13,
        EJEVIAL = 14,
        PASAJE = 15,
        PEATONAL = 16,
        PERIFÉRICO = 17,
        PRIVADA = 18,
        PROLONGACIÓN = 19,
        RETORNO = 20,
        VIADUCTO = 21,
        CARRETERA = 22,
        BRECHA = 23,
        CAMINO = 24,
        TERRACERIA = 25,
        VEREDA = 26,
        NINGUNO = 27
    }
}
