﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api
{
    public class PagosAseguradora
    {
        public string ordenVerificacion { get; set; }
        public string cuv { get; set; }
        public decimal? costoEvaluacionRiesgo { get; set; }
        public decimal? costoPoliza { get; set; }
        public decimal? costoTotalPoliza { get; set; }
        public string nombreAseguradora { get; set; }
        public DateTime? fechaIndividualizacion { get; set; }
        public DateTime? fechaPagoAseguradora { get; set; }
        public decimal? montoPagoAseguradora { get; set; }
    }
}
