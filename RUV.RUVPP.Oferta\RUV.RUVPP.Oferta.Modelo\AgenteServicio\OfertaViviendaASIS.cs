﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.AgenteServicio
{
    public class OfertaViviendaASIS: OfertaViviendaASISBase
    {
        public int numeroViviendas { get; set; }
        public string fechaSolicutudSap { get; set; }
        public int montoPago { get; set; }
        public string fechaVigenciaPago { get; set; }
        public string referenciaBancaria { get; set; }
        public string fechaPagoSap { get; set; }
        public string fechaRealPago { get; set; }
        public string numeroDocumentoSAP { get; set; }
        public PrototipoASIS[] prototipos { get; set; }
        public ViviendaASIS[] viviendas { get; set; }
    }
}
