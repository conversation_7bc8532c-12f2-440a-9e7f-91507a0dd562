﻿using Microsoft.ApplicationInsights;
using Newtonsoft.Json;
using RUV.RUVPP.Negocio.General.OrdenTrabajo.Servicios;
using RUV.Comun.Web.Http;
using RUV.RUVPP.Negocio.General.OrdenTrabajo.Comun;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;
using System.Web.Http.Description;
using RUV.RUVPP.Oferta.Dominio.Dictaminacion;
using RUV.RUVPP.Oferta.Modelo.Dictaminacion;
using RUV.RUVPP.Negocio.Empresa.EmpresaConsulta;
using RUV.RUVPP.Oferta.Dominio.Empresa;
using RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Dictaminacion;


namespace RUV.RUVPP.Oferta.Api.Controllers
{
    /// <summary>
    /// Expone funcionalidad para trabajar con las ordenes de trabajo.
    /// </summary>
    [RoutePrefix("interno/api/ordenes-trabajo")]
    public class OrdenTrabajoController : ApiControllerBase
    {
        #region Campos

        private readonly IServicioOrdenTrabajo _servicioOrdenTrabajo;
        private readonly IServicioEmpresaConsulta _servicioEmpresaConsulta;
        private readonly IServicioDictaminacion _servicioDictaminaciones;
        private readonly IServicioEmpresa _servicioEmpresa;

        #endregion

        #region Constructor

        /// <summary>
        /// Constructor de la clase.
        /// </summary>
        /// <param name="clienteTelemetria">Cliente para comunicación con Applications Insights.</param>
        public OrdenTrabajoController(TelemetryClient clienteTelemetria,
           IServicioOrdenTrabajo servicioOrdenTrabajo, IServicioDictaminacion servicioDictaminacion,
           IServicioEmpresaConsulta servicioEmpresaConsulta, IServicioEmpresa servicioEmpresa)
            : base(clienteTelemetria)
        {
            this._servicioOrdenTrabajo = servicioOrdenTrabajo;
            this._servicioDictaminaciones = servicioDictaminacion;
            this._servicioEmpresaConsulta = servicioEmpresaConsulta;
            this._servicioEmpresa = servicioEmpresa;
        }

        #endregion

        #region Acciones

        /// <summary>
        /// Obtiene los datos de la orden de trabajo
        /// </summary>
        /// <param name="idOrdenTrabajo">Identificador de la orden de trabajo</param>        
        [HttpGet, Route("{idOrdenTrabajo}")]
        [ResponseType(typeof(OrdenBase))]
        public async Task<HttpResponseMessage> ObtenerOrdenDeTrabajo(int idOrdenTrabajo)
        {
            OrdenBase ordenTrabajo = this._servicioOrdenTrabajo.ObtenerPorId(idOrdenTrabajo);
            
            return Request.CreateResponse( HttpStatusCode.OK, ordenTrabajo );
        }

        /// <summary>
        /// Obtiene un objeto DictaminacionContenedor que representa el json de dictaminacion.
        /// </summary>
        /// <param name="idOrdenTrabajo">Identificador de la orden de trabajo</param>
        /// <param name="idServicio">Identificador del servicio</param>
        /// <param name="idRegistro">Identificador del registro</param>
        /// <param name="ultimaOAnterior">Bandera que si es TRUE, regresa el JSON de la orden de trabajo especificada, y si no encuentra ese JSON, regresa el json de la ODT anterior. Si es false, solo regresa el json de orden de trabajo especificada</param>        
        [HttpGet, Route("{idOrdenTrabajo}/dictaminaciones")]
        [ResponseType(typeof(DictaminacionContenedor))]
        public async Task<HttpResponseMessage> ObtenerDictaminaciones(int? idOrdenTrabajo, short idServicio, int idRegistro, bool ultimaOAnterior)
        {
            DictaminacionContenedor result;
            if (ultimaOAnterior)
                result = await this._servicioDictaminaciones.ObtenerUltimaDictaminacionOAnteriorAsync(idServicio, idOrdenTrabajo.Value, idRegistro);
            else
                result = await this._servicioDictaminaciones.ObtenerDictaminacionAsync(idServicio, idRegistro);
            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// Guarda o actualiza el json de dictaminaciones con suspension de la ordden de trabajo
        /// </summary>
        /// <param name="idOrdenTrabajo">Identicador de la orden de trabajo</param>
        /// <param name="idServicio">Identificador del servicio</param>
        /// <param name="idRegistro">Identificador del registro</param>
        /// <param name="parametroBodyDictaminacion">Objeto con parametros necesarios para el guardado: idUsuario-Identificador del usuario, JSONDictaminacion-Cadena que contiene el JSON de dictaminacion y seActualizaDictaminacion-bandera que indica si se actualiza o se inserta el json</param>        
        [HttpPost, Route("{idOrdenTrabajo}/dictaminaciones")]
        [ResponseType(typeof(bool))]
        public async Task<HttpResponseMessage> GuardarDictaminacionesConSuspensionODT(int idOrdenTrabajo, short idServicio, int idRegistro, [FromBody]ParametroBodyDictaminacion parametroBodyDictaminacion)
        {
            var result = false;
            if (parametroBodyDictaminacion.seActualizaDictaminacion)
                result = await this._servicioDictaminaciones.ActualizarDictaminacionConSuspensionODTAsync(idServicio, idOrdenTrabajo, idRegistro, parametroBodyDictaminacion.idUsuario, parametroBodyDictaminacion.DictaminacionJSON);
            else
                result = await this._servicioDictaminaciones.GuardarDictaminacionConSuspensionODTAsync(idServicio, idOrdenTrabajo, idRegistro, parametroBodyDictaminacion.idUsuario, parametroBodyDictaminacion.DictaminacionJSON);
            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// Obtiene la lista de motivos de rechazo de una orden de trabajo determinada
        /// </summary>
        /// <param name="idOrdenTrabajo">Identificador de la orden de trabajo</param>
        /// <param name="idServicio">Identificador del servicio</param>
        /// <param name="idRegistro">Identificador del registro</param>
        /// <returns></returns>
        [HttpGet, Route("{idOrdenTrabajo}/motivos-rechazo")]
        [ResponseType(typeof(List<MotivoRechazo>))]
        public async Task<HttpResponseMessage> ObtenerMotivosRechazo(int idOrdenTrabajo, short idServicio, int idRegistro, bool incluirMotivosRechazoArchivados)
        {
            var resultado = await this._servicioDictaminaciones.ObtenerMotivosRechazo(idServicio, idRegistro, idOrdenTrabajo, incluirMotivosRechazoArchivados);
            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }

        #endregion

        #region Disposible

        protected override void Dispose(bool disposing)
        {
            base.Dispose(disposing);

            this._servicioEmpresa.Dispose();
            this._servicioDictaminaciones.Dispose();
        }

        #endregion
    }
}
