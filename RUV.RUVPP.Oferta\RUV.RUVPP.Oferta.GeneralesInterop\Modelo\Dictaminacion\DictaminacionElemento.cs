﻿namespace RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Dictaminacion
{
    public class DictaminacionElemento
    {
        /**Identificador del elemento (nombre) */
        public string idElemento { get; set; }
        public string idEntidad { get; set; }
        public int? indice { get; set; }
        /**Estatus de dictaminación del elemento, TRUE probado, FALSE rechazado, NULL no dictaminado o inconcluso */
        public bool? estatus { get; set; }
        /**Mensaje o comentario de rechazo*/
        public string mensajeRechazo { get; set; }
        /**Mensaje o comentario de rechazo anterior*/
        public string mensajeRechazoAnterior { get; set; }
        /**Bandera que indica si se muestra el cuadro de comentario de rechazo */
        public bool? _mostrarMensajeRechazo { get; set; }
        /**Texto temporal del mensaje de rechazo mostrato en el cuadro de texto*/
        public string _mensajeRechazoTemporal { get; set; }
        /**Bandera que indica si el elemento ha sido actualizado por el usuario*/
        public bool? actualizadoPorUsuario { get; set; }
        /**Guarda el idSeccion si el campo fué dictaminado por un tab padre.*/
        public int? _actualizadoPorSeccion { get; set; }
        public bool? _actualizaEstatusPanel { get; set; }
        /**Bandera que indica si se muestra el popover de comentario de rechazo anterior */
        public bool? _mostrarMensajeRechazoAnterior { get; set; }
        /**Fecha UTC en la que se dictaminó*/
        public long? fechaDictaminacion { get; set; }
        /**Fecha UTC en que se realizó la dictaminación anterior */
        public long? fechaDictaminacionAnterior { get; set; }
    }
}
