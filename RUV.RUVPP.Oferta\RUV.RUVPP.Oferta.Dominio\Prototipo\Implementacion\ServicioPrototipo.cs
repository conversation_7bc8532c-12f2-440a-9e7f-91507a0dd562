﻿using Microsoft.ApplicationInsights;
using Newtonsoft.Json;
using RUV.RUVPP.Negocio.General.Documentos.Abstracta;
using RUV.Comun.Negocio;
using RUV.RUVPP.Oferta.Comun.Modelo;
using RUV.RUVPP.Oferta.Datos.Prototipos;
using RUV.RUVPP.Oferta.Dominio.Dictaminacion;
using RUV.RUVPP.Oferta.GeneralesInterop.Documentos;
using RUV.RUVPP.Oferta.Modelo.Prototipos.Api;
using RUV.RUVPP.Oferta.Modelo.Proyectos.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Transactions;
using RUV.RUVPP.Oferta.Dominio.ProductoServicio;
using RUV.Comun.Utilerias;
using System.Configuration;
using RUV.RUVPP.Oferta.Modelo.AgenteServicio;
using RUV.RUVPP.Oferta.Modelo.Prototipos.Enum;
using RUV.RUVPP.Oferta.Datos.ConvivenciaAsIs;
using RUV.RUVPP.Oferta.Modelo;
using RUV.RUVPP.Oferta.Datos.Empresa;

namespace RUV.RUVPP.Oferta.Dominio.Prototipo.Implementacion
{
    /// <summary>
    ///
    /// </summary>
    public class ServicioPrototipo : ServicioDominioBase, IServicioPrototipo
    {
        #region Campos

        /// <summary>
        ///
        /// </summary>
        private readonly IPrototiposDataMapper _prototipoDM;

        /// <summary>
        ///
        /// </summary>
        private readonly IServicioDocumento<DocumentoOferta> _servicioDocumento;

        /// <summary>
        ///
        /// </summary>
        private readonly IServicioDictaminacion _servicioDictaminacion;
        private IServicioProductoServicio _servicioProductoServicio;
        private readonly IAgenteServicioConvivenciaASIS _agenteServicioOfertaASIS;
        private readonly IEmpresaDataMapper _empresaDM;

        private readonly TransactionOptions _opcionesTransaccion;

        #endregion

        #region Constructor

        /// <summary>
        ///
        /// </summary>
        /// <param name="clienteTelemetria"></param>
        /// <param name="prototipoDM"></param>
        /// <param name="servicioDocumento"></param>
        public ServicioPrototipo(IPrototiposDataMapper prototipoDM, IServicioDocumento<DocumentoOferta> servicioDocumento,
            IServicioDictaminacion servicioDictaminacion, IServicioProductoServicio servicioProductoServicio, IAgenteServicioConvivenciaASIS agenteServicioOfertaASIS,
            IEmpresaDataMapper empresaDM)
            : base()
        {
            this._prototipoDM = prototipoDM;
            this._servicioDocumento = servicioDocumento;
            this._servicioDictaminacion = servicioDictaminacion;
            this._servicioProductoServicio = servicioProductoServicio;
            this._agenteServicioOfertaASIS = agenteServicioOfertaASIS;
            this._empresaDM = empresaDM;

            var timeoutTransaccion = ConfigurationManager.AppSettings["RUV.RUVPP.Transaccion.Timeout"];
            this._opcionesTransaccion = new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted, Timeout = TimeSpan.Parse(timeoutTransaccion) };
        }

        #endregion Constructor

        

        #region Acciones

        /// <summary>
        ///
        /// </summary>
        /// <param name="nombre"></param>
        /// <param name="idEmpresa"></param>
        /// <param name="idPrototipo"></param>
        /// <returns></returns>
        public async Task<int> ExistePrototipoPorNombreAsync(string nombre, int idEmpresa, int? idPrototipo)
        {
            return await this._prototipoDM.ExistePrototipoPorNombreAsync(nombre, idEmpresa, idPrototipo);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="prototipo"></param>
        /// <param name="guardadoTemporal"></param>
        /// <returns></returns>
        public async Task<Modelo.Prototipos.Api.Prototipo> GuardarPrototipoAsync(Modelo.Prototipos.Api.Prototipo prototipo, bool guardadoTemporal)
        {
            using ( var scope = new TransactionScope(TransactionScopeOption.RequiresNew, this._opcionesTransaccion, TransactionScopeAsyncFlowOption.Enabled))
            {
                bool tieneIdPrototipo = false;
                string nombrePrototipo = prototipo.nombre;
                prototipo.activo = true;
                prototipo.fechaRegistro = DateTime.Now;

                if (prototipo.idPrototipo != null)
                    tieneIdPrototipo = true;

                prototipo.identificadorPrototipo = 0;

                if (!guardadoTemporal)
                {
                    //Aqui poner el identificadorPrototipo
                    var prototiposEmpresa = await ObtenerPrototiposFiltradoAsync(99999, 1, prototipo.idEmpresa, null, null, null, null, null, null, null);

                    if (prototiposEmpresa.Resultado.Count > 0)
                        prototipo.identificadorPrototipo = prototiposEmpresa.Resultado.Select(r => r.identificadorPrototipo).Max() + 1;
                    else
                        prototipo.identificadorPrototipo = 1;
                }

                if (guardadoTemporal)
                {                    
                    prototipo.temporalJSON = JsonConvert.SerializeObject(prototipo);
                }
                
                if(guardadoTemporal && tieneIdPrototipo)
                    prototipo.nombre = null;

                if (!tieneIdPrototipo)
                {
                    prototipo.idPrototipo = await this._prototipoDM.GuardarPrototipoAsync(prototipo);                    
                }
                else
                {                                      
                    await this._prototipoDM.ActualizarPrototipoAsync(prototipo);                    
                }

                if (!guardadoTemporal)
                {
                    prototipo.nombre = nombrePrototipo;
                    await GuardarDetallePrototipoAsync(prototipo);
                    await this.NotificarAsIs(prototipo, 0);
                }

                scope.Complete();
            }

            return new Modelo.Prototipos.Api.Prototipo { idPrototipo = prototipo.idPrototipo.Value, identificadorPrototipo = prototipo.identificadorPrototipo.Value };
        }

        private async Task NotificarAsIs(Modelo.Prototipos.Api.Prototipo prototipo, int accion)
        {
            string url = "";

            switch (accion)
            {
                case 0:
                    url = "UrlGuardarPrototipo";
                    break;
                case 1:
                    url = "UrlActualizarPrototipo";
                    break;
                case 2:
                    url = "UrlEliminarPrototipo";
                    break;
                default:
                    break;
            }

            if (ConfigurationManager.AppSettings["ActivarConvivenciaAsIs"] == "1")
            {
                var empresa = await this._empresaDM.ConsultaEmpresaPorNRRUV(prototipo.idEmpresa, null);                

                if (accion == 2)
                {
                    if (prototipo.identificadorPrototipo != 0 && prototipo.identificadorPrototipo != null) {
                        var resultado = await this._agenteServicioOfertaASIS.LlamarOperacion<PrototipoEliminarASIS, RespuestaPrototipoASIS>(new PrototipoEliminarASIS { idPrototipo = prototipo.idPrototipo.Value }, ConfigurationManager.AppSettings[url]);

                        if (!resultado.estatusTransaccion)
                            throw new Exception(resultado.mensaje);
                    }
                }
                else
                {
                    var prototipoASIS = await this.ObtenerPrototipoASIS(prototipo);
                    prototipoASIS.prototipo.idRUVAsis = empresa.idRuvAsis;
                    var resultado = await this._agenteServicioOfertaASIS.LlamarOperacion<PrototipoASIS, RespuestaPrototipoASIS>(prototipoASIS, ConfigurationManager.AppSettings[url]);

                    if (!resultado.estatusTransaccion)
                        throw new Exception(resultado.mensaje);
                }
            }
        }

        private async Task<PrototipoASIS> ObtenerPrototipoASIS(Modelo.Prototipos.Api.Prototipo prototipo)
        {
            int? numeroAlcobas = null;

            if (prototipo.numeroAlcobas != null && prototipo.numeroAlcobas.ToString().Trim() != "")
                numeroAlcobas = (int)prototipo.numeroAlcobas;

            var prototipoASIS = new PrototipoASIS
            {
                prototipo = new PrototipoEncabezadoASIS
                {
                    idPrototipo = prototipo.idPrototipo.Value,
                    nombre = prototipo.nombre,
                    areaLote = (int)prototipo.areaLote,
                    precioVivienda = (int)prototipo.precioVivienda,
                    numeroRecamaras = (int)prototipo.numeroRecamaras,
                    numeroAlcobas = numeroAlcobas,
                    numeroBaniosCompletos = prototipo.numeroBaniosCompletos ?? 0,
                    numeroBaniosMedios = prototipo.numeroBaniosMedios ?? 0,
                    idTipologiaVivienda = prototipo.idTipologiaVivienda.Value,
                    numeroNivelesPrototipo = (int)prototipo.numeroNivelesPrototipo,
                    numeroNivelesVivienda = (int)prototipo.numeroNivelesVivienda,
                    areaMuros = (decimal)prototipo.areaMuros,

                    alturaLocales = prototipo.alturaLotes == null? 0:(int)prototipo.alturaLotes,
                    huellas = prototipo.huellas == null ? 0 : (int)prototipo.huellas,
                    peraltes = prototipo.peraltes == null ? 0 : (int)prototipo.peraltes,
                    anchoRampa = prototipo.anchoRampa == null ? 0 : (int)prototipo.anchoRampa,

                    metrosFrenteLote = (int)prototipo.metrosFrenteLote,
                    
                    superficieVolados = (decimal)prototipo.superficieVolados,
                    superficieIndivisosaCubierto = (decimal)prototipo.superficieIndivisosaCubierto,

                    superficieHabitable = (int)prototipo.superficiePaniosInteriores,
                    superficieTotalHabitable = (int)(prototipo.superficiePaniosInteriores + prototipo.areaMuros),
                    superficeTotalConstruida = (int)(prototipo.superficiePaniosInteriores + prototipo.areaMuros + prototipo.superficieVolados + prototipo.superficieIndivisosaCubierto)
                }
            };

            prototipoASIS.detallePrototipo = prototipo.distribucionAreaVivienda.Select(da => new DetallePrototipoASIS
            {
                idAreaVivienda = da.idTipoAreaVivienda.Value,
                ancho = (int)da.tipoDimensionAreaVivienda.Where(td => td.idTipoDimensionAreaVivienda.Value == (byte)EnumTipoDimensionAreaVivienda.Ancho).Select(td => td.cantidad).FirstOrDefault(),
                largo = (int)da.tipoDimensionAreaVivienda.Where(td => td.idTipoDimensionAreaVivienda.Value == (byte)EnumTipoDimensionAreaVivienda.Largo).Select(td => td.cantidad).FirstOrDefault(),
                areaAdicional = (int)da.tipoDimensionAreaVivienda.Where(td => td.idTipoDimensionAreaVivienda.Value == (byte)EnumTipoDimensionAreaVivienda.AreaAdicional).Select(td => td.cantidad).FirstOrDefault(),
            }).ToArray();

            return prototipoASIS;
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="prototipo"></param>
        /// <returns></returns>
        public async Task<Modelo.Prototipos.Api.Prototipo> GuardarDetallePrototipoAsync(Modelo.Prototipos.Api.Prototipo prototipo)
        {

            await this._prototipoDM.GuardarDetallePrototipoAsync(prototipo);

            //Se manda guardar cada distribucion
            foreach (DistribucionAreaVivienda distribucion in prototipo.distribucionAreaVivienda)
            {
                if (distribucion.tipoDimensionAreaVivienda.Sum(p => p.cantidad) > 0)
                {
                    foreach (TipoDimensionAreaVivienda tipoDimensionArea in distribucion.tipoDimensionAreaVivienda)
                    {
                        await this._prototipoDM.GuardarTipoDistribucionViviendaAsync(
                            tipoDimensionArea.idTipoDimensionAreaVivienda.Value,
                            tipoDimensionArea.cantidad,
                            prototipo.idPrototipo.Value,
                            distribucion.idTipoAreaVivienda.Value);
                    }
                }
                else
                {
                    await this._prototipoDM.GuardarTipoDistribucionViviendaAsync(
                            4,
                            distribucion.totalSuperficie,
                            prototipo.idPrototipo.Value,
                            distribucion.idTipoAreaVivienda.Value);
                }
            }

            //Se guarda el plano
            await this._prototipoDM.GuardarDocumentosPrototipoAsync(prototipo.cargaPlano, prototipo.idPrototipo.Value);

            //Se guardan las imagenes
            foreach (DocumentoRuv imagen in prototipo.imagenes)
            {
                await this._prototipoDM.GuardarDocumentosPrototipoAsync(imagen, prototipo.idPrototipo.Value);
            }

            return new Modelo.Prototipos.Api.Prototipo { idPrototipo = prototipo.idPrototipo.Value, identificadorPrototipo = prototipo.identificadorPrototipo.Value };
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="idPrototipo"></param>
        /// <param name="nombrePrototipo"></param>
        /// <returns></returns>
        public async Task<Modelo.Prototipos.Api.Prototipo> DuplicarPrototipoAsync(int idPrototipo, string nombrePrototipo)
        {

            Modelo.Prototipos.Api.Prototipo prototipo = new Modelo.Prototipos.Api.Prototipo();


            using (var scope = new TransactionScope(TransactionScopeOption.RequiresNew, this._opcionesTransaccion, TransactionScopeAsyncFlowOption.Enabled))
            {
                //obtenemos el prototipo
                prototipo = await this._prototipoDM.ObtenerPrototipoPorIdAsync(idPrototipo);

                prototipo.idPrototipo = null;
                prototipo.nombre = nombrePrototipo;
                prototipo.activo = true;
                prototipo.fechaRegistro = DateTime.Now;
                prototipo.temporalJSON = null;

                prototipo.cargaPlano.IdDocumento = await this._prototipoDM.GuardarDocumentosComunAsync(new DocumentoRuv
                {
                    IdDocumento = prototipo.cargaPlano.IdDocumento,
                    NombreArchivo = prototipo.cargaPlano.NombreArchivo,
                    UrlArchivo = prototipo.cargaPlano.UrlArchivo
                }, 200);

                foreach (DocumentoRuv archivo in prototipo.imagenes)
                {
                    archivo.IdDocumento = await this._prototipoDM.GuardarDocumentosComunAsync(new DocumentoRuv
                    {
                        IdDocumento = archivo.IdDocumento,
                        NombreArchivo = archivo.NombreArchivo,
                        UrlArchivo = archivo.UrlArchivo
                    }, 32);
                }

                //Aqui poner el identificadorPrototipo
                var prototiposEmpresa = await ObtenerPrototiposFiltradoAsync(99999, 1, prototipo.idEmpresa, null, null, null, null, null, null, null);

                if (prototiposEmpresa.Resultado.Count > 0)
                    prototipo.identificadorPrototipo = prototiposEmpresa.Resultado.Select(r => r.identificadorPrototipo).Max() + 1;
                else
                    prototipo.identificadorPrototipo = 1;

                prototipo.idPrototipo = await this._prototipoDM.GuardarPrototipoAsync(prototipo);

                await GuardarDetallePrototipoAsync(prototipo);

                await this.NotificarAsIs(prototipo, 0);

                //Solo prototipos terminamos se les hace el duplicado
                dynamic jsonObj = await _servicioProductoServicio.ObtenerJsonEstatusAsync(37, idPrototipo, true, false, true);
                jsonObj.idRegistro = prototipo.idPrototipo.Value;
                string output = Newtonsoft.Json.JsonConvert.SerializeObject(jsonObj, Newtonsoft.Json.Formatting.Indented);
                await _servicioProductoServicio.GuardarJsonEstatusAsync(37, prototipo.idPrototipo.Value, output, true, false, true);

                scope.Complete();
            }

            return prototipo;
        }

        /// <summary>
        /// Valida en el ASIS si el prototipo puede ser actualizado.
        /// </summary>
        /// <param name="idEmpresa"></param>
        /// <param name="idPrototipo"></param>
        /// <returns></returns>
        public async Task<bool> ValidarEstatusAsisAsync(int idEmpresa, int idPrototipo)
        {
            bool resultado = true;
            int clave = 0;

            if (ConfigurationManager.AppSettings["ActivarConvivenciaAsIs"] == "1")
            {
                ValidarEstatusPrototipoASIS estatusPrototipo = new ValidarEstatusPrototipoASIS()
                {
                    idPrototipoPlus = idPrototipo,
                    idDesarrollador = idEmpresa
                };
                clave = (await this._agenteServicioOfertaASIS.LlamarOperacion<ValidarEstatusPrototipoASIS, RespuestaValidarEstatusPrototipoASIS>(estatusPrototipo, ConfigurationManager.AppSettings["UrlValidarPrototipo"])).clave;

                if (clave >= 4)
                {
                    resultado = false;
                }
            }
            
            return resultado;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="prototipo"></param>
        /// <param name="guardadoTemporal"></param>
        /// <returns></returns>
        public async Task<int> ActualizarPrototipoAsync(Modelo.Prototipos.Api.Prototipo prototipo, bool guardadoTemporal)
        {
            using (var scope = new TransactionScope(TransactionScopeOption.RequiresNew, TransactionScopeAsyncFlowOption.Enabled))
            {
                string nombrePrototipo = prototipo.nombre;
                prototipo.activo = true;
                prototipo.fechaActualizacion = DateTime.Now;

                if (guardadoTemporal)
                {
                    //Aqui revisar la ditribucion para que se guarde bien
                    foreach (DistribucionAreaVivienda distribucion in prototipo.distribucionAreaVivienda)
                    {
                        if (distribucion.tipoDimensionAreaVivienda.Sum(p => p.cantidad) == 0)
                        {
                            List<TipoDimensionAreaVivienda> tipoDimensionAreaVivienda = new List<TipoDimensionAreaVivienda>();

                            TipoDimensionAreaVivienda tipoDimension = new TipoDimensionAreaVivienda
                            {
                                idTipoDimensionAreaVivienda = 4,
                                nombre = distribucion.nombreAreaVivienda,
                                cantidad = distribucion.totalSuperficie,
                                idTipoAreaVivienda = (byte)distribucion.idTipoAreaVivienda.Value
                            };

                            tipoDimensionAreaVivienda.Add(tipoDimension);

                            distribucion.tipoDimensionAreaVivienda = tipoDimensionAreaVivienda;
                        }
                    }                    

                    prototipo.temporalJSON = JsonConvert.SerializeObject(prototipo);

                    prototipo.nombre = null;
                }
                else
                {
                    prototipo.temporalJSON = null;
                }                                

                await this._prototipoDM.ActualizarPrototipoAsync(prototipo);

                if (!guardadoTemporal)
                {
                    await this._prototipoDM.ActualizarDetallePrototipoAsync(prototipo);

                    //Eliminamos la informacion del tipo de dimension vivienda, para poner la nueva
                    await this._prototipoDM.EliminarTipoDistribucionViviendaAsync(prototipo.idPrototipo.Value);

                    //Se manda guardar cada distribucion
                    foreach (DistribucionAreaVivienda distribucion in prototipo.distribucionAreaVivienda)
                    {
                        if (distribucion.tipoDimensionAreaVivienda.Sum(p => p.cantidad) > 0)
                        {
                            foreach (TipoDimensionAreaVivienda tipoDimensionArea in distribucion.tipoDimensionAreaVivienda)
                            {
                                await this._prototipoDM.GuardarTipoDistribucionViviendaAsync(
                                    tipoDimensionArea.idTipoDimensionAreaVivienda.Value,
                                    tipoDimensionArea.cantidad,
                                    prototipo.idPrototipo.Value,
                                    distribucion.idTipoAreaVivienda.Value);
                            }
                        }
                        else
                        {
                            await this._prototipoDM.GuardarTipoDistribucionViviendaAsync(
                                   4,
                                   distribucion.totalSuperficie,
                                   prototipo.idPrototipo.Value,
                                   distribucion.idTipoAreaVivienda.Value);
                        }
                    }

                    //Primero se revisa si el iddocumento es el mismo que en la base, si no son iguales se borra el de la base y se agrega el nuevo
                    var plano = await this._prototipoDM.ObtenerListaDocumentosRuvAsync(prototipo.idPrototipo.Value, 200);

                    var archivos = await this._prototipoDM.ObtenerListaDocumentosRuvAsync(prototipo.idPrototipo.Value, 32);

                    //Aqui eliminamos los documentos
                    await this._prototipoDM.EliminarDocumentoPrototipoAsync(prototipo.idPrototipo.Value, null);

                    //Se guarda el plano
                    await this._prototipoDM.GuardarDocumentosPrototipoAsync(prototipo.cargaPlano, prototipo.idPrototipo.Value);

                    //Se guardan las imagenes
                    foreach (DocumentoRuv imagen in prototipo.imagenes)
                    {
                        await this._prototipoDM.GuardarDocumentosPrototipoAsync(imagen, prototipo.idPrototipo.Value);
                    }

                    prototipo.nombre = nombrePrototipo;
                   
                    await this.NotificarAsIs(prototipo, 1);                    
                    
                }

                scope.Complete();
            }

            return prototipo.idPrototipo.Value;
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="idPrototipo"></param>
        /// <returns></returns>
        public async Task<Modelo.Prototipos.Api.Prototipo> ObtenerPrototipoPorIdAsync(int idPrototipo)
        {
            var prototipo = await this._prototipoDM.ObtenerPrototipoPorIdAsync(idPrototipo);

            if (!string.IsNullOrEmpty(prototipo.temporalJSON))
            {
                var id = prototipo.idPrototipo;
                prototipo = JsonConvert.DeserializeObject<Modelo.Prototipos.Api.Prototipo>(prototipo.temporalJSON);
                prototipo.idPrototipo = id;
            }

            return prototipo;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="idPrototipo"></param>
        /// <param name="idVivienda"></param>
        /// <returns></returns>
        public async Task<ResultadoPaginado<List<Vivienda>>> ObtenerViviendasPorPrototipoIdAsync(int tamanioPagina, int pagina, int idPrototipo, int? idVivienda)
        {
            ResultadoPaginado<List<Vivienda>> resultado = new ResultadoPaginado<List<Vivienda>>()
            {
                TamanioPagina = tamanioPagina,
                PaginaActual = pagina
            };

            var data = await this._prototipoDM.ObtenerViviendasPorPrototipoIdAsync(tamanioPagina, pagina, idPrototipo, idVivienda);

            if (data.Item1 > 0)
            {
                resultado.TotalRegistros = data.Item1;
                resultado.TotalPaginas = (int)Math.Ceiling((double)(resultado.TotalRegistros / tamanioPagina));
            }

            resultado.Resultado = data.Item2;

            return resultado;
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="idEmpresa"></param>
        /// <param name="idPrototipo"></param>
        /// <param name="precioDesde"></param>
        /// <param name="precioHasta"></param>
        /// <param name="idTipologiaVivienda"></param>
        /// <param name="nombre"></param>
        /// <param name="oferente"></param>
        /// <param name="recamaras"></param>
        /// <returns></returns>
        public async Task<ResultadoPaginado<List<Modelo.Prototipos.Api.Prototipo>>> ObtenerPrototiposFiltradoAsync(int tamanioPagina, int pagina, int? idEmpresa, string identificadorPrototipo, float? precioDesde, float? precioHasta, int? idTipologiaVivienda, string nombre, string oferente, int? recamaras)
        {
            ResultadoPaginado<List<Modelo.Prototipos.Api.Prototipo>> resultado = new ResultadoPaginado<List<Modelo.Prototipos.Api.Prototipo>>()
            {
                TamanioPagina = tamanioPagina,
                PaginaActual = pagina
            };

            bool? esPorPrecio = null;

            if (precioDesde != null && precioHasta != null)
            {
                esPorPrecio = true;
            }

            var data = await this._prototipoDM.ObtenerPrototiposFiltradoAsync(tamanioPagina, pagina, idEmpresa, identificadorPrototipo, precioDesde, precioHasta, idTipologiaVivienda, nombre, esPorPrecio, oferente, recamaras);

            if (data.Item1 > 0)
            {
                resultado.TotalRegistros = data.Item1;
                resultado.TotalPaginas = (int)Math.Ceiling((double)(resultado.TotalRegistros / tamanioPagina));
            }

            resultado.Resultado = data.Item2;

            return resultado;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="idPrototipo"></param>
        /// <param name="soloJSON"></param>
        /// <returns></returns>
        public async Task<bool> EliminarPrototipoAsync(int idPrototipo, bool soloJSON)
        {
            Modelo.Prototipos.Api.Prototipo prototipo = await this.ObtenerPrototipoPorIdAsync(idPrototipo);
            prototipo.activo = true;
            using (var scope = new TransactionScope(TransactionScopeOption.RequiresNew, TransactionScopeAsyncFlowOption.Enabled))
            {
                if (soloJSON)
                {
                    prototipo.temporalJSON = null;
                    prototipo.nombre = null;
                    await this._prototipoDM.ActualizarPrototipoAsync(prototipo);
                    //await this.NotificarAsIs(prototipo, 1);
                }
                else
                {
                    await this._prototipoDM.EliminarDimensionAreaAsync(prototipo);
                    await this._prototipoDM.EliminarDocumentoPrototipoAsync(prototipo);
                    await this._prototipoDM.EliminarDetallePrototipoAsync(prototipo);
                    await this._prototipoDM.EliminarPrototipoAsync(prototipo);
                    await this.NotificarAsIs(prototipo, 2);
                }


                scope.Complete();
            }
            return true;
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="idPrototipo"></param>
        /// <param name="idDocumento"></param>
        /// <returns></returns>
        public async Task<bool> EliminarDocumentoPrototipoAsync(int idPrototipo, int idDocumento)
        {
            await this._prototipoDM.EliminarDocumentoPrototipoAsync(idPrototipo, idDocumento);

            return true;
        }

        public async Task<ResultadoPaginado<List<Modelo.Prototipos.Api.Prototipo>>> ObtenerPrototiposAsociados(int pagina, int tamPag, int? idProyecto, int? idOferta)
        {
            ResultadoPaginado<List<Modelo.Prototipos.Api.Prototipo>> resultado = new ResultadoPaginado<List<Modelo.Prototipos.Api.Prototipo>>()
            {
                TamanioPagina = tamPag,
                PaginaActual = pagina
            };

            var data = await this._prototipoDM.ObtenerPrototiposAsociados(pagina, tamPag, idProyecto, idOferta);

            if (data.Item1 > 0)
            {
                resultado.TotalRegistros = data.Item1;
                resultado.TotalPaginas = (int)Math.Ceiling((double)(resultado.TotalRegistros / tamPag));
            }

            resultado.Resultado = data.Item2;

            return resultado;
        }

        #endregion Acciones

        #region Metodos sobreescritos

        protected override void DisposeManagedResources()
        {
            this._prototipoDM.Dispose();
            this._servicioDictaminacion.Dispose();
        }

        #endregion Metodos sobreescritos
    }
}