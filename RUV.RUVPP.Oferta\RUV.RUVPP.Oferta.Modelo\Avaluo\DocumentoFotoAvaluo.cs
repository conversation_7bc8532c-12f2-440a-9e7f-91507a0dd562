﻿using RUV.RUVPP.Entidades.General.Documentos;
using System;
using System.IO;


namespace RUV.RUVPP.Oferta.Modelo.Avaluo
{
    public class DocumentoFotoAvaluo // : DocumentoDto
    {
        public string usuario { get; set; }
        public string password { get; set; }
        public string idBitacoraAvaluo { get; set; }
        public int? idCatalogoFoto { get; set; }
        public DateTime?  fechaCaptura { get; set; }
        public DateTime?  fechaActualizacion { get; set; }
        public string urlFoto { get; set; }
        public byte[] archivo { get; set; }
        public string nombrearchivo { get; set; }
        public DateTime? fechaReporte { get; set; }
        public string peritoSHF { get; set; }
        public string cuv { get; set; }
        public string id_avaluo { get; set; }
        public string modelo_telefono { get; set; }
        public string version_so { get; set; }
        public string uuid { get; set; }


    }
}
