﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.Promotor.Api
{
    /// <summary>
    /// 
    /// </summary>
    public class PromotorXCobertura
    {
        public int? idPromotorXCobertura { get; set; }
        public int? idPromotor { get; set; }
        public string idEntidad { get; set; }
        public bool? activo { get; set; }
        public DateTime? fechaRegistro { get; set; }
        public DateTime? fechaActualizacion { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class PromotorXCoberturaDTO : PromotorXCobertura
    {
        public List<string> idEntidades { get; set; }
    }
}
