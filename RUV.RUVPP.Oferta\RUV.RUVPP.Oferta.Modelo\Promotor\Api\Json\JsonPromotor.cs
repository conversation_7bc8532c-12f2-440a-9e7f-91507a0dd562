﻿using System.Collections.Generic;

namespace RUV.RUVPP.Oferta.Modelo.Promotor.Api.Json
{
    /// <summary>
    /// 
    /// </summary>
    public class JsonPromotor
    {
        public PromotoresOferta promotoresOferta { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        public JsonPromotor()
        {
            this.promotoresOferta = new Json.PromotoresOferta();
        }

    }

    /// <summary>
    /// 
    /// </summary>
    public class PromotoresOferta
    {
        public int asignar { get; set; }
        public List<OfertaVivienda> ofertaViviendas { get; set; }
        public List<Promotor> datosPromotores { get; set; }
        public PromotoresOferta()
        {
            this.ofertaViviendas = new List<OfertaVivienda>();
            this.datosPromotores = new List<Promotor>();
        }
    }
}
