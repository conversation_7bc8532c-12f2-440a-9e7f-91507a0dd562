﻿using Microsoft.ApplicationInsights;
using Newtonsoft.Json;
using RUV.Comun.Web.Extensions;
using RUV.Comun.Core.ExceptionHandling;
using RUV.Comun.Negocio.CustomExceptions;
using RUV.Comun.Web.Http;
using RUV.RUVPP.Oferta.Api.Recursos;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using System.Web.Http.Filters;

namespace RUV.RUVPP.Oferta.Api
{
    /// <summary>
    /// Filtro temporal para manejo de errores de comunicación de Oracle.
    /// </summary>
    public class FiltroExcepcionesRedOracleApiAttribute : FiltroExcepcionesApiAttribute
    {

        #region Constructor

        /// <summary>
        /// Constructor de la clase.
        /// </summary>
        public FiltroExcepcionesRedOracleApiAttribute()
            : base()
        { }

        #endregion

        #region Sobrecarga de ExceptionFilterAttribute

        /// <summary>
        /// Función que sobrecarga el metodo OnException  para el manejo de excepciones
        /// </summary>
        /// <param name="filterContext"></param>
        public override async Task OnExceptionAsync(HttpActionExecutedContext actionExecutedContext, CancellationToken cancellationToken)
        {
            await base.OnExceptionAsync(actionExecutedContext, cancellationToken);

            var exception = actionExecutedContext.Exception;

            if (!Debugger.IsAttached && exception.Message.Contains("ORA-1257"))
            {
                actionExecutedContext.Response = new HttpResponseMessage(HttpStatusCode.InternalServerError)
                {
                    Content = new StringContent("Error de comunicación interno, intente nuevamente.")
                };
            }
        }

        #endregion
    }
}