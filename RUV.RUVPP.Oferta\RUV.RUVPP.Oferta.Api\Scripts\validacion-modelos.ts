﻿//Mensajes de error de las validaciones
let mensajes: string[] = [];

//Realiza la ejecución de las validaciones en las reglas definidas para el modelo dado.
var func = function (argumento: ArgumentoValidacion, callback: (notSure: any, result: any) => void) {
    //Por cada regla..
    for (let regla of argumento.reglas) {

        //Por cada validación de la regla actual, siendo que se ordenaron descendentemente por el campo "orden"
        for (let validacion of regla.validaciones.sort((a, b) => a.orden - b.orden)) {

            if (validacion.dentroDeLista) {

                //Se obtienen los valores maxiomos para iterar
                let funcion = new Function("return " + validacion.funcionGeneradoraDeIndices);
                let indicesMaiximos: number = funcion(argumento.modelo);
                let debeDetenrse = false;

                for (let i = 0; i < indicesMaiximos[0]; i++) {

                    //Se ejecuta la validación con el indice actual
                    let resultado = ejecutarValidacion(validacion, argumento.modelo, i);

                    //Si el resultado no es satisfactorio...
                    if (!resultado) {

                        //Si la validación indica que debe romper la ejecucuión, se marca que debe de detenerce la validación cunado se acabe de iterar en la lista.
                        if (validacion.detieneEjecucion) {
                            debeDetenrse = true;
                        }
                    }
                }

                if (debeDetenrse) {
                    break;
                }
            }
            else {
                let resultado = ejecutarValidacion(validacion, argumento.modelo);

                //Si el resultado no es satisfactorio...
                if (!resultado) {

                    //Si la validación indica que debe romper la ejecucuión, lo hace
                    if (validacion.detieneEjecucion) {
                        break;
                    }
                }
            }
        }
    }

    //Se devuelve el resultado obtenido a .Net
    callback(null, mensajes);
};

function ejecutarValidacion(validacion: Validacion, modelo: any, indice?: number) {
    //Se genera una función con base a lo definido en la validación y se ejecuta
    let funcion = new Function("validaciones", "modelo", "indice", "return " + validacion.funcion);
    let resultado = funcion(ValidacionesFormularios, modelo, indice);

    //Si el resultado no es satisfactorio...
    if (!resultado) {
        //Se agrega a la lista de mensajes de error
        mensajes.push(validacion.mensajeError);
    }

    return resultado;
}