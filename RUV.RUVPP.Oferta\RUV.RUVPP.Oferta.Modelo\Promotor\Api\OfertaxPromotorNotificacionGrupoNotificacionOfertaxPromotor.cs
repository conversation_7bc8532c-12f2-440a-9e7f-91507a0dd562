﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.Promotor.Api
{
    /// <summary>
    /// 
    /// </summary>
    public class OfertaxPromotorNotificacionGrupoNotificacionOfertaxPromotor
    {
        public int? idPromotor { get; set; }
        public string idOfertaVivienda { get; set; }
        public int? idGrupoNotificacion { get; set; }
        public int? estatusEnviO { get; set; }

    }
}
