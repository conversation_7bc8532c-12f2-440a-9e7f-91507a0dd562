﻿using Microsoft.ApplicationInsights;
using RUV.Comun.Utilerias.Extensions;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Datos.ConvivenciaAsIs.Implementacion
{
    /// <inheritdoc />
    public class AgenteServicioConvivenciaASIS : IAgenteServicioConvivenciaASIS
    {
        #region Fields

        /// <summary>
        /// CLiente de telemetría.
        /// </summary>
        private TelemetryClient _clienteTelemetria;

        /// <summary>
        /// URL base del servicio.
        /// </summary>
        private string _urlBase;

        #endregion

        #region Constructor

        /// <summary>
        /// Constructor de la clase.
        /// </summary>
        /// <param name="urlBase">Url base del servicio.</param>
        public AgenteServicioConvivenciaASIS(string urlBase)
        {
            this._clienteTelemetria = new TelemetryClient();
            this._urlBase = urlBase;
        }

        #endregion

        #region IAgenteServicioOfertaASIS Implementation

        /// <inheritdoc />
        public async Task<R> LlamarOperacion<M, R>(M modelo, string urlAccion) where R : new()
        {
            R result = new R();
            
            using (HttpClient cliente = new HttpClient())
            {
                var requestTimeOutValue = ConfigurationManager.AppSettings["RUV.RUVPP.WebRequests.Timeout"];

                cliente.Timeout = TimeSpan.Parse(requestTimeOutValue);
                cliente.DefaultRequestHeaders.Accept.Clear();
                cliente.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                cliente.DefaultRequestHeaders.Add("Connection", "keep-alive");


                cliente.BaseAddress = new Uri(this._urlBase);

                result = await this._clienteTelemetria.MedirDependenciaAsync(this._urlBase, urlAccion,
                    async () =>
                    {
                        try
                        {
                            HttpResponseMessage response = await cliente.PostAsJsonAsync(urlAccion, modelo);
                            response.EnsureSuccessStatusCode();

                            return await response.Content.ReadAsAsync<R>();
                        }
                        catch(TaskCanceledException)
                        {
                            throw new TimeoutException($"Se execio el tiempo de espera para la petición web {urlAccion}");
                        }
                    },
                    "HTTP"
                );
            }

            return result;
        }

        #endregion
    }
}


