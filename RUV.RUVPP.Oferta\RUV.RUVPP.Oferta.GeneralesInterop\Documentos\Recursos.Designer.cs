﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace RUV.RUVPP.Oferta.GeneralesInterop.Documentos {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "4.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Recursos {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Recursos() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("RUV.RUVPP.Oferta.GeneralesInterop.Documentos.Recursos", typeof(Recursos).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to nombreContenedor solo permite alfanumericos en minúsculas: {0}.
        /// </summary>
        internal static string DocumentoNombreContenedorInvalido {
            get {
                return ResourceManager.GetString("DocumentoNombreContenedorInvalido", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to El nombre del contenedor debe tener entre 3 y 63 caracteres.
        /// </summary>
        internal static string DocumentoNombreContenedorMinMaxLength {
            get {
                return ResourceManager.GetString("DocumentoNombreContenedorMinMaxLength", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to El nombre del archivo solo puede contener alfanuméricos, paréntesis, espacios, guión alto y guión bajo: {0}.
        /// </summary>
        internal static string DocumentoNombreInvalido {
            get {
                return ResourceManager.GetString("DocumentoNombreInvalido", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to El nombre del contenedor debe tener entre 1 y 1,024 caracteres.
        /// </summary>
        internal static string DocumentoNombreMinMaxLength {
            get {
                return ResourceManager.GetString("DocumentoNombreMinMaxLength", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ^[A-Za-z0-9()\-_\s\.]*$.
        /// </summary>
        internal static string DocumentoNombrePatron {
            get {
                return ResourceManager.GetString("DocumentoNombrePatron", resourceCulture);
            }
        }
    }
}
