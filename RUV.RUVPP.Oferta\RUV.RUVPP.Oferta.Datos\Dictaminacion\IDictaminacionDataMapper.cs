﻿using System;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Datos.Dictaminacion
{
    public interface IDictaminacionDataMapper : IDisposable
    {
        /// <summary>
        /// Guarda el JSON de dictaminacion para un registro y orden de trabajo determinados
        /// </summary>
        /// <param name="idServicio">Identificador del servicio</param>
        /// <param name="idOrdenDeTrabajo">Identificador de la orden de trabajo</param>
        /// <param name="idRegistro">Identificador del registro</param>
        /// <param name="dictaminacionJSON">Cadena conteniendo el JSON de dictaminacion</param>
        /// <returns></returns>
        Task<bool> GuardarDictaminacionAsync(short idServicio, int idOrdenDeTrabajo, int idRegistro, string dictaminacionJSON);
        /// <summary>
        /// Actualiza el JSON de dictaminacion de un registro para una ODT determinada
        /// </summary>
        /// <param name="idServicio">Identificador del servicio</param>
        /// <param name="idOrdenDeTrabajo">Identificador de la orden de trabajo</param>
        /// <param name="idRegistro">Identificador del registro</param>
        /// <param name="dictaminacionJSON">Cadena conteniendo el JSON de dictaminacion</param>
        /// <returns></returns>
        Task<bool> ActualizarDictaminacionAsync(short idServicio, int idOrdenDeTrabajo, int idRegistro, string dictaminacionJSON);
        /// <summary>
        /// Obtiene el ultimo JSON de dictaminacion para un registro determinado
        /// </summary>
        /// <param name="idServicio">Identificador del servicio</param>
        /// <param name="idRegistro">Identificador del registro</param>
        /// <returns></returns>
        Task<string> ObtenerDictaminacionAsync(short idServicio, int idRegistro);
        /// <summary>
        /// Obtiene el JSON de dictaminacion de la una oferta para una ODT determinada, si no la encuentra, regresa el JSON de la ultima ODT de ese registro.
        /// </summary>
        /// <param name="idServicio">Identificador del servicio</param>
        /// <param name="idOrdenDeTrabajo">Identificador de la ODT</param>
        /// <param name="idRegistro">Identificador del registro</param>
        /// <returns></returns>
        Task<string> ObtenerUltimaDictaminacionOAnteriorAsync(short idServicio, int idOrdenDeTrabajo, int idRegistro);
        /// <summary>
        /// Obtiene el JSON de dictaminacion de un registro para una ODT determinada
        /// </summary>
        /// <param name="idServicio">Identificador del servicio</param>
        /// <param name="idRegistro">Identificador del registro</param>
        /// <param name="idOrdenTrabajo">Identificador de la orden de trabajo</param>
        /// <returns></returns>
        Task<string> ObtenerDictaminacionPorOrdenTrabajoAsync(short idServicio, int idRegistro, int idOrdenTrabajo);

        Task<int> ObtenerCantidadDictaminaciones(short idServicio, int idRegistro);
    }
}
