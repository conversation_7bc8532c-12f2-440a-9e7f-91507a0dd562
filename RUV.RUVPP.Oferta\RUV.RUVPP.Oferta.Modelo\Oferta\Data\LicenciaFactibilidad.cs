﻿using RUV.RUVPP.Oferta.Comun.Modelo;
using System;

namespace RUV.RUVPP.Oferta.Modelo.Oferta.Data
{
    public class LicenciaFactibilidad
    {
        public int idLicenciaFactibilidad { get; set; }
        public bool esLicencia { get; set; }
        public bool esFactibilidad { get; set; }
        public string numeroOficio { get; set; }
        public string emitidoPor { get; set; }
        public DateTime fechaEmision { get; set; }
        public DateTime? fechaVigencia { get; set; }
        public string fechaEmisionUTC { get; set; }
        public string fechaVigenciaUTC { get; set; }
        public bool activo { get; set; }
        public DocumentoRuv documentoCargado { get; set; }
        public string idOfertaVivienda { get; set; }
        public bool esActualizacion { get; set; }
        public bool esSeleccionado { get; set; }
        public bool mostrar { get; set; }
        public string tipoDocumento { get; set; }
        public string fechaCarga { get; set; }
        public DateTime fechaCargaUTC { get; set; }
    }
}