﻿using System;
using System.Collections.Generic;
using RUV.RUVPP.Oferta.Comun.Modelo;
using RUV.RUVPP.Oferta.Modelo.Proyectos.Data;
using RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Dictaminacion;

namespace RUV.RUVPP.Oferta.Modelo.Proyectos.Api
{
    public class Proyecto
    {
        public int idEmpresa { get; set; }
        public string claveEmpresa { get; set; }
        public int idProyecto { get; set; }
        public int idEstatusProyecto { get; set; }
        public string estatusProyecto { get; set; }
        public string nombre { get; set; }
        public string direccion { get; set; }
        public DocumentoRuv plano { get; set; }
        public int idConstructor { get; set; }
        public int? idPromotor { get; set; }
        public int? idVendedor { get; set; }
        public Sembrado sembrado { get; set; }
        public PropietarioTerreno propietarioTerreno { get; set; }
        public DRO dro { get; set; }
        public bool? esZonaRiesgo { get; set; }
        public List<ZonaRiesgo> zonasRiesgo { get; set; }
        public DocumentoRuv dictamenRiesgo { get; set; }
        public DatosCPV constructor { get; set; }
        public DatosCPV promotor { get; set; }
        public DatosCPV vendedor { get; set; }
        public bool aceptacionCartaResponsabilidad { get; set; }
        public DateTime? fechaRegistro { get; set; }
        public string fechaRegistroUTC { get; set; }
        public DateTime? fechaActualizacion { get; set; }
        public string fechaActualizacionUTC { get; set; }
        public DateTime? fechaAceptacion { get; set; }
        public string folioAyto { get; set; }
        public string folioSEPLADE { get; set; }
        public int ofertasRegistradas { get; set; }
        public int prototiposRegistrados { get; set; }

        public int viviendasRegistradas { get; set; }
        public int cuvsRegistradas { get; set; }
        public int cuvsActivas { get; set; }
        public int cuvsInactivas { get; set; }
        public string temporalJSON { get; set; }
        public bool iniciaRegistro { get; set; }
        public bool tieneDetalle { get; set; }
        public bool fueronModificadosDatosSensibles { get; set; }
        public bool esAccesible { get; set; }
        public string ubicacion { get; set; }
        public MotivoRechazo[] listaMotivosRechazo { get; set; }
    }
}