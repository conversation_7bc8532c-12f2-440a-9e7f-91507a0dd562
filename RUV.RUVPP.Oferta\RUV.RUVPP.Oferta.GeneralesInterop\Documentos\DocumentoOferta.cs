﻿using RUV.RUVPP.Entidades.General.Documentos;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.GeneralesInterop.Documentos
{
    public class DocumentoOferta : DocumentoDto
    {
        public DocumentoOferta()
        {
        }

        public DocumentoOferta(DocumentoDto dto)
        {
            idDocumento = dto.idDocumento;
            rutaArchivo = dto.rutaArchivo;
            nombreArchivo = dto.nombreArchivo;
            fechaCarga = dto.fechaCarga;
            fechaActualizacion = dto.fechaActualizacion;
            documentoActivo = dto.documentoActivo;
            numeroVersion = dto.numeroVersion;
            idUsuarioCarga = dto.idUsuarioCarga;
            idCatalogoDocumento = dto.idCatalogoDocumento;
            tamaño = dto.tamaño;
            Extension = dto.Extension;
            ContentType = dto.ContentType;
        }        

        /// <summary>
        /// Contenedor datos de  archivo 
        /// </summary>
        public Stream archivoStream { get; set; }

        /// <summary>
        ///  Nombre del contendor en repositorio Blob
        /// </summary>
        public string contenedorBlob { get; set; }

        /// <summary>
        /// Carpeta donde almacenar el archivo.
        /// </summary>
        public string carpeta { get; set; }

        /// <summary>
        /// RFC de la empresa dueña del documento.
        /// </summary>
        public string rfcEmpresa { get; set; }
    }
}
