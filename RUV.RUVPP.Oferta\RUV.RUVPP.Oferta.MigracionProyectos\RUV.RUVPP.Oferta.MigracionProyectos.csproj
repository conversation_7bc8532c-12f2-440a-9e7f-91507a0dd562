﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{F17EAF2F-132A-4531-9228-2FA03842F876}</ProjectGuid>
    <OutputType>Exe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>RUV.RUVPP.Oferta.MigracionProyectos</RootNamespace>
    <AssemblyName>RUV.RUVPP.Oferta.MigracionProyectos</AssemblyName>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.1.3\lib\net45\EntityFramework.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.1.3\lib\net45\EntityFramework.SqlServer.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.ApplicationInsights, Version=2.1.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.2.1.0\lib\net45\Microsoft.ApplicationInsights.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Azure.KeyVault.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Azure.KeyVault.Core.1.0.0\lib\net40\Microsoft.Azure.KeyVault.Core.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Data.Edm, Version=5.6.4.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Data.Edm.5.6.4\lib\net40\Microsoft.Data.Edm.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Data.OData, Version=5.6.4.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Data.OData.5.6.4\lib\net40\Microsoft.Data.OData.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Data.Services.Client, Version=5.6.4.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Data.Services.Client.5.6.4\lib\net40\Microsoft.Data.Services.Client.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Practices.ServiceLocation, Version=1.3.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\CommonServiceLocator.1.3\lib\portable-net4+sl5+netcore45+wpa81+wp8\Microsoft.Practices.ServiceLocation.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Practices.Unity, Version=4.0.0.0, Culture=neutral, PublicKeyToken=6d32ff45e0ccc69f, processorArchitecture=MSIL">
      <HintPath>..\packages\Unity.4.0.1\lib\net45\Microsoft.Practices.Unity.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Practices.Unity.Configuration, Version=4.0.0.0, Culture=neutral, PublicKeyToken=6d32ff45e0ccc69f, processorArchitecture=MSIL">
      <HintPath>..\packages\Unity.4.0.1\lib\net45\Microsoft.Practices.Unity.Configuration.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Practices.Unity.RegistrationByConvention, Version=4.0.0.0, Culture=neutral, PublicKeyToken=6d32ff45e0ccc69f, processorArchitecture=MSIL">
      <HintPath>..\packages\Unity.4.0.1\lib\net45\Microsoft.Practices.Unity.RegistrationByConvention.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.Configuration, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.WindowsAzure.ConfigurationManager.3.2.1\lib\net40\Microsoft.WindowsAzure.Configuration.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.Storage, Version=7.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\WindowsAzure.Storage.7.0.0\lib\net40\Microsoft.WindowsAzure.Storage.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=10.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.10.0.2\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="Oracle.ManagedDataAccess, Version=4.121.2.0, Culture=neutral, PublicKeyToken=89b483f429c47342, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\RUV.RUVPP.Oferta.Api\bin\Oracle.ManagedDataAccess.dll</HintPath>
    </Reference>
    <Reference Include="RUV.Comun, Version=2.6.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.Comun.2.6.0\lib\net452\RUV.Comun.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.Comun.Datos">
      <HintPath>..\RUV.RUVPP.Oferta.Datos\bin\Debug\RUV.Comun.Datos.dll</HintPath>
    </Reference>
    <Reference Include="RUV.Comun.Utilerias, Version=2.6.2.4, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.Comun.Utilerias.2.6.2.4\lib\net452\RUV.Comun.Utilerias.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.Spatial, Version=5.6.4.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Spatial.5.6.4\lib\net40\System.Spatial.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Transactions" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Configuracion\UnityConfig.cs" />
    <Compile Include="Funciones\MigracionProyecto.cs" />
    <Compile Include="Modelo\DetalleProyecto.cs">
      <DependentUpon>ModeloBDOferta.tt</DependentUpon>
    </Compile>
    <Compile Include="Modelo\DirectorResponsableObra.cs">
      <DependentUpon>ModeloBDOferta.tt</DependentUpon>
    </Compile>
    <Compile Include="Modelo\DocumentoxProyecto.cs">
      <DependentUpon>ModeloBDOferta.tt</DependentUpon>
    </Compile>
    <Compile Include="Modelo\DomicilioCamino.cs">
      <DependentUpon>ModeloBDOferta.tt</DependentUpon>
    </Compile>
    <Compile Include="Modelo\DomicilioCarretera.cs">
      <DependentUpon>ModeloBDOferta.tt</DependentUpon>
    </Compile>
    <Compile Include="Modelo\DomicilioGeografico.cs">
      <DependentUpon>ModeloBDOferta.tt</DependentUpon>
    </Compile>
    <Compile Include="Modelo\ModeloBDOferta.Context.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ModeloBDOferta.Context.tt</DependentUpon>
    </Compile>
    <Compile Include="Modelo\ModeloBDOferta.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ModeloBDOferta.tt</DependentUpon>
    </Compile>
    <Compile Include="Modelo\ModeloBDOferta.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ModeloBDOferta.edmx</DependentUpon>
    </Compile>
    <Compile Include="Modelo\PromotorExterno.cs">
      <DependentUpon>ModeloBDOferta.tt</DependentUpon>
    </Compile>
    <Compile Include="Modelo\PropietarioTerreno.cs">
      <DependentUpon>ModeloBDOferta.tt</DependentUpon>
    </Compile>
    <Compile Include="Modelo\Proyecto.cs">
      <DependentUpon>ModeloBDOferta.tt</DependentUpon>
    </Compile>
    <Compile Include="Modelo\ProyectoxRiesgoOferta.cs">
      <DependentUpon>ModeloBDOferta.tt</DependentUpon>
    </Compile>
    <Compile Include="Modelo\Vivienda.cs">
      <DependentUpon>ModeloBDOferta.tt</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
    <EntityDeploy Include="Modelo\ModeloBDOferta.edmx">
      <Generator>EntityModelCodeGenerator</Generator>
      <LastGenOutput>ModeloBDOferta.Designer.cs</LastGenOutput>
    </EntityDeploy>
    <None Include="Modelo\ModeloBDOferta.edmx.diagram">
      <DependentUpon>ModeloBDOferta.edmx</DependentUpon>
    </None>
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <Service Include="{508349B6-6B84-4DF5-91F0-309BEEBAD82D}" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\RUV.RUVPP.Oferta.Datos\RUV.RUVPP.Oferta.Datos.csproj">
      <Project>{9a22925d-5f26-47b6-8777-b8d87a412ed8}</Project>
      <Name>RUV.RUVPP.Oferta.Datos</Name>
    </ProjectReference>
    <ProjectReference Include="..\RUV.RUVPP.Oferta.GeneralesInterop\RUV.RUVPP.Oferta.GeneralesInterop.csproj">
      <Project>{a429aec1-ba17-4f6f-96c7-d4a52f760f09}</Project>
      <Name>RUV.RUVPP.Oferta.GeneralesInterop</Name>
    </ProjectReference>
    <ProjectReference Include="..\RUV.RUVPP.Oferta.Modelo\RUV.RUVPP.Oferta.Modelo.csproj">
      <Project>{6266a8ca-7163-4580-8bb8-7a0fa604e5d3}</Project>
      <Name>RUV.RUVPP.Oferta.Modelo</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Modelo\ModeloBDOferta.Context.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>ModeloBDOferta.edmx</DependentUpon>
      <LastGenOutput>ModeloBDOferta.Context.cs</LastGenOutput>
    </Content>
    <Content Include="Modelo\ModeloBDOferta.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>ModeloBDOferta.edmx</DependentUpon>
      <LastGenOutput>ModeloBDOferta.cs</LastGenOutput>
    </Content>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>