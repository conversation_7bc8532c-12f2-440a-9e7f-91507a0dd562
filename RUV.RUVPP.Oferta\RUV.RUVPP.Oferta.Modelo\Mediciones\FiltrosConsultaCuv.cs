﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.Mediciones
{
    /// <summary>
    /// Objeto contenedor de filtros para la consulta de CUVS
    /// </summary>
    public class FiltrosConsultaCuv
    {
        /// <summary>
        /// Identificador del proyecto
        /// </summary>
        public int? IdProyecto { get; set; }
        /// <summary>
        /// Identificador de la oferta
        /// </summary>
        public int? IdOferta { get; set; }
        /// <summary>
        /// Clave de la oferta
        /// </summary>
        public string ClaveOferta { get; set; }
        /// <summary>
        /// Orden de verificacion
        /// </summary>
        public string OrdenVerificacion { get; set; }
        /// <summary>
        /// CUV
        /// </summary>
        public string Cuv { get; set; }

        /// <summary>
        /// Lista de CUVs
        /// </summary>
        public List<string> Cuvs { get; set; }
        /// <summary>
        /// Identificador del estatus de vivienda
        /// </summary>
        public int? IdEstatusJuridicoVivienda { get; set; }
        /// <summary>
        /// Identificador de la entidad federativa
        /// </summary>
        public int? IdEntidadFederativa { get; set; }
        /// <summary>
        /// Nombre de frente
        /// </summary>
        public string NombreFrente { get; set; }
        /// <summary>
        /// Numero de registro RUV
        /// </summary>
        public string NumeroRegistroRuv { get; set; }
    }
}
