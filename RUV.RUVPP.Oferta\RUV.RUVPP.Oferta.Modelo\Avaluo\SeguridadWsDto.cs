﻿using System;
using System.Collections.Generic;
using System.Xml.Serialization;

namespace RUV.RUVPP.Oferta.Modelo.Avaluo
{
    [Serializable]
    [XmlRoot("SeguridadSalida")]
    public class SeguridadWsDto
    {
        public string razonSocial = null;
        public string idUsuarioEncriptado { get; set; }
        public string idUsuario { get; set; }
        public string rfc { get; set; }
        public string nombreUsuario { get; set; }
        public string apellidoPaterno { get; set; }
        public string apellidoMaterno { get; set; }
        public string perfil { get; set; }
        public string resultado { get; set; }
        public string idEntidad { get; set; }
        public string idEmpresa { get; set; }
        public string acreedorSAP { get; set; }
        public string usuarioPadre { get; set; }
        public List<String> permisos { get; set; }
        public string descripcion { get; set; }
    }
}
