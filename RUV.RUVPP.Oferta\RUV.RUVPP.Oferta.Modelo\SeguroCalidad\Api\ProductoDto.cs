﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api
{
    public class ProductoDto
    {
        public short? idProducto { get; set; }
        public string nombre { get; set; }
        public string descripcion { get; set; }
        public DateTime? fechaRegistro { get; set; }
        public DateTime? fechaActualizacion { get; set; }
        public bool? activo { get; set; }
        public bool? usoMenu { get; set; }
        public byte? idCategoria { get; set; }
    }
}
