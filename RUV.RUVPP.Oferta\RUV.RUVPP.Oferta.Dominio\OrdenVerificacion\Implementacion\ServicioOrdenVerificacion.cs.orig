﻿using Microsoft.ApplicationInsights;
using RUV.Comun.Negocio;
using RUV.RUVPP.Oferta.Datos.OrdenVerificacion;
using RUV.RUVPP.Oferta.Dominio.OrdenVerificacion;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RUV.RUVPP.Oferta.Modelo.OrdenVerificacion.Data;
using RUV.Comun.Utilerias;
using RUV.RUVPP.Oferta.Modelo.Proyectos.Data;
using RUV.RUVPP.Oferta.Modelo.Mediciones;
using RUV.RUVPP.Oferta.Datos.Proyectos;
using RUV.RUVPP.Oferta.Datos.Mediciones;

namespace RUV.RUVPP.Oferta.Dominio.OrdenVerificacion.Implementacion
{
    public class ServicioOrdenVerificacion : ServicioDominioBase, IServicioOrdenVerficacion
    {

        private readonly IOrdenVerificacionDataMapper _ordenVerificacionDataMapper;
        private readonly IProyectosDataMapper _proyectosDataMapper;
        private readonly IMedicionesDataMapper _medicionesDataMapper;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="clienteTelemetria"></param>
        /// <param name="_ordenVerificacionDataMapper"></param>
        public ServicioOrdenVerificacion(TelemetryClient clienteTelemetria, IOrdenVerificacionDataMapper _ordenVerificacionDataMapper, IProyectosDataMapper proyectosDataMapper,
                                            IMedicionesDataMapper medicionesDataMapper) : base(clienteTelemetria)
        {
            this._ordenVerificacionDataMapper = _ordenVerificacionDataMapper;
            this._proyectosDataMapper = proyectosDataMapper;
            this._medicionesDataMapper = medicionesDataMapper;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="idOrdenVerificacion"></param>
        /// <returns></returns>
        public async Task<List<Vivienda>> ObtenerDetalleOrdenVerificacionAsync(string idOrdenVerificacion)
        {
            return await this._ordenVerificacionDataMapper.ObtenerDetalleOrdenVerificacionAsync(idOrdenVerificacion);
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="claveOferta"></param>
        /// <param name="idOrdenVerificacion"></param>
        /// <returns></returns>
        public async Task<List<Modelo.OrdenVerificacion.Data.OrdenVerificacion>> ObtenerOrdenesVerificacionFiltroAsync(string claveOferta, string idOrdenVerificacion)
        {
            return await this._ordenVerificacionDataMapper.ObtenerOrdenesVerificacionFiltroAsync(claveOferta, idOrdenVerificacion);
        }

        

        /// <summary>
        /// 
        /// </summary>
        /// <param name="idVivienda"></param>
        /// <param name="idOrdenVerificacion"></param>
        /// <returns></returns>
        public async Task<List<Vivienda>> ObtenerViviendasPorOrdenesVerificacionAsync(string idVivienda, string idOrdenVerificacion)
        {
            return await this._ordenVerificacionDataMapper.ObtenerViviendasPorOrdenesVerificacionAsync(idVivienda, idOrdenVerificacion);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="claveOferta"></param>
        /// <returns></returns>
        public async Task<Modelo.OrdenVerificacion.Data.OrdenVerificacion> ObtenerUltimaOrdenVerificacionPorIdOfertaAsync(string claveOferta)
        {
            return await this._ordenVerificacionDataMapper.ObtenerUltimaOrdenVerificacionPorIdOfertaAsync(claveOferta);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="claveOferta"></param>
        /// <returns></returns>
        public async Task<ResultadoPaginado<List<Modelo.OrdenVerificacion.Data.OrdenVerificacion>>> ObtenerOrdenesVerificacionPorIdOfertaPaginadorAsync(int tamanioPagina, int pagina, string claveOferta)
        {
            ResultadoPaginado<List<Modelo.OrdenVerificacion.Data.OrdenVerificacion>> resultado = new ResultadoPaginado<List<Modelo.OrdenVerificacion.Data.OrdenVerificacion>>()
            {
                TamanioPagina = tamanioPagina,
                PaginaActual = pagina
            };
            
            var data = await this._ordenVerificacionDataMapper.ObtenerOrdenesVerificacionPorIdOfertaPaginadorAsync(tamanioPagina, pagina, claveOferta);

            if (data.Item1 > 0)
            {
                resultado.TotalRegistros = data.Item1;
                resultado.TotalPaginas = (int)Math.Ceiling((double)(resultado.TotalRegistros / tamanioPagina));
            }

            resultado.Resultado = data.Item2;

            //aqui ir por las viviendas de cada uno
            foreach (var orden in resultado.Resultado) {

              var viviendas =  await ObtenerViviendasPorOrdenesVerificacionAsync(null, orden.idOrdenVerificacion);

                foreach (var vivienda in viviendas)
                {
                    if (Convert.ToInt32(vivienda.porcentajeDeAvance) <= 20)
                    {
                        orden.ceroaveinte += 1;
                    }
                    else if (Convert.ToInt32(vivienda.porcentajeDeAvance) >= 21 && Convert.ToInt32(vivienda.porcentajeDeAvance) <= 40)
                    {
                        orden.veintiunoacuarenta += 1;
                    }
                    else if (Convert.ToInt32(vivienda.porcentajeDeAvance) >= 41 && Convert.ToInt32(vivienda.porcentajeDeAvance) <= 60)
                    {
                        orden.cuarentayunoysesenta += 1;
                    }
                    else if (Convert.ToInt32(vivienda.porcentajeDeAvance) >= 61 && Convert.ToInt32(vivienda.porcentajeDeAvance) <= 80)
                    {
                        orden.sesentayunoyochenta += 1;
                    }
                    else if (Convert.ToInt32(vivienda.porcentajeDeAvance) >= 81 && Convert.ToInt32(vivienda.porcentajeDeAvance) <= 99)
                    {
                        orden.ochentayunoynoventaynueve += 1;
                    }
                    else if (Convert.ToInt32(vivienda.porcentajeDeAvance) >= 100)
                    {
                        orden.alcien += 1;
                    }

                }

            }            

            return resultado;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="idVivienda"></param>
        /// <param name="idOrdenVerificacion"></param>
        /// <returns></returns>
        public async Task<ResultadoPaginado<List<Vivienda>>> ObtenerViviendasPorOrdenesVerificacionConPaginadoFiltroAsync(int tamanioPagina, int pagina, string idVivienda, string idOrdenVerificacion)
        {
            ResultadoPaginado<List<Vivienda>> resultado = new ResultadoPaginado<List<Vivienda>>()
            {
                TamanioPagina = tamanioPagina,
                PaginaActual = pagina
            };

            var data = await this._ordenVerificacionDataMapper.ObtenerViviendasPorOrdenesVerificacionConPaginadoFiltroAsync(tamanioPagina, pagina, idVivienda, idOrdenVerificacion);

            if (data.Item1 > 0)
            {
                resultado.TotalRegistros = data.Item1;
                resultado.TotalPaginas = (int)Math.Ceiling((double)(resultado.TotalRegistros / tamanioPagina));
            }

            resultado.Resultado = data.Item2;

            return resultado;
        }

<<<<<<< Updated upstream
        public async Task<List<Vivienda>> ObtenerViviendasReportePorOrdenesVerificacionAsync(string idOrdenVerificacion)
        {
            var viviendasReporte = new List<Vivienda>();
            var viviendaReporte = new Vivienda();
            var viviendas = await this._ordenVerificacionDataMapper.ObtenerViviendasPorOrdenesVerificacionAsync(null, idOrdenVerificacion);

            foreach (var item in viviendas)
            {
                viviendaReporte = (await this._proyectosDataMapper.ObtenerViviendasPorCUVAsync(item.cuv)).FirstOrDefault() ?? new Vivienda() { domicilioGeografico = new DomicilioGeografico(), domicilioCarreteraCamino = new DomicilioCarreteraCamino(), Puntajes = new Puntajes() };
                viviendaReporte.numeroAtributos = (await this._medicionesDataMapper.ObtenerAtributosXCuvsAsync(1, 7, item.cuv)).Item1;
                viviendaReporte.numeroEcotecnologias = (await this._medicionesDataMapper.ObtenerEcotecnologiasXCuvsAsync(1, 7, item.cuv)).Item1;
                viviendaReporte.Habitabilidad = item.Habitabilidad;
                viviendaReporte.porcentajeDeAvance = item.porcentajeDeAvance;

                viviendasReporte.Add(viviendaReporte);
            }

            return viviendasReporte;
        }
=======
        #region Metodos sobreescritos

        protected override void DisposeManagedResources()
        {
            this._ordenVerificacionDataMapper.Dispose();           
        }

        #endregion Metodos sobreescritos
>>>>>>> Stashed changes
    }
}
