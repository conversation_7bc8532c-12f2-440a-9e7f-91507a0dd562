﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.AgenteServicio
{
    public class HeaderRespuestaASIS
    {
        public int? idTransaccion { get; set; }
        public string fechaTransaccion { get; set; }
        public string error { get; set; }
        public string mensaje { get; set; }
        public string idOfertaRuvPlus { get; set; }
        public string usuario { get; set; }
        public string idEmpresaRuvPlus { get; set; }
        public bool estatusTransaccion { get; set; }
        public string moduloInvocaServicio { get; set; }
    }
}
