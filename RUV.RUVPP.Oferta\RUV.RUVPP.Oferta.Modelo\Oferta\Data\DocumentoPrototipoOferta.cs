﻿using RUV.RUVPP.Oferta.Comun.Modelo;
using System;

namespace RUV.RUVPP.Oferta.Modelo.Oferta.Data
{
    public class DocumentoPrototipoOferta : DocumentoRuv
    {
        public int idPrototipo { get; set; }
        public string nombrePrototipo { get; set; }
        public bool estaSeleccionado { get; set; }
        public int? idCatalogoDocumento { get; set; }
        public string NombreArchivo { get; set; }
        public string UrlArchivo { get; set; }
        public DateTime fechaCarga { get; set; }
    }
}
