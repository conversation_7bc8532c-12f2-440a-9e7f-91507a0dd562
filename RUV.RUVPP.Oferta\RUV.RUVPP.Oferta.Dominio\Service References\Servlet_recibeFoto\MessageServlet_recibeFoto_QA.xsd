<?xml version="1.0" encoding="utf-8"?>
<xsd:schema xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:p1="http://infonavit.org.mx/Avaluo/sndrecibeFoto" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns="http://infonavit.org.mx/Avaluo/sndrecibeFoto" targetNamespace="http://infonavit.org.mx/Avaluo/sndrecibeFoto" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <xsd:element name="MT_recibeFoto_res" type="p1:DT_recibeFoto_res" />
  <xsd:element name="MT_recibeFoto_req" type="p1:DT_recibeFoto" />
  <xsd:complexType name="DT_recibeFoto">
    <xsd:annotation>
      <xsd:appinfo source="http://sap.com/xi/VersionID">0b9dfab2aac711eaaa4200000106cf7e</xsd:appinfo>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element minOccurs="0" name="cuv" type="xsd:string">
        <xsd:annotation>
          <xsd:appinfo source="http://sap.com/xi/TextID">eeba2d23aa8a11eac6c554a07b82061c</xsd:appinfo>
        </xsd:annotation>
      </xsd:element>
      <xsd:element minOccurs="0" name="id_avaluo" type="xsd:string">
        <xsd:annotation>
          <xsd:appinfo source="http://sap.com/xi/TextID">eeba2d24aa8a11ea8da054a07b82061c</xsd:appinfo>
        </xsd:annotation>
      </xsd:element>
      <xsd:element minOccurs="0" name="id_foto" type="xsd:string">
        <xsd:annotation>
          <xsd:appinfo source="http://sap.com/xi/TextID">eeba2d25aa8a11eace0954a07b82061c</xsd:appinfo>
        </xsd:annotation>
      </xsd:element>
      <xsd:element minOccurs="0" name="foto" type="xsd:string">
        <xsd:annotation>
          <xsd:appinfo source="http://sap.com/xi/TextID">eeba2d26aa8a11eac7a254a07b82061c</xsd:appinfo>
        </xsd:annotation>
      </xsd:element>
      <xsd:element minOccurs="0" name="id_perito_shf" type="xsd:string">
        <xsd:annotation>
          <xsd:appinfo source="http://sap.com/xi/TextID">eeba2d27aa8a11eaa72c54a07b82061c</xsd:appinfo>
        </xsd:annotation>
      </xsd:element>
      <xsd:element minOccurs="0" name="usuarioVO">
        <xsd:annotation>
          <xsd:appinfo source="http://sap.com/xi/TextID">eeba5823aa8a11eaa83d54a07b82061c</xsd:appinfo>
        </xsd:annotation>
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element minOccurs="0" name="id_usuario" type="xsd:string">
              <xsd:annotation>
                <xsd:appinfo source="http://sap.com/xi/TextID">eeba5821aa8a11ea856654a07b82061c</xsd:appinfo>
              </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="contrasenia" type="xsd:string">
              <xsd:annotation>
                <xsd:appinfo source="http://sap.com/xi/TextID">eeba5822aa8a11ea963f54a07b82061c</xsd:appinfo>
              </xsd:annotation>
            </xsd:element>
          </xsd:sequence>
        </xsd:complexType>
      </xsd:element>
      <xsd:element minOccurs="0" name="metadatosVO">
        <xsd:annotation>
          <xsd:appinfo source="http://sap.com/xi/TextID">eeba59bfaa8a11ea94d954a07b82061c</xsd:appinfo>
        </xsd:annotation>
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element minOccurs="0" name="latitud" type="xsd:string">
              <xsd:annotation>
                <xsd:appinfo source="http://sap.com/xi/TextID">eeba5824aa8a11eab45c54a07b82061c</xsd:appinfo>
              </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="longitud" type="xsd:string">
              <xsd:annotation>
                <xsd:appinfo source="http://sap.com/xi/TextID">eeba5825aa8a11ea9f5254a07b82061c</xsd:appinfo>
              </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="fechaVisitaInmueble" type="xsd:string">
              <xsd:annotation>
                <xsd:appinfo source="http://sap.com/xi/TextID">eeba5826aa8a11eaa1d654a07b82061c</xsd:appinfo>
              </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="modeloTelefono" type="xsd:string">
              <xsd:annotation>
                <xsd:appinfo source="http://sap.com/xi/TextID">eeba5827aa8a11ea856754a07b82061c</xsd:appinfo>
              </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="versionSO" type="xsd:string">
              <xsd:annotation>
                <xsd:appinfo source="http://sap.com/xi/TextID">eeba59beaa8a11eabe4454a07b82061c</xsd:appinfo>
              </xsd:annotation>
            </xsd:element>
          </xsd:sequence>
        </xsd:complexType>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="DT_recibeFoto_res">
    <xsd:annotation>
      <xsd:appinfo source="http://sap.com/xi/VersionID">e831e8c8a6e811eaa3cc00000106cf7e</xsd:appinfo>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element minOccurs="0" name="folio" type="xsd:string" />
      <xsd:element minOccurs="0" name="codigo_recepcion" type="xsd:string" />
    </xsd:sequence>
  </xsd:complexType>
</xsd:schema>