﻿using RUV.RUVPP.Oferta.GeneralesInterop.Modelo.ConfiguracionSeccion;
using RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Estatus;
using RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Reglas;
using System;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Dominio.ProductoServicio
{
    public interface IServicioProductoServicio : IDisposable
    {
        /// <summary>
        /// Obtiene el JSON de configuracion como objeto de un servicio especificado
        /// </summary>
        /// <param name="idServicio">Identificador del servicio</param>
        /// <returns></returns>
        Task<ConfiguracionSeccion> ObtenerConfiguracionAsync(int idServicio);

        /// <summary>
        /// Obtiene una tupla que contiene el JSON de configuracion y de reglas como objeto y arreglo de un servicio determinado
        /// </summary>
        /// <param name="idServicio">Identificador del servicio</param>
        /// <returns></returns>
        Task<Tuple<ConfiguracionSeccion, ReglaElemento[]>> ObtenerConfiguracionReglasAsync(int idServicio);
        /// <summary>
        /// Actualiza el JSON de estatus de registro/vyd de un registro
        /// </summary>
        /// <param name="idServicio">Identificador del servicio</param>
        /// <param name="idRegistro">Identificador del registro</param>
        /// <param name="jsonEstatus">Cadena conteniendo el JSON de estatus</param>
        /// <param name="esRegistro">Bandera que indica que es el JSON de la etapa de registro</param>
        /// <param name="esVyd">Bandera que indica que es el JSON de la etapa de vyd</param>
        /// <param name="esFinal">Bandera que indica si es el JSON final</param>
        /// <returns></returns>
        Task<bool> ActualizarJsonEstatusAsync(short idServicio, int idRegistro, string jsonEstatus, bool esRegistro, bool esVyd, bool? esFinal);
        /// <summary>
        /// Obtiene el JSON de estatus de registro/vyd como objeto para un registro determinado
        /// </summary>
        /// <param name="idServicio">Identificador del servicio</param>
        /// <param name="idRegistro">Identificador del registro</param>
        /// <param name="esRegistro">Bandera que indica si es el JSON de la etapa de registro</param>
        /// <param name="esVyd">Bandera que indica si es el JSON de la etapa de vyd</param>
        /// <param name="esFinal">Bandera que indica si es el JSON final</param>
        /// <returns></returns>
        Task<EstatusProcedimiento> ObtenerJsonEstatusAsync(short idServicio, int idRegistro, bool esRegistro, bool esVyd, bool? esFinal);
        /// <summary>
        /// Guarda el JSON de estatus de registro/vyd de un registro
        /// </summary>
        /// <param name="idServicio">Identificador del servicio</param>
        /// <param name="idRegistro">Identificador de registro</param>
        /// <param name="jsonEstatus">Cadena conteniendo elJSON de estatus</param>
        /// <param name="esRegistro">Bandera que indica que es el JSON de la etapa de registro</param>
        /// <param name="esVyd">Bandera que indica que es el JSON de la etapa de vyd</param>
        /// <param name="esFinal">Bandera que indica si es el JSON final</param>
        /// <returns></returns>
        Task<bool> GuardarJsonEstatusAsync(short idServicio, int idRegistro, string jsonEstatus, bool esRegistro, bool esVyd, bool? esFinal);

        /// <summary>
        /// Elimina el JSON de estatus de registro/vyd de un registro
        /// </summary>
        /// <param name="idServicio">Identificador del servicio</param>
        /// <param name="idRegistro">Identificador del registro</param>        
        /// <param name="esRegistro">Bandera que indica que es el JSON de la etapa de registro</param>
        /// <param name="esVyd">Bandera que indica que es el JSON de la etapa de vyd</param>
        /// <param name="esFinal">Bandera que indica si es el JSON final</param>
        /// <returns></returns>
        Task<bool> EliminarJsonEstatusAsync(short idServicio, int idRegistro, bool esRegistro, bool esVyd, bool esFinal);
    }
}
