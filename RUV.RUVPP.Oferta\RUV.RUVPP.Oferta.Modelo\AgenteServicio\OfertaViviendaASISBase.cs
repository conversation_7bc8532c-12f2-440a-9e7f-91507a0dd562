﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.AgenteServicio
{
    public class OfertaViviendaASISBase
    {
        public int idOfertaVivienda { get; set; }
        public string claveOfertaVivienda { get; set; }
        public string nombreDRO { get; set; }
        public string apellidoPaternoDRO { get; set; }
        public string apellidoMaternoDRO { get; set; }
        public string fechaVigenciaDRO { get; set; }
        public string numeroPerito { get; set; }
        public string idEmpresaInst { get; set; }
        public string fechaResgistro { get; set; }
        public string nombreDeFrente { get; set; }
        public string constructorEmpresaInst { get; set; }
        public string nombrePropietarioTerreno { get; set; }
        public string numeroRPP { get; set; }
        public string numeroEscritura { get; set; }
        public string volumen { get; set; }
        public string tomoEscrituraTerreno { get; set; }
        public string numeroNotario { get; set; }
        public string fechaEscrituracionTerreno { get; set; }
        public decimal areaTerreno { get; set; }
        public int estatusOfertaVivienda { get; set; }
        public string fechaAceptacion { get; set; }
        public string fechaActualizacion { get; set; }
        public string numeroTelefonoVendedor { get; set; }
        public string correoElectronicoVendedor { get; set; }
        public decimal latitud { get; set; }
        public decimal longitud { get; set; }
        public RiesgoOfertaASIS[] riesgosOferta { get; set; }
        public DocumentoOfertaASIS[] documentosOferta { get; set; }
    }
}
