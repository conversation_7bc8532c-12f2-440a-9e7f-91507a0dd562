﻿using System.Collections.Generic;

namespace RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Dictaminacion
{
    public class DictaminacionSeccion
    {
        /**Identificador de la sección*/
        public int? idSeccion { get; set; }
        /**Estatus de dictaminación de la sección, TRUE probado, FALSE rechazado, NULL no dictaminado o inconcluso */
        public bool? estatus { get; set; }
        /**Mensaje o comentario de rechazo guardado para el tab o sección*/
        public string mensajeRechazo { get; set; }
        /**Mensaje o comentario de rechazo anterior para el tab o sección*/
        public string mensajeRechazoAnterior { get; set; }
        /**Bandera que indica si se muestra el cuadro de comentario de rechazo */
        public string _mostrarMensajeRechazo { get; set; }
        /**Texto temporal del mensaje de rechazo mostrato en el cuadro de texto*/
        public string _mensajeRechazoTemporal { get; set; }
        /**Bandera que indica si algun elemento o seccion contenido en la seccion ha sido actualizado por el usuario*/
        public bool? actualizadoPorUsuario { get; set; }
        /**Guarda el idSeccion si la sección fué dictaminada por un tab padre.*/
        public int? _actualizadoPorSeccion { get; set; }
        /**tabs o secciones hijas*/
        public List<DictaminacionSeccion> secciones { get; set; }
        /**Elementos del tab o sección*/
        public List<DictaminacionElemento> elementos { get; set; }
        public bool? _actualizaEstatusPanel { get; set; }
        /**Bandera que indica si se muestra el popover de comentario de rechazo anterior */
        public bool? _mostrarMensajeRechazoAnterior { get; set; }
        /**Fecha UTC en la que se dictaminó*/
        public long? fechaDictaminacion { get; set; }
        /**Fecha UTC en que se realizó la dictaminación anterior */
        public long? fechaDictaminacionAnterior { get; set; }
    }
}
