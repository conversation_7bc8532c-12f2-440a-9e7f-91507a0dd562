﻿/**
 * Contiene funciones de validación genericas utilizadas en formularios
 */
class ValidacionesFormularios {
    /**
     * Determina si el valor es un RFC válido.
     * @param valor Texto a validar.
     */
    public static esRfcValido(valor: string) {
        let rfcFisico = null;
        let rfcMoral = null;

        rfcFisico = valor.match("^(([A-Z]|[a-z]|\\s){1})(([A-Z]|[a-z]){3})([0-9]{6})((([A-Z]|[a-z]|[0-9]){3}))$");

        rfcMoral = valor.match("^(([A-Z]|[a-z]){3})([0-9]{6})((([A-Z]|[a-z]|[0-9]){3}))$");

        return rfcFisico != null || rfcMoral != null;
    }

    public static esCorreoValido(valor: string) {
        return valor.match("^[_a-zA-Z0-9-]+(.[_a-zA-Z0-9-]+)*@[a-zA-Z0-9-]+(.[a-zA-Z0-9-]+)*(.[a-zA-Z]{2,4})$") !== null;
    }

    public static esNombreRegistroValido(valor: string, longmin: number, longmax: number) {
        var expresionRegular = "[a-zA-Z0-9áéíóúüñÁÉÍÓÚÜÑ$-_.+!*'(),]";

        if (longmax > 1) {
            var expresionRegular = "[a-zA-Z0-9áéíóúüñÁÉÍÓÚÜÑ$-_.+!*'(),]{1}[a-zA-Z0-9áéíóúüñÁÉÍÓÚÜÑ$-_.+!*'(), ]";
            longmin = longmin - 1;
            longmax = longmax - 1;
        }

        var expresion: string = '^' + expresionRegular + '{' + longmin + ',' + longmax + '}$';

        return String(valor).match(expresion) !== null;
    }

    public static esLadaValida(valor: string) {
        return String(valor).match('^([0-9]){2,3}$') !== null;
    }

    public static esTelefonoValido(valor: string) {
        return String(valor).match('^([0-9]){10}$') !== null;
    }

    /**
     * Determina si el valor es un RFC válido.
     * @param valor Texto a validar.
     * @param tipo (N:Numerico, T:Texto, NT:Numerico y Texto)
     */
    public static formatoCampo(valor: string, tipo: string, longmin: number, longmax: number) {
        var expresionTipo: string;
        if (tipo == 'NT') {
            if (longmax > 1) {
                expresionTipo = '[a-zA-Z0-9áéíóúüñÁÉÍÓÚÜÑ]{1}[a-zA-Z0-9áéíóúüñÁÉÍÓÚÜÑ ]';
                longmin = longmin - 1;
                longmax = longmax - 1;
            } else {
                expresionTipo = '[a-zA-Z0-9áéíóúüñÁÉÍÓÚÜÑ]';
            }
        } else if (tipo == 'N') {
            expresionTipo = '([0-9])';
        } else {
            if (longmax > 1) {
                expresionTipo = '[a-zA-ZáéíóúüñÁÉÍÓÚÜÑ]{1}[a-zA-ZáéíóúüñÁÉÍÓÚÜÑ ]';
                longmin = longmin - 1;
                longmax = longmax - 1;
            } else {
                expresionTipo = '[a-zA-ZáéíóúüñÁÉÍÓÚÜÑ]';
            }
        }

        var expresion: string = '^' + expresionTipo + '{' + longmin + ',' + longmax + '}$';

        return String(valor).match(expresion) !== null;
    }

    /**
     * Valida si un dato es numérico.
     * @param valor Dato a validar.
     */
    public static esNumerico(valor: any) {
        return !isNaN(parseFloat(valor)) && isFinite(valor);
    }

    public static filtrar(arreglo, campo: string, valor: string) {
        for (var i = 0; i < arreglo.length; i++){
            if (arreglo[i][campo] == valor)
                return arreglo[i];
        }

        return null;
    }

    public static filtrarValorDiferente(arreglo, campo: string, valor: string) {
        for (var i = 0; i < arreglo.length; i++) {
            if (arreglo[i][campo] !== valor)
                return arreglo[i];
        }

        return null;
    }
}