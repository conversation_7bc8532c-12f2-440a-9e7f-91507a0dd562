﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.Mediciones
{
    public class PuntajeSalida
    {
        public string cuv { get; set; }
        public string message { get; set; }
        public UbicacionPuntaje ubicacionDto { get; set; }
        public EquipamientoServicio equipamientoDTO{ get; set; }
        public Densificacion densificacionDTO { get; set; }
        public Competitividad competitividadDTO { get; set; }
        public IMP impDTO { get; set; }
        public float? puntajeTotalReal { get; set; }
        public float? puntajeTotalEsperado { get; set; }
    }
}
