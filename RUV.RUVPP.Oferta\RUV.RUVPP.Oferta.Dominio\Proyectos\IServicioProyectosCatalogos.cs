﻿using RUV.RUVPP.Oferta.Modelo.Proyectos.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RUV.RUVPP.Oferta.Modelo.Proyectos;
using RUV.RUVPP.Oferta.Modelo.Proyectos.Api;

namespace RUV.RUVPP.Oferta.Dominio.Proyectos
{
    public interface IServicioProyectosCatalogos : IDisposable
    {
        /// <summary>
        /// Obtiene los elementos del catalago de zonas de riesgo del proyecto.
        /// </summary>
        /// <returns>Elementos del catalogo.</returns>
        Task<List<ZonaRiesgo>> ObtenerZonasDeRiesgoAsync();

        Task<List<OpcionCatalogo>> ObtenerCatalogoAsync(CatalogosProyectos catalogo, int? idBusqueda = null);

        /// <summary>
        /// Obtiene los elementos del catalago para mostrar los prototipos por empresa.
        /// </summary>
        /// <returns>Elementos del catalogo.</returns>
        Task<List<OpcionCatalogo>> ObtenerCatalogoPrototiposxEmpresa(int idEmpresa);

        /// <summary>
        /// Obtiene los elementos del catalago para mostrar los proyectos por empresa.
        /// </summary>
        /// <returns>Elementos del catalogo.</returns>
        Task<List<OpcionCatalogo>> ObtenerCatalogoProyectosxEmpresa(int idEmpresa);

        /// <summary>
        /// Obtiene la lista de vialidades de un estado y municipio
        /// </summary>
        /// <param name="idEstado"></param>
        /// <param name="idMunicipio"></param>
        /// <returns></returns>
        Task<List<OpcionCatalogo>> ObtenerCatalogoVialidadesFiltradoAsync(string idEstado, string idMunicipio);

        /// <summary>
        /// Obtiene la lista de asentamientos de un estado y municipio
        /// </summary>
        /// <param name="idEstado"></param>
        /// <param name="idMunicipio"></param>
        /// <returns></returns>
        Task<List<OpcionCatalogo>> ObtenerCatalogoasentamientosFiltradoAsync(string idEstado, string idMunicipio);

        /// <summary>
        /// Obtiene la lista de vialidades de un estado y municipio
        /// </summary>
        /// <param name="idEstado"></param>
        /// <param name="idMunicipio"></param>
        /// <returns></returns>
        Task<List<Vialidad>> ObtenerVialidadesFiltradoAsync(string idEstado, string idMunicipio);

        /// <summary>
        /// Obtiene la lista de asentamientos de un estado y municipio
        /// </summary>
        /// <param name="idEstado"></param>
        /// <param name="idMunicipio"></param>
        /// <returns></returns>
        Task<List<Asentamiento>> ObtenerAsentamientosFiltradoAsync(string idEstado, string idMunicipio);

        /// <summary>
        /// Obtiene la lista de localidadse de un estado y municipio
        /// </summary>
        /// <param name="idEstado"></param>
        /// <param name="idMunicipio"></param>
        /// <returns></returns>
        Task<List<OpcionCatalogo>> ObtenerCatalogoLocalidadesFiltradoAsync(string idEstado, string idMunicipio);

        /// <summary>
        /// Obtiene la lista de CP de un estado y municipio
        /// </summary>
        /// <param name="idEstado"></param>
        /// <param name="idMunicipio"></param>
        /// <returns></returns>
        Task<List<OpcionCatalogo>> ObtenerCatalogoCPFiltradoAsync(string idEstado, string idMunicipio);
    }
}
    