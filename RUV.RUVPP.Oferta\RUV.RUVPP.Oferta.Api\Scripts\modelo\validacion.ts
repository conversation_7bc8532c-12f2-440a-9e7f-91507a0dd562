﻿/** Representa una validación que debe de ejecutarse. */
interface Validacion {
    /** Nombre de la validación. */
    nombre: string;

    /** Orden de ejecución de la validación con respecto a sus hermanos. */
    orden: number;

    /** Determina si al fallar la regla, se detiene la ejecución de las demas reglas. */
    detieneEjecucion?: boolean;

    /** Mensaje de error a mostrar en caso de no cumplirse la regla. */
    mensajeError: string;

    /** Función Javascript que realiza la validación. Debe de regresar un valor que pueda ser evaluado como boleano */
    funcion: string;

    /** Indica si la regla valida un elemento que forma parte de una lista. */
    dentroDeLista?: boolean;

    /** Función que genera un arreglo de numeros que indica el numero máximo de registros a iterar por cada nivel de profundida requerida. */
    funcionGeneradoraDeIndices?: string;
}
