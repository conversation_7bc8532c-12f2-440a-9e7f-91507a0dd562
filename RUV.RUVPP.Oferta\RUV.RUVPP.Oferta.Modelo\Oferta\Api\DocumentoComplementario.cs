﻿using RUV.RUVPP.Oferta.Comun.Modelo;
using RUV.RUVPP.Oferta.Modelo.Oferta.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.Oferta.Api
{
    public class DocumentoComplementario
    {
        public Documento tipoDocumento { get; set; }
        public DocumentoRuv documento { get; set; }
        public bool esActualizacion { get; set; }
        public bool mostrar { get; set; }
        public string fechaCarga { get; set; }
        public DateTime fechaCargaUTC { get; set; }
    }
}
