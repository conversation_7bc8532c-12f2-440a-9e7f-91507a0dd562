﻿using RUV.RUVPP.Oferta.Datos.util;
using RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api;
using RUV.RUVPP.Oferta.Modelo.siniestros;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Dominio.Util
{
    public class ValidacionesServiceSiniestro
    {
        public ResultadoSiniestro validacionSiniestro(RespuestaSiniestro respuestaSiniestro, string fechaRegistro)
        {
            ResultadoSiniestro resultadoSiniestro = new ResultadoSiniestro();
            resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.RegistroOK;


            try
            {
                if (this.validaEstatusReporte(respuestaSiniestro.estatusReporte) == true)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.EstatusReporteNoValido;
                    resultadoSiniestro.descripcion = " El estatus del reporte no es válido";
                }
                else
                {
                    switch (Convert.ToInt32(respuestaSiniestro.estatusReporte))
                    {
                        case (int)EnumEstatusSiniestro.Abierto:
                            resultadoSiniestro = this.validaEstatusAbierto(respuestaSiniestro, fechaRegistro);
                            break;
                        case (int)EnumEstatusSiniestro.Cerrada:
                            resultadoSiniestro = this.validaEstatusCerrado(respuestaSiniestro, fechaRegistro);
                            break;
                        case (int)EnumEstatusSiniestro.Declinada: // case (int)3:
                            resultadoSiniestro = this.validaEstatusDeclinado(respuestaSiniestro, fechaRegistro);
                            break;
                        default:
                            this.validaEstatusAbierto(respuestaSiniestro, fechaRegistro);
                            break;

                    }
                }
            }
            catch (Exception ex)
            {
                resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.RegistroNOK;
                resultadoSiniestro.descripcion = "No se guardó el registro, favor de intentarlo más tarde. ";
            }

          

            return resultadoSiniestro;

        }

        public bool validaEstatusReporte(string estatusReporte)
        {
            bool bandera = true;

            if (estatusReporte != "" || estatusReporte != null)
            {

                int idestatusReporte = Convert.ToInt32(estatusReporte);

                if (idestatusReporte == 1 || idestatusReporte == 2 || idestatusReporte == 3)
                {
                    bandera = false;
                }
            }
            return bandera;
        }

        public ResultadoSiniestro validaEstatusAbierto(RespuestaSiniestro respuestaSiniestro, string fechaRegistro)
        {

            ResultadoSiniestro resultadoSiniestro = new ResultadoSiniestro();

            try
            {
                if (respuestaSiniestro.cuv == "" || respuestaSiniestro.cuv == null || respuestaSiniestro.cuv.Length != 16)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.CuvNoValida;
                    resultadoSiniestro.descripcion = "La Cuv no es válida ";
                    return resultadoSiniestro;
                }
                string mensajeOcirrencia = this.validacionFechaOcurrencia(fechaRegistro, respuestaSiniestro.fechaOcurrencia);
                if (!mensajeOcirrencia.Equals(""))
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.FechaOcurrenciaNoValida;
                    resultadoSiniestro.descripcion = mensajeOcirrencia; ;
                    return resultadoSiniestro;
                }
                if (this.validacionFechaREporte(fechaRegistro, respuestaSiniestro.fechaReporte) == true)

                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.FechaReporteNoEsvalida;
                    resultadoSiniestro.descripcion = "La fecha de reporte no es válida debe ser Mayor o igual a la fecha de Registro";
                    return resultadoSiniestro;
                }
                if (respuestaSiniestro.poliza == "" || respuestaSiniestro.poliza == null)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.NumeroPolizaNoValio;
                    resultadoSiniestro.descripcion = " El Número de póliza no es válido";
                    return resultadoSiniestro;
                }
                if (respuestaSiniestro.descripcionSiniestro == "" || respuestaSiniestro.descripcionSiniestro == null)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.DescripcionSiniestroNoValida;
                    resultadoSiniestro.descripcion = "La descripción del siniestro no es válida ";
                    return resultadoSiniestro;
                }
                if (this.validaCoberturaAfectada(respuestaSiniestro.coberturaAfectada) == true)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.CoberturaAfectadaNoVaida;
                    resultadoSiniestro.descripcion = " La cobertura afectada no es válida";
                    return resultadoSiniestro;
                }
                /*
                if (this.validacionFechaRespuestaAseguradora(fechaRegistro, respuestaSiniestro.fechaRespuestaAseguradora) == true)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.FechaOcurrenciaNoValida;
                    resultadoSiniestro.descripcion = "Fecha Respuesta Aseguradora debe ser Mayor a la fecha registro";
                    return resultadoSiniestro;
                }
                if (respuestaSiniestro.ajustador == "" || respuestaSiniestro.ajustador == null)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.NombreAjustadorNoValido;
                    resultadoSiniestro.descripcion = "El nombre del ajustador no es válido ";
                    return resultadoSiniestro;
                }
                if (this.validaEstatusReporte(respuestaSiniestro.estatusReporte) == true)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.EstatusReporteNoValido;
                    resultadoSiniestro.descripcion = " El estatus del reporte no es válido";
                    return resultadoSiniestro;
                }
                if (this.validaTipoMitigacion(respuestaSiniestro.tipoMitigacion) == true)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.tipoMitigacionNoValido;
                    resultadoSiniestro.descripcion = "El tipo de mitigación no es válido ";
                    return resultadoSiniestro;
                }
                if (respuestaSiniestro.respuesta == "" || respuestaSiniestro.respuesta == null )
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.ReservaFInalNoValida;
                    resultadoSiniestro.descripcion = "La respuesta no es válida";
                    return resultadoSiniestro;
                }
                if (this.validaAseguradora(respuestaSiniestro.aseguradora) == true)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.idAseguradorNoValida;
                    resultadoSiniestro.descripcion = "El id aseguradora no es válido";
                    return resultadoSiniestro;
                }
                if (respuestaSiniestro.folioreporteAseguradora == "" || respuestaSiniestro.folioreporteAseguradora == null)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.FolioReporteSeguradoraNoValido;
                    resultadoSiniestro.descripcion = "El folio reporte Aseguradora no es válida ";
                    return resultadoSiniestro;
                }
                if (respuestaSiniestro.numeroCertificado == "" || respuestaSiniestro.numeroCertificado == null)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.NumeroCertificadoNoValido;
                    resultadoSiniestro.descripcion = " El Número de Certificado no es Válido";
                    return resultadoSiniestro;
                }
                if (respuestaSiniestro.nombreAcreditado == "" || respuestaSiniestro.nombreAcreditado == null)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.NombreAcreditadoNoValido;
                    resultadoSiniestro.descripcion = "El Nombre del acreditado no es válido ";
                    return resultadoSiniestro;
                }
                if (respuestaSiniestro.causadeSiniestro == "" || respuestaSiniestro.causadeSiniestro == null)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.CausaSiniestroNoValida;
                    resultadoSiniestro.descripcion = " La causa del siniestro no es válida";
                    return resultadoSiniestro;
                }*/
                if (this.validacionFechaInspeccionAjustador(fechaRegistro, respuestaSiniestro.fechaInspeccionAjustador) == true)
                {
                    if (respuestaSiniestro.numeroCertificado == "" || respuestaSiniestro.numeroCertificado == null)
                        resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.FechaInspeccionAjustador;
                    resultadoSiniestro.descripcion = "La fecha de inspección del ajustador de la aseguradora no es válida ";
                    return resultadoSiniestro;
                } 
                /*if (this.validacionFechaDeterminacion(fechaRegistro, respuestaSiniestro.fechadedeterminacion) == true)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.FechaDeterminacionAseguradora;
                    resultadoSiniestro.descripcion = "Fecha Determinacion  debe ser Mayor a la fecha registro";
                    return resultadoSiniestro;
                }
                if (respuestaSiniestro.documentoBytes == null || respuestaSiniestro.documentoBytes.Length<=0)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.EvidenciaMitigacionNoValida;
                    resultadoSiniestro.descripcion = "La evidencia de mitigación no es válida. *Verificar que el formato sea óptimo para envío";
                    return resultadoSiniestro;
                }

                if (this.validacionFechaPagosAbierto(fechaRegistro, respuestaSiniestro.pagosIndemnizacionFecha) == true)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.FechaPagoINdemnizacionNoValida;
                    resultadoSiniestro.descripcion = "La fecha de pago de indemnización no es válida ";
                    return resultadoSiniestro;
                }

                if (this.validacionFechaPagosAbierto(fechaRegistro, respuestaSiniestro.pagosIndemnizacionFecha) == true)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.FechaPagoINdemnizacionNoValida;
                    resultadoSiniestro.descripcion = "La fecha de pago de indemnización no es válida ";
                    return resultadoSiniestro;
                }
                if (this.validacionFechaPagosAbierto(fechaRegistro, respuestaSiniestro.pagosHonorariosFecha) == true)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.FechaPagoHonorariosNoValida;
                    resultadoSiniestro.descripcion = "La fecha de pago de Honorarios no es válida ";
                    return resultadoSiniestro;
                }
                if (this.validacionFechaPagosAbierto(fechaRegistro, respuestaSiniestro.pagosGastosFecha) == true)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.FechaPagoHonorariosNoValida;
                    resultadoSiniestro.descripcion = "La fecha de pago de Gastos no es válida ";
                    return resultadoSiniestro;
                }
                if (this.validacionFechaPagosAbierto(fechaRegistro, respuestaSiniestro.totalPagoFecha) == true)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.FechaPagoHonorariosNoValida;
                    resultadoSiniestro.descripcion = "La fecha de pago Total no es válida ";
                    return resultadoSiniestro;
                }
*/
            }
            catch (Exception ex) {
                resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.RegistroNOK;
                resultadoSiniestro.descripcion = "No se guardó el registro, favor de intentarlo más tarde. ";
                return resultadoSiniestro;
            }

            


            return resultadoSiniestro;
        }

        public ResultadoSiniestro validaEstatusCerrado(RespuestaSiniestro respuestaSiniestro, string fechaRegistro)
        {

            ResultadoSiniestro resultadoSiniestro = new ResultadoSiniestro();
            resultadoSiniestro.codigo = 0;
            resultadoSiniestro.descripcion = "";
            try
            {
                if (respuestaSiniestro.cuv == "" || respuestaSiniestro.cuv == null || respuestaSiniestro.cuv.Length != 16)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.CuvNoValida;
                    resultadoSiniestro.descripcion = "La Cuv no es válida ";
                    return resultadoSiniestro;
                }
                string mensajeOcirrencia = this.validacionFechaOcurrencia(fechaRegistro, respuestaSiniestro.fechaOcurrencia);
                if (!mensajeOcirrencia.Equals(""))
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.FechaOcurrenciaNoValida;
                    resultadoSiniestro.descripcion = mensajeOcirrencia; ;
                    return resultadoSiniestro;
                }
                if (this.validacionFechaREporte(fechaRegistro, respuestaSiniestro.fechaReporte) == true)

                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.FechaReporteNoEsvalida;
                    resultadoSiniestro.descripcion = "La fecha de reporte no es válida debe ser Mayor o igual a la fecha de Registro";
                    return resultadoSiniestro;
                }
                if (respuestaSiniestro.poliza == "" || respuestaSiniestro.poliza == null)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.NumeroPolizaNoValio;
                    resultadoSiniestro.descripcion = " El Número de póliza no es válido";
                    return resultadoSiniestro;
                }
                if (respuestaSiniestro.descripcionSiniestro == "" || respuestaSiniestro.descripcionSiniestro == null)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.DescripcionSiniestroNoValida;
                    resultadoSiniestro.descripcion = "La descripción del siniestro no es válida ";
                    return resultadoSiniestro;
                }
                if (this.validaCoberturaAfectada(respuestaSiniestro.coberturaAfectada) == true)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.CoberturaAfectadaNoVaida;
                    resultadoSiniestro.descripcion = " La cobertura afectada no es válida";
                    return resultadoSiniestro;
                }

                if (this.validacionFechaRespuestaAseguradora(fechaRegistro, respuestaSiniestro.fechaRespuestaAseguradora) == true)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.FechaOcurrenciaNoValida;
                    resultadoSiniestro.descripcion = "Fecha Respuesta Aseguradora debe ser Mayor a la fecha registro";
                    return resultadoSiniestro;
                }
                if (respuestaSiniestro.ajustador == "" || respuestaSiniestro.ajustador == null)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.NombreAjustadorNoValido;
                    resultadoSiniestro.descripcion = "El nombre del ajustador no es válido ";
                    return resultadoSiniestro;
                }
                if (this.validaEstatusReporte(respuestaSiniestro.estatusReporte) == true)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.EstatusReporteNoValido;
                    resultadoSiniestro.descripcion = " El estatus del reporte no es válido";
                    return resultadoSiniestro;
                }
                if (this.validaTipoMitigacion(respuestaSiniestro.tipoMitigacion) == true)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.tipoMitigacionNoValido;
                    resultadoSiniestro.descripcion = "El tipo de mitigación no es válido ";
                    return resultadoSiniestro;
                }
                if (respuestaSiniestro.respuesta == "" || respuestaSiniestro.respuesta == null  )
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.ReservaFInalNoValida;
                    resultadoSiniestro.descripcion = "La respuesta no es válida";
                    return resultadoSiniestro;
                }
                if (this.validaAseguradora(respuestaSiniestro.aseguradora) == true)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.idAseguradorNoValida;
                    resultadoSiniestro.descripcion = "El id aseguradora no es válido";
                    return resultadoSiniestro;
                }
                if (respuestaSiniestro.folioreporteAseguradora == "" || respuestaSiniestro.folioreporteAseguradora == null)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.FolioReporteSeguradoraNoValido;
                    resultadoSiniestro.descripcion = "El folio reporte Aseguradora no es válida ";
                    return resultadoSiniestro;
                }
                else
                {
                    if (respuestaSiniestro.folioreporteAseguradora.Length>20)
                    {
                        resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.FolioReporteSeguradoraNoValido;
                        resultadoSiniestro.descripcion = "El folio reporte Aseguradora no debe exceder los 20 caracteres ";
                        return resultadoSiniestro;
                    }
                }
                if (respuestaSiniestro.numeroCertificado == "" || respuestaSiniestro.numeroCertificado == null)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.NumeroCertificadoNoValido;
                    resultadoSiniestro.descripcion = " El Número de Certificado no es Válido";
                    return resultadoSiniestro;
                }
                else
                {
                    if (respuestaSiniestro.numeroCertificado.Length>16)
                    {
                        resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.NumeroCertificadoNoValido;
                        resultadoSiniestro.descripcion = " El Número de Certificado no debeb exceder los 16 caracteres";
                        return resultadoSiniestro;
                    }
                }
                if (respuestaSiniestro.nombreAcreditado == "" || respuestaSiniestro.nombreAcreditado == null)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.NombreAcreditadoNoValido;
                    resultadoSiniestro.descripcion = "El Nombre del acreditado no es válido ";
                    return resultadoSiniestro;
                }
                if (respuestaSiniestro.causadeSiniestro == "" || respuestaSiniestro.causadeSiniestro == null)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.CausaSiniestroNoValida;
                    resultadoSiniestro.descripcion = " La causa del siniestro no es válida";
                    return resultadoSiniestro;
                }
                else
                {
                    if (respuestaSiniestro.causadeSiniestro.Length>50)
                    {
                        resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.CausaSiniestroNoValida;
                        resultadoSiniestro.descripcion = " La causa del siniestro no debeb exceder los 50 caracteres";
                        return resultadoSiniestro;
                    }
                }
                if (this.validacionFechaInspeccionAjustador(fechaRegistro, respuestaSiniestro.fechaInspeccionAjustador) == true)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.FechaInspeccionAjustador;
                    resultadoSiniestro.descripcion = "La fecha de inspección del ajustador de la aseguradora no es válida ";
                    return resultadoSiniestro;
                } 
                if (this.validacionFechaDeterminacion(fechaRegistro, respuestaSiniestro.fechadedeterminacion) == true)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.FechaDeterminacionAseguradora;
                    resultadoSiniestro.descripcion = "Fecha Determinacion  debe ser Mayor a la fecha registro";
                    return resultadoSiniestro;
                }
                if (respuestaSiniestro.documentoBytes == null || respuestaSiniestro.documentoBytes.Length <= 0)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.EvidenciaMitigacionNoValida;
                    resultadoSiniestro.descripcion = "La evidencia de mitigación no es válida. *Verificar que el formato sea óptimo para envío";
                    return resultadoSiniestro;
                }
                if (respuestaSiniestro.estimacion == null || respuestaSiniestro.estimacion.Value <= 0)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.EstimacionPagoNoValido;
                    resultadoSiniestro.descripcion = "La estimación del pago no es válida";
                    return resultadoSiniestro;
                }

                if (respuestaSiniestro.ajusteReserva == null || respuestaSiniestro.ajusteReserva.Value<=0)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.AjusteReservaNoValido;
                    resultadoSiniestro.descripcion = "El ajuste de la reserva no es válido";
                    return resultadoSiniestro;
                }

                if (respuestaSiniestro.pagosIndemnizacionCantidad == null || respuestaSiniestro.pagosIndemnizacionCantidad.Value <= 0)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.PagoIndemnizacionNoValido;
                    resultadoSiniestro.descripcion = "El pago de la indemnización no es válido";
                    return resultadoSiniestro;
                }
                if (this.validacionFechaPagos(fechaRegistro, respuestaSiniestro.pagosIndemnizacionFecha) == true)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.FechaPagoINdemnizacionNoValida;
                    resultadoSiniestro.descripcion = "La fecha de pago de indemnización no es válida ";
                    return resultadoSiniestro;
                }

                if (respuestaSiniestro.pagosHonorariosCantidad == null || respuestaSiniestro.pagosHonorariosCantidad.Value <= 0)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.PagoHonorarios;
                    resultadoSiniestro.descripcion = "El pago de los honorarios no es válido pago de la indemnización no es válido";
                    return resultadoSiniestro;
                }
                if (this.validacionFechaPagos(fechaRegistro, respuestaSiniestro.pagosHonorariosFecha) == true)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.FechaPagoHonorariosNoValida;
                    resultadoSiniestro.descripcion = "La fecha de pago de honorarios no es válida  ";
                    return resultadoSiniestro;
                }
                if (respuestaSiniestro.pagosGastosCantidad == null || respuestaSiniestro.pagosGastosCantidad.Value <= 0)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.PagoGastosNoValido;
                    resultadoSiniestro.descripcion = "El pago de gastos no es válido";
                    return resultadoSiniestro;
                }
                if (this.validacionFechaPagos(fechaRegistro, respuestaSiniestro.pagosGastosFecha) == true)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.FechaPagoGastoNoValida;
                    resultadoSiniestro.descripcion = "La fecha de pago de gastos no es válida  ";
                    return resultadoSiniestro;
                }

                if (respuestaSiniestro.pagosGastosCantidad == null || respuestaSiniestro.pagosGastosCantidad.Value <= 0)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.PagoGastosNoValido;
                    resultadoSiniestro.descripcion = "El pago de gastos no es válido";
                    return resultadoSiniestro;
                }
                if (this.validacionFechaPagos(fechaRegistro, respuestaSiniestro.pagosGastosFecha) == true)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.FechaPagoGastoNoValida;
                    resultadoSiniestro.descripcion = "La fecha de pago de gastos no es válida  ";
                    return resultadoSiniestro;
                }

                if (respuestaSiniestro.totalPagadoCantidad == null || respuestaSiniestro.totalPagadoCantidad.Value <= 0)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.PagoTotalNoValido;
                    resultadoSiniestro.descripcion = "El pago total no es válido ";
                    return resultadoSiniestro;
                }
                if (this.validacionFechaPagos(fechaRegistro, respuestaSiniestro.totalPagoFecha) == true)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.FechaPagoTotalNoValido;
                    resultadoSiniestro.descripcion = "La fecha de pago total no es válida   ";
                    return resultadoSiniestro;
                }
                if (respuestaSiniestro.reservaFinal == null || respuestaSiniestro.reservaFinal.Value <=0)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.ReservaFInalNoValida;
                    resultadoSiniestro.descripcion = "La reserva final no es válida ";
                    return resultadoSiniestro;
                }

                if (this.validacionFechaCierreSiniestro(fechaRegistro, respuestaSiniestro.fechadecierredeSiniestro) == true)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.FechaCierreSiniestroNoValido;
                    resultadoSiniestro.descripcion = "La fecha de cierre del Siniestro no es válida";
                    return resultadoSiniestro;
                }

                if (respuestaSiniestro.observacionesdeSiniestro == "" || respuestaSiniestro.observacionesdeSiniestro == null  )
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.ObservacionesSIniestroNoValido;
                    resultadoSiniestro.descripcion = "Las observaciones del siniestro no son válidas";
                    return resultadoSiniestro;
                }
                else
                {
                    if (respuestaSiniestro.observacionesdeSiniestro.Length>500)
                    {
                        resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.ObservacionesSIniestroNoValido;
                        resultadoSiniestro.descripcion = "Las observaciones del siniestro supera los 500 caracteres";
                        return resultadoSiniestro;
                    }
                }

            }
            catch (Exception ex)
            {
                resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.RegistroNOK;
                resultadoSiniestro.descripcion = "No se guardó el registro, favor de intentarlo más tarde. ";
                return resultadoSiniestro;
            }



            return resultadoSiniestro;
        }

        public ResultadoSiniestro validaEstatusDeclinado(RespuestaSiniestro respuestaSiniestro, string fechaRegistro)
        {

            ResultadoSiniestro resultadoSiniestro = new ResultadoSiniestro();

            try
            {
                if (respuestaSiniestro.cuv == "" || respuestaSiniestro.cuv == null || respuestaSiniestro.cuv.Length != 16)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.CuvNoValida;
                    resultadoSiniestro.descripcion = "La Cuv no es válida ";
                    return resultadoSiniestro;
                }

                string mensajeOcirrencia = this.validacionFechaOcurrencia(fechaRegistro, respuestaSiniestro.fechaOcurrencia);
                if (!mensajeOcirrencia.Equals(""))
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.FechaOcurrenciaNoValida;
                    resultadoSiniestro.descripcion = mensajeOcirrencia;;
                    return resultadoSiniestro;
                }
                if (this.validacionFechaREporte(fechaRegistro, respuestaSiniestro.fechaReporte) == true)

                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.FechaReporteNoEsvalida;
                    resultadoSiniestro.descripcion = "La fecha de reporte no es válida debe ser Mayor o igual a la fecha de Registro";
                    return resultadoSiniestro;
                }
                if (respuestaSiniestro.poliza == "" || respuestaSiniestro.poliza == null)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.NumeroPolizaNoValio;
                    resultadoSiniestro.descripcion = " El Número de póliza no es válido";
                    return resultadoSiniestro;
                }
                if (respuestaSiniestro.descripcionSiniestro == "" || respuestaSiniestro.descripcionSiniestro == null)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.DescripcionSiniestroNoValida;
                    resultadoSiniestro.descripcion = "La descripción del siniestro no es válida ";
                    return resultadoSiniestro;
                }
                if (this.validaCoberturaAfectada(respuestaSiniestro.coberturaAfectada) == true)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.CoberturaAfectadaNoVaida;
                    resultadoSiniestro.descripcion = " La cobertura afectada no es válida";
                    return resultadoSiniestro;
                }

                if (this.validacionFechaRespuestaAseguradora(fechaRegistro, respuestaSiniestro.fechaRespuestaAseguradora) == true)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.FechaOcurrenciaNoValida;
                    resultadoSiniestro.descripcion = "Fecha Respuesta Aseguradora debe ser Mayor a la fecha registro";
                    return resultadoSiniestro;

                }
                if (respuestaSiniestro.ajustador == "" || respuestaSiniestro.ajustador == null)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.NombreAjustadorNoValido;
                    resultadoSiniestro.descripcion = "El nombre del ajustador no es válido ";
                    return resultadoSiniestro;
                }
                if (this.validaEstatusReporte(respuestaSiniestro.estatusReporte) == true)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.EstatusReporteNoValido;
                    resultadoSiniestro.descripcion = " El estatus del reporte no es válido";
                    return resultadoSiniestro;
                }
                if (respuestaSiniestro.respuesta == "" || respuestaSiniestro.respuesta == null)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.ReservaFInalNoValida;
                    resultadoSiniestro.descripcion = "La respuesta no es válida";
                    return resultadoSiniestro;
                }
                if (this.validaAseguradora(respuestaSiniestro.aseguradora) == true)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.idAseguradorNoValida;
                    resultadoSiniestro.descripcion = "El id aseguradora no es válido";
                    return resultadoSiniestro;
                }
                if (respuestaSiniestro.folioreporteAseguradora == "" || respuestaSiniestro.folioreporteAseguradora == null)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.FolioReporteSeguradoraNoValido;
                    resultadoSiniestro.descripcion = "El folio reporte Aseguradora no es válida ";
                    return resultadoSiniestro;
                }
                else
                {
                    if (respuestaSiniestro.folioreporteAseguradora.Length>20)
                    {
                        resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.FolioReporteSeguradoraNoValido;
                        resultadoSiniestro.descripcion = "El folio reporte Aseguradora no debe exceder los 20 caracteres";
                        return resultadoSiniestro;
                    }
                }
                if (respuestaSiniestro.numeroCertificado == "" || respuestaSiniestro.numeroCertificado == null)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.NumeroCertificadoNoValido;
                    resultadoSiniestro.descripcion = " El Número de Certificado no es Válido";
                    return resultadoSiniestro;
                }
                else
                {
                    if (respuestaSiniestro.numeroCertificado.Length>16)
                    {
                        resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.NumeroCertificadoNoValido;
                        resultadoSiniestro.descripcion = " El Número de Certificado no debe exceder los 16 caracteres";
                        return resultadoSiniestro;
                    }
                }
                if (respuestaSiniestro.nombreAcreditado == "" || respuestaSiniestro.nombreAcreditado == null)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.NombreAcreditadoNoValido;
                    resultadoSiniestro.descripcion = "El Nombre del acreditado no es válido ";
                    return resultadoSiniestro;
                }
                if (respuestaSiniestro.causadeSiniestro == "" || respuestaSiniestro.causadeSiniestro == null)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.CausaSiniestroNoValida;
                    resultadoSiniestro.descripcion = " La causa del siniestro no es válida";
                    return resultadoSiniestro;
                }else{
                    if (respuestaSiniestro.causadeSiniestro.Length>50)
                    {
                        resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.CausaSiniestroNoValida;
                        resultadoSiniestro.descripcion = " La causa del siniestro no debe exceder los 50 caracteres";
                        return resultadoSiniestro;
                    }
                }
                 //if (this.validacionFechaOpcional(fechaRegistro, respuestaSiniestro.fechaInspeccionAjustador) == true)        
                  if (this.validacionFechaInspeccionAjustador(fechaRegistro, respuestaSiniestro.fechaInspeccionAjustador) == true) 
                  {
                      resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.FechaInspeccionAjustador;
                      resultadoSiniestro.descripcion = "La fecha de inspección del ajustador de la aseguradora no es válida ";
                      return resultadoSiniestro;
                  } 
                if (this.validacionFechaOpcional(fechaRegistro, respuestaSiniestro.fechadedeterminacion) == true)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.FechaDeterminacionAseguradora;
                    resultadoSiniestro.descripcion = "Fecha Determinacion  debe ser Mayor a la fecha registro";
                    return resultadoSiniestro;
                }

                if (respuestaSiniestro.observacionesdeSiniestro == "" || respuestaSiniestro.observacionesdeSiniestro == null)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.ObservacionesSIniestroNoValido;
                    resultadoSiniestro.descripcion = "Las observaciones del siniestro no son válidas";
                    return resultadoSiniestro;
                }
                else
                {
                    if (respuestaSiniestro.observacionesdeSiniestro.Length > 500)
                    {
                        resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.ObservacionesSIniestroNoValido;
                        resultadoSiniestro.descripcion = "Las observaciones del siniestro supera los 500 caracteres";
                        return resultadoSiniestro;
                    }
                }
                if (respuestaSiniestro.documentoBytes == null || respuestaSiniestro.documentoBytes.Length <= 0)
                {
                    resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.EvidenciaMitigacionNoValida;
                    resultadoSiniestro.descripcion = "La evidencia de mitigación no es válida. *Verificar que el formato sea óptimo para envío";
                    return resultadoSiniestro;
                }
            }
            catch (Exception ex)
            {
                resultadoSiniestro.codigo = (int)EnumValidacionSiniestro.RegistroNOK;
                resultadoSiniestro.descripcion = "No se guardó el registro, favor de intentarlo más tarde. ";
                return resultadoSiniestro;
            }


            return resultadoSiniestro;
        }

        public string validacionFechaOcurrencia(string fechaRegistro, string fechaOcurrencia)
        {
            string mensaje = "";
            try
            {
                string _fechaRegistro = fechaRegistro.Substring(0, 10);

                DateTime dateTimeRegistro = DateTime.Parse(_fechaRegistro);
                DateTime dateOcurrencia = DateTime.Parse(fechaOcurrencia);
            

                DateTime dateTimeMinima= DateTime.Parse("2018-06-30");

                int valorMinimo = dateTimeMinima.CompareTo(dateOcurrencia);



                if (valorMinimo > 0)
                {
                    mensaje = "Fecha Ocurrencia debe ser Mayor a Junio del 2018";
                }

                if (mensaje.Equals(""))
                {
                    int valor = dateTimeRegistro.CompareTo(dateOcurrencia);

                    if (valor >= 0)
                    {
                        Console.WriteLine(" fechaOcurrencia OK");
                    }
                    else
                    {
                        mensaje = "Fecha Ocurrencia debe ser Menor a la fecha registro";
                    }
                }

                


            }
            catch (Exception ex)
            {
                mensaje = ex.Message;
            }
           
            return mensaje;
        }


        public bool validaCoberturaAfectada(string coberturaAfectada)
        {
            bool bandera = true;

            try
            {

                if (coberturaAfectada != "" || coberturaAfectada != null)
                {

                    int idcoberturaAfectada = Convert.ToInt32(coberturaAfectada);

                    if (idcoberturaAfectada == 1 || idcoberturaAfectada == 2 || idcoberturaAfectada == 3)
                    {
                        bandera = false;
                    }
                }
            }
            catch (Exception ex)
            {
                bandera = true;
            }

            return bandera;
        }


        public bool validacionFechaREporte(string fechaRegistro, string FechaRespuestaReporte)
        {
            bool bandera = false;

            try
            {

                string _fechaRegistro = fechaRegistro.Substring(0, 10);

                DateTime dateTimeRegistro = DateTime.Parse(_fechaRegistro);

                DateTime dateTimeFechaFechaRespuestaReporte = DateTime.Parse(FechaRespuestaReporte);
                
                int valor = dateTimeRegistro.CompareTo(dateTimeFechaFechaRespuestaReporte);

                if (valor <= 0)
                {
                  
                    Console.WriteLine(" Fecha Reporte Mayor a la fecha de Registro ");
                }
                else
                {
                    bandera = true;
                    Console.WriteLine("Fecha Reporte Menor a la fecha de Registro ");
                }

            }
            catch (Exception ex)
            {
                bandera = true;
            }

            return bandera;
        }



        public bool validacionFechaRespuestaAseguradora(string fechaRegistro, string FechaRespuestaAseguradora)
        {
            bool bandera = false;

            try
            {
                string _fechaRegistro = fechaRegistro.Substring(0, 10);

                DateTime dateTimeRegistro = DateTime.Parse(_fechaRegistro);

                DateTime dateTimeFechaRespuestaAseguradora = DateTime.Parse(FechaRespuestaAseguradora);

                int valor = dateTimeRegistro.CompareTo(dateTimeFechaRespuestaAseguradora);

                if (valor >= 0)
                {
                    bandera = true;
                }

            }
            catch (Exception ex)
            {
                bandera = true;
            }

            return bandera;
        }


        public bool validaTipoMitigacion(string tipoMitigacion)
        {
            bool bandera = true;

            try
            {
                if (tipoMitigacion != "" || tipoMitigacion != null)
                {

                    int idtipoMitigacion = Convert.ToInt32(tipoMitigacion);

                    if (idtipoMitigacion == 1 || idtipoMitigacion == 2 || idtipoMitigacion == 3 || idtipoMitigacion == 4)
                    {
                        bandera = false;
                    }
                }
            }
            catch (Exception ex) {
                bandera = true;

            }

            return bandera;
        }

        public bool validaAseguradora(string aseguradora)
        {
            bool bandera = true;


            try
            {
                if (aseguradora != "" || aseguradora != null)
                {

                    int idAseguradora = Convert.ToInt32(aseguradora);

                    if (idAseguradora == 3 || idAseguradora == 5 || idAseguradora == 6)
                    {
                        bandera = false;
                    }
                }

            }
            catch (Exception ex)
            {
                bandera = true;

            }
            return bandera;
        }

        public bool validacionFechaInspeccionAjustador(string fechaRegistro, string FechaInspeccionAjustador)
        {
            bool bandera = false;
            try
            {
                string _fechaRegistro = fechaRegistro.Substring(0, 10);

                DateTime dateTimeRegistro = DateTime.Parse(_fechaRegistro);

                DateTime dateTimeFechaInspeccionAjustador = DateTime.Parse(FechaInspeccionAjustador);

                int valor = dateTimeRegistro.CompareTo(dateTimeFechaInspeccionAjustador);

                if (valor >= 0)
                {
                    bandera = true;
                }
            }
            catch (Exception ex)
            {
                bandera = true;

            }

            return bandera;
        } 

        public bool validacionFechaDeterminacion(string fechaRegistro, string FechaDeterminacion)
        {
            bool bandera = false;

            try
            {

                string _fechaRegistro = fechaRegistro.Substring(0, 10);

                DateTime dateTimeRegistro = DateTime.Parse(_fechaRegistro);

                DateTime dateTimeFechaDeterminacion = DateTime.Parse(FechaDeterminacion);


                int valor = dateTimeRegistro.CompareTo(dateTimeFechaDeterminacion);

                if (valor >= 0)
                {
                    bandera = true;
                }

            }
            catch (Exception ex)
            {
                bandera = true;

            }

            return bandera;
        }

        public bool validacionFechaPagos(string fechaRegistro, string fechaPagos)
        {
            bool bandera = false;
            try
            {
               
                string _fechaRegistro = fechaRegistro.Substring(0, 10);

                DateTime dateTimeRegistro = DateTime.Parse(_fechaRegistro);
                DateTime datePagos = DateTime.Parse(fechaPagos);
                int valor = dateTimeRegistro.CompareTo(datePagos);

                if (valor <=0)
                {
                    Console.WriteLine(" datePagos OK");
                }
                else
                {
                    bandera = true;
                }
            }
            catch (Exception ex)
            {
                bandera = true;
            }

            return bandera;
        }
        public bool validacionFechaPagosAbierto(string fechaRegistro, string fechaPagos)
        {
            bool bandera = false;
            try
            {

                if (fechaPagos.Equals(""))
                {
                    Console.WriteLine(" datePagos OK");
                }
                else
                {
                    string _fechaRegistro = fechaRegistro.Substring(0, 10);

                    DateTime dateTimeRegistro = DateTime.Parse(_fechaRegistro);
                    DateTime datePagos = DateTime.Parse(fechaPagos);
                    int valor = dateTimeRegistro.CompareTo(datePagos);

                    if (valor < 0)
                    {
                        Console.WriteLine(" datePagos OK");
                    }
                    else
                    {
                        bandera = true;
                    }
                }

                
            }
            catch (Exception ex)
            {
                bandera = true;
            }

            return bandera;
        }
        public bool validacionFechaCierreSiniestro(string fechaRegistro, string fechaCierreSiniestro)
        {
            bool bandera = false;

            try
            {
                string _fechaRegistro = fechaRegistro.Substring(0, 10);

                DateTime dateTimeRegistro = DateTime.Parse(_fechaRegistro);

                DateTime dateTimeFechaCisrreSiniestro = DateTime.Parse(fechaCierreSiniestro);

                int valor = dateTimeRegistro.CompareTo(dateTimeFechaCisrreSiniestro);

                if (valor >= 0)
                {
                    bandera = true;
                }

            }
            catch (Exception ex)
            {
                bandera = true;
            }

            return bandera;
        }

        public bool validacionFechaOpcional(string fechaRegistro, string fechaOpcional)
        {
            bool bandera = false;
            try
            {
                if (fechaOpcional==null)
                {
                    bandera = false;
                }
                else
                {
                    if (fechaOpcional.Equals(""))
                    {
                        bandera = false;
                    }
                    else
                    {
                        string _fechaRegistro = fechaRegistro.Substring(0, 10);

                        DateTime dateTimeRegistro = DateTime.Parse(_fechaRegistro);

                        DateTime dateTimefechaOpcional = DateTime.Parse(fechaOpcional);

                        int valor = dateTimeRegistro.CompareTo(dateTimefechaOpcional);

                        if (valor > 0)
                        {
                            bandera = true;
                        }
                    }
                }

                
            }

             
            catch (Exception ex)
            {
                bandera = true;

            }

            return bandera;
        }

        public string validacionFechaNull( string fechaValidar)
        {
            string msg = "";

            try
            {

                if ( fechaValidar==null)
                {
                    msg = null;
                }
                else
                {
                    if (fechaValidar.Equals(""))
                    {
                        msg = null;
                    }
                    else
                    {
                        DateTime dateTimeFechaCisrreSiniestro = DateTime.Parse(fechaValidar);

                        msg = dateTimeFechaCisrreSiniestro.ToString("yyyy-MM-dd");
                    }
                }

       

            }
            catch (Exception ex)
            {
                msg = null;
            }

            return msg;
        }

        public string formatoFecha(string fechaValidar)
        {
            string msg = "";

            try
            {

                if (!fechaValidar.Equals("") || fechaValidar!=null)
                {
                    DateTime dateTimeFechaCisrreSiniestro = DateTime.Parse(fechaValidar);

                    msg = dateTimeFechaCisrreSiniestro.ToString("yyyy-MM-dd");
                }
                else
                {
                    msg = "";
                }
               
               
            }
            catch (Exception ex)
            {
                msg = "";
            }

            return msg;
        }

        public int? validarCantidad(int? cantidad)
        {
            int valor = 0;

            try
            {
              

            }
            catch (Exception ex)
            {
                valor=0;
            }

            return valor;
        }

        public string validaFechaCIerraxCobertura(int idTipoCoberturaSiniestro, string fechaInicio)
        {
            string fechaFinal = "";
            DateTime dateTime = DateTime.Parse(fechaInicio);
            switch (idTipoCoberturaSiniestro)
            {
                case (int)EnumTipoCobertura.Estructura:
                    fechaFinal = dateTime.AddYears(10).ToString("dd-MM-yyyy hh:mm:ss");
                    break;
                case (int)EnumTipoCobertura.Impermeabilización:
                    fechaFinal = dateTime.AddYears(5).ToString("dd-MM-yyyy hh:mm:ss");
                    break;
                case (int)EnumTipoCobertura.Instalaciones:
                    fechaFinal = dateTime.AddYears(2).ToString("dd-MM-yyyy hh:mm:ss");
                    break;
                case (int)EnumTipoCobertura.Gastos:
                    fechaFinal = dateTime.AddYears(10).ToString("dd-MM-yyyy hh:mm:ss");
                    break;
                default:
                    fechaFinal = dateTime.AddYears(10).ToString("dd-MM-yyyy hh:mm:ss");
                    break;
            }

            return fechaFinal;
        }
    }
}
