﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api
{
    public class EstadoDeCuenta
    {
        public string usuarioSeguridad { get; set; }
        public string nombreEmpresa { get; set; }
        public string rfc { get; set; }
        public decimal saldo { get; set; }
        public decimal saldoPendiente { get; set; }
        public decimal totalDepositado { get; set; }
        public decimal totalUtilizado { get; set; }
        public string domicilio { get; set; }
        public int idEmpresa { get; set; }
        public string fechaActual { get; set; }
        public decimal tarifaConIva { get; set; }

    }
}
