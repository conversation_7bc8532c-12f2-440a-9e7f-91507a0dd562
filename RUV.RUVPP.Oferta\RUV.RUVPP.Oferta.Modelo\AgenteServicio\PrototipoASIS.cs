﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.AgenteServicio
{
    public class PrototipoASIS
    {
        public PrototipoEncabezadoASIS prototipo;

        public DetallePrototipoASIS[] detallePrototipo;
    }

    public class PrototipoEliminarASIS  
    {
        public int idPrototipo { get; set; }
    }
}
