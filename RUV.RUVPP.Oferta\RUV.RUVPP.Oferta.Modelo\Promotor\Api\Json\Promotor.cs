﻿using System;
using System.Collections.Generic;

namespace RUV.RUVPP.Oferta.Modelo.Promotor.Api.Json
{
    /// <summary>
    /// 
    /// </summary>
    public class Promotor
    {
        public Int32 idPromotor { get; set; }
        public List<Estado> cobertura { get; set; }
        public string nombre { get; set; }
        public string apellidoPaterno { get; set; }
        public string apellidoMaterno { get; set; }
        public string rfc { get; set; }
        public string curp { get; set; }
        public string telefono { get; set; }
        public string correo { get; set; }
        public string numeroCertificado { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        public Promotor()
        {
            this.cobertura = new List<Estado>();
        }

    }
}
