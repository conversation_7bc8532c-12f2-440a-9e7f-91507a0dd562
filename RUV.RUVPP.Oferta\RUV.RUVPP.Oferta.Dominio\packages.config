﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="ClosedXML" version="0.88.0" targetFramework="net452" />
  <package id="CommonServiceLocator" version="1.3" targetFramework="net452" />
  <package id="CoordinateSharp" version="2.6.1.1" targetFramework="net452" />
  <package id="Dapper" version="1.50.2" targetFramework="net452" />
  <package id="DocumentFormat.OpenXml" version="2.7.2" targetFramework="net452" />
  <package id="EnterpriseLibrary.TransientFaultHandling" version="6.0.1304.0" targetFramework="net452" />
  <package id="EnterpriseLibrary.TransientFaultHandling.Data" version="6.0.1304.1" targetFramework="net452" />
  <package id="FastMember.Signed" version="1.1.0" targetFramework="net452" />
  <package id="iTextSharp" version="5.5.13.1" targetFramework="net452" />
  <package id="MetadataExtractor" version="2.4.2" targetFramework="net452" />
  <package id="Microsoft.ApplicationInsights" version="2.2.0" targetFramework="net452" />
  <package id="Microsoft.ApplicationInsights.Agent.Intercept" version="2.0.7" targetFramework="net452" />
  <package id="Microsoft.ApplicationInsights.DependencyCollector" version="2.2.0" targetFramework="net452" />
  <package id="Microsoft.ApplicationInsights.PerfCounterCollector" version="2.2.0" targetFramework="net452" />
  <package id="Microsoft.ApplicationInsights.PersistenceChannel" version="1.2.3" targetFramework="net452" />
  <package id="Microsoft.ApplicationInsights.Web" version="2.2.0" targetFramework="net452" />
  <package id="Microsoft.ApplicationInsights.WindowsServer" version="2.2.0" targetFramework="net452" />
  <package id="Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel" version="2.2.0" targetFramework="net452" />
  <package id="Microsoft.AspNet.Cors" version="5.2.3" targetFramework="net452" />
  <package id="Microsoft.AspNet.Mvc" version="5.2.3" targetFramework="net452" />
  <package id="Microsoft.AspNet.Razor" version="3.2.3" targetFramework="net452" />
  <package id="Microsoft.AspNet.WebApi" version="5.2.3" targetFramework="net452" />
  <package id="Microsoft.AspNet.WebApi.Client" version="5.2.3" targetFramework="net452" />
  <package id="Microsoft.AspNet.WebApi.Core" version="5.2.3" targetFramework="net452" />
  <package id="Microsoft.AspNet.WebApi.Owin" version="5.2.3" targetFramework="net452" />
  <package id="Microsoft.AspNet.WebApi.WebHost" version="5.2.3" targetFramework="net452" />
  <package id="Microsoft.AspNet.WebPages" version="3.2.3" targetFramework="net452" />
  <package id="Microsoft.Azure.KeyVault.Core" version="1.0.0" targetFramework="net452" />
  <package id="Microsoft.Bcl" version="1.1.10" targetFramework="net452" />
  <package id="Microsoft.Bcl.Async" version="1.0.168" targetFramework="net452" />
  <package id="Microsoft.Bcl.Build" version="1.0.21" targetFramework="net452" />
  <package id="Microsoft.Data.Edm" version="5.6.4" targetFramework="net452" />
  <package id="Microsoft.Data.OData" version="5.6.4" targetFramework="net452" />
  <package id="Microsoft.Data.Services.Client" version="5.6.4" targetFramework="net452" />
  <package id="Microsoft.Diagnostics.Tracing.EventSource.Redist" version="1.1.28" targetFramework="net452" />
  <package id="Microsoft.IdentityModel.Protocol.Extensions" version="1.0.0" targetFramework="net452" />
  <package id="Microsoft.Owin" version="3.0.1" targetFramework="net452" />
  <package id="Microsoft.Owin.Cors" version="3.0.0" targetFramework="net452" />
  <package id="Microsoft.Owin.Host.SystemWeb" version="3.0.1" targetFramework="net452" />
  <package id="Microsoft.Owin.Security" version="3.0.1" targetFramework="net452" />
  <package id="Microsoft.Owin.Security.Jwt" version="3.0.0" targetFramework="net452" />
  <package id="Microsoft.Owin.Security.OAuth" version="3.0.1" targetFramework="net452" />
  <package id="Microsoft.Owin.Security.OpenIdConnect" version="3.0.0" targetFramework="net452" />
  <package id="Microsoft.ReportViewer" version="11.0.3366.16" targetFramework="net452" />
  <package id="Microsoft.SqlServer.Types" version="14.0.1016.290" targetFramework="net452" />
  <package id="Microsoft.Web.Infrastructure" version="*******" targetFramework="net452" />
  <package id="Microsoft.WindowsAzure.ConfigurationManager" version="3.2.1" targetFramework="net452" />
  <package id="Newtonsoft.Json" version="9.0.1" targetFramework="net452" />
  <package id="NPOI" version="2.4.1" targetFramework="net452" />
  <package id="Oracle.ManagedDataAccess" version="12.1.24160719" targetFramework="net452" />
  <package id="Owin" version="1.0" targetFramework="net452" />
  <package id="RestSharp" version="105.2.3" targetFramework="net452" />
  <package id="RUV.Comun" version="2.6.0" targetFramework="net452" />
  <package id="RUV.Comun.Datos" version="2.6.2.1" targetFramework="net452" />
  <package id="RUV.Comun.Negocio" version="2.6.0.2" targetFramework="net452" />
  <package id="RUV.Comun.Seguridad.JWT" version="2.6.0.8" targetFramework="net452" />
  <package id="RUV.Comun.Servicios" version="2.6.1" targetFramework="net452" />
  <package id="RUV.Comun.Utilerias" version="2.6.2.4" targetFramework="net452" />
  <package id="RUV.Comun.Web" version="2.6.2.3" targetFramework="net452" />
  <package id="RUV.RUVPP.Datos.Comun.Contratos" version="2.5.0.7" targetFramework="net452" />
  <package id="RUV.RUVPP.Datos.Empresa.SqlAzure" version="2.5.0.7" targetFramework="net452" />
  <package id="RUV.RUVPP.Entidades.Comun" version="1.0.0.15" targetFramework="net452" />
  <package id="RUV.RUVPP.Entidades.Empresa" version="2.5.0.11" targetFramework="net452" />
  <package id="RUV.RUVPP.Entidades.General.Notificaciones" version="2.5.1.33" targetFramework="net452" />
  <package id="RUV.RUVPP.Generales.Domino" version="1.0.0.37" targetFramework="net452" />
  <package id="RUV.RUVPP.Negocio.Empresa" version="2.5.0.14" targetFramework="net452" />
  <package id="RUV.RUVPP.Negocio.General.Documentos" version="*******" targetFramework="net452" />
  <package id="RUV.RUVPP.Negocio.General.Notificaciones" version="********" targetFramework="net452" />
  <package id="RUV.RUVPP.Negocio.General.OrdenTrabajo" version="********" targetFramework="net452" />
  <package id="RUV.RUVPP.Negocio.General.Seguridad" version="********" targetFramework="net452" />
  <package id="RUV.RUVPP.Negocio.General.Tarificador" version="********" targetFramework="net452" />
  <package id="SharpZipLib" version="1.0.0" targetFramework="net452" />
  <package id="StackExchange.Redis" version="1.0.394" targetFramework="net452" />
  <package id="System.Configuration.Abstractions" version="********" targetFramework="net452" />
  <package id="System.Drawing.Primitives" version="4.3.0" targetFramework="net452" />
  <package id="System.IdentityModel.Tokens.Jwt" version="4.0.1" targetFramework="net452" />
  <package id="System.Spatial" version="5.6.4" targetFramework="net452" />
  <package id="Unity" version="4.0.1" targetFramework="net452" />
  <package id="Unity.Interception" version="4.0.1" targetFramework="net452" />
  <package id="WindowsAzure.Storage" version="7.0.0" targetFramework="net452" />
  <package id="XmpCore" version="6.1.10" targetFramework="net452" />
</packages>