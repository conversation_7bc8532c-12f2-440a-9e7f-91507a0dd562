﻿using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Owin;
using Owin;
using Microsoft.Owin.Cors;

[assembly: OwinStartup(typeof(RUV.RUVPP.Oferta.Api.Startup))]

namespace RUV.RUVPP.Oferta.Api
{
    public partial class Startup
    {
        public void Configuration(IAppBuilder app)
        {
          //! Por seguirdad agregar disabledCors en las accciones de administracion de usuarios de RuvEmpresa 
            app.UseCors(CorsOptions.AllowAll);

            //! para validacion de WebApi
            ////*
            // var config = new HttpConfiguration();
            //config.SuppressDefaultHostAuthentication();
            //config.Filters.Add(new HostAuthenticationFilter(OAuthDefaults.AuthenticationType));
            ////*

            //config.MapHttpAttributeRoutes();
            StartupAuth.Configuration(app);
        }
    }
}
