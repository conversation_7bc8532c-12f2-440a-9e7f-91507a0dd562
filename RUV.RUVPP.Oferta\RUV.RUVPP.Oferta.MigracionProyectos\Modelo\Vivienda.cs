//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace RUV.RUVPP.Oferta.MigracionProyectos.Modelo
{
    using System;
    using System.Collections.Generic;
    
    public partial class Vivienda
    {
        public int idVivienda { get; set; }
        public int idProyecto { get; set; }
        public Nullable<int> idOfertaVivienda { get; set; }
        public Nullable<byte> idEstatusVivienda { get; set; }
        public int idPrototipo { get; set; }
        public byte idOrientacionVivienda { get; set; }
        public byte idNivelVivienda { get; set; }
        public byte idTipoZonaVivienda { get; set; }
        public Nullable<int> idDomicilioGeografico { get; set; }
        public short identificadorVivienda { get; set; }
        public string numeroCatastralLote { get; set; }
        public string edificio { get; set; }
        public string planta { get; set; }
        public Nullable<decimal> costo { get; set; }
        public Nullable<decimal> metros2Lote { get; set; }
        public Nullable<decimal> metrosFrenteLote { get; set; }
        public string cuv { get; set; }
        public System.DateTime fechaRegistro { get; set; }
        public Nullable<System.DateTime> fechaActualizacion { get; set; }
        public string nombreCondominio { get; set; }
        public string cuvGeografica { get; set; }
        public Nullable<int> idTipoAsentamiento { get; set; }
        public Nullable<byte> idEstatusJuridicoVivienda { get; set; }
        public Nullable<byte> idModalidadVivienda { get; set; }
        public Nullable<byte> idTipoAportacionVivienda { get; set; }
        public Nullable<byte> idTipoProgramaVivienda { get; set; }
        public Nullable<int> featId { get; set; }
        public Nullable<byte> numeroEstacionamientos { get; set; }
        public Nullable<int> idViviendaPlanoSIG { get; set; }
    
        public virtual DomicilioGeografico DomicilioGeografico { get; set; }
        public virtual Proyecto Proyecto { get; set; }
    }
}
