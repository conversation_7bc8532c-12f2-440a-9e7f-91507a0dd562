﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.AgenteServicio
{
    public class PrototipoEncabezadoASIS
    {
        public int idPrototipo { get; set; }
        public int idRUVAsis { get; set; }
        public string nombre { get; set; }
        public int idTipologiaVivienda { get; set; }
        public int areaLote { get; set; }
        public int metrosFrenteLote { get; set; }
        public int numeroNivelesVivienda { get; set; }
        public int numeroNivelesPrototipo { get; set; }
        public int numeroRecamaras { get; set; }
        public int? numeroAlcobas { get; set; }
        public int numeroBaniosCompletos { get; set; }
        public int numeroBaniosMedios { get; set; }
        public int precioVivienda { get; set; }
        public decimal areaMuros { get; set; }
        public decimal superficieVolados { get; set; }
        public decimal superficieIndivisosaCubierto { get; set; }
        public int alturaLocales { get; set; }
        public int huellas { get; set; }
        public int peraltes { get; set; }
        public int anchoRampa { get; set; }
        public int superficieHabitable { get; set; }
        public int superficieTotalHabitable { get; set; }
        public int superficeTotalConstruida { get; set; }
    }
}
