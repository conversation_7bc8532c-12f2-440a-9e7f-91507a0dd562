﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.OrdenVerificacion.Data
{
    /// <summary>
    /// 
    /// </summary>
    public class OrdenVerificacion
    {
        public string idOrdenVerificacion { get; set; }
        public string idOfertaVivienda { get; set; }
        public string claveOferta { get; set; }

        public string estatus { get; set; }
        public DateTime? fechaRegistro { get; set; }
        public DateTime? fechaFin { get; set; }

        public string nombreFrente { get; set; }

        public string noRuvVerificador { get; set; }


        public string razonSocial { get; set; }

        public string noRuvOferente { get; set; }


        public string razonSocialOferente { get; set; }

        public string direccionEntidad { get; set; }

        public string porcentajeAvanceObra { get; set; }

        public int numeroPrototipos { get; set; }

        public int numeroViviendas { get; set; }

        public int ceroaveinte { get; set; }

        public int veintiunoacuarenta { get; set; }

        public int cuarentayunoysesenta { get; set; }

        public int sesentayunoyochenta { get; set; }

        public int ochentayunoynoventaynueve { get; set; }

        public int alcien { get; set; }

        public List<string> correos { get; set; }

        public List<string> telefonos { get; set; }

        public int idVerificador { get; set; }
        public string acreedorSAPOferente { get; set; }
        public string acreedorSAPVerificador { get; set; }
        public DateTime? fechaRegistroVerificador { get; set; }

    }
}
