﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.OrdenVerificacion.Api
{
    public class PagosVOExcel
    {
        public string ReferenciaFormat { get; set; }
        public string proyecto { get; set; }
        public string Referencia { get; set; }
        public string clabe { get; set; }
        public string FechaSolicitud { get; set; }
        public string NoPago { get; set; }
        public decimal Monto { get; set; }
        public string Producto { get; set; }
        public string RFC_Verificador { get; set; }
        public string Verificador { get; set; }
        public string ReferenciaPago { get; set; }
        public string Prepago { get; set; }
        public int estatus { get; set; }
        public string claveBanco { get; set; }
    }
}
