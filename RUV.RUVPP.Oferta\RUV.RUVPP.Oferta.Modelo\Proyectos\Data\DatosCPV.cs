﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.Proyectos.Data
{
    public class DatosCPV
    {
        public int idCPVExterno { get; set; }
        public int idEmpresa { get; set; }
        public int idEmpresaInst { get; set; }
        public int idRuvAsis { get; set; }
        public string noRegistroRUV { get; set; }
        public string nombreRazonSocial { get; set; }
        public string representanteLegal { get; set; }
        public string lada { get; set; }
        public string numeroTelefono { get; set; }
        public string correoElectronico { get; set; }
        public string registroPatronal { get; set; }
        public string rfc { get; set; }
        public bool esVendedor { get; set; }
        public bool? esElMismo { get; set; }
        public bool mostrar { get; set; }
        public bool esActualizacion { get; set; }
        public bool esSeleccionado { get; set; }
    }
}
