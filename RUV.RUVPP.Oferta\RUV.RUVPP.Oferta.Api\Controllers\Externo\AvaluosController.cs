﻿using RUV.Comun.Utilerias;
using RUV.Comun.Web.Http;
using RUV.RUVPP.Entidades.Empresa;
using RUV.RUVPP.Entidades.General.Seguridad;
using RUV.RUVPP.Negocio.General.Seguridad;
using RUV.RUVPP.Oferta.Dominio.Avaluo;
using RUV.RUVPP.Oferta.Dominio.Oferta;
using RUV.RUVPP.Oferta.Dominio.SeguroCalidad;
using RUV.RUVPP.Oferta.Modelo;
using RUV.RUVPP.Oferta.Modelo.Avaluo;
using RUV.RUVPP.Oferta.Modelo.Comun;
using RUV.RUVPP.Oferta.Modelo.Oferta.Api;
using RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api;
using RUV.RUVPP.Oferta.SeguridadUsuarioAsis.WSC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;
using System.Web.Http.Description;

namespace RUV.RUVPP.Oferta.Api.Controllers.Externo
{
    [RoutePrefix("externo/api/avaluo")]
    public class AvaluosController : ApiControllerBase
    {
        private readonly IServicioOferta _servicioOferta;
        private readonly IServicioSeguridad _servicioSeguridad;
        private readonly IServicioSeguroCalidad _servicioSeguroCalidad;
        private readonly IServicioAvaluo _servicioAvaluo;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="servicioOferta"></param>
        /// <param name="servicioSeguridad"></param>
        /// <param name="servicioSeguroCalidad"></param>
        /// <param name="servicioAvaluo"></param>
        public AvaluosController(IServicioOferta servicioOferta, IServicioSeguridad servicioSeguridad, IServicioSeguroCalidad servicioSeguroCalidad , IServicioAvaluo servicioAvaluo)
            : base()
        {
            this._servicioOferta = servicioOferta;
            this._servicioSeguridad = servicioSeguridad;
            this._servicioSeguroCalidad = servicioSeguroCalidad;
            this._servicioAvaluo = servicioAvaluo;
        }

        #region metodos web

        /// <summary>
        /// 
        /// </summary>
        /// <param name="datosAvaluoSolicitud"></param>
        /// <returns></returns>
        [HttpPost, Route("datos-solicitud-avaluo")]
        [ResponseType(typeof(ResultadoPeticionAvaluo))]
        public async Task<ResultadoPeticionAvaluo> DatosSolicitudAvaluoAsyn(DatosAvaluoSolicitud datosAvaluoSolicitud)
        {
            ResultadoPeticionAvaluo dto = null;

            try
            {
                 dto = await this._servicioAvaluo.NotificacionDatosAvaluo(datosAvaluoSolicitud);
            }
            catch (Exception e)
            {
                dto.codigo = "02";
                dto.descripcion = "Los datos recibidos no son válidos.";
            }
            return dto;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="datosCancelacion"></param>
        /// <returns></returns>
        [HttpPost, Route("datos-cancelacion-avaluo")]
        [ResponseType(typeof(ResultadoPeticionAvaluo))]
        public async Task<ResultadoPeticionAvaluo> DatosCancelacionAvaluoAsyn(DatosCancelacion datosCancelacion)
        {

            ResultadoPeticionAvaluo dto = null;

            try
            {
                dto = await this._servicioAvaluo.CancelacionAvaluo(datosCancelacion);
            }
            catch (Exception e)
            {
                dto.codigo = "02";
                dto.descripcion = "Los datos recibidos no son válidos.";
            }
            return dto;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="datosReasignacion"></param>
        /// <returns></returns>
        [HttpPost, Route("datos-reasignacion-avaluo")]
        [ResponseType(typeof(ResultadoPeticionAvaluo))]
        public async Task<ResultadoPeticionAvaluo> DatosReasignacionAvaluoAsyn(DatosReasignacion datosReasignacion)
        {
            ResultadoPeticionAvaluo dto = null;

            try
            {
               dto = await this._servicioAvaluo.ReasignacionAvaluo(datosReasignacion);

            }
            catch (Exception e)
            {
                dto.codigo = "02";
                dto.descripcion = "Los datos recibidos no son válidos.";
            }

            return dto;

        }

      #endregion




        #region Servicios App

        ///<summary>
        ///Método inicio   sesión y consulta domicilio
        ///</summary>
        ///<param name="LoginUsuario">
        ///</param>
        [HttpPost, Route("iniciosesion-viviendas")]
        [ResponseType(typeof(JsonResponse))]
        public async Task<JsonResponse> InicioSesionConViviendas(LoginUsuario loginUsuario)
        {
            JsonResponse response = new JsonResponse();

            try
            {
                response = await this._servicioAvaluo.InicioSesionConViviendas(loginUsuario);
            }
            catch (Exception e)
            {
                response.code = 500;
                response.message = e.Message;
            }

            return response;

        }

        ///<summary>
        ///Guarda la dirección en  bitacora domicilio
        ///</summary>
        ///<param name="DomicilioAvaluo">
        ///</param>
        [HttpPost, Route("actualizacion-direccion-avaluo")]
        [ResponseType(typeof(JsonResponse))]
        public async Task<JsonResponse> actualizacionDireccionAvaluo(DomicilioAvaluo entity)
        {
            JsonResponse response = new JsonResponse();

            try
            {
                response = await this._servicioAvaluo.actualizacionDireccionAvaluo(entity);

            }
            catch (Exception e)
            {
                response.code = 500;
                response.message = e.Message;
            }

            return response;

        }

        ///<summary>
        ///Lista fotografias
        ///</summary>
        ///<param name="List<DocumentoFotoAvaluo> ">
        ///</param>
        [HttpPost, Route("envio-lista-fotos-avaluo")]
        [ResponseType(typeof(JsonResponse))]
        public async Task<JsonResponse> listaFotosAvaluo(List<DocumentoFotoAvaluo> datosAvaluoFotos)
        {
            JsonResponse response = new JsonResponse();
            try
            {
                response = await this._servicioAvaluo.listaFotosAvaluo(datosAvaluoFotos);
            }
            catch (Exception ex)
            {
                response.code = 500;
                response.message = ex.Message;
            }

            return response;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="subirFotos"></param>
        /// <returns></returns>

        #endregion


        [HttpPost, Route("envio-fotos-infonavit")]
        [ResponseType(typeof(JsonResponse))]
        public async Task<JsonResponse> envioFotosInfonavit(string cuv,string id_usuario)
        {
            JsonResponse response = new JsonResponse();
            try
            {
                response = await this._servicioAvaluo.envioFotosInfonavit( cuv, id_usuario);
            }
            catch (Exception ex)
            {
                response.code = 500;
                response.message = ex.Message;
            }

            return response;
        }







        #region metodos anteriores
        [HttpPost, Route("obtener-viviendasavaluos-x-unidadvaluacion")]
        [ResponseType(typeof(JsonResponse))]
        public async Task<JsonResponse> ObtenerViviendasAvaluosXUnidadValuacion(FiltroAvaluo filtroAvaluo)
        {
            JsonResponse response = new JsonResponse();

            try
            {
                response = await this._servicioAvaluo.ObtenerViviendasAvaluosXUnidadValuacion(filtroAvaluo);
            }
            catch (Exception e)
            {
                response.code = 500;
                response.message = e.Message;
            }

            return response;

        }
        [HttpPost, Route("iniciosesion-viviendas-x-estado")]
        [ResponseType(typeof(JsonResponse))]
        public async Task<JsonResponse> InicioSesionConViviendasXEstado(LoginUsuario loginUsuario)
        {
            JsonResponse response = new JsonResponse();

            try
            {
                response = await this._servicioAvaluo.InicioSesionConViviendasXEstado(loginUsuario);
            }
            catch (Exception e)
            {
                response.code = 500;
                response.message = e.Message;
            }

            return response;

        }
        [HttpPost, Route("datos-fotos-avaluo")]
        [ResponseType(typeof(JsonResponse))]
        public async Task<JsonResponse> SubirFotos(DocumentoFotoAvaluo datosAvaluoFotos)
        {
            JsonResponse response = new JsonResponse();

            try
            {
                var listEntity = await this._servicioAvaluo.FotoAvaluo(datosAvaluoFotos);

                response.code = (int)HttpStatusCode.OK;

                response.model = listEntity;
            }
            catch (Exception e)
            {
                response.code = 500;
                response.message = e.Message;
            }

            return response;

        }
        [HttpPost, Route("filtro-estado-avaluo")]
        [ResponseType(typeof(JsonResponse))]
        public async Task<JsonResponse> filtroEstadoAvaluo(FiltroAvaluo filtroAvaluo)
        {
            JsonResponse response = new JsonResponse();

            try
            {
                var listEntity = await this._servicioAvaluo.filtroAvaluo(filtroAvaluo);

                response.code = (int)HttpStatusCode.OK;

                response.model = listEntity;
            }
            catch (Exception e)
            {
                response.code = 500;
                response.message = e.Message;
            }

            return response;

        }
        [HttpPost, Route("envio-fotos-avaluo")]
        [ResponseType(typeof(JsonResponse))]
        public async Task<JsonResponse> envioFotosAvaluo(DocumentoFotoAvaluo datosAvaluoFotos)
        {
            JsonResponse response = new JsonResponse();
            try
            {
                response = await this._servicioAvaluo.envioFotosAvaluo(datosAvaluoFotos);
            }
            catch (Exception ex)
            {
                response.code = 500;
                response.message = ex.Message;
            }

            return response;
        }


        #endregion

    }



}
