﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.Oferta.Api
{
    public class ServicioDto
    {
        public short? idServicio { get; set; }
        public string clave { get; set; }
        public string nombre { get; set; }
        public short? idProducto { get; set; }
        public byte? idPrioridad { get; set; }
        public DateTime? fechaRegistro { get; set; }
        public DateTime? fechaActualizacion { get; set; }
        public bool? activo { get; set; }
        public bool? usadaEnODT { get; set; }
        public bool? usoMenu { get; set; }
        public bool? link { get; set; }
        public string urlValidacion { get; set; }
        public string urlConfirmacion { get; set; }
        public string claveSAP { get; set; }
    }
}
