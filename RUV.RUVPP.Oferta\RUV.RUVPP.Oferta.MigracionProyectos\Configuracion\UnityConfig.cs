﻿using Microsoft.Practices.Unity;
using RUV.Comun.Datos.Mapper;
using RUV.Comun.Utilerias;
using RUV.RUVPP.Oferta.Datos.Empresa;
using RUV.RUVPP.Oferta.Datos.Empresa.Implementacion;
using RUV.RUVPP.Oferta.Datos.Oferta;
using RUV.RUVPP.Oferta.Datos.Oferta.Implementacion;
using RUV.RUVPP.Oferta.Datos.Plano;
using RUV.RUVPP.Oferta.Datos.Plano.Implementacion;
using RUV.RUVPP.Oferta.Datos.Proyectos;
using RUV.RUVPP.Oferta.Datos.Proyectos.Implementacion;
using System.Configuration;

namespace RUV.RUVPP.Oferta.MigracionProyectos.Configuracion
{
    public static class UnityConfig
    {
        public const string CadenaConexionOferta = "OfertaConnection";

        public const string CadenaConexionOracle = "OracleConnection";

        public static IUnityContainer ConfigureContainer()
        {
            var container = new UnityContainer();

            var numeroReintentos = int.Parse(ConfigurationManager.AppSettings["RUV.RUVPP.RetryTimes"]);
            container.RegisterType<GestorConexiones>(new PerResolveLifetimeManager());        
            container.RegisterType<AyudanteReintentos>(new HierarchicalLifetimeManager(), new InjectionConstructorRelaxed(numeroReintentos));
            //Proyectos
            container.RegisterType<ICatalogosProyectosDataMapper, CatalogosProyectosDataMapper>(new InjectionConstructorRelaxed(CadenaConexionOferta));
            container.RegisterType<IProyectosDataMapper, ProyectosDataMapper>(new InjectionConstructorRelaxed(CadenaConexionOferta));
            container.RegisterType<IEmpresaDataMapper, EmpresaDataMapper>(new InjectionConstructorRelaxed(CadenaConexionOferta));
            //container.RegisterType<IServicioProyectos, ServicioProyectos>(new InjectionConstructorRelaxed(new ResolvedParameter<IServicioOrdenTrabajo>("odtProyectosAlta"), new ResolvedParameter<IServicioOrdenTrabajo>("odtProyectosActualizacion")));
            //container.RegisterType<IServicioProyectosCatalogos, ServicioProyectosCatalogos>();

            //Oferta
            container.RegisterType<IOfertaDataMapper, OfertaDataMapper>(new InjectionConstructorRelaxed(CadenaConexionOferta));
                     
            //Plano
            container.RegisterType<IPlanoDataMapper, PlanoDataMapper>(new InjectionConstructorRelaxed(CadenaConexionOracle));
            




            return container;
        }
    }
}