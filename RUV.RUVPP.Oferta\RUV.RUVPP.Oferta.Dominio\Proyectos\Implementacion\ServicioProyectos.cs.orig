﻿using Microsoft.ApplicationInsights;
using RUV.RUVPP.Entidades.Comun.Enums;
using RUV.RUVPP.Entidades.Empresa;
using RUV.RUVPP.Entidades.General.Notificaciones;
using RUV.RUVPP.Entidades.General.Notificaciones.Enum;
using RUV.RUVPP.Negocio.Empresa.Comun;
using RUV.RUVPP.Negocio.Empresa.EmpresaConsulta;
using RUV.RUVPP.Negocio.General.Notificaciones.Interfaces;
using RUV.RUVPP.Negocio.General.OrdenTrabajo.Comun;
using RUV.RUVPP.Negocio.General.OrdenTrabajo.Servicios;
using RUV.Comun.Negocio;
using RUV.RUVPP.Oferta.Comun.Modelo;
using RUV.Comun.Utilerias;
using RUV.RUVPP.Oferta.Datos.Plano;
using RUV.RUVPP.Oferta.Datos.Proyectos;
using RUV.RUVPP.Oferta.Dominio.Dictaminacion;
using RUV.RUVPP.Oferta.Dominio.Historico;
using RUV.RUVPP.Oferta.GeneralesInterop.Documentos;
using RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Dictaminacion;
using RUV.RUVPP.Oferta.Modelo.Dictaminacion;
using RUV.RUVPP.Oferta.Modelo.Historico.Data;
using RUV.RUVPP.Oferta.Modelo.Proyectos;
using RUV.RUVPP.Oferta.Modelo.Proyectos.Api;
using RUV.RUVPP.Oferta.Modelo.Proyectos.Data;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Transactions;
using System.Web;
using ProyectoConfiguracion = RUV.RUVPP.Oferta.GeneralesInterop.Modelo.ConfiguracionSeccion;
<<<<<<< 75f022b7804b5fd92f8e29511fe70d1283ea1499
using RUV.RUVPP.Oferta.Datos.Oferta;
=======
using RUV.RUVPP.Oferta.Modelo.Mediciones;
>>>>>>> Se agrega consulta de cuvs a capa de datos y dominio.

//using RUV.RUVPP.Negocio.Empresa.EmpresaConsulta;

namespace RUV.RUVPP.Oferta.Dominio.Proyectos.Implementacion
{
    public class ServicioProyectos : ServicioDominioBase, IServicioProyectos
    {
        #region Constantes

        private const string AsuntoCorreoRegistroFinalizado = "Registro del proyecto \"{0}\" finalizado.";
        private const string AsuntoCorreoProyectoAceptado = "Proyecto \"{0}\" aceptado.";
        private const string AsuntoCorreoProyectoRechazado = "Proyecto \"{0}\" rechazado.";

        #endregion Constantes

        #region Campos

        private readonly IProyectosDataMapper _proyectoDM;
        private readonly IPlanoDataMapper _planoDM;
        private readonly IOfertaDataMapper _ofertaDataMapper;
        private readonly IServicioProyectosCatalogos _servicioProyectosCatalogos;
        private readonly Empresa.IServicioEmpresa _servicioEmpresa;
        private readonly IServicioOrdenTrabajo _servicioOrdenTrabajoAlta;
        private readonly IServicioOrdenTrabajo _servicioOrdenTrabajoActualizacion;
        private readonly INotificacion<MensajeDto> _servicioNotificaciones;
        private readonly IServicioEmpresaConsulta _servicioEmpresaConsulta;
        private readonly IServicioEmpresaComun _servicioEmpresaComun;
        private readonly IServicioDictaminacion _servicioDictaminacion;
        private readonly IServicioHistoricos _servicioHistorico;

        private int validarSig = Convert.ToInt32(ConfigurationManager.AppSettings["validarSig"]);
        #endregion Campos

        #region Constructor

        public ServicioProyectos(TelemetryClient clienteTelemetria, IProyectosDataMapper proyectoDM, IOfertaDataMapper ofertaDM, 
                                IPlanoDataMapper planoDM, Empresa.IServicioEmpresa servicioEmpresa,
                                IServicioProyectosCatalogos servicioProyectosCatalogos, IServicioOrdenTrabajo servicioOrdenTrabajoAlta,
                                IServicioOrdenTrabajo servicioOrdenTrabajoActualizacion, INotificacion<MensajeDto> servicioNotificaciones,
                                IServicioEmpresaConsulta servicioEmpresaConsulta, IServicioEmpresaComun servicioEmpresaComun,
                                IServicioDictaminacion servicioDictaminacion,
                                IServicioHistoricos servicioHistorico)
           : base(clienteTelemetria)
        {
            this._proyectoDM = proyectoDM;
            this._ofertaDataMapper = ofertaDM;
            this._planoDM = planoDM;
            this._servicioProyectosCatalogos = servicioProyectosCatalogos;
            this._servicioEmpresa = servicioEmpresa;
            this._servicioOrdenTrabajoAlta = servicioOrdenTrabajoAlta;
            this._servicioOrdenTrabajoActualizacion = servicioOrdenTrabajoActualizacion;
            this._servicioNotificaciones = servicioNotificaciones;
            this._servicioEmpresaConsulta = servicioEmpresaConsulta;
            this._servicioEmpresaComun = servicioEmpresaComun;
            this._servicioDictaminacion = servicioDictaminacion;
            this._servicioHistorico = servicioHistorico;
        }

        #endregion Constructor

        #region Metodos

        #region implementacion IServicioProyecto

        public async Task<int> ExisteProyectoPorNombreAsync(string nombre, int idEmpresa, int? idProyecto)
        {
            return await this._proyectoDM.ExisteProyectoPorNombreAsync(nombre, idEmpresa, idProyecto);
        }

        public async Task<bool> EliminarDocumentoProyecto(int idEmpresa, int idDocumento)
        {
            return await this._proyectoDM.EliminarDocumentoxProyectoAsync(idEmpresa, idDocumento);
        }

        public async Task<bool> EliminarProyectoAsync(int idProyecto)
        {
            var result = false;
            var proyecto = await this._proyectoDM.ObtenerProyectoAsync(idProyecto);

            if (proyecto.idEstatusProyecto == (int)EstatusProyecto.Registro)
                result = await this._proyectoDM.EliminarProyectoAsync(idProyecto);
            else if (proyecto.idEstatusProyecto == (int)EstatusProyecto.Rechazada)
                result = await this._proyectoDM.ActualizarProyectoLogicoAsync(idProyecto);

            return result;
        }

        public async Task<bool> EliminarProyectoParcialAsync(int idProyecto)
        {
            return await this._proyectoDM.EliminarProyectoParcialAsync(idProyecto);
        }

        public async Task<int> GuardarProyectoAsync(int idEmpresa, string claveEmpresa, string nombre, string proyecto)
        {
            var options = new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted, Timeout = TimeSpan.FromMinutes(2) };
            using (var scope = new TransactionScope(TransactionScopeOption.Required, options, TransactionScopeAsyncFlowOption.Enabled))
            {
                int idProyecto = await this._proyectoDM.GuardarProyectoAsync(idEmpresa, nombre, proyecto);
                using (var innerScope = new TransactionScope(TransactionScopeOption.RequiresNew, options))
                {
                    bool esCreadoOracle = await this._planoDM.GuardarProyectoTemporal(idProyecto, idEmpresa, claveEmpresa, nombre, "Registro", "Se crea Proyecto");
                    innerScope.Complete();
                }
                scope.Complete();
                return idProyecto;
            }
        }

        public async Task<int> GuardarProyectoDetalleAsync(Proyecto proyecto)
        {
            int result = 0;
            //using (var scope = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
            var options = new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted, Timeout = TimeSpan.FromMinutes(2) };

            using (var scope = new TransactionScope(TransactionScopeOption.Required, options, TransactionScopeAsyncFlowOption.Enabled))
            {
                proyecto.idPromotor = proyecto.idPromotor == 0 ? null : proyecto.idPromotor;
                proyecto.idVendedor = proyecto.idVendedor == 0 ? null : proyecto.idVendedor;
                await this._proyectoDM.GuardarProyectoDetalleAsync(proyecto);

                if (proyecto.idPromotor == null)
                {
                    proyecto.promotor.idCPVExterno = await this._proyectoDM.GuardarPromotorVendedorAsync(proyecto.promotor);
                    await this._proyectoDM.GuardarPromotorVendedorxProyectoAsync(proyecto.promotor.idCPVExterno, proyecto.idProyecto);
                }

                if (proyecto.idVendedor == null)
                {
                    proyecto.vendedor.idCPVExterno = await this._proyectoDM.GuardarPromotorVendedorAsync(proyecto.vendedor);
                    await this._proyectoDM.GuardarPromotorVendedorxProyectoAsync(proyecto.vendedor.idCPVExterno, proyecto.idProyecto);
                }

                await this._proyectoDM.GuardarDocumentoxProyectoAsync(proyecto.plano.IdDocumento, proyecto.idProyecto);

                proyecto.propietarioTerreno.idPropietarioTerreno = await this._proyectoDM.GuardarPropietarioTerrenoAsync(proyecto.propietarioTerreno);
                await this._proyectoDM.GuardarPropietarioTerrenoxProyectoAsync(proyecto.propietarioTerreno.idPropietarioTerreno, proyecto.idProyecto);

                proyecto.dro.idDRO = await this._proyectoDM.GuardarDROAsync(proyecto.dro);
                await this._proyectoDM.GuardarDROxProyectoAsync(proyecto.dro.idDRO, proyecto.idProyecto);

                if (proyecto.esZonaRiesgo)
                {
                    await this._proyectoDM.GuardarZonasRiesgoAsync(proyecto.zonasRiesgo.Where(z => z.esSeleccionada).ToArray(), proyecto.idProyecto);

                    if (proyecto.dictamenRiesgo.IdDocumento != 0)
                    {
                        await this._proyectoDM.GuardarDocumentoxProyectoAsync(proyecto.dictamenRiesgo.IdDocumento, proyecto.idProyecto);
                    }
                }

                foreach (var vivienda in proyecto.sembrado.viviendas)
                {
                    vivienda.domicilioGeografico.idLocalidad = "0000";
                    vivienda.domicilioGeografico.idPeriodo = 4;
                    vivienda.domicilioGeografico.idAsentamiento = "340000001";
                    vivienda.domicilioGeografico.idVialidad1 = 269761;
                    vivienda.domicilioGeografico.idVialidad2 = 269761;
                    vivienda.domicilioGeografico.idVialidad3 = 269761;
                    vivienda.domicilioGeografico.nombreTipoVialidadPrincipal = "CALLE";
                    vivienda.domicilioGeografico.nombreTipoVialidad1 = "CALLE";
                    vivienda.domicilioGeografico.nombreTipoVialidad2 = "CALLE";
                    vivienda.domicilioGeografico.nombreTipoVialidad3 = "CALLE";
                    vivienda.domicilioGeografico.idVialidadPrincipal = 269761;
                    vivienda.domicilioGeografico.idTipoDomicilioRUV = 2;
                    vivienda.domicilioGeografico.xmlSIG = "";

                    var idDomicilioGeografico = await this._proyectoDM.GuardarDomicilioGeograficoAsync(vivienda.domicilioGeografico);

                    vivienda.idDomicilioGeografico = idDomicilioGeografico;
                    vivienda.idEstatusVivienda = 1;
                    vivienda.idProyecto = proyecto.idProyecto;

                    await this._proyectoDM.GuardarViviendaAsync(vivienda);

                    vivienda.domicilioCarreteraCamino.idDomicilioGeografico = idDomicilioGeografico;
                    vivienda.domicilioCarreteraCamino.nombreVialidad = vivienda.domicilioGeografico.nombreVialidadPrincipal;

                    if (vivienda.domicilioGeografico.tipoVialidadP == 100)
                    {
                        await this._proyectoDM.GuardarDomicilioCarretera(vivienda.domicilioCarreteraCamino);
                    }
                    else if (vivienda.domicilioGeografico.tipoVialidadP == 101)
                    {
                        await this._proyectoDM.GuardarDomicilioCamino(vivienda.domicilioCarreteraCamino);
                    }

                    //Guardado de historico para la vivienda
                    /*EstatusViviendas estatusVivienda = new EstatusViviendas();
                    estatusVivienda.estatusCUV = DescriptionHelperExtension.ToDescription(EstatusVivienda.Inactiva);
                    estatusVivienda.identificadorVivienda = vivienda.identificadorVivienda;
                    estatusVivienda.idEvento = (int)Evento.RegistroProyecto;
                    List<CampoAdicional> listaCamposAdicionales = new List<CampoAdicional>();
                    CampoAdicional campoAdicional = new CampoAdicional() { idCampoAdicional = (int)EnumCampoAdicional.IdProyecto };
                    listaCamposAdicionales.Add(campoAdicional);
                    await this._servicioHistorico.GuardarHistoricoVivienda(estatusVivienda);*/
                }
                using (var innerScope = new TransactionScope(TransactionScopeOption.RequiresNew, options))
                {
                    string mensajeError = string.Empty;
                    foreach (var vivienda in proyecto.sembrado.viviendas)
                    {
                        await this._planoDM.ActualizarViviendasSembradoTemporal(vivienda, proyecto.folioSEPLADE, proyecto.folioAyto);
                    }

                    if (validarSig == 1)
                    {
                        string resultadoMigracion = await this._planoDM.MigracionTemporalOficial(proyecto.idProyecto);
                        if (!string.IsNullOrEmpty(resultadoMigracion))
                        {

                            this._clienteTelemetria?.TrackException(new Exception(mensajeError));
                            throw new Exception(mensajeError);
                        }
                    }
                    innerScope.Complete();
                }

                result = proyecto.idProyecto;
                scope.Complete();
            }
            if (result > 0)
            {
                OrdenBase odtCreada = this._servicioOrdenTrabajoAlta.Crear(proyecto.idProyecto, proyecto.nombre);
                odtCreada.PorAsignar();
                await ActualizarEstatusProyectoAsync(proyecto.idProyecto, (int)EstatusProyecto.Validación);
                await ClienteHttpRuv.Post("48f0b866-8f87-4749-8e9f-c1ed46eb96ed", "Tableros/AgregarOrden", odtCreada.ToOrdenServicioDto());
                await this.EnviarCorreoOdtGenerada(72, proyecto, odtCreada);
            }

            return result;
        }

        public async Task<int> ActualizarProyectoAsync(int idProyecto, string nombre, string proyecto)
        {
            await this._proyectoDM.ActualizarProyectoAsync(idProyecto, nombre, proyecto);

            return idProyecto;
        }

        public async Task<int> ActualizarProyectoDetalleAsync(Proyecto proyecto)
        {
            int result = 0;

            using (var scope = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
            {
                proyecto.idPromotor = proyecto.idPromotor == 0 ? null : proyecto.idPromotor;
                proyecto.idVendedor = proyecto.idVendedor == 0 ? null : proyecto.idVendedor;
                var proyectoActual = await ObtenerProyectoAsync(proyecto.idProyecto);

                await this._proyectoDM.ActualizarProyectoAsync(proyecto.idProyecto, proyecto.nombre, string.Empty);
                await this._proyectoDM.ActualizarProyectoDetalleAsync(proyecto);

                if (proyecto.idPromotor == null)
                {
                    if (proyectoActual.idPromotor > 0)
                    {
                        var idCPV = await this._proyectoDM.GuardarPromotorVendedorAsync(proyecto.promotor);
                        await this._proyectoDM.GuardarPromotorVendedorxProyectoAsync(idCPV, proyecto.idProyecto);
                    }
                    else
                    {
                        proyecto.promotor.idCPVExterno = proyectoActual.promotor.idCPVExterno;
                        await this._proyectoDM.ActualizarPromotorVendedorAsync(proyecto.promotor);
                    }
                }
                if (proyecto.idPromotor > 0 && proyectoActual.idPromotor == null)
                {
                    await this._proyectoDM.EliminarPromotorExternoxProyecto(proyecto.idProyecto, proyectoActual.promotor.idCPVExterno);
                }

                if (proyecto.idVendedor == null)
                {
                    if (proyectoActual.idVendedor > 0)
                    {
                        var idCPV = await this._proyectoDM.GuardarPromotorVendedorAsync(proyecto.vendedor);
                        await this._proyectoDM.GuardarPromotorVendedorxProyectoAsync(idCPV, proyecto.idProyecto);
                    }
                    else
                    {
                        proyecto.vendedor.idCPVExterno = proyectoActual.vendedor.idCPVExterno;
                        await this._proyectoDM.ActualizarPromotorVendedorAsync(proyecto.vendedor);
                    }
                }
                if (proyecto.idVendedor > 0 && proyectoActual.idVendedor == null)
                {
                    await this._proyectoDM.EliminarPromotorExternoxProyecto(proyecto.idProyecto, proyectoActual.vendedor.idCPVExterno);
                }

                if (proyectoActual.plano.IdDocumento != proyecto.plano.IdDocumento)
                {
                    await this._proyectoDM.EliminarDocumentoxProyectoAsync(proyectoActual.plano.IdDocumento, proyecto.idProyecto);
                    await this._proyectoDM.GuardarDocumentoxProyectoAsync(proyecto.plano.IdDocumento, proyecto.idProyecto);
                }

                await this._proyectoDM.ActualizarPropietarioTerrenoAsync(proyecto.propietarioTerreno);

                await this._proyectoDM.ActualizarDROAsync(proyecto.dro);

                if (proyecto.esZonaRiesgo)
                {
                    await this._proyectoDM.EliminarZonaRiesgoAsync(proyecto.zonasRiesgo.Where(z => !z.esSeleccionada && z.esActualizacion).ToArray(), proyecto.idProyecto);
                    await this._proyectoDM.GuardarZonasRiesgoAsync(proyecto.zonasRiesgo.Where(z => z.esSeleccionada && !z.esActualizacion).ToArray(), proyecto.idProyecto);
                    await this._proyectoDM.ActualizarZonasRiesgoAsync(proyecto.zonasRiesgo.Where(z => z.esSeleccionada && z.esActualizacion).ToArray(), proyecto.idProyecto);

                    if (proyectoActual.dictamenRiesgo != null && proyectoActual.dictamenRiesgo.IdDocumento != 0)
                    {
                        if (proyecto.dictamenRiesgo.IdDocumento != 0)
                        {
                            if (proyectoActual.dictamenRiesgo.IdDocumento != proyecto.dictamenRiesgo.IdDocumento)
                            {
                                await this._proyectoDM.EliminarDocumentoxProyectoAsync(proyectoActual.dictamenRiesgo.IdDocumento, proyecto.idProyecto);
                                await this._proyectoDM.GuardarDocumentoxProyectoAsync(proyecto.dictamenRiesgo.IdDocumento, proyecto.idProyecto);
                            }
                        }
                        else
                        {
                            await this._proyectoDM.EliminarDocumentoxProyectoAsync(proyectoActual.dictamenRiesgo.IdDocumento, proyecto.idProyecto);
                        }
                    }
                    else
                    {
                        if (proyecto.dictamenRiesgo.IdDocumento != 0)
                        {
                            await this._proyectoDM.GuardarDocumentoxProyectoAsync(proyecto.dictamenRiesgo.IdDocumento, proyecto.idProyecto);
                        }
                    }
                }
                else
                {
                    await this._proyectoDM.EliminarZonaRiesgoAsync(proyecto.zonasRiesgo.ToArray(), proyecto.idProyecto);
                    if (proyectoActual.dictamenRiesgo != null)
                        await this._proyectoDM.EliminarDocumentoxProyectoAsync(proyectoActual.dictamenRiesgo.IdDocumento, proyecto.idProyecto);
                }

                //Actualizacion de viviendas sembrado
                var viviendasActuales = proyectoActual.sembrado.viviendas;
                var viviendasModificadas = proyecto.sembrado.viviendas;

                var viviendasNuevas = viviendasModificadas.Except(viviendasActuales, new ComparadorViviendas()).ToList();
                var viviendasEliminadas = viviendasActuales.Except(viviendasModificadas, new ComparadorViviendas()).ToList();
                var viviendasIguales = viviendasModificadas.Intersect(viviendasActuales, new ComparadorViviendas()).ToList();


                //Agregar nuevas viviendas
                if (viviendasNuevas != null && viviendasNuevas.Any())
                {
                    foreach (var viviendaNueva in viviendasNuevas)
                    {
                        viviendaNueva.domicilioGeografico.idLocalidad = "0000";
                        viviendaNueva.domicilioGeografico.idPeriodo = 4;
                        viviendaNueva.domicilioGeografico.idAsentamiento = "340000001";
                        viviendaNueva.domicilioGeografico.idVialidad1 = 269761;
                        viviendaNueva.domicilioGeografico.idVialidad2 = 269761;
                        viviendaNueva.domicilioGeografico.idVialidad3 = 269761;
                        viviendaNueva.domicilioGeografico.nombreTipoVialidadPrincipal = "CALLE";
                        viviendaNueva.domicilioGeografico.nombreTipoVialidad1 = "CALLE";
                        viviendaNueva.domicilioGeografico.nombreTipoVialidad2 = "CALLE";
                        viviendaNueva.domicilioGeografico.nombreTipoVialidad3 = "CALLE";
                        viviendaNueva.domicilioGeografico.idVialidadPrincipal = 269761;
                        viviendaNueva.domicilioGeografico.idTipoDomicilioRUV = 2;
                        viviendaNueva.domicilioGeografico.xmlSIG = "";

                        var idDomicilioGeografico = await this._proyectoDM.GuardarDomicilioGeograficoAsync(viviendaNueva.domicilioGeografico);

                        viviendaNueva.idDomicilioGeografico = idDomicilioGeografico;
                        viviendaNueva.idEstatusVivienda = 1;
                        viviendaNueva.idProyecto = proyecto.idProyecto;

                        await this._proyectoDM.GuardarViviendaAsync(viviendaNueva);

                        viviendaNueva.domicilioCarreteraCamino.idDomicilioGeografico = idDomicilioGeografico;
                        viviendaNueva.domicilioCarreteraCamino.nombreVialidad = viviendaNueva.domicilioGeografico.nombreVialidadPrincipal;

                        if (viviendaNueva.domicilioGeografico.tipoVialidadP == 100)
                        {
                            await this._proyectoDM.GuardarDomicilioCarretera(viviendaNueva.domicilioCarreteraCamino);
                        }
                        else if (viviendaNueva.domicilioGeografico.tipoVialidadP == 101)
                        {
                            await this._proyectoDM.GuardarDomicilioCamino(viviendaNueva.domicilioCarreteraCamino);
                        }
                    }
                }

                //Actualizar viviendas existentes

                if (viviendasIguales != null && viviendasIguales.Any())
                {

                    foreach (var viviendaModificada in viviendasModificadas)
                    {
                        await this._proyectoDM.ActualizarViviendaAsync(viviendaModificada);

                        await this._proyectoDM.ActualizarDomiciioGeograficoAsync(viviendaModificada.domicilioGeografico);

                        if (viviendasActuales.Where(v => v.idVivienda == viviendaModificada.idVivienda).FirstOrDefault().domicilioGeografico.tipoVialidadP == 100)
                        {
                            if (viviendaModificada.domicilioGeografico.tipoVialidadP == 100)
                            {
                                await this._proyectoDM.ActualizarDomicilioCarretera(viviendaModificada.domicilioCarreteraCamino);
                            }
                            else
                            {
                                await this._proyectoDM.EliminarDomicilioCarretera(viviendaModificada.idDomicilioGeografico);
                            }

                            if (viviendaModificada.domicilioGeografico.tipoVialidadP == 101)
                            {
                                //await this._proyectoDM.EliminarDomicilioCamino(vivienda.A.idDomicilioGeografico);

                                viviendaModificada.domicilioCarreteraCamino.idDomicilioGeografico = viviendaModificada.idDomicilioGeografico;
                                viviendaModificada.domicilioCarreteraCamino.nombreVialidad = viviendaModificada.domicilioGeografico.nombreVialidadPrincipal;
                                await this._proyectoDM.GuardarDomicilioCamino(viviendaModificada.domicilioCarreteraCamino);
                            }
                        }
                        else if (viviendasActuales.Where(v => v.idVivienda == viviendaModificada.idVivienda).FirstOrDefault().domicilioGeografico.tipoVialidadP == 101)
                        {
                            if (viviendaModificada.domicilioGeografico.tipoVialidadP == 101)
                            {
                                await this._proyectoDM.ActualizarDomicilioCamino(viviendaModificada.domicilioCarreteraCamino);
                            }
                            else
                            {
                                await this._proyectoDM.EliminarDomicilioCamino(viviendaModificada.idDomicilioGeografico);
                            }

                            if (viviendaModificada.domicilioGeografico.tipoVialidadP == 100)
                            {
                                //await this._proyectoDM.EliminarDomicilioCarretera(vivienda.A.idDomicilioGeografico);

                                viviendaModificada.domicilioCarreteraCamino.idDomicilioGeografico = viviendaModificada.idDomicilioGeografico;
                                viviendaModificada.domicilioCarreteraCamino.nombreVialidad = viviendaModificada.domicilioGeografico.nombreVialidadPrincipal;
                                await this._proyectoDM.GuardarDomicilioCarretera(viviendaModificada.domicilioCarreteraCamino);
                            }
                        }
                        else
                        {
                            if (viviendaModificada.domicilioGeografico.tipoVialidadP == 100)
                            {
                                viviendaModificada.domicilioCarreteraCamino.idDomicilioGeografico = viviendaModificada.idDomicilioGeografico;
                                viviendaModificada.domicilioCarreteraCamino.nombreVialidad = viviendaModificada.domicilioGeografico.nombreVialidadPrincipal;
                                await this._proyectoDM.GuardarDomicilioCarretera(viviendaModificada.domicilioCarreteraCamino);
                            }
                            else if (viviendaModificada.domicilioGeografico.tipoVialidadP == 101)
                            {
                                viviendaModificada.domicilioCarreteraCamino.idDomicilioGeografico = viviendaModificada.idDomicilioGeografico;
                                viviendaModificada.domicilioCarreteraCamino.nombreVialidad = viviendaModificada.domicilioGeografico.nombreVialidadPrincipal;
                                await this._proyectoDM.GuardarDomicilioCamino(viviendaModificada.domicilioCarreteraCamino);
                            }
                        }
                    }

                }

                result = proyecto.idProyecto;

                scope.Complete();
            }

            if (result > 0)
            {
                OrdenBase odtCreada = null;

                if (proyecto.idEstatusProyecto == (int)EstatusProyecto.Rechazada)
                {
                    odtCreada = this._servicioOrdenTrabajoAlta.Crear(proyecto.idProyecto, proyecto.nombre);
                    await ActualizarEstatusProyectoAsync(proyecto.idProyecto, (int)EstatusProyecto.Validación);
                }
                else
                {
                    odtCreada = this._servicioOrdenTrabajoActualizacion.Crear(proyecto.idProyecto, proyecto.nombre);
                    await ActualizarEstatusProyectoAsync(proyecto.idProyecto, (int)EstatusProyecto.ValidaciónPorActualización);
                }

                odtCreada.PorAsignar();
                await ClienteHttpRuv.Post("48f0b866-8f87-4749-8e9f-c1ed46eb96ed", "Tableros/AgregarOrden", odtCreada.ToOrdenServicioDto());
                await this.EnviarCorreoOdtGenerada(72, proyecto, odtCreada);
            }

            return result;
        }

        /// <summary>
        /// Actualiza el estatus de un proyecto
        /// </summary>
        /// <param name="idProyecto">Identificador del proyecto</param>
        /// <param name="idEstatus">Identificador del estatus</param>
        /// <returns></returns>
        public async Task<bool> ActualizarEstatusProyectoAsync(int idProyecto, int idEstatus)
        {
            return await this._proyectoDM.ActualizarEstatusProyectoAsync(idProyecto, idEstatus);
        }

        public async Task<ResultadoPaginado<List<Proyecto>>> ObtenerProyectosAsync(int idEmpresa, int tamanioPagina, int pagina, int? idProyecto, string nombreProyecto, int? idEntidadFederativa, int? idMunicipio, int? idLocalidad)
        {
            ResultadoPaginado<List<Proyecto>> resultado = new ResultadoPaginado<List<Proyecto>>()
            {
                TamanioPagina = tamanioPagina,
                PaginaActual = pagina
            };

            var data = await this._proyectoDM.ObtenerProyectosAsync(idEmpresa, tamanioPagina, pagina, idProyecto, nombreProyecto, idEntidadFederativa, idMunicipio, idLocalidad);

            if (data.Item1 > 0)
            {
                resultado.TotalRegistros = data.Item1;
                resultado.TotalPaginas = (int)Math.Ceiling((double)(resultado.TotalRegistros / tamanioPagina));
            }

            resultado.Resultado = data.Item2;

            return resultado;
        }

        public async Task<Proyecto> ObtenerProyectoAsync(int idProyecto)
        {
            List<int> idsTMP;

            Proyecto proyecto = await this._proyectoDM.ObtenerProyectoAsync(idProyecto);
            Proyecto proyectoDetalle = await this._proyectoDM.ObtenerProyectoDetalleAsync(idProyecto) ?? new Proyecto();
            proyectoDetalle.idProyecto = proyecto.idProyecto;
            proyectoDetalle.idEmpresa = proyecto.idEmpresa;
            proyectoDetalle.idEstatusProyecto = proyecto.idEstatusProyecto;
            proyectoDetalle.estatusProyecto = proyecto.estatusProyecto;
            proyectoDetalle.nombre = proyecto.nombre;
            proyectoDetalle.temporalJSON = proyecto.temporalJSON;
            proyectoDetalle.plano = await this._proyectoDM.ObtenerDocumentoxProyecto(idProyecto, TiposDocumento.Plano) ?? new DocumentoRuv();

            proyectoDetalle.constructor = new DatosCPV();
            proyectoDetalle.vendedor = new DatosCPV();
            proyectoDetalle.promotor = new DatosCPV();

            if (proyectoDetalle.idConstructor != proyectoDetalle.idEmpresa)
                proyectoDetalle.constructor = await this._servicioEmpresa.ConsultaEmpresaPorNRRUV(proyectoDetalle.idConstructor, null);

            idsTMP = await this._proyectoDM.ObtenerPromotorVendedorxProyecto(idProyecto);

            if (proyectoDetalle.idPromotor == null || proyectoDetalle.idVendedor == null)
            {
                foreach (var idPV in idsTMP)
                {
                    var pvTMP = await this._proyectoDM.ObtenerPromotorVendedor(idPV);
                    pvTMP.mostrar = true;
                    pvTMP.esActualizacion = true;

                    if (pvTMP.esVendedor)
                        proyectoDetalle.vendedor = pvTMP;
                    else
                        proyectoDetalle.promotor = pvTMP;
                }
            }

            if (proyectoDetalle.idConstructor == proyectoDetalle.idEmpresa)
                proyectoDetalle.constructor.esElMismo = true;

            if (proyectoDetalle.idPromotor == proyectoDetalle.idEmpresa)
                proyectoDetalle.promotor.esElMismo = true;

            if (proyectoDetalle.idVendedor == proyectoDetalle.idEmpresa)
                proyectoDetalle.vendedor.esElMismo = true;

            idsTMP = await this._proyectoDM.ObtenerPropietarioTerrenoxProyecto(idProyecto);
            proyectoDetalle.propietarioTerreno = await this._proyectoDM.ObtenerPropietarioTerreno(idsTMP.FirstOrDefault());

            idsTMP = await this._proyectoDM.ObtenerDROxProyecto(idProyecto);
            proyectoDetalle.dro = await this._proyectoDM.ObtenerDRO(idsTMP.FirstOrDefault());

            if (proyectoDetalle.dro != null)
            {
                proyectoDetalle.dro.identificacionOficial = await this._proyectoDM.ObtenerDocumentosDRO(proyectoDetalle.dro.idDRO, TiposDocumento.IdentificacionOficialDRO);
                proyectoDetalle.dro.licencia = await this._proyectoDM.ObtenerDocumentosDRO(proyectoDetalle.dro.idDRO, TiposDocumento.LicenciaDRO);
            }
            proyectoDetalle.zonasRiesgo = await this._servicioProyectosCatalogos.ObtenerZonasDeRiesgoAsync();
            List<ZonaRiesgo> zonasGuardadas = await this._proyectoDM.ObtenerZonasRiesgo(idProyecto);
            foreach (var zona in proyectoDetalle.zonasRiesgo)
            {
                var datosZona = zonasGuardadas.Where(z => z.idRiesgoOferta == zona.idRiesgoOferta);
                if (datosZona.ToList().Count > 0)
                {
                    proyectoDetalle.esZonaRiesgo = true;
                    zona.solucionMitigarRiesgo = datosZona.FirstOrDefault().solucionMitigarRiesgo;
                    zona.esSeleccionada = true;
                    zona.esActualizacion = true;
                }
            }

            var dictamenRiesgo = await (this._proyectoDM.ObtenerDocumentoxProyecto(idProyecto, TiposDocumento.DictamenRiesgoProyecto));
            proyectoDetalle.dictamenRiesgo = dictamenRiesgo ?? new DocumentoRuv();

            proyectoDetalle.sembrado = await this._proyectoDM.ObtenerSembradoxProyecto(idProyecto);

            return proyectoDetalle;
        }

        public async Task<Proyecto> ObtenerProyectoDetalleAsync(int idProyecto)
        {
            var proyectoDetalle = await this._proyectoDM.ObtenerProyectoDetalleAsync(idProyecto);

            return proyectoDetalle;
        }

        public async Task<List<Proyecto>> ObtenerProyectosPorIdEmpresaAsync(int idEmpresa)
        {

            List<Proyecto> listaProyecto = await this._proyectoDM.ObtenerProyectosPorIdEmpresaAsync(idEmpresa);

            return listaProyecto;

        }

        public async Task<List<Vivienda>> ObtenerViveindasPorProyectoAsync(int idProyecto)
        {
            var viviendasProyecto = await this._proyectoDM.ObtenerViviendasPorProyectoAsync(idProyecto);

            return viviendasProyecto;
        }

        public async Task<bool> ActualizarViviendasAsync(Sembrado sembrado)
        {
            var resultado = false;
            var proyectoActual = await ObtenerProyectoAsync(sembrado.viviendas[0].idProyecto);
            
            using (var scope = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
            {
                foreach (var vivienda in sembrado.viviendas)
                {
                    await this._proyectoDM.ActualizarViviendaAsync(vivienda);

                    await this._proyectoDM.ActualizarDomiciioGeograficoAsync(vivienda.domicilioGeografico);

                    if (proyectoActual.sembrado.viviendas.Where(v => v.idVivienda == vivienda.idVivienda).FirstOrDefault().domicilioGeografico.tipoVialidadP == 100)
                    {
                        if (vivienda.domicilioGeografico.tipoVialidadP == 100)
                        {
                            await this._proyectoDM.ActualizarDomicilioCarretera(vivienda.domicilioCarreteraCamino);
                        }
                        else
                        {
                            await this._proyectoDM.EliminarDomicilioCarretera(vivienda.idDomicilioGeografico);
                        }

                        if (vivienda.domicilioGeografico.tipoVialidadP == 101)
                        {
                            //await this._proyectoDM.EliminarDomicilioCamino(vivienda.A.idDomicilioGeografico);

                            vivienda.domicilioCarreteraCamino.idDomicilioGeografico = vivienda.idDomicilioGeografico;
                            vivienda.domicilioCarreteraCamino.nombreVialidad = vivienda.domicilioGeografico.nombreVialidadPrincipal;
                            await this._proyectoDM.GuardarDomicilioCamino(vivienda.domicilioCarreteraCamino);
                        }
                    }
                    else if (proyectoActual.sembrado.viviendas.Where(v => v.idVivienda == vivienda.idVivienda).FirstOrDefault().domicilioGeografico.tipoVialidadP == 101)
                    {
                        if (vivienda.domicilioGeografico.tipoVialidadP == 101)
                        {
                            await this._proyectoDM.ActualizarDomicilioCamino(vivienda.domicilioCarreteraCamino);
                        }
                        else
                        {
                            await this._proyectoDM.EliminarDomicilioCamino(vivienda.idDomicilioGeografico);
                        }

                        if (vivienda.domicilioGeografico.tipoVialidadP == 100)
                        {
                            //await this._proyectoDM.EliminarDomicilioCarretera(vivienda.A.idDomicilioGeografico);

                            vivienda.domicilioCarreteraCamino.idDomicilioGeografico = vivienda.idDomicilioGeografico;
                            vivienda.domicilioCarreteraCamino.nombreVialidad = vivienda.domicilioGeografico.nombreVialidadPrincipal;
                            await this._proyectoDM.GuardarDomicilioCarretera(vivienda.domicilioCarreteraCamino);
                        }
                    }
                    else
                    {
                        if (vivienda.domicilioGeografico.tipoVialidadP == 100)
                        {
                            vivienda.domicilioCarreteraCamino.idDomicilioGeografico = vivienda.idDomicilioGeografico;
                            vivienda.domicilioCarreteraCamino.nombreVialidad = vivienda.domicilioGeografico.nombreVialidadPrincipal;
                            await this._proyectoDM.GuardarDomicilioCarretera(vivienda.domicilioCarreteraCamino);
                        }
                        else if (vivienda.domicilioGeografico.tipoVialidadP == 101)
                        {
                            vivienda.domicilioCarreteraCamino.idDomicilioGeografico = vivienda.idDomicilioGeografico;
                            vivienda.domicilioCarreteraCamino.nombreVialidad = vivienda.domicilioGeografico.nombreVialidadPrincipal;
                            await this._proyectoDM.GuardarDomicilioCamino(vivienda.domicilioCarreteraCamino);
                        }
                    }
                }

                resultado = true;
                scope.Complete();

            
                return resultado;
            }
        }


        #endregion implementacion IServicioProyecto

            #region EnvioDictaminacion

            /// <summary>
            /// Guarda/Actualiza la dictaminación, actualiza el estatus del proyecto y atiende la ODT.
            /// </summary>
            /// <param name="idOrdenTrabajo">Identificador de la orden de trabajo</param>
            /// <param name="idServicio">Identificador del servicio</param>
            /// <param name="idRegistro">Identificador del proyecto</param>
            /// <param name="parametrosDictaminacion">Objeto que contiene los siguientes parametros:
            /// seActualizaDictaminacion - FALSE si se inserta la dictaminacion, TRUE si se actualiza
            /// DictaminacionJSON -  cadena que contiene el JSON de dictaminacion</param>
            /// aceptacion - TRUE indica que se acepta la oferta, FALSE que se rechaza la oferta<returns></returns>
            /// <returns></returns>
        public async Task<bool> EnviarDictaminacionProyecto(int idOrdenTrabajo, short idServicio, int idRegistro, ParametroBodyDictaminacion parametrosDictaminacion)
        {
            var result = false;
            var dictaminacionGuardada = false;
            var estatusActualizado = false;
            var proyecto = new Proyecto();

            proyecto = await this.ObtenerProyectoAsync(idRegistro);

            using (var scope = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
            {
                //Se guarda o actualiza la dictaminacion
                if (parametrosDictaminacion.seActualizaDictaminacion)
                    dictaminacionGuardada = await this._servicioDictaminacion.ActualizarDictaminacionAsync(idServicio, idOrdenTrabajo, idRegistro, parametrosDictaminacion.DictaminacionJSON);
                else
                    dictaminacionGuardada = await this._servicioDictaminacion.GuardarDictaminacionAsync(idServicio, idOrdenTrabajo, idRegistro, parametrosDictaminacion.DictaminacionJSON);
                //Se actualiza el estatus del proyecto
                if (dictaminacionGuardada)
                    if (proyecto.idEstatusProyecto == (int)EstatusProyecto.ValidaciónPorActualización)
                    {
                        estatusActualizado = await this.ActualizarEstatusProyectoAsync(idRegistro, parametrosDictaminacion.aceptacion ? (int)EstatusProyecto.Aceptada : (int)EstatusProyecto.RechazadaPorActualización);
                    }
                    else
                    {
                        estatusActualizado = await this.ActualizarEstatusProyectoAsync(idRegistro, parametrosDictaminacion.aceptacion ? (int)EstatusProyecto.Aceptada : (int)EstatusProyecto.Rechazada);
                        await this._ofertaDataMapper.ActualizarEstatusViviendaPoProyectoAsync(idRegistro, (int)EstatusVivienda.SinValidar);
                    }
                //Se atiende la ODT
                if (estatusActualizado)
                {
                    if (parametrosDictaminacion.aceptacion)
                    {
                        this._servicioOrdenTrabajoAlta.Atender(idOrdenTrabajo, true);
                        await this.EnviarCorreoProyectoAceptado(proyecto);
                    }
                    else
                    {
                        this._servicioOrdenTrabajoAlta.Atender(idOrdenTrabajo, false);
                        await this.EnviarCorreoProyectoRechazado(proyecto, idOrdenTrabajo);
                    }

                    result = true;
                    scope.Complete();
                }
            }
            return result;
        }

        #endregion EnvioDictaminacion

        #region Notificaciones

        /// <summary>
        /// Envia un correo electronico informando envio de datos a VyD
        /// </summary>
        /// <param name="idEmpresa">idEmpresa que registra el proyecto</param>
        /// <param name="proyecto">Proyecto a dictaminar</param>
        /// <param name="odt">Orden de trabajo generada</param>
        /// /// <returns></returns>
        public async Task EnviarCorreoOdtGenerada(int idEmpresa, Proyecto proyecto, OrdenBase odt)
        {
            MensajeDto mensaje = null;
            EmpresaDto empresa = null;
            List<string> correo = new List<string>();

            correo = this._servicioEmpresaComun.ObtenerCorreosContacto(idEmpresa).ToList();
            empresa = this._servicioEmpresaConsulta.ObtenerEmpresaDto(idEmpresa);

            Dictionary<string, string> parametrosCorreo = new Dictionary<string, string>
                {
                    {"0", proyecto.nombre },
                    {"1", String.Format("{0} {1} {2}", empresa.nombreRazonSocial, empresa.apellidoPaterno, empresa.apellidoMaterno)},
                    {"2", odt.clave}
                };

            mensaje = new MensajeDto(String.Format(AsuntoCorreoRegistroFinalizado, proyecto.nombre),
                correo,
                eTipoCorreo.ContenidoHtmlMailNuevaOdtProyectos,
                eTipoMensaje.InformativoComunicacion,
                null,
                parametrosCorreo);

            _servicioNotificaciones.AgregarNotificacion(mensaje);
        }

        /// <summary>
        /// Envia un correo electronico informando dictaminacion aceptada
        /// </summary>
        /// <param name="proyecto">Proyecto dictaminado</param>
        /// <returns></returns>
        public async Task EnviarCorreoProyectoAceptado(Proyecto proyecto)
        {
            MensajeDto mensaje = null;
            EmpresaDto empresa = null;
            List<string> correo = new List<string>();

            correo = this._servicioEmpresaComun.ObtenerCorreosContacto(proyecto.idEmpresa).ToList();
            empresa = this._servicioEmpresaConsulta.ObtenerEmpresaDto(proyecto.idEmpresa);

            Dictionary<string, string> parametrosCorreo = new Dictionary<string, string>
                {
                    {"0", proyecto.nombre },
                    {"1", DateTime.Now.ToShortDateString() }
                };

            mensaje = new MensajeDto(String.Format(AsuntoCorreoProyectoAceptado, proyecto.nombre),
                correo,
                eTipoCorreo.ContenidoHtmlMailAceptadoProyectos,
                eTipoMensaje.InformativoComunicacion,
                null,
                parametrosCorreo);

            _servicioNotificaciones.AgregarNotificacion(mensaje);
        }

        /// <summary>
        /// Envia un correo electronico informando dictaminacion rechazada
        /// </summary>
        /// <param name="proyecto">Proyecto dictaminado</param>
        /// <param name="idOrdenTrabajo">Orden de trabajo generada</param>
        /// <returns></returns>
        public async Task EnviarCorreoProyectoRechazado(Proyecto proyecto, int idOrdenTrabajo)
        {
            MensajeDto mensaje = null;
            EmpresaDto empresa = null;
            List<string> correo = new List<string>();
            string motivosRechazoCorreo = "";

            DictaminacionContenedor motivosRechazo = await this._servicioDictaminacion.ObtenerUltimaDictaminacionOAnteriorAsync((int)ServicioProducto.AltaUbicacion, idOrdenTrabajo, proyecto.idProyecto);
            motivosRechazoCorreo = await this.ObtenerMotivosRechazoCorreo(motivosRechazo);

            correo = this._servicioEmpresaComun.ObtenerCorreosContacto(proyecto.idEmpresa).ToList();

            empresa = this._servicioEmpresaConsulta.ObtenerEmpresaDto(proyecto.idEmpresa);

            Dictionary<string, string> parametrosCorreo = new Dictionary<string, string>
                {
                    {"0", proyecto.nombre },
                    {"1", String.Format("{0} {1} {2}", empresa.nombreRazonSocial, empresa.apellidoPaterno, empresa.apellidoMaterno)},
                    {"2", motivosRechazoCorreo }
                };

            mensaje = new MensajeDto(String.Format(AsuntoCorreoProyectoRechazado, proyecto.nombre),
                correo,
                eTipoCorreo.ContenidoHtmlMailRechazadoProyectos,
                eTipoMensaje.InformativoComunicacion,
                null,
                parametrosCorreo);

            _servicioNotificaciones.AgregarNotificacion(mensaje);
        }

        #endregion Notificaciones

        #region Metodos de ayuda

        /// <summary>
        /// Obtiene los motivos de rechazo del proyecto
        /// </summary>
        /// <param name="motivosRechazo">json de dictaminacion</param>
        /// <returns>Cadena con el codigo html generado</returns>
        private async Task<string> ObtenerMotivosRechazoCorreo(DictaminacionContenedor motivosRechazo)
        {
            string configuracionSecciones;

            string rutaConfiguracion = HttpContext.Current.Server.MapPath("~/App_Data/proyectos-configuracion-secciones.json");

            //Deserializa el json de dictaminaciones
            DictaminacionContenedor dictaminaciones = motivosRechazo;

            using (StreamReader reader = new StreamReader(rutaConfiguracion))
                configuracionSecciones = await reader.ReadToEndAsync();

            //Deserializa el json de configuracion de secciones
            ProyectoConfiguracion.ConfiguracionSeccion configuracionSeccion = Newtonsoft.Json.JsonConvert.DeserializeObject<ProyectoConfiguracion.ConfiguracionSeccion>(configuracionSecciones);

            //Se obtienen todas las secciones y controles del json de configuracion.
            List<ProyectoConfiguracion.Seccion> seccionesFinales = ObtenerSecciones(configuracionSeccion.secciones, new List<ProyectoConfiguracion.Seccion>());
            List<ProyectoConfiguracion.Elemento> elementos = ObtenerElementos(configuracionSeccion.secciones, new List<ProyectoConfiguracion.Elemento>());

            List<MotivoRechazo> motivosRechazoCorreo = await ConstruirListaMotivosRechazo(seccionesFinales, elementos, dictaminaciones.secciones, new List<MotivoRechazo>());

            string motivosHtml = await GenerarListaHtmlMotivosRechazo(motivosRechazoCorreo);

            return motivosHtml;
        }

        /// <summary>
        /// Genera una lista de tipo MotivosRechazo con la informacion obtenida del json de dictaminaciones
        /// </summary>
        /// <param name="seccionesFinales"></param>
        /// <param name="elementos">Total de elementos contenidos en las secciones obtenidos del json de configuracion</param>
        /// <param name="seccionesDictaminacion">Total de secciones contenidos en el json de ocnfiguracion</param>
        /// <param name="listaMotivosRechazo"></param>
        /// <returns>Lista de motivos rechazo</returns>
        private async Task<List<MotivoRechazo>> ConstruirListaMotivosRechazo(List<ProyectoConfiguracion.Seccion> seccionesFinales, List<ProyectoConfiguracion.Elemento> elementos, List<DictaminacionSeccion> seccionesDictaminacion, List<MotivoRechazo> listaMotivosRechazo)
        {
            seccionesDictaminacion.ForEach(async s =>
            {
                if (s.mensajeRechazo != null && s.mensajeRechazo.Length > 0)
                {
                    listaMotivosRechazo.Add(new MotivoRechazo()
                    {
                        consecutivo = listaMotivosRechazo.Count + 1,
                        secciones = seccionesFinales.Where(sf => sf.idSeccion == s.idSeccion).FirstOrDefault().nombre.ToString(),
                        elemento = null,
                        nombreElemento = null,
                        indice = null,
                        mensajeRechazo = s.mensajeRechazo,
                        idSeccion = s.idSeccion,
                        estActualizado = s._actualizaEstatusPanel != null ? s._actualizaEstatusPanel : false,
                        estaActivo = false
                    });
                }
                if (s.elementos != null && s.elementos.Count > 0)
                {
                    s.elementos.ForEach(c =>
                    {
                        if (c.mensajeRechazo != null && c.mensajeRechazo.Length > 0)
                        {
                            listaMotivosRechazo.Add(new MotivoRechazo()
                            {
                                consecutivo = listaMotivosRechazo.Count + 1,
                                secciones = seccionesFinales.Where(sf => sf.idSeccion == s.idSeccion).FirstOrDefault().nombre.ToString(),
                                elemento = c.idElemento,
                                nombreElemento = elementos.Where(e => e.idElemento == c.idElemento).FirstOrDefault().nombre.ToString(),
                                indice = c.indice,
                                mensajeRechazo = c.mensajeRechazo,
                                idSeccion = s.idSeccion,
                                estActualizado = c._actualizaEstatusPanel != null ? c._actualizaEstatusPanel : false,
                                estaActivo = false
                            });
                        }
                    });
                }

                if (s.secciones != null && s.secciones.Count > 0)
                {
                    await ConstruirListaMotivosRechazo(seccionesFinales, elementos, s.secciones, listaMotivosRechazo);
                }
            });

            return listaMotivosRechazo;
        }

        /// <summary>
        /// Genera una cadena que contiene el codigo html para generar la tabla de motivos de rechazo en el correo
        /// </summary>
        /// <param name="motivosRechazo">Lista de motivos de rechazo a renderizar</param>
        /// <returns>Cadena con el codigo html generado</returns>
        private async Task<string> GenerarListaHtmlMotivosRechazo(List<MotivoRechazo> motivosRechazo)
        {
            StringBuilder html = new StringBuilder();

            if (motivosRechazo != null && motivosRechazo.Any())
            {
                html.Append(@"<table>
                            <thead>
                                <tr><th> No.</th><th> Sección </th><th> Campo </th><th> Motivo </th></tr>
                            </thead>
                            <tbody class='modal-body'>");

                foreach (MotivoRechazo motivo in motivosRechazo)
                {
                    html.Append(@"<tr><td>");
                    html.Append(motivo.consecutivo);
                    html.Append("</td><td>");
                    html.Append(motivo.secciones != null ? motivo.secciones : "");
                    html.Append("</td><td>");
                    html.Append(motivo.nombreElemento != null ? motivo.elemento : "");
                    html.Append("</td><td>");
                    html.Append(motivo.mensajeRechazo != null ? motivo.mensajeRechazo : "");
                    html.Append("</td><td>");
                }

                html.Append("</tbody></table>");
            }
            else
                html.Append("Sin motivos de rechazo");

            return html.ToString();
        }

        /// <summary>
        /// Obtiene el total de secciones cpntenidos ene le json de configuración
        /// </summary>
        /// <param name="seccion">Lista de secciones que contienen a su vez otras secciones</param>
        /// <param name="seccionesFinales">Lista de secciones finales</param>
        /// <returns>Lista con el total de secciones</returns>
        private List<ProyectoConfiguracion.Seccion> ObtenerSecciones(List<ProyectoConfiguracion.Seccion> seccion, List<ProyectoConfiguracion.Seccion> seccionesFinales)
        {
            seccion.ForEach(s =>
            {
                if (s.secciones.Any())
                {
                    ObtenerSecciones(s.secciones, seccionesFinales);
                }
                else
                {
                    seccionesFinales.Add(s);
                }
            });

            return seccionesFinales;
        }

        /// <summary>
        /// Obtiene el total de controles contenidos en el json de configuracion
        /// </summary>
        /// <param name="seccion">Lista de secciones que contienen los contoles</param>
        /// <param name="elementos">Lista de elementos a obtener</param>
        /// <returns>Lista con el total de controles de cada seccion</returns>
        private List<ProyectoConfiguracion.Elemento> ObtenerElementos(List<ProyectoConfiguracion.Seccion> seccion, List<ProyectoConfiguracion.Elemento> elementos)
        {
            seccion.ForEach(s =>
            {
                if (s.secciones.Any())
                {
                    ObtenerElementos(s.secciones, elementos);
                }
                else
                {
                    if (s.elementos != null)
                    {
                        s.elementos.ForEach(e =>
                        {
                            elementos.Add(e);
                        });
                    }
                }
            });

            return elementos;
        }

        public async Task<int> CargarArchivoSDFAsync(int idArchivoSDF, int idUsuario, int idProyecto)
        {
            string esquemaSDF = ConfigurationManager.AppSettings["schema"];
            string usuarioOracle = ConfigurationManager.AppSettings["usuario"];
            string passwordOracle = ConfigurationManager.AppSettings["password"];
            string urlServicioOracle = ConfigurationManager.AppSettings["servicio"];
            string esquemaOracle = ConfigurationManager.AppSettings["oracleSchema"];
            string mensaje = string.Empty;
            int codigoRespuestaEjecutable = -1;
            string rutaEsquema = HttpContext.Current.Server.MapPath("~/App_Data/Esquemas/RegistroProyectoSchema.xml");
            string rutaArchivo = await this.ObtenerRutaArchivoSDF(idArchivoSDF);
            idUsuario = 13;

            Process procesoCargaSDF = new Process();
            string rutaExe = HttpContext.Current.Server.MapPath("~/App_Data/FDO/CargaPlano_SFDToOracle.exe");
            DirectoryInfo directory = new DirectoryInfo(Path.GetFullPath(Path.Combine(System.Web.HttpRuntime.AppDomainAppPath, rutaExe)));
            procesoCargaSDF.StartInfo.FileName = directory.ToString();
            procesoCargaSDF.StartInfo.Arguments = string.Format("\"{0}\" \"{1}\" \"{2}\" \"{3}\" \"{4}\" \"{5}\" \"{6}\" \"{7}\" \"{8}\" ", rutaArchivo, rutaEsquema, idUsuario, idProyecto, esquemaSDF, usuarioOracle, passwordOracle, urlServicioOracle, esquemaOracle);
            procesoCargaSDF.StartInfo.WindowStyle = ProcessWindowStyle.Hidden;
            procesoCargaSDF.StartInfo.UseShellExecute = false;
            procesoCargaSDF.StartInfo.RedirectStandardOutput = true;
            procesoCargaSDF.Start();
            mensaje = await procesoCargaSDF.StandardOutput.ReadToEndAsync();
            procesoCargaSDF.WaitForExit();
            codigoRespuestaEjecutable = procesoCargaSDF.ExitCode;
            procesoCargaSDF.Close();

            if (!string.IsNullOrEmpty(mensaje))
            {
                this._clienteTelemetria?.TrackException(new Exception(mensaje));
            }

            if (validarSig == 1)
            {
                if (codigoRespuestaEjecutable == 0)
                {
                    string validacionEspacial = await this._planoDM.ValidacionProyecto(idProyecto);
                    if (!string.IsNullOrEmpty(validacionEspacial))
                    {
                        this._clienteTelemetria?.TrackException(new Exception(validacionEspacial));
                        codigoRespuestaEjecutable = -2;

                    }
                }
            }
            return codigoRespuestaEjecutable;
        }

        private async Task<string> ObtenerRutaArchivoSDF(int idArchivoSDF)
        {
            string rutaArchivoBlob = string.Empty;
            string rutaArchivoTemporal = string.Empty;
            ServicioDocumentoOferta servicioDocumento = new ServicioDocumentoOferta();
            var archivos = servicioDocumento.Obtener(new DocumentoOferta() { idDocumento = idArchivoSDF });
            rutaArchivoBlob = archivos.First().rutaArchivo;
            rutaArchivoTemporal = HttpContext.Current.Server.MapPath("~/App_Data/SDF_Temporal/" + archivos.First().nombreArchivo);
            using (HttpClient cliente = new HttpClient())
            {
                using (Stream response = await cliente.GetStreamAsync(rutaArchivoBlob))
                {
                    using (FileStream file = new FileStream(rutaArchivoTemporal, FileMode.Create))
                    {
                        response.CopyTo(file);
                    }
                }
            }
            return rutaArchivoTemporal;
        }

        public async Task<Sembrado> ObtenerSembradoTemporalAsync(int idProyectoTemporal)
        {
            Sembrado listaViviendasSembrado = new Sembrado();
            listaViviendasSembrado = await _planoDM.ObtenerSembradoTemporal(idProyectoTemporal);
            return listaViviendasSembrado;
        }

        #endregion Metodos de ayuda

        #region CUVs

        /// <summary>
        /// Obtiene el resultado de la consulta de CUVS filtrada desde la BD del RUV++
        /// </summary>
        /// <param name="filtros">Objeto que contiene los siguientes filtros: IdProyecto, IdOferta, Cuv, IdEstatusJuridicoVivienda, IdEntidadFederativa, NombreFrente, NumeroRegistroRuv</param>        
        public async Task<Dictionary<string, ConsultaCuv>> ObtenerCuvsFiltradasAsync(FiltrosConsultaCuv filtros)
        {
            var result = await this._proyectoDM.ObtenerCuvsFiltradasAsync(filtros);
            return result;
        }

        #endregion

        #endregion Metodos

        #region Metodos sobreescritos

        protected override void DisposeManagedResources()
        {
            this._planoDM.Dispose();
            this._proyectoDM.Dispose();
            this._servicioDictaminacion.Dispose();
            this._servicioEmpresa.Dispose();
            this._servicioProyectosCatalogos.Dispose();
        }

        #endregion Metodos sobreescritos


        #region Metodos Consulta

        /// <summary>
        /// Obtiene el listado de ordenes de trabajo para un proyecto por idProyecto paginado
        /// </summary>
        /// <param name="idProyecto">Identificador del proyecto</param>
        /// <param name="tamanioPagina">Tamaño de la pagina</param>
        /// <param name="pagina">Pagina</param>
        /// <returns></returns>
        public async Task<ResultadoPaginado<List<OrdenTrabajoHistoricoValidacion>>> ObtenerOrdenesTrabajoPorProyectoPaginadoAsync(int idProyecto, int tamanioPagina, int pagina)
        {
            ResultadoPaginado<List<OrdenTrabajoHistoricoValidacion>> resultado = new ResultadoPaginado<List<OrdenTrabajoHistoricoValidacion>>()
            {
                TamanioPagina = tamanioPagina,
                PaginaActual = pagina
            };

            var data = await this._proyectoDM.ObtenerOrdenesTrabajoPorProyectoPaginadoAsync(idProyecto, tamanioPagina, pagina);

            if (data.Item1 > 0)
            {
                resultado.TotalRegistros = data.Item1;
                resultado.TotalPaginas = (int)Math.Ceiling((double)(resultado.TotalRegistros / tamanioPagina));
            }

            resultado.Resultado = data.Item2;

            return resultado;
        }

        public async Task<ResultadoPaginado<List<Proyecto>>> ObtenerProyectosPorIdEmpresaConPaginadorAsync(int tamanioPagina, int pagina, int idEmpresa)
        {
            ResultadoPaginado<List<Proyecto>> resultado = new ResultadoPaginado<List<Proyecto>>()
            {
                TamanioPagina = tamanioPagina,
                PaginaActual = pagina
            };

            var data = await this._proyectoDM.ObtenerProyectosPorIdEmpresaConPaginadorAsync(tamanioPagina, pagina, idEmpresa);

            if (data.Item1 > 0)
            {
                resultado.TotalRegistros = data.Item1;
                resultado.TotalPaginas = (int)Math.Ceiling((double)(resultado.TotalRegistros / tamanioPagina));
            }

            resultado.Resultado = data.Item2;

            return resultado;            

        }

        /// <summary>
        /// Obtiene el listado de viviendas para un proyecto por idProyecto paginado
        /// </summary>
        /// <param name="idProyecto">Identificador del proyecto</param>
        /// <param name="tamanioPagina">Tamaño de la pagina</param>
        /// <param name="pagina">Pagina</param>
        /// <returns></returns>
        public async Task<ResultadoPaginado<List<Vivienda>>> ObtenerViviendasPorProyectoPaginadoAsync(int idProyecto, int tamanioPagina, int pagina)
        {
            ResultadoPaginado<List<Vivienda>> resultado = new ResultadoPaginado<List<Vivienda>>()
            {
                TamanioPagina = tamanioPagina,
                PaginaActual = pagina
            };

            var data = await this._proyectoDM.ObtenerVivienasPorProyectoPaginadoAsync(idProyecto, tamanioPagina, pagina);

            if (data.Item1 > 0)
            {
                resultado.TotalRegistros = data.Item1;
                resultado.TotalPaginas = (int)Math.Ceiling((double)(resultado.TotalRegistros / tamanioPagina));
            }

            resultado.Resultado = data.Item2;

            return resultado;
        }


        #endregion

    }
}