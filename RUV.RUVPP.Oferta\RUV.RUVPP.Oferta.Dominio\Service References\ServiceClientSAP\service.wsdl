<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:tns="http://localhost" xmlns:s="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tm="http://microsoft.com/wsdl/mime/textMatching/" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" targetNamespace="http://localhost" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <s:schema elementFormDefault="qualified" targetNamespace="http://localhost">
      <s:element name="WSConsultaOrdenPago">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Usuario" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Pass" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sAcreedor" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sDocumento" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sReferencia" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="WSConsultaOrdenPagoResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="WSConsultaOrdenPagoResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="WSRegistraPago">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sUsuario" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sPass" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sDesarrollador" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sAcreedor" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sReferencia" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sConcepto" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sMoneda" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="dImporte" type="s:decimal" />
            <s:element minOccurs="0" maxOccurs="1" name="sNoPago" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sEjercicio" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sClabe" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="WSRegistraPagoResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="WSRegistraPagoResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="string" nillable="true" type="s:string" />
    </s:schema>
  </wsdl:types>
  <wsdl:message name="WSConsultaOrdenPagoSoapIn">
    <wsdl:part name="parameters" element="tns:WSConsultaOrdenPago" />
  </wsdl:message>
  <wsdl:message name="WSConsultaOrdenPagoSoapOut">
    <wsdl:part name="parameters" element="tns:WSConsultaOrdenPagoResponse" />
  </wsdl:message>
  <wsdl:message name="WSRegistraPagoSoapIn">
    <wsdl:part name="parameters" element="tns:WSRegistraPago" />
  </wsdl:message>
  <wsdl:message name="WSRegistraPagoSoapOut">
    <wsdl:part name="parameters" element="tns:WSRegistraPagoResponse" />
  </wsdl:message>
  <wsdl:message name="WSConsultaOrdenPagoHttpGetIn">
    <wsdl:part name="Usuario" type="s:string" />
    <wsdl:part name="Pass" type="s:string" />
    <wsdl:part name="sAcreedor" type="s:string" />
    <wsdl:part name="sDocumento" type="s:string" />
    <wsdl:part name="sReferencia" type="s:string" />
  </wsdl:message>
  <wsdl:message name="WSConsultaOrdenPagoHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="WSRegistraPagoHttpGetIn">
    <wsdl:part name="sUsuario" type="s:string" />
    <wsdl:part name="sPass" type="s:string" />
    <wsdl:part name="sDesarrollador" type="s:string" />
    <wsdl:part name="sAcreedor" type="s:string" />
    <wsdl:part name="sReferencia" type="s:string" />
    <wsdl:part name="sConcepto" type="s:string" />
    <wsdl:part name="sMoneda" type="s:string" />
    <wsdl:part name="dImporte" type="s:string" />
    <wsdl:part name="sNoPago" type="s:string" />
    <wsdl:part name="sEjercicio" type="s:string" />
    <wsdl:part name="sClabe" type="s:string" />
  </wsdl:message>
  <wsdl:message name="WSRegistraPagoHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="WSConsultaOrdenPagoHttpPostIn">
    <wsdl:part name="Usuario" type="s:string" />
    <wsdl:part name="Pass" type="s:string" />
    <wsdl:part name="sAcreedor" type="s:string" />
    <wsdl:part name="sDocumento" type="s:string" />
    <wsdl:part name="sReferencia" type="s:string" />
  </wsdl:message>
  <wsdl:message name="WSConsultaOrdenPagoHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="WSRegistraPagoHttpPostIn">
    <wsdl:part name="sUsuario" type="s:string" />
    <wsdl:part name="sPass" type="s:string" />
    <wsdl:part name="sDesarrollador" type="s:string" />
    <wsdl:part name="sAcreedor" type="s:string" />
    <wsdl:part name="sReferencia" type="s:string" />
    <wsdl:part name="sConcepto" type="s:string" />
    <wsdl:part name="sMoneda" type="s:string" />
    <wsdl:part name="dImporte" type="s:string" />
    <wsdl:part name="sNoPago" type="s:string" />
    <wsdl:part name="sEjercicio" type="s:string" />
    <wsdl:part name="sClabe" type="s:string" />
  </wsdl:message>
  <wsdl:message name="WSRegistraPagoHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:portType name="ServiceSoap">
    <wsdl:operation name="WSConsultaOrdenPago">
      <wsdl:input message="tns:WSConsultaOrdenPagoSoapIn" />
      <wsdl:output message="tns:WSConsultaOrdenPagoSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="WSRegistraPago">
      <wsdl:input message="tns:WSRegistraPagoSoapIn" />
      <wsdl:output message="tns:WSRegistraPagoSoapOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:portType name="ServiceHttpGet">
    <wsdl:operation name="WSConsultaOrdenPago">
      <wsdl:input message="tns:WSConsultaOrdenPagoHttpGetIn" />
      <wsdl:output message="tns:WSConsultaOrdenPagoHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="WSRegistraPago">
      <wsdl:input message="tns:WSRegistraPagoHttpGetIn" />
      <wsdl:output message="tns:WSRegistraPagoHttpGetOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:portType name="ServiceHttpPost">
    <wsdl:operation name="WSConsultaOrdenPago">
      <wsdl:input message="tns:WSConsultaOrdenPagoHttpPostIn" />
      <wsdl:output message="tns:WSConsultaOrdenPagoHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="WSRegistraPago">
      <wsdl:input message="tns:WSRegistraPagoHttpPostIn" />
      <wsdl:output message="tns:WSRegistraPagoHttpPostOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="ServiceSoap" type="tns:ServiceSoap">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="WSConsultaOrdenPago">
      <soap:operation soapAction="http://localhost/WSConsultaOrdenPago" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="WSRegistraPago">
      <soap:operation soapAction="http://localhost/WSRegistraPago" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="ServiceSoap12" type="tns:ServiceSoap">
    <soap12:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="WSConsultaOrdenPago">
      <soap12:operation soapAction="http://localhost/WSConsultaOrdenPago" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="WSRegistraPago">
      <soap12:operation soapAction="http://localhost/WSRegistraPago" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="ServiceHttpGet" type="tns:ServiceHttpGet">
    <http:binding verb="GET" />
    <wsdl:operation name="WSConsultaOrdenPago">
      <http:operation location="/WSConsultaOrdenPago" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="WSRegistraPago">
      <http:operation location="/WSRegistraPago" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="ServiceHttpPost" type="tns:ServiceHttpPost">
    <http:binding verb="POST" />
    <wsdl:operation name="WSConsultaOrdenPago">
      <http:operation location="/WSConsultaOrdenPago" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="WSRegistraPago">
      <http:operation location="/WSRegistraPago" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="Service">
    <wsdl:port name="ServiceSoap" binding="tns:ServiceSoap">
      <soap:address location="http://sap-dev02-sd/WSOrdenPago/service.asmx" />
    </wsdl:port>
    <wsdl:port name="ServiceSoap12" binding="tns:ServiceSoap12">
      <soap12:address location="http://sap-dev02-sd/WSOrdenPago/service.asmx" />
    </wsdl:port>
    <wsdl:port name="ServiceHttpGet" binding="tns:ServiceHttpGet">
      <http:address location="http://sap-dev02-sd/WSOrdenPago/service.asmx" />
    </wsdl:port>
    <wsdl:port name="ServiceHttpPost" binding="tns:ServiceHttpPost">
      <http:address location="http://sap-dev02-sd/WSOrdenPago/service.asmx" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>