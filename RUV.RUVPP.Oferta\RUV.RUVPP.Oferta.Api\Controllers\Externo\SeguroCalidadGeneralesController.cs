﻿using RUV.Comun.Utilerias;
using RUV.Comun.Web.Http;
using RUV.RUVPP.Generales.Dominio.Generales;
using RUV.RUVPP.Generales.Modelo.Generales;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;
using System.Web.Http.Description;

namespace RUV.RUVPP.Oferta.Api.Controllers.Externo
{
    [RoutePrefix("externo/api/seguro-calidad-generales")]
    public class SeguroCalidadGeneralesController : ApiControllerBase
    {

        #region Campos

        private readonly IServicioGenerales _servicioGenerales;

        #endregion

        #region Constructor

        /// <summary>
        /// 
        /// </summary>        
        /// <param name="servicioGenerales"></param>
        public SeguroCalidadGeneralesController(IServicioGenerales servicioGenerales)
            : base()
        {
            this._servicioGenerales = servicioGenerales;
        }

        #endregion Constructor


        #region Tareas

        /// <summary>
        /// Obtiene la lista de tareas dependiento el parametro enviado
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="idEmpresa"></param>
        /// <param name="idModuloTarea"></param>
        /// <param name="idTipoTarea"></param>
        /// <param name="nombreTarea"></param>
        /// <param name="nombrePaquete"></param>
        /// <param name="mostrarPaquete"></param>
        /// <param name="idTarea"></param>
        /// <returns></returns>
        [HttpGet, Route("tareas")]
        [ResponseType(typeof(ResultadoPaginado<List<Tarea>>))]
        public async Task<HttpResponseMessage> ObtenerTareasFiltro(int tamanioPagina, int pagina, int? idEmpresa = null, int? idModuloTarea = null, int? idTipoTarea = null, string nombreTarea = null, string nombrePaquete = null, bool? mostrarPaquete = null, int? idTarea = null)
        {
            var result = await this._servicioGenerales.ObtenerTareasFiltro(tamanioPagina, pagina, idEmpresa, idModuloTarea, idTipoTarea, nombreTarea, nombrePaquete, mostrarPaquete, idTarea);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet, Route("catalogo/modulo-tarea")]
        [ResponseType(typeof(List<ModuloTarea>))]
        public async Task<HttpResponseMessage> ObtenerCatalogoModuloTareaGeneral()
        {
            var result = await this._servicioGenerales.ObtenerCatalogoModuloTarea();

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet, Route("catalogos/tipo-tarea")]
        [ResponseType(typeof(List<TipoTarea>))]
        public async Task<HttpResponseMessage> ObtenerCatalogoTipoTarea()
        {
            var result = await this._servicioGenerales.ObtenerCatalogoTipoTarea();

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tarea"></param>
        /// <returns></returns>
        [HttpPost, Route("agregar-tareas")]
        [ResponseType(typeof(int))]
        public async Task<HttpResponseMessage> AgregarTareass(TareaBancos tarea)
        {
            var result = await this._servicioGenerales.AgregarTarea(tarea);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tarea"></param>
        /// <returns></returns>
        [HttpPost, Route("actualizar-tareas")]
        [ResponseType(typeof(int))]
        public async Task<HttpResponseMessage> ActualizarTareass(TareaBancos tarea)
        {
            var result = await this._servicioGenerales.ActualizarTarea(tarea);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        //[HttpGet, Route("catalogos/productos")]
        //[ResponseType(typeof(List<OpcionCatalogo>))]
        //public async Task<HttpResponseMessage> ObtenerCatalogoProductos()
        //{
        //    var result = await this._servicioGenerales.ObtenerCatalogoProductos();

        //    return Request.CreateResponse(HttpStatusCode.OK, result);
        //}

        //[HttpGet, Route("catalogos/servicios/{idProducto}")]
        //[ResponseType(typeof(List<OpcionCatalogo>))]
        //public async Task<HttpResponseMessage> ObtenerCatalogoServicios(int idProducto)
        //{
        //    var result = await this._servicioGenerales.ObtenerCatalogoServicios(idProducto);

        //    return Request.CreateResponse(HttpStatusCode.OK, result);
        //}

        #endregion

        #region Disposible

        protected override void Dispose(bool disposing)
        {
            base.Dispose(disposing);
        }

        #endregion
    }
}
