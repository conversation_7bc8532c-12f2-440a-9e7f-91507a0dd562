﻿using RUV.Comun.Negocio;
using RUV.RUVPP.Oferta.Datos.ProductoServicio;
using Microsoft.ApplicationInsights;
using RUV.RUVPP.Oferta.GeneralesInterop.Modelo.ConfiguracionSeccion;
using RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Reglas;
using System.Threading.Tasks;
using System;
using RUV.RUVPP.Oferta.GeneralesInterop.Modelo.Estatus;

namespace RUV.RUVPP.Oferta.Dominio.ProductoServicio.Implementacion
{
    public class ServicioProductoServicio : ServicioDominioBase, IServicioProductoServicio
    {
        private IProductoServicioDataMapper _productoServicio;

        public ServicioProductoServicio(IProductoServicioDataMapper _productoServicio) : base()
        {
            this._productoServicio = _productoServicio;
        }

        /// <summary>
        /// Obtiene el JSON de configuracion como objeto de un servicio especificado
        /// </summary>
        /// <param name="idServicio">Identificador del servicio</param>
        /// <returns></returns>
        public async Task<ConfiguracionSeccion> ObtenerConfiguracionAsync(int idServicio)
        {
            var tupla = await _productoServicio.ObtenerJSONConfiguracionReglasAsync(idServicio, 0);
            ConfiguracionSeccion objetoConfiguracion = Newtonsoft.Json.JsonConvert.DeserializeObject<ConfiguracionSeccion>(tupla.Item1);
            return objetoConfiguracion;
        }

        /// <summary>
        /// Obtiene una tupla que contiene el JSON de configuracion y de reglas como objeto y arreglo de un servicio determinado
        /// </summary>
        /// <param name="idServicio">Identificador del servicio</param>
        /// <returns></returns>
        public async Task<Tuple<ConfiguracionSeccion, ReglaElemento[]>> ObtenerConfiguracionReglasAsync(int idServicio)
        {
            var tupla = await _productoServicio.ObtenerJSONConfiguracionReglasAsync(idServicio, 1);
            ConfiguracionSeccion objetoConfiguracion = Newtonsoft.Json.JsonConvert.DeserializeObject<ConfiguracionSeccion>(tupla.Item1);
            ReglaElemento[] objetoReglas = Newtonsoft.Json.JsonConvert.DeserializeObject<ReglaElemento[]>(tupla.Item2);
            return Tuple.Create(objetoConfiguracion, objetoReglas);
        }
        /// <summary>
        /// Obtiene el JSON de estatus de registro/vyd como objeto para un registro determinado
        /// </summary>
        /// <param name="idServicio">Identificador del servicio</param>
        /// <param name="idRegistro">Identificador del registro</param>
        /// <param name="esRegistro">Bandera que indica si es el JSON de la etapa de registro</param>
        /// <param name="esVyd">Bandera que indica si es el JSON de la etapa de vyd</param>
        /// <param name="esFinal">Bandera que indica si es el JSON final</param>
        /// <returns></returns>
        public async Task<EstatusProcedimiento> ObtenerJsonEstatusAsync(short idServicio, int idRegistro, bool esRegistro, bool esVyd, bool? esFinal)
        {
            var resultado = await this._productoServicio.ObtenerJsonEstatusAsync(idServicio, idRegistro, esRegistro, esVyd, esFinal);
            EstatusProcedimiento estatus = resultado != null ? Newtonsoft.Json.JsonConvert.DeserializeObject<EstatusProcedimiento>(resultado) : null;
            return estatus;
        }

        /// <summary>
        /// Guarda el JSON de estatus de registro/vyd de un registro
        /// </summary>
        /// <param name="idServicio">Identificador del servicio</param>
        /// <param name="idRegistro">Identificador de registro</param>
        /// <param name="jsonEstatus">Cadena conteniendo elJSON de estatus</param>
        /// <param name="esRegistro">Bandera que indica que es el JSON de la etapa de registro</param>
        /// <param name="esVyd">Bandera que indica que es el JSON de la etapa de vyd</param>
        /// <param name="esFinal">Bandera que indica si es el JSON final</param>
        /// <returns></returns>
        public async Task<bool> GuardarJsonEstatusAsync(short idServicio, int idRegistro, string jsonEstatus, bool esRegistro, bool esVyd, bool? esFinal)
        {
            return await this._productoServicio.GuardarJsonEstatusAsync(idServicio, idRegistro, jsonEstatus, esRegistro, esVyd, esFinal);
        }

        /// <summary>
        /// Actualiza el JSON de estatus de registro/vyd de un registro
        /// </summary>
        /// <param name="idServicio">Identificador del servicio</param>
        /// <param name="idRegistro">Identificador del registro</param>
        /// <param name="jsonEstatus">Cadena conteniendo el JSON de estatus</param>
        /// <param name="esRegistro">Bandera que indica que es el JSON de la etapa de registro</param>
        /// <param name="esVyd">Bandera que indica que es el JSON de la etapa de vyd</param>
        /// <param name="esFinal">Bandera que indica si es el JSON final</param>
        /// <returns></returns>
        public async Task<bool> ActualizarJsonEstatusAsync(short idServicio, int idRegistro, string jsonEstatus, bool esRegistro, bool esVyd, bool? esFinal)
        {
            return await this._productoServicio.ActualizarJsonEstatusAsync(idServicio, idRegistro, jsonEstatus, esRegistro, esVyd, esFinal);
        }

        /// <summary>
        /// Elimina el JSON de estatus de registro/vyd de un registro
        /// </summary>
        /// <param name="idServicio">Identificador del servicio</param>
        /// <param name="idRegistro">Identificador del registro</param>        
        /// <param name="esRegistro">Bandera que indica que es el JSON de la etapa de registro</param>
        /// <param name="esVyd">Bandera que indica que es el JSON de la etapa de vyd</param>
        /// <param name="esFinal">Bandera que indica si es el JSON final</param>
        /// <returns></returns>
        public async Task<bool> EliminarJsonEstatusAsync(short idServicio, int idRegistro, bool esRegistro, bool esVyd, bool esFinal)
        {
            return await this._productoServicio.EliminarJsonEstatusAsync(idServicio, idRegistro, esRegistro, esVyd, esFinal);
        }
    }
}
