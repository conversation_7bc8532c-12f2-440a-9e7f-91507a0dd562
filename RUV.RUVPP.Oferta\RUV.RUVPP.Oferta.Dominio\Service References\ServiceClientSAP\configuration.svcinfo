﻿<?xml version="1.0" encoding="utf-8"?>
<configurationSnapshot xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="urn:schemas-microsoft-com:xml-wcfconfigurationsnapshot">
  <behaviors />
  <bindings>
    <binding digest="System.ServiceModel.Configuration.BasicHttpBindingElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089:&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data name=&quot;ServiceSoap&quot; /&gt;" bindingType="basicHttpBinding" name="ServiceSoap" />
    <binding digest="System.ServiceModel.Configuration.CustomBindingElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089:&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data name=&quot;ServiceSoap12&quot;&gt;&lt;httpTransport /&gt;&lt;textMessageEncoding messageVersion=&quot;Soap12&quot; /&gt;&lt;/Data&gt;" bindingType="customBinding" name="ServiceSoap12" />
  </bindings>
  <endpoints>
    <endpoint normalizedDigest="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data address=&quot;http://sap-dev02-sd/WSOrdenPago/service.asmx&quot; binding=&quot;basicHttpBinding&quot; bindingConfiguration=&quot;ServiceSoap&quot; contract=&quot;ServiceClientSAP.ServiceSoap&quot; name=&quot;ServiceSoap&quot; /&gt;" digest="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data address=&quot;http://sap-dev02-sd/WSOrdenPago/service.asmx&quot; binding=&quot;basicHttpBinding&quot; bindingConfiguration=&quot;ServiceSoap&quot; contract=&quot;ServiceClientSAP.ServiceSoap&quot; name=&quot;ServiceSoap&quot; /&gt;" contractName="ServiceClientSAP.ServiceSoap" name="ServiceSoap" />
    <endpoint normalizedDigest="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data address=&quot;http://sap-dev02-sd/WSOrdenPago/service.asmx&quot; binding=&quot;customBinding&quot; bindingConfiguration=&quot;ServiceSoap12&quot; contract=&quot;ServiceClientSAP.ServiceSoap&quot; name=&quot;ServiceSoap12&quot; /&gt;" digest="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data address=&quot;http://sap-dev02-sd/WSOrdenPago/service.asmx&quot; binding=&quot;customBinding&quot; bindingConfiguration=&quot;ServiceSoap12&quot; contract=&quot;ServiceClientSAP.ServiceSoap&quot; name=&quot;ServiceSoap12&quot; /&gt;" contractName="ServiceClientSAP.ServiceSoap" name="ServiceSoap12" />
  </endpoints>
</configurationSnapshot>