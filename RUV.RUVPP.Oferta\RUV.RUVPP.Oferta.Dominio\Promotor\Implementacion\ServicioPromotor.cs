﻿using Microsoft.IdentityModel.Protocols;
using Org.BouncyCastle.X509;
using RUV.Comun.Negocio;
using RUV.Comun.Utilerias;
using RUV.RUVPP.Negocio.Empresa.EmpresaConsulta;
using RUV.RUVPP.Oferta.Datos.Promotor;
using RUV.RUVPP.Oferta.Modelo.Promotor.Api;
using RUV.RUVPP.Oferta.Modelo.Promotor.Api.Json;
using RUV.RUVPP.Oferta.Modelo.Promotor.Api.Servicio;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Net.Security;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Threading.Tasks;
using System.Transactions;
using System.Web.Script.Serialization;

namespace RUV.RUVPP.Oferta.Dominio.Promotor.Implementacion
{
    public class ServicioPromotor : ServicioDominioBase, IServicioPromotor
    {
        private readonly IServicioEmpresaConsulta _servicioEmpresaConsulta;

        private readonly IPromotorDataMapper _promotorDataMapper;
        private readonly IPromotorDataMapper__ IPromotorDataMapperDM__;

        private readonly TransactionOptions _opcionesTransaccion;

        private string UrlAsignarPromotor = null;
        private string UrlRegistrarPromotor = null;

        enum Estatus
        {
            ENVIADO_ASIGNACION_CORRECTO = 1,
            ENVIADO_ASIGNACION_INCORRECTO = 2,
            ENVIADO_DESASIGNADO_CORRECTO = 3,
            ENVIADO_DESASIGNADO_INCORRECTO = 4
        };

        public ServicioPromotor(IPromotorDataMapper _promotorDataMapper, IServicioEmpresaConsulta _servicioEmpresaConsulta, IPromotorDataMapper__ IPromotorDataMapperDM__) : base()
        {
            this._servicioEmpresaConsulta = _servicioEmpresaConsulta;

            this._promotorDataMapper = _promotorDataMapper;
            this.IPromotorDataMapperDM__ = IPromotorDataMapperDM__;
            var timeoutTransaccion = ConfigurationManager.AppSettings["RUV.RUVPP.Transaccion.Timeout"];

            this._opcionesTransaccion = new TransactionOptions { IsolationLevel = System.Transactions.IsolationLevel.ReadCommitted, Timeout = TimeSpan.Parse(timeoutTransaccion) };

            this.UrlAsignarPromotor = ConfigurationManager.AppSettings["UrlAsignarPromotor"];
            this.UrlRegistrarPromotor = ConfigurationManager.AppSettings["UrlRegistrarPromotor"];

        }

        #region Promotor

        /// <summary>
        /// 
        /// </summary>
        /// <param name="model"></param>
        /// <param name="idEntidades"></param>
        /// <returns></returns>
        public async Task<int> GuardarPromotor(RUV.RUVPP.Oferta.Modelo.Promotor.Api.PromotorDTO model)
        {

            int idPromotor = await this._promotorDataMapper.GuardarPromotor(model); ;

            if (idPromotor > 0 && model.idEntidades != null && model.idEntidades.Any())
            {
                foreach (string idEntidad in model.idEntidades)
                {
                    int PromotorCobertura = await this._promotorDataMapper.GuardarPromotorXCobertura(new PromotorXCobertura() { idPromotor = idPromotor, activo = true, fechaRegistro = DateTime.Now, idEntidad = idEntidad }); ;
                }
            }

            return idPromotor;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public async Task<bool> EliminarPromotorXCobertura(PromotorXCoberturaDTO dto)
        {
            bool result = await this._promotorDataMapper.EliminarPromotorXCobertura(dto);

            return result;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<List<RUV.RUVPP.Oferta.Modelo.Promotor.Api.PromotorXCobertura>> ObtenerListadoPromotorXCobertura(int idPromotor)
        {
            List<RUV.RUVPP.Oferta.Modelo.Promotor.Api.PromotorXCobertura> result = await this._promotorDataMapper.ObtenerListadoPromotorXCobertura(idPromotor);
            return result;
        }



        /// <summary>
        /// 
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<int> ActualizarPromotor(RUV.RUVPP.Oferta.Modelo.Promotor.Api.PromotorDTO model)
        {

            int resultado = await this._promotorDataMapper.ActualizarPromotor(model);

            int? idPromotor = model.idPromotor;

            List<RUV.RUVPP.Oferta.Modelo.Promotor.Api.PromotorXCobertura> listado = await this._promotorDataMapper.ObtenerListadoPromotorXCobertura(model.idPromotor.Value);

            if (listado != null && listado.Any())
            {
                List<string> idEntidades = new List<string>();

                foreach (PromotorXCobertura entity in listado)
                {
                    idEntidades.Add(entity.idEntidad);
                }

                await this._promotorDataMapper.EliminarPromotorXCobertura(new PromotorXCoberturaDTO() { idPromotor = idPromotor, idEntidades = idEntidades });

            }

            if (model.idEntidades != null && model.idEntidades.Any())
            {
                foreach (string idEntidad in model.idEntidades)
                {
                    int PromotorCobertura = await this._promotorDataMapper.GuardarPromotorXCobertura(new PromotorXCobertura() { idPromotor = idPromotor, activo = true, fechaRegistro = DateTime.Now, idEntidad = idEntidad }); ;
                }
            }

            return resultado;
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<ResultadoPaginado<List<RUV.RUVPP.Oferta.Modelo.Promotor.Api.Promotor>>> ObtenerPromotorPaginado(int tamanioPagina, int pagina, RUV.RUVPP.Oferta.Modelo.Promotor.Api.Promotor model)
        {
            ResultadoPaginado<List<RUV.RUVPP.Oferta.Modelo.Promotor.Api.Promotor>> resultado = new ResultadoPaginado<List<RUV.RUVPP.Oferta.Modelo.Promotor.Api.Promotor>>()
            {
                TamanioPagina = tamanioPagina,
                PaginaActual = pagina
            };

            var data = await this._promotorDataMapper.ObtenerPromotorPaginado(tamanioPagina, pagina, model);

            if (data.Item1 > 0)
            {
                resultado.TotalRegistros = data.Item1;
                resultado.TotalPaginas = (int)Math.Ceiling((double)(resultado.TotalRegistros / tamanioPagina));
            }

            resultado.Resultado = data.Item2;

            return resultado;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<ResultadoPaginado<List<RUV.RUVPP.Oferta.Modelo.Promotor.Data.OfertaViviendaAsis>>> ObtenerOfertaViviendaPaginado(int tamanioPagina, int pagina, RUV.RUVPP.Oferta.Modelo.Promotor.Data.OfertaViviendaAsis model)
        {
            ResultadoPaginado<List<RUV.RUVPP.Oferta.Modelo.Promotor.Data.OfertaViviendaAsis>> resultado = new ResultadoPaginado<List<RUV.RUVPP.Oferta.Modelo.Promotor.Data.OfertaViviendaAsis>>()
            {
                TamanioPagina = tamanioPagina,
                PaginaActual = pagina
            };

            var data = await this._promotorDataMapper.ObtenerOfertaViviendaPaginado(tamanioPagina, pagina, model);

            if (data.Item1 > 0)
            {
                resultado.TotalRegistros = data.Item1;
                resultado.TotalPaginas = (int)Math.Ceiling((double)(resultado.TotalRegistros / tamanioPagina));
            }

            resultado.Resultado = data.Item2;

            return resultado;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<RUV.RUVPP.Oferta.Modelo.Promotor.Api.Promotor> ObtenerPromotor(RUV.RUVPP.Oferta.Modelo.Promotor.Api.Promotor model)
        {
            RUV.RUVPP.Oferta.Modelo.Promotor.Api.Promotor result = await this._promotorDataMapper.ObtenerPromotor(model);
            return result;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public async Task<bool> GuardarOfertaxPromotor(OfertaxPromotorDTO dto)
        {
            Dictionary<string, bool> result = await this._promotorDataMapper.GuardarOfertaxPromotor(dto);

            await this.saveJson(dto, true, result);
            
            return true;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public async Task<bool> ActualizarOfertaxPromotor(OfertaxPromotorDTO dto)
        {
            bool result = await this._promotorDataMapper.ActualizarOfertaxPromotor(dto);

            return result;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="idPromotores"></param>
        /// <returns></returns>
        public async Task<bool> EliminarOfertaxPromotor(OfertaxPromotorDTO dto)
        {
            bool result = await this._promotorDataMapper.EliminarOfertaxPromotor(dto);

            await this.saveJson(dto, false, null);
            
            return result;
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<List<RUV.RUVPP.Oferta.Modelo.Promotor.Api.OfertaxPromotor>> ObtenerListadoOfertaxPromotor(RUV.RUVPP.Oferta.Modelo.Promotor.Api.OfertaxPromotor model)
        {
            List<RUV.RUVPP.Oferta.Modelo.Promotor.Api.OfertaxPromotor> result = await this._promotorDataMapper.ObtenerListadoOfertaxPromotor(model);
            return result;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="idOfertaVivienda"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<ResultadoPaginado<List<RUV.RUVPP.Oferta.Modelo.Promotor.Api.Promotor>>> ObtenerOfertaxPromotorPaginado(int tamanioPagina, int pagina, string[] idOfertaVivienda, RUV.RUVPP.Oferta.Modelo.Promotor.Api.Promotor model)
        {
            ResultadoPaginado<List<RUV.RUVPP.Oferta.Modelo.Promotor.Api.Promotor>> resultado = new ResultadoPaginado<List<RUV.RUVPP.Oferta.Modelo.Promotor.Api.Promotor>>()
            {
                TamanioPagina = tamanioPagina,
                PaginaActual = pagina
            };

            var data = await this._promotorDataMapper.ObtenerOfertaxPromotorPaginado(tamanioPagina, pagina, idOfertaVivienda, model);

            if (data.Item1 > 0)
            {
                resultado.TotalRegistros = data.Item1;
                resultado.TotalPaginas = (int)Math.Ceiling((double)(resultado.TotalRegistros / tamanioPagina));
            }

            resultado.Resultado = data.Item2;

            return resultado;
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<int> GuardarDocumento(RUV.RUVPP.Oferta.Modelo.Promotor.Api.Documentos model)
        {
            int result = await this._promotorDataMapper.GuardarDocumento(model); ;

            return result;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="idDocumentos"></param>
        /// <returns></returns>
        public async Task<List<RUV.RUVPP.Oferta.Modelo.Promotor.Api.Documentos>> ObtenerListadoDocumentos(int[] idDocumentos)
        {
            List<RUV.RUVPP.Oferta.Modelo.Promotor.Api.Documentos> result = await this._promotorDataMapper.ObtenerListadoDocumentos(idDocumentos);
            return result;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public async Task<List<RUV.RUVPP.Oferta.Modelo.Promotor.Api.CatEstado>> ObtenerListadoCatEstado()
        {
            List<RUV.RUVPP.Oferta.Modelo.Promotor.Api.CatEstado> result = await this._promotorDataMapper.ObtenerListadoCatEstado();
            return result;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public async Task<int> GuardarOfertaxPromotorNotificacion(OfertaxPromotorNotificacion entity)
        {
            int idPrimaryKey = await this._promotorDataMapper.GuardarOfertaxPromotorNotificacion(entity);
            return idPrimaryKey;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public async Task<List<OfertaxPromotorNotificacion>> ObtenerListadoOfertaxPromotorNotificacion(OfertaxPromotorNotificacion entity)
        {
            List<RUV.RUVPP.Oferta.Modelo.Promotor.Api.OfertaxPromotorNotificacion> result = await this._promotorDataMapper.ObtenerListadoOfertaxPromotorNotificacion(entity);
            return result;

        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public async Task<int> GuardarGrupoNotificacionOfertaxPromotor(GrupoNotificacionOfertaxPromotor entity)
        {
            int idPrimaryKey = await this._promotorDataMapper.GuardarGrupoNotificacionOfertaxPromotor(entity);
            return idPrimaryKey;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public async Task<List<GrupoNotificacionOfertaxPromotor>> ObtenerListadoGrupoNotificacionOfertaxPromotor(GrupoNotificacionOfertaxPromotor entity)
        {
            List<RUV.RUVPP.Oferta.Modelo.Promotor.Api.GrupoNotificacionOfertaxPromotor> result = await this._promotorDataMapper.ObtenerListadoGrupoNotificacionOfertaxPromotor(entity);
            return result;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public async Task<int> ActualizarGrupoNotificacionOfertaxPromotor(GrupoNotificacionOfertaxPromotor entity)
        {
            int rows = await this._promotorDataMapper.ActualizarGrupoNotificacionOfertaxPromotor(entity);
            return rows;

        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="idOfertaViviendas"></param>
        /// <param name="idPromotores"></param>
        /// <returns></returns>
        private async Task<JsonPromotor> getJsonPromotor(string[] idOfertaViviendas, int[] idPromotores)
        {

            JsonPromotor json = new JsonPromotor();

            decimal? idOferente = null;

            if (idPromotores != null && idPromotores.Length > 0)
            {

                List<RUV.RUVPP.Oferta.Modelo.Promotor.Api.CatEstado> listCatEstado = await this.ObtenerListadoCatEstado();

                foreach (int idPromotor in idPromotores)
                {

                    RUV.RUVPP.Oferta.Modelo.Promotor.Api.Promotor entity = await this.ObtenerPromotor(new Modelo.Promotor.Api.Promotor() { idPromotor = idPromotor });

                    Modelo.Promotor.Api.Json.Promotor promotor = new Modelo.Promotor.Api.Json.Promotor()
                    {
                        idPromotor = (90000000 + entity.idPromotor.Value),
                        nombre = entity.nombre,
                        apellidoPaterno = entity.apellidoPaterno,
                        apellidoMaterno = entity.apellidoMaterno,
                        rfc = entity.rfc,
                        curp = entity.curp,
                        telefono = entity.telefono,
                        correo = entity.correoElectronico,
                        numeroCertificado = entity.numCertificado,
                    };

                    idOferente = entity.idOferente;

                    List<RUV.RUVPP.Oferta.Modelo.Promotor.Api.PromotorXCobertura> listPromotorXCobertura = await this.ObtenerListadoPromotorXCobertura(idPromotor);

                    List<Modelo.Promotor.Api.Json.Estado> cobertura = new List<Modelo.Promotor.Api.Json.Estado>();

                    if (listPromotorXCobertura != null && listPromotorXCobertura.Any())
                    {
                        foreach (RUV.RUVPP.Oferta.Modelo.Promotor.Api.PromotorXCobertura promotorXCobertura in listPromotorXCobertura)
                        {
                            IEnumerable<RUV.RUVPP.Oferta.Modelo.Promotor.Api.CatEstado> filterCatEstado = listCatEstado.Where(row => row.cve_ent == promotorXCobertura.idEntidad);

                            if (filterCatEstado != null && filterCatEstado.Any())
                            {
                                RUV.RUVPP.Oferta.Modelo.Promotor.Api.CatEstado estado = filterCatEstado.FirstOrDefault();

                                cobertura.Add(new Modelo.Promotor.Api.Json.Estado()
                                {
                                    cvEstado = estado.cve_ent
                                });
                            }

                        }

                        promotor.cobertura = cobertura;

                    }

                    json.promotoresOferta.datosPromotores.Add(promotor);

                }

            }

            if (idOfertaViviendas != null && idOfertaViviendas.Length > 0)
            {

                ResultadoPaginado<List<RUV.RUVPP.Oferta.Modelo.Promotor.Data.OfertaViviendaAsis>> resultadoPaginado = await ObtenerOfertaViviendaPaginado(5000, 1, new RUV.RUVPP.Oferta.Modelo.Promotor.Data.OfertaViviendaAsis() { idOferente = idOferente });

                IEnumerable<RUV.RUVPP.Oferta.Modelo.Promotor.Data.OfertaViviendaAsis> filterOfertaViviendaAsis = resultadoPaginado.Resultado.Where(row => idOfertaViviendas.Contains(row.idOfertaVivienda));

                if (filterOfertaViviendaAsis != null && filterOfertaViviendaAsis.Any())
                {
                    foreach (RUV.RUVPP.Oferta.Modelo.Promotor.Data.OfertaViviendaAsis ofertaViviendaAsis in filterOfertaViviendaAsis)
                    {
                        json.promotoresOferta.ofertaViviendas.Add(new Modelo.Promotor.Api.Json.OfertaVivienda() { idOfertaVivienda = ofertaViviendaAsis.idOfertaVivienda, nombreFrente = ofertaViviendaAsis.nombreFrente });
                    }

                }

            }

            return json;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="asignar"></param>
        private async Task<bool> saveJson(OfertaxPromotorDTO dto, bool asignar, Dictionary<string, bool> datos)
        {
            bool registro = false;

            if (dto != null && (dto.idOfertaVivienda != null && dto.idOfertaVivienda.Trim().Length > 0) && (dto.idPromotores != null && dto.idPromotores.Length > 0))
            {
                string[] idOfertaViviendas = dto.idOfertaVivienda.Split(',');

                JsonPromotor jsonPromotor = await this.getJsonPromotor(idOfertaViviendas, dto.idPromotores);

                jsonPromotor.promotoresOferta.asignar = asignar == true ? 1 : 0;

                var json = Newtonsoft.Json.JsonConvert.SerializeObject(jsonPromotor);

                //Insert Json

                //Desasignar

                if (!asignar)
                {
                    int estatusEnviO = (int)ServicioPromotor.Estatus.ENVIADO_DESASIGNADO_INCORRECTO;

                    int idGrupoNotificacion = await this.GuardarGrupoNotificacionOfertaxPromotor(new GrupoNotificacionOfertaxPromotor() { estatusEnviO = estatusEnviO, fechaRegistro = DateTime.Now, fechaActualizacion = null, respuesta = null });

                    foreach (string idOfertaVivienda in idOfertaViviendas)
                    {
                        foreach (int idPromotor in dto.idPromotores)
                        {
                            await this.GuardarOfertaxPromotorNotificacion(new OfertaxPromotorNotificacion() { idOfertaVivienda = idOfertaVivienda, idPromotor = idPromotor, idGrupoNotificacion = idGrupoNotificacion });
                        }
                    }

                    //CALL SERVICE

                    Dictionary<string, object> response = new Dictionary<string, object>();

                    CallService<JsonPromotor> proxy = new CallService<JsonPromotor>();

                    response = await proxy.PostEntity(this.UrlAsignarPromotor, jsonPromotor);

                    if (!response.ContainsKey("message") && (response.ContainsKey("code") && Convert.ToInt16(response["code"]) == 200))
                    {
                        estatusEnviO = (int)ServicioPromotor.Estatus.ENVIADO_DESASIGNADO_CORRECTO;

                        await this.ActualizarGrupoNotificacionOfertaxPromotor(new GrupoNotificacionOfertaxPromotor() { idGrupoNotificacion = idGrupoNotificacion, estatusEnviO = estatusEnviO, fechaActualizacion = DateTime.Now });
                    }
                    else
                    {
                        await this.ActualizarGrupoNotificacionOfertaxPromotor(new GrupoNotificacionOfertaxPromotor() { idGrupoNotificacion = idGrupoNotificacion, fechaActualizacion = DateTime.Now, respuesta = response["message"].ToString() });
                    }

                }
                else
                {
                    //Asignar

                    //Verificar que exista un Insert

                    bool isInsert = false;

                    foreach (string idOfertaVivienda in idOfertaViviendas)
                    {

                        foreach (int idPromotor in dto.idPromotores)
                        {
                            string key = string.Format("{0}-{1}", idOfertaVivienda, idPromotor);

                            if (datos.ContainsKey(key) && datos[key])
                            {
                                isInsert = true;
                            }
                        }

                    }

                    if (isInsert)
                    {
                        int estatusEnviO = (int)ServicioPromotor.Estatus.ENVIADO_ASIGNACION_INCORRECTO;

                        int idGrupoNotificacion = await this.GuardarGrupoNotificacionOfertaxPromotor(new GrupoNotificacionOfertaxPromotor() { estatusEnviO = estatusEnviO, fechaRegistro = DateTime.Now, fechaActualizacion = null, respuesta = null });

                        foreach (string idOfertaVivienda in idOfertaViviendas)
                        {

                            foreach (int idPromotor in dto.idPromotores)
                            {
                                string key = string.Format("{0}-{1}", idOfertaVivienda, idPromotor);

                                if (datos.ContainsKey(key) && datos[key])
                                {
                                    await this.GuardarOfertaxPromotorNotificacion(new OfertaxPromotorNotificacion() { idOfertaVivienda = idOfertaVivienda, idPromotor = idPromotor, idGrupoNotificacion = idGrupoNotificacion });
                                }
                            }

                        }

                        //CALL SERVICE

                        Dictionary<string, object> response = new Dictionary<string, object>();

                        CallService<JsonPromotor> proxy = new CallService<JsonPromotor>();

                        response = await proxy.PostEntity(this.UrlAsignarPromotor, jsonPromotor);

                        if (!response.ContainsKey("message") && (response.ContainsKey("code") && Convert.ToInt16(response["code"]) == 200))
                        {
                            estatusEnviO = (int)ServicioPromotor.Estatus.ENVIADO_ASIGNACION_CORRECTO;

                            await this.ActualizarGrupoNotificacionOfertaxPromotor(new GrupoNotificacionOfertaxPromotor() { idGrupoNotificacion = idGrupoNotificacion, estatusEnviO = estatusEnviO, fechaActualizacion = DateTime.Now });
                        }
                        else
                        {
                            await this.ActualizarGrupoNotificacionOfertaxPromotor(new GrupoNotificacionOfertaxPromotor() { idGrupoNotificacion = idGrupoNotificacion, fechaActualizacion = DateTime.Now, respuesta = response["message"].ToString() });
                        }

                    }


                }


            }



            return registro;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public async Task<bool> reEnviarJSON()
        {

            bool result = false;

            try
            {
                List<OfertaxPromotorNotificacionGrupoNotificacionOfertaxPromotor> listOfertaxPromotorNotificacionGrupoNotificacionOfertaxPromotor = await this._promotorDataMapper.ObtenerListadoOfertaxPromotorNotificacionGrupoNotificacionOfertaxPromotor();

                Dictionary<int, Dictionary<string, object>> grupos = new Dictionary<int, Dictionary<string, object>>();

                if (listOfertaxPromotorNotificacionGrupoNotificacionOfertaxPromotor != null && listOfertaxPromotorNotificacionGrupoNotificacionOfertaxPromotor.Any())
                {
                    foreach (OfertaxPromotorNotificacionGrupoNotificacionOfertaxPromotor entity in listOfertaxPromotorNotificacionGrupoNotificacionOfertaxPromotor)
                    {
                        int idGrupoNotificacion = entity.idGrupoNotificacion.Value;

                        Dictionary<string, object> datosListas = new Dictionary<string, object>();

                        List<string> idOfertaViviendas = new List<string>();

                        List<int> idPromotores = new List<int>();

                        bool asignar = false;

                        if (grupos.ContainsKey(idGrupoNotificacion))
                        {
                            datosListas = grupos[idGrupoNotificacion];

                            idOfertaViviendas = (List<string>)datosListas["ID_OFERTA_VIVIENDAS"];

                            idPromotores = (List<int>)datosListas["ID_PROMOTORES"];

                            asignar = (bool)datosListas["ASIGNAR"];
                        }

                        if(idOfertaViviendas != null)
                        {
                            IEnumerable<string> subfilter = idOfertaViviendas.Where(row => row == entity.idOfertaVivienda);

                            if(subfilter != null && !subfilter.Any())
                            {
                                idOfertaViviendas.Add(entity.idOfertaVivienda);
                            }

                        }

                        if (idPromotores != null)
                        {
                            IEnumerable<int> subfilter = idPromotores.Where(row => row == entity.idPromotor.Value);

                            if (subfilter != null && !subfilter.Any())
                            {
                                idPromotores.Add(entity.idPromotor.Value);
                            }

                        }
                        
                        asignar = entity.estatusEnviO == ((int)(ServicioPromotor.Estatus.ENVIADO_ASIGNACION_INCORRECTO)) ? true : false;


                        datosListas = new Dictionary<string, object>();

                        datosListas.Add("ID_OFERTA_VIVIENDAS", idOfertaViviendas);

                        datosListas.Add("ID_PROMOTORES", idPromotores);

                        datosListas.Add("ASIGNAR", asignar);

                        if (!grupos.ContainsKey(idGrupoNotificacion))
                        {
                            grupos.Add(idGrupoNotificacion, datosListas);
                        }


                    }


                }

                if (grupos != null && grupos.Any())
                {

                    foreach (int idGrupoNotificacion in grupos.Keys)
                    {
                        Dictionary<string, object> datosListas = grupos[idGrupoNotificacion];

                        List<string> idOfertaViviendas = (List<string>)datosListas["ID_OFERTA_VIVIENDAS"];

                        List<int> idPromotores = (List<int>)datosListas["ID_PROMOTORES"];

                        bool asignar = ((bool)datosListas["ASIGNAR"]);

                        JsonPromotor jsonPromotor = await this.getJsonPromotor(idOfertaViviendas.ToArray(), idPromotores.ToArray());

                        jsonPromotor.promotoresOferta.asignar = asignar == true ? 1 : 0;

                        //CALL SERVICE

                        Dictionary<string, object> response = new Dictionary<string, object>();

                        CallService<JsonPromotor> proxy = new CallService<JsonPromotor>();

                        response = await proxy.PostEntity(this.UrlAsignarPromotor, jsonPromotor);

                        if (!asignar)
                        {

                            if (!response.ContainsKey("message") && (response.ContainsKey("code") && Convert.ToInt16(response["code"]) == 200))
                            {
                                int estatusEnviO = (int)ServicioPromotor.Estatus.ENVIADO_DESASIGNADO_CORRECTO;

                                await this.ActualizarGrupoNotificacionOfertaxPromotor(new GrupoNotificacionOfertaxPromotor() { idGrupoNotificacion = idGrupoNotificacion, estatusEnviO = estatusEnviO, fechaActualizacion = DateTime.Now });
                            }

                        }

                        if (asignar)
                        {

                            if (!response.ContainsKey("message") && (response.ContainsKey("code") && Convert.ToInt16(response["code"]) == 200))
                            {
                                int estatusEnviO = (int)ServicioPromotor.Estatus.ENVIADO_ASIGNACION_CORRECTO;

                                await this.ActualizarGrupoNotificacionOfertaxPromotor(new GrupoNotificacionOfertaxPromotor() { idGrupoNotificacion = idGrupoNotificacion, estatusEnviO = estatusEnviO, fechaActualizacion = DateTime.Now });
                            }

                        }

                    }

                }

                result = true;
            }
            catch (Exception e)
            {

            }

            return result;
        }

        #endregion Promotor


        #region serivicio promotor

        /// <summary>
        /// 
        /// </summary>
        /// <param name="model"></param>
        /// <param name="idEntidades"></param>
        /// <returns></returns>
        public async Task<bool> registrarPromotor()
        {

            List<EmpresaxPromotor> lisEmmpresaxPromotor = await this.IPromotorDataMapperDM__.ObtenerEmpresaRUV();  //await this._promotorDataMapper.ObtenerEmpresaRUV(); 

            int tipoCAtegoria = 2;


            string objetoSocialEmpresaStr = "";
            string curpStr = "";

            foreach (EmpresaxPromotor empresaxPromotor in lisEmmpresaxPromotor)
            {
                 

                if ( Convert.ToInt32( empresaxPromotor.idempresa) == 15035)
                {
                    Console.WriteLine(">>>>>");
                }

                registrarPromotor _registrarPromotor = new registrarPromotor();

                _registrarPromotor.empresa = new RUV.RUVPP.Oferta.Modelo.Promotor.Api.Servicio.Empresa();

                if (empresaxPromotor.apellidoPaterno != null && !empresaxPromotor.apellidoPaterno.Equals(""))
                {
                    tipoCAtegoria = 3;
                }

                _registrarPromotor.empresa.numeroEmpresa = empresaxPromotor.idempresa;
                _registrarPromotor.empresa.tipoCategoria = tipoCAtegoria = 3; ;
                _registrarPromotor.empresa.nombre = validaNull(empresaxPromotor.nombreRazonSocial);
                _registrarPromotor.empresa.apellidoPaterno = validaNull(empresaxPromotor.apellidoPaterno);
                _registrarPromotor.empresa.apellidoMaterno = validaNull(empresaxPromotor.apellidoMaterno);
                _registrarPromotor.empresa.rfc = validaNull(empresaxPromotor.rfc);
                _registrarPromotor.empresa.nrp = validaNull(empresaxPromotor.numeroRegistroPatronal);
                _registrarPromotor.empresa.direccion = validaNull(empresaxPromotor.direccion);

                List<TelefonoEntity> listTelefono = await this.IPromotorDataMapperDM__.ObtenerTelefonosEmpresaRUV(Convert.ToInt32(empresaxPromotor.idempresa));  //await this._promotorDataMapper.ObtenerEmpresaRUV(); 

                List<Telefono> lisTelefono_ = new List<Telefono>();
                List<Correo> lisCorreo_ = new List<Correo>();
                List<Accionista> lisAccionista_ = new List<Accionista>();
                List<Promotore> lisPromotorAsis_ = new List<Promotore>();
                List<EstadosOperacion> lisEstadoOperacion_ = new List<EstadosOperacion>();
                string telefonoStr = "";
                foreach (TelefonoEntity telefono in listTelefono)
                {
                    Telefono telefono_ = new Telefono();

                    if (telefono.principal == 1)
                    {
                        telefonoStr = telefono.telefono;
                    }

                    telefono.extension = validaNull(telefono.extension);

                    telefono_.extension = telefono.extension;
                    telefono_.lada = telefono.lada;
                    telefono_.telefono = telefono.telefono;

                    lisTelefono_.Add(telefono_);
                }
                _registrarPromotor.empresa.telefonos = lisTelefono_;

                List<CorreoEntity> listCorreo = await this.IPromotorDataMapperDM__.ObtenerCorreosEmpresaRUV(Convert.ToInt32(empresaxPromotor.idempresa));  //await this._promotorDataMapper.ObtenerEmpresaRUV(); 
                string correoStr = "";

                foreach (CorreoEntity correo in listCorreo)
                {
                    Correo corrreo_ = new Correo();

                    if (correo.principal == 1)
                    {
                        correoStr = correo.correo;
                    }

                    corrreo_.correo = correo.correo;

                    lisCorreo_.Add(corrreo_);

                }
                _registrarPromotor.empresa.correos = lisCorreo_;

                List<Accionista> listAccionista = await this.IPromotorDataMapperDM__.ObtenerAccionistasEmpresaRUV(Convert.ToInt32(empresaxPromotor.idempresa));  //await this._promotorDataMapper.ObtenerEmpresaRUV(); 

                if (listAccionista != null && listAccionista.Any())
                {
                    foreach (Accionista accionista in listAccionista)
                    {
                        lisAccionista_.Add(accionista);

                    }
                }
                else
                {
                    Accionista accionista = new Accionista();

                    accionista.nombre = " ";
                    accionista.apellidoPaterno = " ";
                    accionista.apellidoMaterno = " ";
                    accionista.curp = " ";
                    accionista.rfc = " ";

                    lisAccionista_.Add(accionista);
                }

                _registrarPromotor.empresa.accionistas = lisAccionista_;

                List<PromotorEntity> listPromotorAsis = await this.IPromotorDataMapperDM__.ObtenerPromotorRUV(Convert.ToInt32(empresaxPromotor.idempresa));  //await this._promotorDataMapper.ObtenerEmpresaRUV(); 

                List<RUV.RUVPP.Oferta.Modelo.Promotor.Api.CatEstado> listCatEstado = await this.ObtenerListadoCatEstado();

                List<EstadosOperacion> estadosOperacion = new List<EstadosOperacion>();

                foreach (PromotorEntity promotore in listPromotorAsis)
                {


                    if (  promotore.certificado != null )
                    {
                        Promotore promotor = new Promotore();

                        List<CoberturaPromotor> listaCobertura = await IPromotorDataMapperDM__.ObtenerCoberturaPromotor(Convert.ToInt32(promotore.idUsuario));

                        foreach (CoberturaPromotor cobertura in listaCobertura)
                        {
                            IEnumerable<RUV.RUVPP.Oferta.Modelo.Promotor.Api.CatEstado> filterCatEstado = listCatEstado.Where(row => row.cve_ent == cobertura.idEstado);

                            if (filterCatEstado != null && filterCatEstado.Any())
                            {
                                RUV.RUVPP.Oferta.Modelo.Promotor.Api.CatEstado estado = filterCatEstado.FirstOrDefault();

                                estadosOperacion.Add(new EstadosOperacion()
                                {
                                    claveEstado = estado.cve_ent,
                                    nombreEstado = estado.nom_ent
                                });
                            }
                        }


                        //string cveEstado = promotore.estadoFiscal.Substring(0, 2);

                        //        IEnumerable<RUV.RUVPP.Oferta.Modelo.Promotor.Api.CatEstado> filterCatEstado = listCatEstado.Where(row => row.cve_ent == cveEstado);

                        //        if (filterCatEstado != null && filterCatEstado.Any())
                        //        {
                        //            RUV.RUVPP.Oferta.Modelo.Promotor.Api.CatEstado estado = filterCatEstado.FirstOrDefault();

                        //            estadosOperacion.Add(new EstadosOperacion()
                        //            {
                        //                claveEstado = estado.cve_ent,
                        //                nombreEstado = estado.nom_ent
                        //            });
                        //        }


                        promotore.estadosOperacion = estadosOperacion;

                        if (promotore.apellidoPaterno.Equals(""))
                        {
                            promotore.apellidoPaterno = _registrarPromotor.empresa.apellidoPaterno;
                        }
                        if (promotore.apellidoMaterno.Equals(""))
                        {
                            promotore.apellidoMaterno = _registrarPromotor.empresa.apellidoMaterno;
                        }
                        if (promotore.telefono == null)
                        {
                            promotore.telefono = telefonoStr;
                        }
                        if (promotore.curp.Equals(""))
                        {
                            promotore.curp = " ";
                        }
                        if (promotore.certificado == null)
                        {
                            promotore.certificado = " ";
                        }
                        if (!promotore.correo.Equals(""))
                        {
                            correoStr = promotore.correo;
                        }
                        promotore.tipoPromotor = "2";

                        promotor.idPromotor = promotore.certificado;
                        promotor.tipoPromotor = promotore.tipoPromotor;
                        promotor.nombre = promotore.nombre;
                        promotor.apellidoPaterno = promotore.apellidoPaterno;
                        promotor.apellidoMaterno = promotore.apellidoMaterno;
                        promotor.certificado = promotore.certificado;
                        promotor.curp = promotore.curp;
                        promotor.telefono = promotore.telefono;
                        promotor.correo = correoStr;
                        promotor.estadosOperacion = promotore.estadosOperacion;
                        lisPromotorAsis_.Add(promotor);

                        string empresaStr = _registrarPromotor.empresa.numeroEmpresa;
                        string nombrePromotro = promotor.nombre + "  " + promotor.apellidoPaterno + " " + promotor.apellidoMaterno;

                        imprimirPromotores(empresaStr, nombrePromotro);
                    }


                    

                }

                if (_registrarPromotor.empresa.objetoSocial != null)
                {
                    objetoSocialEmpresaStr = _registrarPromotor.empresa.objetoSocial;
                }

                _registrarPromotor.empresa.objetoSocial = " ";
                _registrarPromotor.empresa.promotores = lisPromotorAsis_;

                Dictionary<string, object> response = new Dictionary<string, object>();

                CallService<registrarPromotor> proxy = new CallService<registrarPromotor>();

                response = await proxy.PostEntity(this.UrlRegistrarPromotor, _registrarPromotor);

            }

            return true;
        }
        #endregion

        #region promotore envio infonavit


       

        public void imprimirPromotores(string texto, string json)
        {

            DateTime dateTime = new DateTime();
            dateTime = DateTime.Now;
            string strDate = Convert.ToDateTime(dateTime).ToString("dd/MM/yyyy HH:mm:ss");
            string strName = Convert.ToDateTime(dateTime).ToString("dd_MM_yyyy_HH_mm_s");

            string rutaCompleta = @" C:\log_promotores\" + texto + ".txt";

            using (StreamWriter mylogs = File.AppendText(rutaCompleta))         //se crea el archivo
            {



                mylogs.WriteLine(strDate);
                mylogs.WriteLine("Empresa  : " + texto);
                mylogs.WriteLine("Promotor : " + json);


                mylogs.Close();

            }

        }
        public string validaNull(string variable)
        {
            string resultado = " ";

            if (variable != null)
            {
                resultado = variable;
            }

            return resultado;
        }
        #endregion

    }

    public class CallService<T>
    {

        /// <summary>
        ///
        /// </summary>
        /// <param name="url"></param>
        /// <returns></returns>
        public async Task<T> getEntity(string url)
        {

            Object entity = null;

            using (var client = new HttpClient())
            {
                client.BaseAddress = new Uri(url);
                client.DefaultRequestHeaders.Accept.Clear();
                client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

                HttpResponseMessage responseMessage = await client.GetAsync(url);

                if (responseMessage.IsSuccessStatusCode)
                {
                    var responseData = responseMessage.Content.ReadAsStringAsync().Result;

                    entity = Newtonsoft.Json.JsonConvert.DeserializeObject<T>(responseData);

                }

            }

            return (T)entity;
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="url"></param>
        /// <returns></returns>
        public async Task<List<T>> getListEntity(string url)
        {

            List<T> entity = null;

            using (var client = new HttpClient())
            {
                client.BaseAddress = new Uri(url);
                client.DefaultRequestHeaders.Accept.Clear();
                client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

                HttpResponseMessage responseMessage = await client.GetAsync(url);

                if (responseMessage.IsSuccessStatusCode)
                {
                    var responseData = responseMessage.Content.ReadAsStringAsync().Result;

                    entity = Newtonsoft.Json.JsonConvert.DeserializeObject<List<T>>(responseData);

                }

            }

            return (List<T>)entity;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="url"></param>
        /// <returns></returns>
        public async Task<string> getObject(string url)
        {

            string obj = null;

            using (var client = new HttpClient())
            {
                client.BaseAddress = new Uri(url);
                client.DefaultRequestHeaders.Accept.Clear();
                client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

                HttpResponseMessage responseMessage = await client.GetAsync(url);

                if (responseMessage.IsSuccessStatusCode)
                {
                    obj = responseMessage.Content.ReadAsStringAsync().Result;
                }

            }

            return obj;
        }


        /// <summary>
        ///
        /// </summary>
        /// <param name="url"></param>
        /// <param name="entity"></param>
        /// <returns></returns>
        public async Task<Dictionary<string, object>> PostEntity(string url, T entity)
        {
            Dictionary<string, object> resultado = new Dictionary<string, object>();

            try
            {
                using (var client = new HttpClient())
                {
                    client.BaseAddress = new Uri(url);
                    client.DefaultRequestHeaders.Accept.Clear();
                    client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

                    var param = Newtonsoft.Json.JsonConvert.SerializeObject(entity);

                    HttpContent contentPost = new StringContent(param, Encoding.UTF8, "application/json");

                    ServicePointManager.ServerCertificateValidationCallback = (object sender, System.Security.Cryptography.X509Certificates.X509Certificate certificate, X509Chain chain, SslPolicyErrors sslPolicyErrors) => { return true; };

                    HttpResponseMessage responseMessage = await client.PostAsync(url, contentPost);

                    if (responseMessage != null)
                    {
                        string mensaje = await responseMessage.Content.ReadAsStringAsync();

                        this.respuestaPromotores(mensaje, param);

                        bool result = mensaje.Contains("\"codigoRespuesta\":\"0000\"");

                        if (!result)
                        {
                            resultado.Add("message", mensaje);
                        }

                        resultado.Add("code", (int)responseMessage.StatusCode);

                    }

                }
            }
            catch (Exception e)
            {
                resultado = new Dictionary<string, object>();
                resultado.Add("code", "500");
                resultado.Add("message", e.Message);
            }

            return resultado;

        }

        #region promotore envio infonavit


        public void respuestaPromotores(string texto, string json)
        {

            DateTime dateTime = new DateTime();
            dateTime = DateTime.Now;
            string strDate = Convert.ToDateTime(dateTime).ToString("dd/MM/yyyy HH:mm:ss");
            string strName = Convert.ToDateTime(dateTime).ToString("dd_MM_yyyy_HH_mm_s");

            string rutaCompleta = @" C:\log_promotores\" + strName + ".txt";

            using (StreamWriter mylogs = File.AppendText(rutaCompleta))         //se crea el archivo
            {



                mylogs.WriteLine(strDate);
                mylogs.WriteLine("Respuesta Infonavit : " + texto);
                mylogs.WriteLine("Json Envio : " + json);


                mylogs.Close();

            }

        }
        #endregion

    }
}
