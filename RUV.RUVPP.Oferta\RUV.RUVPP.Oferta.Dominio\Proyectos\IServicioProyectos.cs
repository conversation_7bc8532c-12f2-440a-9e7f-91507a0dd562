﻿using RUV.RUVPP.Oferta.Modelo.Proyectos.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RUV.RUVPP.Oferta.Modelo.Proyectos.Api;
using RUV.RUVPP.Oferta.Comun.Modelo;
using RUV.RUVPP.Oferta.Modelo.Dictaminacion;
using RUV.Comun.Utilerias;
using RUV.RUVPP.Oferta.Modelo.Historico.Data;
using RUV.RUVPP.Oferta.Modelo.Mediciones;
using RUV.Comun.Seguridad.JWT;
using RUV.RUVPP.Oferta.Modelo.Plano;
using System.IO;

namespace RUV.RUVPP.Oferta.Dominio.Proyectos
{
    public interface IServicioProyectos : IDisposable
    {
        Task<int> ExisteProyectoPorNombreAsync(string nombre, int idEmpresa, int? idProyecto);
        Task<int> GuardarProyectoAsync(int idEmpresa,string claveEmpresa, string nombre, string proyecto, CustomUserRuv usuario);
        Task<RespuestaCargaPlano> GuardarProyectoDetalleAsync(Proyecto proyecto, CustomUserRuv usuario, bool esGuardadoAsincrono);
        Task<int> ActualizarProyectoAsync(int   idProyecto, string nombre, string proyecto, int? idEstatusProyecto, bool fueronModificadosDatosSensible);
        Task<string> ActualizarProyectoDetalleAsync(Proyecto proyecto, CustomUserRuv usuario, int? esAsincrono);

        /// <summary>
        /// Actualiza el estatus de un proyecto, manda y loguea excepcion si no puedo actualizarlo.
        /// </summary>
        /// <param name="idProyecto">Identificador del proyecto</param>
        /// <param name="idEstatus">Identificador del estatus</param>
        /// <returns></returns>
        Task<bool> ActualizarEstatusProyectoAsync(int idProyecto, int idEstatus, bool actualizarFechaAceptacion = false);
        Task<ResultadoPaginado<List<Proyecto>>> ObtenerProyectosAsync(int idEmpresa, int tamanioPagina, int pagina, int? idProyecto, string nombreProyecto, string idEntidadFederativa, string idMunicipio, string idLocalidad);
        Task<Proyecto> ObtenerProyectoAsync(int idProyecto, bool? esValidacionASIS = null);
        Task BorrarTemporalAsync(int idProyecto);
        Task<Proyecto> ObtenerProyectoDetalleAsync(int idProyecto);

        Task<ResultadoPaginado<List<Modelo.OrdenVerificacion.Data.OrdenVerificacion>>> ObtenerOrdenesVerificacionPorIdProyectoAsync(int tamanioPagina, int pagina, int idProyecto);

        Task<ResultadoPaginado<List<Proyecto>>> ObtenerProyectosPorIdEmpresaConPaginadorAsync(int tamanioPagina, int pagina, int idEmpresa);

        Task<bool> EliminarDocumentoProyecto(int idEmpresa, int idDocumento);
        /// <summary>
        /// Guarda/Actualiza la dictaminación, actualiza el estatus del proyecto y atiende la ODT.
        /// </summary>
        /// <param name="idOrdenTrabajo">Identificador de la orden de trabajo</param>
        /// <param name="idServicio">Identificador del servicio</param>
        /// <param name="idRegistro">Identificador del proyecto</param>
        /// <param name="parametrosDictaminacion">Objeto que contiene los siguientes parametros:
        /// seActualizaDictaminacion - FALSE si se inserta la dictaminacion, TRUE si se actualiza
        /// DictaminacionJSON -  cadena que contiene el JSON de dictaminacion</param>
        /// aceptacion - TRUE indica que se acepta la oferta, FALSE que se rechaza la oferta<returns></returns>
        /// <returns></returns>
        Task<Tuple<bool,bool>> EnviarDictaminacionProyecto(int idOrdenTrabajo, short idServicio, int idRegistro, ParametroBodyDictaminacion parametrosDictaminacion, CustomUserRuv usuario, bool esGuardadoAsincrono);
        Task<bool> EliminarProyectoAsync(int idProyecto, CustomUserRuv usuario);
        Task<bool> EliminarProyectoParcialAsync(int idProyecto);
        Task<bool> EliminarTemporalProyectoAsync(int idProyecto);

        //Task<int> CargarArchivoSDFAsync(int rutaArchivo, int idEmpresa, string nombreProyecto, int idProyecto);
        Task<RespuestaCargaPlano> CargarArchivoSDFAsync(int idArchivoSDF, int idUsuario, int idProyecto, CustomUserRuv usuario);
        Task<Sembrado> ObtenerSembradoTemporalAsync(int idProyectoTemporal);

        Task<Sembrado> ObtenerSembradoOficialTemporalAsync(int idProyecto, bool esConsulta);

        Task<Proyecto> ObtenerProyectosFiltroAsync(int? idEmpresa, string noRuv, string nombreProyecto, int? idProyecto);

        Task<ResultadoPaginado<List<Proyecto>>> ObtenerProyectosFiltroConPaginadoAsync(int tamanioPagina, int pagina, int? idEmpresa, string noRuv, string nombreProyecto, int? idProyecto, int? idEntidad, int? idEstatus);

        
        Task<List<Vivienda>> ObtenerViveindasPorProyectoAsync(int idProyecto);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="idProyecto"></param>
        /// <returns></returns>
        Task<List<Vivienda>> ObtenerViveindasReportePorProyectoAsync(int idProyecto);

        Task<List<Vivienda>> ObtenerViviendasReportePorCUVAsync(string cuv);

        Task<List<Vivienda>> ActualizarViviendasAsync(Sembrado sembrado, int? idProyecto, string folioSEPLADE = null, string folioAyto = null);
        /// <summary>
        /// Obtiene el listado de ordenes de trabajo para un proyecto por idProyecto paginado
        /// </summary>
        /// <param name="idProyecto">Identificador del proyecto</param>
        /// <param name="tamanioPagina">Tamaño de la pagina</param>
        /// <param name="pagina">Pagina</param>
        /// <returns></returns>
        Task<ResultadoPaginado<List<OrdenTrabajoHistoricoValidacion>>> ObtenerOrdenesTrabajoPorProyectoPaginadoAsync(int idProyecto, int tamanioPagina, int pagina);

        /// <summary>
        /// Obtiene el listado de viviendas para un proyecto por idProyecto paginado
        /// </summary>
        /// <param name="idProyecto">Identificador del proyecto</param>
        /// <param name="tamanioPagina">Tamaño de la pagina</param>
        /// <param name="pagina">Pagina</param>
        /// <returns></returns>
        Task<ResultadoPaginado<List<Vivienda>>> ObtenerViviendasPorProyectoPaginadoAsync(int idProyecto, int tamanioPagina, int pagina);

        /// <summary>
        /// Obtiene el resultado de la consulta de CUVS filtrada desde la BD del RUV++
        /// </summary>
        /// <param name="filtros">Objeto que contiene los siguientes filtros: IdProyecto, IdOferta, Cuv, IdEstatusJuridicoVivienda, IdEntidadFederativa, NombreFrente, NumeroRegistroRuv</param>
        /// <param name="tamanioPagina">Tamaño de la pagina</param>
        /// <param name="pagina">pagina consultada</param>
        Task<ResultadoPaginado<List<ConsultaCuv>>> ObtenerCuvsFiltradasPaginadasAsync(FiltrosConsultaCuv filtros, int pagina, int tamanioPagina);
        Task<List<Vivienda>> ObtenerVivendasPorIdProyectoAsync(int idProyecto);
        Task<List<Proyecto>> ObtenerProyectosPorIdEmpresaAsync(int idEmpresa);

        /// <summary>
        /// Método para obtener los equipamientos de un proyecto.
        /// </summary>
        /// <param name="idProyecto">Identificador del proyecto.</param>
        /// <returns>Objeto con la lista de equipamientos del proyecto.</returns>
        Task<EquipamientosProyecto> ObtenerEquipamientosProyecto(int idProyecto, bool esTemporal = false);

        /// <summary>
        /// Elimina una viveinda de la tabla temporal de Oracle
        /// </summary>
        /// <param name="featId"></param>
        /// <returns></returns>
        Task<bool> EliminarViviendaTemporal(int featId);

        /// <summary>
        /// Elimina una viveinda de la tabla oficial de Oracle
        /// </summary>
        /// <param name="featId"></param>
        /// <returns></returns>
        Task<bool> EliminarViviendaOficial(int featId);

        /// <summary>
        /// Obtiene el resultado de la consulta de CUVS filtrada y paginada
        /// </summary>
        /// <param name="filtros">Objeto que contiene los siguientes filtros: IdProyecto, IdOferta, Cuv, IdEstatusJuridicoVivienda, IdEntidadFederativa, NombreFrente, NumeroRegistroRuv</param>        
        Task<List<ConsultaCuv>> ObtenerCuvsFiltradasAsync(FiltrosConsultaCuv filtros);


        /// <summary>
        /// Agrega un mensaje a la queue del WebJob de Azure para procesar el guardado o actualizacion de un proyecto
        /// </summary>
        /// <returns></returns>
        Task<string> GuardarProyectoAsincronoAsync(Proyecto proyecto, CustomUserRuv usuario);

        /// <summary>
        /// Obtiene dos cadenas con los listados de los nombres de estados y municipios de las viviendas de un proyecto
        /// </summary>
        /// <param name="idsEstados">identificadores de estados</param>
        /// <param name="idsMunicipios">identificadores de municipios</param>
        /// <returns></returns>
        Task<Tuple<string, string>> ObtenerEstadosMunicipiosViviendasAsync(List<string> idsEstados, List<Tuple<string, string>> idsMunicipios);

        /// <summary>
        /// Valida la tipologia y espacio geografico del proyecto
        /// </summary>
        /// <param name="proyecto"></param>
        /// <returns></returns>
        Task<Tuple<bool, string>> ValidarProyecto(Proyecto proyecto,bool superaMaximoViviendas, bool existeDetalleProyecto = false);
        Task<bool> EliminarEquipamientoTemporlAsync(string capa, int featId);
    }
}
