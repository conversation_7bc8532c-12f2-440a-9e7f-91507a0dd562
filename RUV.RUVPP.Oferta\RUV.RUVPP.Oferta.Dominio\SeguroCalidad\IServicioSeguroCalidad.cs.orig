﻿using RUV.Comun.Seguridad.JWT;
using RUV.Comun.Utilerias;
using RUV.RUVPP.Oferta.Comun.Modelo;
using RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api;
using RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Dominio.SeguroCalidad
{
    public interface IServicioSeguroCalidad
    {
        Task<int> ActualizarPagoDiferenciaOVAsync(PagoDiferenciaOV pagoDiferenciaOV);
        Task<DesarrolladorRelacion> ObtenerOferenteAsync(string idDesarrollador);

        Task<PagoDiferenciaOV> AgregarPagoDiferenciaOVAsync(PagoDiferenciaOV pagoDiferenciaOV);

        Task<List<int>> AgregarPolizaAsync(List<CuvPoliza> listaCuvPoliza);
        Task<bool> RevisarParametros90Dias();
        Task<bool> ActualizarParametrosConfigs90DiasAsync(List<ParametrosConfigs> listaParametros);
        Task<List<ParametrosConfigs>> ObtenerParametrosConfigs90DiasAsync(List<string> listaClaves);
        Task<OrdenDeVerificacion> ObtenerInfoOrdenVerificacionAsync(string ordenVerificacion);

        Task<List<RelacionesComercialesOferente>> ObtenerRelacionesComercialesXDesarrolladorAsync();

        Task<ResultadoPaginado<List<Aseguradora>>> ObtenerAseguradoraConPaginadoAsync(int tamanioPagina, int pagina, string razonSocial, string noRegistroRUV);

        Task<Aseguradora> ObtenerAseguradoraEnPadronSinOferenteXDefectoAsync(int idDesarrollador);

        Task<bool> ActualizarAseguradoraOrdenPadronAsync(List<Aseguradora> listaAseguradora);

        Task<ResultadoPaginado<List<Avaluo>>> ObtenerListaAvaluosAsync(int tamanioPagina, int pagina, string cuv);

        Task<ResultadoPaginado<List<OrdenDeVerificacion>>> ObtenerConsultaAseguradoraConPaginadoAsync(int tamanioPagina, int pagina, string noRegistroRUV, string razonSocial, string ordenVerificacion, string noContrato, string fechaInicial, string fechaFinal, string fechaAceptacionInicio, string fechaAceptacionFinal, int? idTipoAsignacion, int? idOferenteExterno, int? idAseguradoraExterno, int? idEstatusPagoEvaluacionRiesgo);

        Task<ResultadoPaginado<List<CuvPoliza>>> ObtenerCuvSinPolizaConPaginadoAsync(int tamanioPagina, int pagina, string cuv, string ordenVerificacion, int? idOferente, bool? cambioValorAvaluo);        

        Task<ResultadoPaginado<List<CuvPoliza>>> ObtenerCuvConPolizaConPaginadoAsync(int tamanioPagina, int pagina, string cuv, string ordenVerificacion, int? idOferente);        

        Task<ResultadoPaginado<List<Aseguradora>>> ObtenerAseguradoraRelacionComercialConPaginadoAsync(int tamanioPagina, int pagina, string razonSocial, string noRegistroRUV, int? idDesarrollador, int? idAseguradora);

        Task<ResultadoPaginado<List<DesarrolladorRelacion>>> ObtenerDesarrolladoresRelacionComercialConPaginadoAsync(int tamanioPagina, int pagina, string razonSocial, string noRegistroRUV, string ordenVerificacion, string estatusCosto, int? idDesarrollador, int? idAseguradora);

        Task<ResultadoPaginado<List<Aseguradora>>> ObtenerAseguradoraEnPadronSinOferenteEnRelacionComercialConPaginadoAsync(int tamanioPagina, int pagina, string razonSocial, string noRegistroRUV, int? idDesarrollador);

        Task<ResultadoPaginado<List<Aseguradora>>> ObtenerAseguradoraEnPadronConPaginadoAsync(int tamanioPagina, int pagina, string razonSocial, string noRegistroRUV);

        Task<RelacionComercial> ObtenerRelacionComercialAsync(int? idDesarrollador, int? idAseguradora, int? activo, int? idRelacionComercial);

        Task<Aseguradora> ObtenerAseguradoraCompletaAsync(int idAseguradora);

        /// <summary>
        /// Guarda los datos de titulación enviados por el INFONAVIT
        /// </summary>
        /// <param name="datosTitulacion"></param>
        /// <returns></returns>
        Task<bool> GuardarDatosTitulacionAsync(DatosTitulacionINFONAVIT datosTitulacion);

        Task<bool> PasarAseguradoraPadronAsync(List<int> listaAseguradoras, int enPadronAseguradora, int idUsuario);

        Task<int> GuardarSancionAsync(Sancion sancion, int? idUsuario);

        Task<List<int>> GuardarRelacionComercialAsync(List<Aseguradora> listaAseguradora, int? idUsuario, string idEmpresaInst);

        Task<int> ActualizarSancionAsync(Sancion sancion, int idUsuario);

        Task<int> ActualizarRelacionComercialAsync(RelacionComercial relacion, int? idUsuario);

        Task<int> ActualizarRelacionComercialXRechazoAsync(RelacionComercial relacion, int? idUsuario);

        Task<List<Sancion>> ObtenerSancionesAsync(int? idSancion, int? idAseguradora);

        /// <summary>
        /// Obtiene el catalogo de tipo de incidencias
        /// </summary>
        /// <returns></returns>
        Task<List<OpcionCatalogoSeguroCalidad>> ObtenerTipoIncidenciasAsync();

        /// <summary>
        /// Obtiene el catalogo de estatus de incidencias
        /// </summary>
        /// <returns></returns>
        Task<List<OpcionCatalogoSeguroCalidad>> ObtenerEstatusIncidenciasAsync();

        /// <summary>
        /// Obtiene el catalogo de clasificacion de incidencias
        /// </summary>
        /// <returns></returns>
        Task<List<OpcionCatalogoSeguroCalidad>> ObtenerClasificacionIncidenciasAsync();

        /// <summary>
        /// Obtiene el catalogo de estatus pago evaluacion riesgo
        /// </summary>
        /// <returns></returns>
        Task<List<OpcionCatalogoSeguroCalidad>> ObtenerEstatusPagoEvaluacionRiesgoAsync();

        /// <summary>
        /// Obtiene el catalogo de cobertura afectada
        /// </summary>
        /// <returns></returns>
        Task<List<OpcionCatalogoSeguroCalidad>> ObtenerCatalogoCoberturaAfectadaAsync();

        /// <summary>
        /// Obtiene el catalogo de clasificacion de riesos
        /// </summary>
        /// <returns></returns>
        Task<List<OpcionCatalogoSeguroCalidad>> ObtenerCatalogoClasificacioRiesgoAsync();

        /// <summary>
        /// Obtiene una lista de incidencias de acuerdo a los parametros especificados.
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="ordenVerificacion"></param>
        /// <param name="cuv"></param>
        /// <param name="desarrollador"></param>
        /// <param name="claveIncidencia"></param>
        /// <param name="idTipoIncidencia"></param>
        /// <param name="idEstatusIncidencia"></param>
        /// <param name="idClasificacionIncidencia"></param>
        /// <param name="esFechaRegistro"></param>
        /// <param name="esFechaAtencion"></param>
        /// <param name="fechaInicial"></param>
        /// <param name="fechaFinal"></param>
        /// <returns></returns>
        Task<ResultadoPaginado<List<IncidenciaGestion>>> ObtenerIncidenciasGestionAsync(int tamanioPagina, int pagina, string ordenVerificacion = null, string cuv = null, string desarrollador = null, string claveIncidencia = null,
                                                                    string idTipoIncidencia = null, string idEstatusIncidencia = null, string idClasificacionIncidencia = null, bool? esFechaRegistro = null, bool? esFechaAtencion = null,
                                                                    DateTime? fechaInicial = null, DateTime? fechaFinal = null, string idEntidad = null, string idEmpresa = null, string conRiesgo = null);

        /// <summary>
        /// Obtiene una lista de incidencias de acuerdo a los parametros especificados.
        /// </summary>
        /// <param name="ordenVerificacion"></param>
        /// <param name="cuv"></param>
        /// <param name="desarrollador"></param>
        /// <param name="claveIncidencia"></param>
        /// <param name="idTipoIncidencia"></param>
        /// <param name="idEstatusIncidencia"></param>
        /// <param name="idClasificacionIncidencia"></param>
        /// <param name="esFechaRegistro"></param>
        /// <param name="esFechaAtencion"></param>
        /// <param name="fechaInicial"></param>
        /// <param name="fechaFinal"></param>
        /// <returns></returns>
        Task<List<IncidenciaGestion>> ObtenerIncidenciasGestionSinPaginadoAsync(string ordenVerificacion = null, string cuv = null, string desarrollador = null, string claveIncidencia = null,
                                                                    string idTipoIncidencia = null, string idEstatusIncidencia = null, string idClasificacionIncidencia = null, bool? esFechaRegistro = null, bool? esFechaAtencion = null,
                                                                    DateTime? fechaInicial = null, DateTime? fechaFinal = null, string idEntidad = null, string idEmpresa = null, string conRiesgo = null);

        /// <summary>
        /// Obtiene una lista con las claves de las ordenes de verificacion con los parametros especificados
        /// </summary>
        /// <param name="idEntidad"></param>
        /// <param name="idEmpresa"></param>
        /// <returns></returns>
        Task<List<OpcionCatalogoSeguroCalidad>> ObtenerOrdenesVerificacionAsync(string idEntidad, string idEmpresa);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="noRuv"></param>
        /// <param name="razonSocial"></param>
        /// <param name="rfc"></param>
        /// <returns></returns>
        Task<List<OpcionCatalogoSeguroCalidad>> ObtenerOrdenesVerificacionConsultaAsync(string noRuv, string razonSocial, string rfc);

        /// <summary>
        /// Obtiene la informacion del encabezado mostrado en el registro de una nueva incidencia/notificacion
        /// </summary>
        /// <param name="OrdenVerificacion"></param>
        /// <param name="cuv"></param>
        /// <param name="idEntidad"></param>
        /// <param name="idEmpresa"></param>
        /// <returns></returns>
        Task<InformacionOVNuevaincidencia> ObtenerInformacionOVNuevaIncidenciaAsync(string OrdenVerificacion, string cuv, string idEntidad, string idEmpresa);

        /// <summary>
        /// Verifica que una cuv especifica pertenezca a un verificador o bien tenga una relacion comercial aseuradora - desarrollador, dependiendo del perfil del usuario firmado
        /// </summary>
        /// <param name="cuv"></param>
        /// <param name="idEntidad"></param>
        /// <param name="idEmpresa"></param>
        /// <returns></returns>
        Task<CuvValida> ValidarCuvValidaAsync(string cuv, int? idEntidad, int? idEmpresa);


        /// <summary>
        /// Obtiene las cuvs de una OV
        /// </summary>
        /// <param name="cuv"></param>
        /// <param name="ordenVerificacion"></param>
        /// <returns></returns>
        Task<List<CuvSeleccionada>> ObtenerCuvsXOVAsync(string ordenVerificacion, string cuv);

        /// <summary>
        /// Obtiene las cuvs de una OV paginado
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="ordenVerificacion"></param>
        /// <param name="cuv"></param>
        /// <returns></returns>
        Task<ResultadoPaginado<List<CuvSeleccionada>>> ObtenerCuvsXOVPaginadoAsync(int tamanioPagina, int pagina, string ordenVerificacion = null, string cuv = null);

        /// <summary>
        /// Guarda una incidencia / notificacion
        /// </summary>
        /// <param name="incidencia"></param>
        /// <returns></returns>
        Task<bool> GuardarIncidenciaNotificacionAsync(Incidencia incidencia, int idEntidad);

        /// <summary>
        /// Obtiene los documentos que pertenecen a una incidencia
        /// </summary>
        /// <param name="idIncidencia"></param>
        /// <param name="idVvGeneral"></param>
        /// <returns></returns>
        Task<InformacionPreviaNotificacion> ObtenerInformacionPreviaNotificacionAsync(int idIncidencia, int idVvGeneral);

        /// <summary>
        /// Obtiene las cuvs de una OV
        /// </summary>
        /// <param name="cuv"></param>
        /// <param name="ordenVerificacion"></param>
        /// <returns></returns>
        Task<List<CuvSeleccionada>> ObtenerCuvsXOVEvaluacionRiesgosAsync(string ordenVerificacion, string cuv);

        /// <summary>
        /// Obtiene las cuvs de una OV paginado
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="ordenVerificacion"></param>
        /// <param name="cuv"></param>
        /// <returns></returns>
        Task<ResultadoPaginado<List<CuvSeleccionada>>> ObtenerCuvsXOVEvaluacionRiesgosPaginadoAsync(int tamanioPagina, int pagina, string ordenVerificacion = null, string cuv = null);

        /// <summary>
        /// Verifica que una cuv especifica pertenezca a un verificador o bien tenga una relacion comercial aseuradora - desarrollador, dependiendo del perfil del usuario firmado
        /// </summary>
        /// <param name="cuv"></param>
        /// <param name="idEntidad"></param>
        /// <param name="idEmpresa"></param>
        /// <returns></returns>
        Task<CuvValida> ValidarCuvValidaEvaluacionRiesgosAsync(string cuv, int? idEntidad, int? idEmpresa);

        /// <summary>
        /// Guarda la relacion de una evaluacion de riesgo con una ov
        /// </summary>
        /// <param name="ordenVerificacion"></param>
        /// <returns></returns>
        Task<int> GuardarEvaluacionRiesgoAsync(EvaluacionRiesgo evaluacion);

        /// <summary>
        /// Obtiene la informacion general de una incidencia
        /// </summary>
        /// <param name="idIncidencia"></param>
        /// <returns></returns>
        Task<DetalleIncidencia> ObtenerDetalleIncidenciaAsync(int? idIncidencia, int? idVvGeneral, int? idEntidad, int? idEmpresa);

        /// <summary>
        /// Obtiene los datos mostrados en ek registro de la mmitigacion de incidencia
        /// </summary>
        /// <param name="idIncidencia"></param>
        /// <returns></returns>
        Task<Mitigacion> ObtenerDatosMitigacionAsync(int? idIncidencia);

        

        /// <summary>
        /// Guarda una mitigacion de incidencia
        /// </summary>
        /// <param name="mitigacion"></param>
        /// <returns></returns>
        Task<bool> GuardarMitigacionIncidencia(Mitigacion mitigacion, string idEmpresainst);

        /// <summary>
        /// Cierra una incidencia
        /// </summary>
        /// <param name="idIncidencia"></param>
        /// <returns></returns>
        Task<bool> CerrarIncidenciaAsync(int idIncidencia, string ordenVerificacion, int idUsuario, string idEmpresaInst, int idEntidad);

        /// <summary>
        /// Rechaza una mitigacion de incidencia
        /// </summary>
        /// <param name="incidencia"></param>
        /// <returns></returns>
        Task<bool> RechazarMitigacionAsync(Incidencia incidencia);

        #region Ficha Evaluacion

        /// <summary>
        /// Metodo para generar la ficha de pago de la evaluacion de riesgo
        /// </summary>
        /// <param name="fichaPagoRiesgo">Objeto que contiene el id de la empresa, id de la orden de verificacion y los precios de las viviendas que conforman la orden.</param>
        /// <returns></returns>
        Task<bool> GenerarFichaPagoEvaluacion(FichaPagoRiesgo fichaPagoRiesgo);
        #endregion

        #region Seleccion Aseguradora

        /// <summary>
        /// 
        /// </summary>
        /// <param name="ordenVerificacion"></param>
        /// <returns></returns>
        Task<string> ObtenerCostoEvaluacionRiesgo(string ordenVerificacion);


        /// <summary>
        /// 
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="idRuvAsis"></param>
        /// <param name="ordenVerificacion"></param>
        /// <param name="noContrato"></param>
        /// <param name="idEmpresaInstAseguradora"></param>
        /// <param name="idTipoAsignacion"></param>
        /// <param name="razonSocialAseguradora"></param>
        /// <returns></returns>
        Task<ResultadoPaginado<List<OrdenDeVerificacion>>> ObtenerOrdenesDeVerificacionAsync(int tamanioPagina, int pagina, int idRuvAsis, string ordenVerificacion = null, string noContrato = null, string idEmpresaInstAseguradora = null, int? idTipoAsignacion = null, string razonSocialAseguradora = null);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="idRuvAsis"></param>
        /// <returns></returns>
        Task<ResultadoPaginado<List<RelacionComercial>>> ObtenerRelacionesComercialesAsync(int tamanioPagina, int pagina, int idRuvAsis, string idEmpresaInst = null, string nombreRazonSocial = null);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="idOrdenVerificacion"></param>
        /// <param name="idAseguradora"></param>
        /// <param name="idRelacionComercial"></param>
        /// <returns></returns>
        Task<bool> GuardarRelacionesComercialOrdenAsync(string idOrdenVerificacion, int idAseguradora, int idRelacionComercial);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="idOrdenVerificacion"></param>
        /// <returns></returns>
        Task<DetalleSeleccionAseguradora> ObtenerRelacionComercialOrdenAsync(string idOrdenVerificacion);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="idOrdenVerificacion"></param>
        /// <returns></returns>
        Task<List<PrecioViviendas>> ObtenerViviendasOrdenAsync(string idOrdenVerificacion);
        #endregion

        #region Servicio Infonavit

        Task<ResultadoPeticion> GuardarDatosValorAvaluo(DatosValorAvaluo datosValorAvaluo);

        Task<ViviendaAsegurada> ValidarSeguroVivienda(DatosVivienda datosVivienda);

        #endregion


        /// <summary>
        /// 
        /// </summary>
        /// <param name="listaCuvs"></param>
        /// <param name="tipoIncidenciaNotificacion"></param>
        /// <returns></returns>
        Task<string> GenerarDocumentoIncidenciaNotificacionCorreo(List<CuvSeleccionada> listaCuvs, string tipoIncidenciaNotificacion);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="idRuvAsIs"></param>
        /// <returns></returns>
        Task<DatosGeneralesEmpresa> ConsultarDatosGeneralesEmpresa(int idRuvAsIs);



        /// <summary>
        /// Obtiene una lista de incidencias de acuerdo a los parametros especificados.
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="ordenVerificacion"></param>
        /// <param name="cuv"></param>
        /// <param name="desarrollador"></param>
        /// <param name="claveIncidencia"></param>
        /// <param name="idTipoIncidencia"></param>
        /// <param name="idEstatusIncidencia"></param>
        /// <param name="idClasificacionIncidencia"></param>
        /// <param name="esFechaRegistro"></param>
        /// <param name="esFechaAtencion"></param>
        /// <param name="fechaInicial"></param>
        /// <param name="fechaFinal"></param>
        /// <param name="verificador"></param>
        /// <param name="aseguradora"></param>
        /// <returns></returns>
        Task<ResultadoPaginado<List<IncidenciaGestion>>> ObtenerIncidenciasInternoGestionAsync(int tamanioPagina, int pagina, string ordenVerificacion = null, string cuv = null, string desarrollador = null, string claveIncidencia = null,
                                                                  string idTipoIncidencia = null, string idEstatusIncidencia = null, string idClasificacionIncidencia = null, bool? esFechaRegistro = null, bool? esFechaAtencion = null,
                                                                  DateTime? fechaInicial = null, DateTime? fechaFinal = null, string idEntidad = null, string idEmpresa = null, string conRiesgo = null, string verificador = null, string aseguradora = null);


        /// <summary>
        /// Obtiene una lista de incidencias de acuerdo a los parametros especificados.
        /// </summary>
        /// <param name="ordenVerificacion"></param>
        /// <param name="cuv"></param>
        /// <param name="desarrollador"></param>
        /// <param name="claveIncidencia"></param>
        /// <param name="idTipoIncidencia"></param>
        /// <param name="idEstatusIncidencia"></param>
        /// <param name="idClasificacionIncidencia"></param>
        /// <param name="esFechaRegistro"></param>
        /// <param name="esFechaAtencion"></param>
        /// <param name="fechaInicial"></param>
        /// <param name="fechaFinal"></param>
        /// <param name="verificador"></param>
        /// <param name="aseguradora"></param>
        /// <returns></returns>
        Task<List<IncidenciaGestion>> ObtenerIncidenciasInternoGestionSinPaginadoAsync(string ordenVerificacion = null, string cuv = null, string desarrollador = null, string claveIncidencia = null,
                                                                    string idTipoIncidencia = null, string idEstatusIncidencia = null, string idClasificacionIncidencia = null, bool? esFechaRegistro = null, bool? esFechaAtencion = null,
                                                                    DateTime? fechaInicial = null, DateTime? fechaFinal = null, string idEntidad = null, string idEmpresa = null, string conRiesgo = null, string verificador = null, string aseguradora = null);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="envioCorreoConfirmacion"></param>
        /// <returns></returns>
        Task<bool> EnvioCorreoAseguradoraAsync(EnvioCorreoConfirmacion envioCorreoConfirmacion);

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        Task<bool> RecalcularCostoPoliza();
<<<<<<< Updated upstream
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="datosGeneracionPoliza"></param>
        /// <returns></returns>
        Task<bool> GenerarDocumentoPoliza(PeticionSolicitudPoliza peticionSolicitudPoliza);
=======

        Task<ResultadoPaginado<List<PagoDiferenciaOV>>> ObtenerPagoDiferenciaOV(int tamanioPagina, int pagina, string ordenVerificacion);

>>>>>>> Stashed changes
    }
}
