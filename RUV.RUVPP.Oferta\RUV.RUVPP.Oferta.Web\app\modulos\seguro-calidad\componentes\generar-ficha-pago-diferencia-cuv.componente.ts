import { Component, OnInit } from '@angular/core';
import { ServicioDatosSeguroCalidad } from '../datos/seguro-calidad.servicio-datos';
import { ResultadoPeticion } from '../modelo/index';
import { ServicioAlertas } from '../../../../libs/ruvpp-angular2/index';

@Component({
    selector: 'generar-ficha-pago-diferencia-cuv',
    templateUrl: './generar-ficha-pago-diferencia-cuv.componente.html'
})
export class GenerarFichaPagoDiferenciaCuvComponente implements OnInit {

    public cuv: string = '';
    public procesando: boolean = false;
    public resultado: ResultadoPeticion = null;

    constructor(
        private servicioSeguroCalidad: ServicioDatosSeguroCalidad,
        private servicioAlertas: ServicioAlertas
    ) { }

    ngOnInit() {
        this.cuv = '';
        this.procesando = false;
        this.resultado = null;
    }

    public generarFichaPago() {
        if (!this.cuv || this.cuv.trim() === '') {
            this.servicioAlertas.mostrarAlertaError('Error', 'Debe ingresar una CUV válida.');
            return;
        }

        this.procesando = true;
        this.resultado = null;

        this.servicioSeguroCalidad.generarFichaPagoDiferenciaPorCUV(this.cuv.trim())
            .subscribe(
                (respuesta: ResultadoPeticion) => {
                    this.procesando = false;
                    this.resultado = respuesta;
                    
                    if (respuesta.resultado === 'OK') {
                        this.servicioAlertas.mostrarAlertaExito('Éxito', respuesta.descripcion);
                    } else {
                        this.servicioAlertas.mostrarAlertaError('Error', respuesta.descripcion);
                    }
                },
                (error) => {
                    this.procesando = false;
                    this.servicioAlertas.mostrarAlertaError('Error', 'Ocurrió un error al procesar la solicitud.');
                    console.error('Error al generar ficha de pago:', error);
                }
            );
    }

    public limpiar() {
        this.cuv = '';
        this.resultado = null;
    }
}
