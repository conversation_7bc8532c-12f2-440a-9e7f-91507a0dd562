import { Component, OnInit } from '@angular/core';
import { ServicioDatosSeguroCalidad } from '../datos/seguro-calidad.servicio-datos';
import { ResultadoPeticion } from '../modelo/index';
import { ServicioAlertas } from '../../../../libs/ruvpp-angular2/index';

@Component({
    selector: 'generar-ficha-pago-diferencia-cuv',
    templateUrl: './generar-ficha-pago-diferencia-cuv.componente.html'
})
export class GenerarFichaPagoDiferenciaCuvComponente implements OnInit {

    public cuv: string = '';
    public procesando: boolean = false;
    public resultado: ResultadoPeticion = null;

    constructor(
        private servicioSeguroCalidad: ServicioDatosSeguroCalidad,
        private servicioAlertas: ServicioAlertas
    ) { }

    ngOnInit() {
        this.cuv = '';
        this.procesando = false;
        this.resultado = null;
    }

    public generarFichaPago() {
        if (!this.cuv || this.cuv.trim() === '') {
            this.servicioAlertas.mostrar('Error', 'Debe ingresar una CUV válida.', 'error');
            return;
        }

        this.procesando = true;
        this.resultado = null;

        this.servicioSeguroCalidad.generarFichaPagoDiferenciaPorCUV(this.cuv.trim())
            .subscribe(
                (respuesta: ResultadoPeticion) => {
                    this.procesando = false;
                    this.resultado = respuesta;

                    if (respuesta.resultado === 'OK') {
                        this.servicioAlertas.mostrar('Éxito', respuesta.descripcion, 'success');
                    } else {
                        this.servicioAlertas.mostrar('Error', respuesta.descripcion, 'error');
                    }
                },
                (error) => {
                    this.procesando = false;
                    this.servicioAlertas.mostrar('Error', 'Ocurrió un error al procesar la solicitud.', 'error');
                    console.error('Error al generar ficha de pago:', error);
                }
            );
    }

    public limpiar() {
        this.cuv = '';
        this.resultado = null;
    }
}
