﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api
{
    public class PreciosxId
    {
        //public string cuv { get; set; }
        //public decimal costoOrdenVerificacion { get; set; }
        //public bool criterioSinInicioObra { get; set; }
        //public int id { get; set; }
        //public decimal montoAvaluo { get; set; }
        //public decimal precio { get; set; }
        //public decimal precioEvaluacion { get; set; }
        //public decimal costoPoliza { get; set; }
        //public decimal diferenciaOv { get; set; }
        //public bool costoContingencia { get; set; }

        public string cuv { get; set; }

        public int id { get; set; }

        public decimal precio { get; set; }

        public decimal precioEvaluacion { get; set; }

        public decimal costoPoliza { get; set; }

        public decimal costoOrdenVerificacion { get; set; }

        public decimal montoAvaluo { get; set; }

        public bool criterioSinInicioObra { get; set; }

        public decimal diferenciaOv { get; set; }

        public string ordenVerificacion { get; set; }

        public string direccion { get; set; }

        public bool costoContingencia { get; set; }
    }
}
