﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.Mediciones
{
    public class ElementoPuntaje
    {
        public int? estatus { get; set; }
        public string cumple
        {
            get
            {
                switch (estatus)
                {
                    case 0:
                        return "No cumple";
                    case 1:
                        return "Cumple";
                    default:
                        return "Sin calificar";
                }
            }
        }
        public string descripcion { get; set; }
        public float? distancia { get; set; }
        public string id { get; set; }
        public float? puntaje { get; set; }
        public string rango { get; set; }
    }
}
