﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace RUV.RUVPP.Oferta.MigracionProyectos.Modelo
{
    using System;
    using System.Data.Entity;
    using System.Data.Entity.Infrastructure;
    
    public partial class ModeloOferta : DbContext
    {
        public ModeloOferta()
            : base("name=ModeloOferta")
        {
        }
    
        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            throw new UnintentionalCodeFirstException();
        }
    
        public virtual DbSet<DomicilioCamino> DomicilioCamino { get; set; }
        public virtual DbSet<DomicilioCarretera> DomicilioCarretera { get; set; }
        public virtual DbSet<DomicilioGeografico> DomicilioGeografico { get; set; }
        public virtual DbSet<DirectorResponsableObra> DirectorResponsableObra { get; set; }
        public virtual DbSet<DocumentoxProyecto> DocumentoxProyecto { get; set; }
        public virtual DbSet<PromotorExterno> PromotorExterno { get; set; }
        public virtual DbSet<PropietarioTerreno> PropietarioTerreno { get; set; }
        public virtual DbSet<Proyecto> Proyecto { get; set; }
        public virtual DbSet<ProyectoxRiesgoOferta> ProyectoxRiesgoOferta { get; set; }
        public virtual DbSet<Vivienda> Vivienda { get; set; }
        public virtual DbSet<DetalleProyecto> DetalleProyecto { get; set; }
    }
}
