﻿using RUV.RUVPP.Oferta.Modelo.Proyectos.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.Proyectos.Api
{
    public class ComparadorViviendas : IEqualityComparer<Vivienda>
    {
        public bool Equals(Vivienda x, Vivienda y)
        {
            //Verifica si los objetos comparados referencian el mismo dato.
            if (Object.ReferenceEquals(x, y)) return true;

            //Verifica si uno de los objetos es nulo.
            if (Object.ReferenceEquals(x, null) || Object.ReferenceEquals(y, null))
                return false;

            //Verifica si el id vivienda es igual.
            return x.featID == y.featID;
        }

        public int GetHashCode(Vivienda vivienda)
        {
            //Verifica si el objeto es nulo
            if (Object.ReferenceEquals(vivienda, null)) return 0;

            //Obtiene el codigo hash del idvivienda
            int? hashIdVivienda = vivienda.featID == null ? 0 : vivienda.featID.GetHashCode();
            
            //Calculate the hash code for the product.
            return hashIdVivienda.Value;
        }
    }
}
