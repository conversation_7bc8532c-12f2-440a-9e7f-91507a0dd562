﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.Oferta.Data
{
    /// <summary>
    /// Clase que modela un elemento del catalogo de estatus vivienda
    /// </summary>
    public class EstatusVivienda
    {
        /// <summary>
        /// Identificador del estatus de la vivienda
        /// </summary>
        public int IdEstatusVivienda { get; set; }
        /// <summary>
        /// Nombre del estatus
        /// </summary>
        public string Nombre { get; set; }
        /// <summary>
        /// Descripcion del estatus
        /// </summary>
        public string Descripcion { get; set; }
    }
}
