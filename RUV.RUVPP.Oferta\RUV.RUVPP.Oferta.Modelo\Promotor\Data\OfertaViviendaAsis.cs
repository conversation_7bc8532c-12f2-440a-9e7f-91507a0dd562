﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.Promotor.Data
{
    public class OfertaViviendaAsis
    {
        public string idOfertaVivienda { get; set; }
        public decimal? idOferente { get; set; }
        public string estado { get; set; }
        public string municipio { get; set; }
        public string nombreFrente { get; set; }
        public string codigoPostal { get; set; }
        public string colonia { get; set; }
        public string otraColonia { get; set; }
        public string numeroExterior { get; set; }
        public string calle { get; set; }
        public float? latitud { get; set; }
        public float? longitud { get; set; }
        public int? altitud { get; set; }
        public int? idDomicilioINEGI { get; set; }
        public int? estatusOfertaVivienda { get; set; }

    }
}
