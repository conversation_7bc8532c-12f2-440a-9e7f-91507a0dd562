<?xml version="1.0" encoding="utf-8"?>
<edmx:Edmx Version="3.0" xmlns:edmx="http://schemas.microsoft.com/ado/2009/11/edmx">
 <!-- EF Designer content (DO NOT EDIT MANUALLY BELOW HERE) -->
  <edmx:Designer xmlns="http://schemas.microsoft.com/ado/2009/11/edmx">
    <!-- Diagram content (shape and connector positions) -->
    <edmx:Diagrams>
      <Diagram DiagramId="409ba16c9c434999a2fa3267bc501f89" Name="Diagram1">
        <EntityTypeShape EntityType="ruvPlusDesarrolloModel.DomicilioCamino" Width="1.5" PointX="8.25" PointY="17.625" IsExpanded="true" />
        <EntityTypeShape EntityType="ruvPlusDesarrolloModel.DomicilioCarretera" Width="1.5" PointX="8.25" PointY="21" IsExpanded="true" />
        <EntityTypeShape EntityType="ruvPlusDesarrolloModel.DomicilioGeografico" Width="1.5" PointX="6" PointY="16" IsExpanded="true" />
        <EntityTypeShape EntityType="ruvPlusDesarrolloModel.DirectorResponsableObra" Width="1.875" PointX="0.5" PointY="6.875" IsExpanded="true" />
        <EntityTypeShape EntityType="ruvPlusDesarrolloModel.DocumentoxProyecto" Width="3.375" PointX="7.875" PointY="2" IsExpanded="true" />
        <EntityTypeShape EntityType="ruvPlusDesarrolloModel.PromotorExterno" Width="1.5" PointX="0.75" PointY="2.75" IsExpanded="true" />
        <EntityTypeShape EntityType="ruvPlusDesarrolloModel.PropietarioTerreno" Width="2.125" PointX="0.75" PointY="13.125" IsExpanded="true" />
        <EntityTypeShape EntityType="ruvPlusDesarrolloModel.Proyecto" Width="2" PointX="3" PointY="7.875" IsExpanded="true" />
        <EntityTypeShape EntityType="ruvPlusDesarrolloModel.ProyectoxRiesgoOferta" Width="2.625" PointX="4.875" PointY="4.25" IsExpanded="true" />
        <EntityTypeShape EntityType="ruvPlusDesarrolloModel.Vivienda" Width="1.5" PointX="8.25" PointY="6.375" IsExpanded="true" />
        <EntityTypeShape EntityType="ruvPlusDesarrolloModel.DetalleProyecto" Width="1.875" PointX="6.25" PointY="10.25" IsExpanded="true" />
        <AssociationConnector Association="ruvPlusDesarrolloModel.fk_dirINEGI_DomicilioCamino_DomicilioGeografico" ManuallyRouted="false" />
        <AssociationConnector Association="ruvPlusDesarrolloModel.fk_dirINEGI_DomicilioCarretera_DomicilioGeografico" ManuallyRouted="false" />
        <AssociationConnector Association="ruvPlusDesarrolloModel.FK_dirINEGI_DomicilioGeografico_oferta_Vivienda" ManuallyRouted="false" />
        <AssociationConnector Association="ruvPlusDesarrolloModel.FK_oferta_DocumentoxProyecto_Proyecto" ManuallyRouted="false" />
        <AssociationConnector Association="ruvPlusDesarrolloModel.FK_oferta_Proyecto_DetalleProyecto" ManuallyRouted="false" />
        <AssociationConnector Association="ruvPlusDesarrolloModel.FK_oferta_Proyecto_Vivienda" ManuallyRouted="false" />
        <AssociationConnector Association="ruvPlusDesarrolloModel.FK_oferta_ProyectoxRiesgoOferta_Proyecto" ManuallyRouted="false" />
        <AssociationConnector Association="ruvPlusDesarrolloModel.DirectorResponsableObraxProyecto" ManuallyRouted="false" />
        <AssociationConnector Association="ruvPlusDesarrolloModel.PromotorExternoxProyecto" ManuallyRouted="false" />
        <AssociationConnector Association="ruvPlusDesarrolloModel.PropietarioTerrenoxProyecto" ManuallyRouted="false" />
      </Diagram>
    </edmx:Diagrams>
  </edmx:Designer>
</edmx:Edmx>