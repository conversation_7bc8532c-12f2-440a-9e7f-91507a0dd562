﻿using Microsoft.ApplicationInsights;
using RUV.Comun.Web;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;

using System.Web.Http.Description;
using RUV.RUVPP.Negocio.General.Documentos.Abstracta;
using RUV.RUVPP.Oferta.GeneralesInterop.Documentos;
using System.IO;
using RUV.Comun.Negocio.CustomExceptions;
using RUV.Comun.Web.Http;
using RUV.RUVPP.Oferta.Comun.Modelo;
using RUV.RUVPP.Oferta.Modelo.Proyectos.Data;
using RUV.RUVPP.Oferta.Dominio.Empresa;
using RUV.Comun.Seguridad.JWT;
using RUV.RUVPP.Entidades.Empresa;
using RUV.RUVPP.Oferta.Modelo.Oferta.Api;
using System.Web.Http.Results;

namespace RUV.RUVPP.Oferta.Api.Controllers.Empresa
{
    /// <summary>
    /// Expone operaciones relacionadas al registro de empresa.
    /// </summary>
    [RoutePrefix("interno/api/empresa")]
    public class EmpresaController : ApiControllerBase
    {
        #region Campos

        private readonly IServicioEmpresa _servicioEmpresa;
        private readonly IServicioDocumento<DocumentoOferta> _servicioDocumento;


        #endregion

        #region Constructor

        /// <summary>
        /// Constructor de la clase.
        /// </summary>
        /// <param name="clienteTelemetria">Cliente para comunicación con Applications Insights.</param>        
        /// <param name="servicioDocumento">Servicio para manejo de documentos.</param>
        public EmpresaController(IServicioDocumento<DocumentoOferta> servicioDocumento, IServicioEmpresa servicioEmpresa)
            : base()
        {
            this._servicioDocumento = servicioDocumento;
            this._servicioEmpresa = servicioEmpresa;
        }

        #endregion

        #region Acciones
        
        /// <summary>
        /// Carga un documento y lo relaciona a la empresa.
        /// </summary>
        /// <param name="idEmpresa">Id de la empresa.</param>
        /// <param name="idCatalogoDocumento">Tipo del documento.</param>
        /// <param name="carpetaArchivo">Carpeta donde guardar el documento.</param>
        /// <returns></returns>
        [HttpPost, Route("{idEmpresa}/documentos")]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> CargarDocumentos(int idEmpresa, int idCatalogoDocumento, string carpetaArchivo = "")
        {
            var usuario = (CustomUserRuv)User;
            HttpResponseMessage respuesta = null;
            List<DocumentoRuv> documentosCargados = new List<DocumentoRuv>();

            if (!Request.Content.IsMimeMultipartContent())
                respuesta = Request.CreateErrorResponse(HttpStatusCode.UnsupportedMediaType, "La solicitud no tiene contenido válido.");
            else
            {
                if (carpetaArchivo.Last() != '/')
                    carpetaArchivo = carpetaArchivo + "/";

                var multipartData = await Request.Content.ParseMultipartAsync();

                try
                {
                    foreach (var archivoActual in multipartData.Files)
                    {
                        var documento = this._servicioDocumento.AgregarYObtener(new DocumentoOferta
                        {
                            rfcEmpresa = usuario.rfcEmpresa, //TODO: Tomar de la identidad del usuario
                            idCatalogoDocumento = idCatalogoDocumento,
                            carpeta = carpetaArchivo,
                            nombreArchivo = archivoActual.Value.Filename,
                            archivoStream = new MemoryStream(archivoActual.Value.Data),
                        });

                        documentosCargados.Add(new DocumentoRuv
                        {
                            IdDocumento = documento.idDocumento.Value,
                            NombreArchivo = documento.nombreArchivo,
                            UrlArchivo = documento.rutaArchivo
                        });
                    }

                    respuesta = Request.CreateResponse(HttpStatusCode.OK, documentosCargados);
                }
                catch (RuvBusinessException ex)
                {
                    respuesta = Request.CreateResponse(HttpStatusCode.BadRequest, ex.Message);
                }
            }

            return await Task.FromResult(respuesta);
        }


        /// <summary>
        /// Desasocia un documento de una empresa, pero sin borrarlo.
        /// </summary>
        /// <param name="idEmpresa">Id de la empresa.</param>
        /// <param name="idDocumento">Id del documento.</param>
        /// <returns></returns>
        [HttpDelete, Route("{idEmpresa}/documentos/{idDocumento}")]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> EliminarDocumento(int idEmpresa, int idDocumento)
        {
            var usuario = (CustomUserRuv)User;
            HttpResponseMessage respuesta;

            var resultado = this._servicioDocumento.Eliminar(new DocumentoOferta
            {
                rfcEmpresa = usuario.rfcEmpresa, //TODO: Tomar de la identidad del usuario
                idDocumento = idDocumento
            }, false);

            if (resultado == 1)
            {
                respuesta = Request.CreateResponse(HttpStatusCode.OK, true);
            }
            else
            {
                respuesta = Request.CreateResponse(HttpStatusCode.Conflict, false);
            }
            
            return await Task.FromResult(respuesta);
        }

        /// <summary>
        /// Obtiene los datos de la empresa a partir de su ID o Número de Registro RUV.
        /// </summary>
        /// <param name="nrruv">Número de Registro RUV.</param>
        /// <param name="idEmpresa">Id de la empresa.</param>
        /// <returns></returns>
        [HttpGet, Route("nrruv/{nrruv}")]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        [ResponseType(typeof(DatosCPV))]
        public async Task<HttpResponseMessage> ConsultaEmpresaPorNRRUV(string nrruv, int? idEmpresa = null)
        {
            var result = (DatosCPV)(null);

            if (nrruv != null)
                result = await this._servicioEmpresa.ConsultaEmpresaPorNRRUV(idEmpresa, nrruv);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// Obtiene los datos de la empresa a partir de su ID o Número de Registro RUV.
        /// </summary>
        /// <param name="idEmpresa">Id de la empresa.</param>
        /// <returns></returns>
        [HttpGet, Route("obtenercorreos")]
        [ResponseType(typeof(List<string>))]
        public HttpResponseMessage ObtenerCorreos(int idEmpresa)
        {
            List<string> result = this._servicioEmpresa.ConsultaCorreoEmpresa(idEmpresa);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// Obtiene los datos de los contactos a partir del idEmpresa.
        /// </summary>
        /// <param name="idEmpresa">Id de la empresa.</param>
        /// <returns></returns>
        [HttpGet, Route("obtenerdatoscontacto")]
        [ResponseType(typeof(DatosContactoRespuesta))]
        public async Task<HttpResponseMessage> ObtenerDatosContactoEmpresa(string idStrEmpresa)
        {
            DatosContactoRespuesta result = new DatosContactoRespuesta();
            int idEmpresa = 0;

            try
            {
                if (!string.IsNullOrEmpty(idStrEmpresa))
                {
                    idEmpresa = Convert.ToInt32(idStrEmpresa);
                    result = await this._servicioEmpresa.ObtenerContactoUsuariosEmpresa(idEmpresa);
                }
                else
                {
                    result = new DatosContactoRespuesta
                    {
                        codigo = 2,
                        mensaje = "El id de la empresa tiene que ser un valor numérico: " + idEmpresa.ToString()
                    };
                }

            }
            catch (Exception ex)
            {
                result = new DatosContactoRespuesta
                {
                    codigo = 0,
                    mensaje = ex.Message.ToString()
                };

                return Request.CreateResponse(HttpStatusCode.OK, result);
            }

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        #endregion

        #region Disposible

        protected override void Dispose(bool disposing)
        {
            base.Dispose(disposing);

            this._servicioEmpresa.Dispose();
        }

        #endregion
    }
}