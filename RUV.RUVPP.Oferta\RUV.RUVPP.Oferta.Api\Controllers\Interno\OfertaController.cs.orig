﻿using Microsoft.ApplicationInsights;
using Newtonsoft.Json;
using RUV.RUVPP.Entidades.Empresa;
using RUV.RUVPP.Negocio.Empresa.EmpresaConsulta;
using RUV.Comun.Web.Http;
using RUV.RUVPP.Oferta.Comun.Modelo;
using RUV.RUVPP.Oferta.Dominio.Empresa;
using RUV.RUVPP.Oferta.Dominio.Oferta;
using RUV.RUVPP.Oferta.Modelo.Dictaminacion;
using RUV.RUVPP.Oferta.Modelo.Oferta.Api;
using RUV.RUVPP.Oferta.Modelo.Oferta.Data;
using RUV.RUVPP.Oferta.Modelo.Proyectos.Data;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Web.Http;
using System.Web.Http.Description;
using RUV.Comun.Utilerias;
using RUV.RUVPP.Oferta.Modelo.Historico.Data;

namespace RUV.RUVPP.Oferta.Api.Controllers.Interno
{
    [RoutePrefix("interno/api/ofertas")]
    public class OfertaController : ApiControllerBase
    {
        private readonly IServicioOferta _servicioOferta;
        private readonly IServicioEmpresaConsulta _servicioEmpresaConsulta;
        private readonly IServicioEmpresa _servicioEmpresa;

        /// <summary>
        /// Constructor del Controlador
        /// </summary>
        /// <param name="clienteTelemetria">Instancia de Cliente Telemetria</param>
        /// <param name="_servicioOferta">Instancia de Servicio Oferta</param>
        public OfertaController(TelemetryClient clienteTelemetria, IServicioOferta _servicioOferta,
            IServicioEmpresaConsulta servicioEmpresaConsulta, IServicioEmpresa servicioEmpresa)
            : base(clienteTelemetria)
        {
            this._servicioOferta = _servicioOferta;
            this._servicioEmpresaConsulta = servicioEmpresaConsulta;
            this._servicioEmpresa = servicioEmpresa;
        }

        [HttpGet, Route("proyectos/{idEmpresa}")]
        public async Task<HttpResponseMessage> ObtenerProyectosEmpresaAsync(int idEmpresa)
        {
            Dictionary<int, string> proyectos = new Dictionary<int, string>();
            proyectos = await this._servicioOferta.ObtenerProyectosEmpresaAsync(idEmpresa);

            return Request.CreateResponse(HttpStatusCode.OK, proyectos);
        }

        [HttpGet, Route("viviendasprototipos/{idProyecto}")]
        [ResponseType(typeof(List<ViviendaPrototipo>))]
        public async Task<HttpResponseMessage> ObtenerViviendasPrototiposProyectoAsync(int idProyecto, int? idOferta = null)
        {
            var result = await this._servicioOferta.ObtenerViviendasPrototiposAsync(idProyecto, idOferta);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("viviendas")]
        [ResponseType(typeof(ResultadoPaginado<ViviendaSinAsignar>))]
        public async Task<HttpResponseMessage> ObtenerViviendasSinAsignarOfertaAsync(int idProyecto, int idEstatusOferta, int tamanioPagina, int pagina, int? idOferta = null)
        {
            var result = await this._servicioOferta.ObtenerViviendasSinAsingarOfertaAsync(idProyecto, idEstatusOferta, tamanioPagina, pagina, idOferta);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpPost, Route("")]
        public async Task<HttpResponseMessage> GuardarOfertaAsync(Modelo.Oferta.Api.Oferta oferta)
        {
            var result = oferta.ofertaVivienda.idOfertaVivienda > 0 ?
            await this._servicioOferta.ActualizarOfertaAsync(oferta.ofertaVivienda, JsonConvert.SerializeObject(oferta)) :
            await this._servicioOferta.GuardarOfertaAsync(oferta.ofertaVivienda.idProyecto, oferta.ofertaVivienda.nombreFrente, JsonConvert.SerializeObject(oferta));

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpPost, Route("detalle")]
        public async Task<HttpResponseMessage> GuardarOfertaDetalleAsync(Modelo.Oferta.Api.Oferta oferta)
        {
            bool existeProyectoDetalle = await this._servicioOferta.ObtenerOfertaDetalleAsync(oferta.ofertaVivienda.idOfertaVivienda) != null;

            await this._servicioOferta.EliminarOfertaParcialAsync(oferta.ofertaVivienda.idOfertaVivienda);

            var result = existeProyectoDetalle ?
                await this._servicioOferta.ActualizarOfertaDetalleAsync(oferta) :
                await this._servicioOferta.GuardarOfertaDetalleAsync(oferta);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("idParcialOferta/{idParcialOferta}")]
        [ResponseType(typeof(bool))]
        public async Task<HttpResponseMessage> EliminarOfertaAsync(int idParcialOferta)
        {
            await this._servicioOferta.EliminarOfertaParcialAsync(idParcialOferta);

            return Request.CreateResponse(HttpStatusCode.OK, true);
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="idOferta"></param>
        /// <returns></returns>
        [HttpGet, Route("ofertas/{idOferta}")]
        [ResponseType(typeof(ResultadoPaginado<Modelo.Oferta.Api.Oferta>))]
        public async Task<HttpResponseMessage> ObtenerOfertaPorIdAsync(int idOferta)
        {
            var result = await this._servicioOferta.ObtenerOfertaPorIdAsync(idOferta);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="idOferta"></param>
        /// <param name="nombreProyecto"></param>
        /// <param name="idEmpresa"></param>
        /// <returns></returns>
        [HttpGet, Route("ofertas/tamanioPagina/{tamanioPagina}/pagina/{pagina}")]
        [ResponseType(typeof(ResultadoPaginado<List<Modelo.Oferta.Api.Oferta>>))]
        public async Task<HttpResponseMessage> ObtenerOfertaFiltradoAsync(int tamanioPagina, int pagina, int? idOferta = null, string nombreProyecto = null, int? idEmpresa = null)
        {
            var result = await this._servicioOferta.ObtenerOfertasFiltradoAsync(tamanioPagina, pagina, idOferta, nombreProyecto, idEmpresa);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("ofertas/proyecto/{idproyecto}")]
        [ResponseType(typeof(List<Modelo.Oferta.Api.Oferta>))]
        public async Task<HttpResponseMessage> ObtenerOfertasxProyectoAsync(int idProyecto)
        {
            var result = await this._servicioOferta.ObtenerOfertasxProyectoAsync(idProyecto);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("{idOferta}/datosGenerales")]
        [ResponseType(typeof(ResultadoPaginado<Modelo.Oferta.Api.Oferta>))]
        public async Task<HttpResponseMessage> ObtenerDatosGeneralesAsync(int idOferta)
        {
            var result = await this._servicioOferta.ObtenerDatosGeneralesAsync(idOferta);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("{idOferta}")]
        [ResponseType(typeof(ResultadoPaginado<Modelo.Oferta.Api.Oferta>))]
        public async Task<HttpResponseMessage> ObtenerOfertaAsync(int idOferta)
        {
            var result = await this._servicioOferta.ObtenerOfertaAsync(idOferta);

            if (!string.IsNullOrEmpty(result.ofertaVivienda.temporalJSON))
            {
                var id = result.ofertaVivienda.idOfertaVivienda;
                var idEstatus = result.ofertaVivienda.idEstatusOfertaVivienda;
                result = JsonConvert.DeserializeObject<Modelo.Oferta.Api.Oferta>(result.ofertaVivienda.temporalJSON);
                result.idOferta= id;
                result.ofertaVivienda.idOfertaVivienda = id;
                result.ofertaVivienda.idEstatusOfertaVivienda = idEstatus;
            }            

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("proyecto/{idProyecto}")]
        [ResponseType(typeof(ResultadoPaginado<Modelo.Oferta.Api.Oferta>))]
        public async Task<HttpResponseMessage> ObtenerOfertaPorProyectoAsync(int idProyecto)
        {
            var result = await this._servicioOferta.ObtenerOfertaxProyectoAsync(idProyecto);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("catalogos/prototipos")]
        [ResponseType(typeof(Modelo.Oferta.Data.PrototipoCatalogo))]
        public async Task<HttpResponseMessage> ObtenerPrototiposAsync()
        {
            var resultado = await this._servicioOferta.ObtenerCatalogoPrototipoAsyn();
            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }

        /// <summary>
        ///
        /// </summary>
        /// <returns></returns>
        [HttpGet, Route("catalogos/documentoscomplementarios")]
        [ResponseType(typeof(Modelo.Oferta.Data.Documento))]
        public async Task<HttpResponseMessage> ObtenerDocumentosComplementariosAsync()
        {
            var resultado = await this._servicioOferta.ObtenerCatalogoDocumentosAsyn();
            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }

<<<<<<< HEAD
        [HttpGet, Route("{idOferta}/viviendas")]
        [ResponseType(typeof(List<Vivienda>))]
        public async Task<HttpResponseMessage> ObtenerViviendasPorOfertaViviendaAsync(int idOferta)
        {
            var resultado = await this._servicioOferta.ObtenerViviendasPorOfertaVivienda(idOferta);
            return Request.CreateResponse(HttpStatusCode.OK, resultado);
        }

=======
        /// <summary>
        /// Obtiene el historico de validaciones(lista de ordenes de trabajo de una oferta con paginacion)
        /// </summary>
        /// <param name="idOfertaVivienda">Identificador de la oferta</param>
        /// <param name="tamanioPagina">Tamaño de pagina</param>
        /// <param name="pagina">Pagina consultada</param>
        /// <returns></returns>
        [HttpGet, Route("{idOfertaVivienda}/ordenes-trabajo")]
        [ResponseType(typeof(ResultadoPaginado<List<OrdenTrabajoOferta>>))]
        public async Task<HttpResponseMessage> ObtenerOrdenesTrabajoPorOfertaPaginadoAsync(int idOfertaVivienda, int tamanioPagina, int pagina)
        {
            var result = await this._servicioOferta.ObtenerOrdenesTrabajoPorOfertaPaginadoAsync(idOfertaVivienda, tamanioPagina, pagina);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }


>>>>>>> b31489300476573d134fc4a8438001685ad8b685
        #region DictaminarOferta

        [HttpGet, Route("{idRegistro}/Empresa")]
        [ResponseType(typeof(DatosCPV))]
        public async Task<HttpResponseMessage> ObtenerDatosEmpresa(int idRegistro)
        {
            Oferta.Modelo.Oferta.Api.Oferta oferta = await this._servicioOferta.ObtenerOfertaPorIdAsync(idRegistro);
            EmpresaDto empresa = this._servicioEmpresaConsulta.ObtenerEmpresaDto(oferta.idEmpresa.Value);
            DatosCPV datosEmpresa = await this._servicioEmpresa.ConsultaEmpresaPorNRRUV(empresa.idEmpresa, empresa.IdEmpresaInst);
            return Request.CreateResponse(HttpStatusCode.OK, datosEmpresa);
        }

        [HttpPost, Route("{idOferta}/dictaminaciones")]
        [ResponseType(typeof(bool))]
        public async Task<HttpResponseMessage> EnviarDictaminacionOfertaVivienda(int idOferta, int idOrdenTrabajo, short idServicio, [FromBody]ParametroBodyDictaminacion parametroBodyDictaminacion)
        {
            var result = await this._servicioOferta.EnviarDictaminacionOfertaVivienda(idOrdenTrabajo, idServicio, idOferta, parametroBodyDictaminacion);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }
        #endregion

        #region Pago Oferta

        /// <summary>
        /// Metodo para confirmar el pago en SAP una vez que éste sea pagado
        /// </summary>
        /// <param name="idOfertaVivienda">id de la oferta de vivienda</param>
        /// <returns>Regresa true si el proceso se ejecuta correctamente</returns>
        [HttpPost, Route("fichapago/{idOferta}")]
        [ResponseType(typeof(ResultadoPaginado<Modelo.Oferta.Api.Oferta>))]
        public async Task<HttpResponseMessage> ConfirmarPagoPorIdAsync(int idOfertaVivienda)
        {
            var result = await this._servicioOferta.ConfirmarPagoPorIdAsync(idOfertaVivienda);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        ///Metodo para calcular el monto de las viviendas seleccionadas
        /// </summary>
        /// <param name="viviendasProyecto">Viviendas del proyecto</param>
        /// /// <param name="idProyecto">Id del proyecto donde se va a generar la oferta de vivienda</param>
        /// <returns>Regresa el monto total de la oferta</returns>
        [HttpPost, Route("{idProyecto}/montooferta")]
        [ResponseType(typeof(ResultadoPaginado<Modelo.Oferta.Api.Oferta>))]
        public async Task<HttpResponseMessage> ObtenerMontoAsync(int idProyecto, List<ViviendaPrototipo> viviendasProyecto)
        {
            var result = await this._servicioOferta.ObtenerMontoOfertaAsync(viviendasProyecto, idProyecto);
            return Request.CreateResponse(HttpStatusCode.OK, result);
        }
        #endregion

        #region Disposible

        protected override void Dispose(bool disposing)
        {
            base.Dispose(disposing);

            this._servicioEmpresa.Dispose();
            this._servicioOferta.Dispose();
        }

        #endregion
    }
}