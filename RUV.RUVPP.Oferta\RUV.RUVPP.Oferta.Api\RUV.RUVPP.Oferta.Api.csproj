﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.1.0.2\build\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props" Condition="Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.1.0.2\build\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.Default.props" Condition="Exists('$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.Default.props')" />
  <Import Project="..\packages\Microsoft.Net.Compilers.1.3.2\build\Microsoft.Net.Compilers.props" Condition="Exists('..\packages\Microsoft.Net.Compilers.1.3.2\build\Microsoft.Net.Compilers.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{F147AFC4-A07A-4EAB-A02D-2C0684F7A129}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>RUV.RUVPP.Oferta.Api</RootNamespace>
    <AssemblyName>RUV.RUVPP.Oferta.Api</AssemblyName>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <MvcBuildViews>false</MvcBuildViews>
    <UseIISExpress>true</UseIISExpress>
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <ApplicationInsightsResourceId>/subscriptions/01a044c6-80ea-47db-9eab-723e72c7d884/resourcegroups/PocOferta/providers/microsoft.insights/components/ruvplanooferta</ApplicationInsightsResourceId>
    <TypeScriptToolsVersion>2.0</TypeScriptToolsVersion>
    <Use64BitIISExpress />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <DocumentationFile>bin\RUV.RUVPP.Oferta.Api.XML</DocumentationFile>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <DocumentationFile>bin\RUV.RUVPP.Oferta.Api.XML</DocumentationFile>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Antlr3.Runtime, Version=*******, Culture=neutral, PublicKeyToken=eb42632606e9261f, processorArchitecture=MSIL">
      <HintPath>..\packages\Antlr.*******\lib\Antlr3.Runtime.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Dapper, Version=1.50.2.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Dapper.1.50.2\lib\net451\Dapper.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.RichEdit.v12.2.Core, Version=12.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.Comun.Web.2.6.2.3\lib\net452\DevExpress.RichEdit.v12.2.Core.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="itextsharp, Version=5.5.13.1, Culture=neutral, PublicKeyToken=8354ae6d2174ddca, processorArchitecture=MSIL">
      <HintPath>..\packages\iTextSharp.5.5.13.1\lib\itextsharp.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.AI.Agent.Intercept, Version=2.0.7.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.Agent.Intercept.2.0.7\lib\net45\Microsoft.AI.Agent.Intercept.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.AI.DependencyCollector, Version=2.2.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.DependencyCollector.2.2.0\lib\net45\Microsoft.AI.DependencyCollector.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.AI.PerfCounterCollector, Version=2.2.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.PerfCounterCollector.2.2.0\lib\net45\Microsoft.AI.PerfCounterCollector.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.AI.ServerTelemetryChannel, Version=2.2.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel.2.2.0\lib\net45\Microsoft.AI.ServerTelemetryChannel.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.AI.Web, Version=2.2.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.Web.2.2.0\lib\net45\Microsoft.AI.Web.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.AI.WindowsServer, Version=2.2.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.WindowsServer.2.2.0\lib\net45\Microsoft.AI.WindowsServer.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.ApplicationInsights, Version=2.2.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.2.2.0\lib\net45\Microsoft.ApplicationInsights.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.ApplicationInsights.PersistenceChannel, Version=1.2.3.490, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.PersistenceChannel.1.2.3\lib\net45\Microsoft.ApplicationInsights.PersistenceChannel.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.ApplicationInsights.TraceListener, Version=2.2.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.TraceListener.2.2.0\lib\net45\Microsoft.ApplicationInsights.TraceListener.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Azure.KeyVault.Core, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Azure.KeyVault.Core.1.0.0\lib\net40\Microsoft.Azure.KeyVault.Core.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=1.0.2.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.1.0.2\lib\net45\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.Data.Edm, Version=5.6.4.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Data.Edm.5.6.4\lib\net40\Microsoft.Data.Edm.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Data.OData, Version=5.6.4.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Data.OData.5.6.4\lib\net40\Microsoft.Data.OData.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Data.Services.Client, Version=5.6.4.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Data.Services.Client.5.6.4\lib\net40\Microsoft.Data.Services.Client.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Diagnostics.Tracing.EventSource, Version=1.1.28.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Diagnostics.Tracing.EventSource.Redist.1.1.28\lib\net40\Microsoft.Diagnostics.Tracing.EventSource.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Protocol.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Protocol.Extensions.1.0.0\lib\net45\Microsoft.IdentityModel.Protocol.Extensions.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Owin.Cors, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Cors.3.0.0\lib\net45\Microsoft.Owin.Cors.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.Jwt, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.Jwt.3.0.0\lib\net45\Microsoft.Owin.Security.Jwt.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.OpenIdConnect, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.OpenIdConnect.3.0.0\lib\net45\Microsoft.Owin.Security.OpenIdConnect.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Practices.EnterpriseLibrary.TransientFaultHandling, Version=6.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\EnterpriseLibrary.TransientFaultHandling.6.0.1304.0\lib\portable-net45+win+wp8\Microsoft.Practices.EnterpriseLibrary.TransientFaultHandling.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Practices.EnterpriseLibrary.TransientFaultHandling.Data, Version=6.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\EnterpriseLibrary.TransientFaultHandling.Data.6.0.1304.1\lib\NET45\Microsoft.Practices.EnterpriseLibrary.TransientFaultHandling.Data.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Practices.ServiceLocation, Version=1.3.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\CommonServiceLocator.1.3\lib\portable-net4+sl5+netcore45+wpa81+wp8\Microsoft.Practices.ServiceLocation.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Practices.Unity, Version=4.0.0.0, Culture=neutral, PublicKeyToken=6d32ff45e0ccc69f, processorArchitecture=MSIL">
      <HintPath>..\packages\Unity.4.0.1\lib\net45\Microsoft.Practices.Unity.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Practices.Unity.Configuration, Version=4.0.0.0, Culture=neutral, PublicKeyToken=6d32ff45e0ccc69f, processorArchitecture=MSIL">
      <HintPath>..\packages\Unity.4.0.1\lib\net45\Microsoft.Practices.Unity.Configuration.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Practices.Unity.Interception, Version=4.0.0.0, Culture=neutral, PublicKeyToken=6d32ff45e0ccc69f, processorArchitecture=MSIL">
      <HintPath>..\packages\Unity.Interception.4.0.1\lib\Net45\Microsoft.Practices.Unity.Interception.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Practices.Unity.Interception.Configuration, Version=4.0.0.0, Culture=neutral, PublicKeyToken=6d32ff45e0ccc69f, processorArchitecture=MSIL">
      <HintPath>..\packages\Unity.Interception.4.0.1\lib\Net45\Microsoft.Practices.Unity.Interception.Configuration.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Practices.Unity.RegistrationByConvention, Version=4.0.0.0, Culture=neutral, PublicKeyToken=6d32ff45e0ccc69f, processorArchitecture=MSIL">
      <HintPath>..\packages\Unity.4.0.1\lib\net45\Microsoft.Practices.Unity.RegistrationByConvention.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.ReportViewer.WebForms, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Microsoft.ReportViewer.11.0.3366.16\lib\Microsoft.ReportViewer.WebForms.DLL</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Types, Version=14.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.Types.14.0.1016.290\lib\net40\Microsoft.SqlServer.Types.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Threading.Tasks, Version=1.0.12.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.Async.1.0.168\lib\net40\Microsoft.Threading.Tasks.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Threading.Tasks.Extensions, Version=1.0.12.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.Async.1.0.168\lib\net40\Microsoft.Threading.Tasks.Extensions.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Threading.Tasks.Extensions.Desktop, Version=1.0.168.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.Async.1.0.168\lib\net40\Microsoft.Threading.Tasks.Extensions.Desktop.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.Configuration, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.WindowsAzure.ConfigurationManager.3.2.1\lib\net40\Microsoft.WindowsAzure.Configuration.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.Storage, Version=7.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\WindowsAzure.Storage.7.0.0\lib\net40\Microsoft.WindowsAzure.Storage.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=9.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.9.0.1\lib\net45\Newtonsoft.Json.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Oracle.ManagedDataAccess, Version=4.121.2.0, Culture=neutral, PublicKeyToken=89b483f429c47342, processorArchitecture=MSIL">
      <HintPath>..\packages\Oracle.ManagedDataAccess.12.1.24160719\lib\net40\Oracle.ManagedDataAccess.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RestSharp, Version=10*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RestSharp.105.2.3\lib\net452\RestSharp.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.Comun, Version=2.6.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.Comun.2.6.0\lib\net452\RUV.Comun.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.Comun.Datos, Version=2.6.2.1, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.Comun.Datos.2.6.2.1\lib\net452\RUV.Comun.Datos.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.Comun.Negocio, Version=2.6.0.2, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.Comun.Negocio.2.6.0.2\lib\net452\RUV.Comun.Negocio.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.Comun.Servicios, Version=2.6.1.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.Comun.Servicios.2.6.1\lib\net452\RUV.Comun.Servicios.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.Comun.Utilerias, Version=2.6.2.4, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.Comun.Utilerias.2.6.2.4\lib\net452\RUV.Comun.Utilerias.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.Comun.Web, Version=2.6.2.3, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.Comun.Web.2.6.2.3\lib\net452\RUV.Comun.Web.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.Integracion.Entidades.SAP, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.Empresa.2.5.0.14\lib\net452\RUV.Integracion.Entidades.SAP.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.Integracion.Procesos.SAP, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.Empresa.2.5.0.14\lib\net452\RUV.Integracion.Procesos.SAP.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.Ayuntamiento, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.Empresa.2.5.0.14\lib\net452\RUV.RUVPP.Datos.Ayuntamiento.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.Ayuntamiento.Historico, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.Empresa.2.5.0.14\lib\net452\RUV.RUVPP.Datos.Ayuntamiento.Historico.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.Ayuntamiento.SqlAzure, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.Empresa.2.5.0.14\lib\net452\RUV.RUVPP.Datos.Ayuntamiento.SqlAzure.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.Comun.Contratos, Version=2.5.0.7, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Datos.Comun.Contratos.2.5.0.7\lib\net452\RUV.RUVPP.Datos.Comun.Contratos.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.Comun.Historico, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Datos.Comun.Historico.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.Comun.SqlAzure, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Datos.Comun.SqlAzure.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.Empresa, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Datos.Empresa.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.Empresa.SqlAzure, Version=2.5.0.5, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\RUV.RUVPP.Datos.Empresa.SqlAzure.2.5.0.7\lib\net452\RUV.RUVPP.Datos.Empresa.SqlAzure.dll</HintPath>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.Entorno, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Datos.Entorno.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.Entorno.SqlAzure, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Datos.Entorno.SqlAzure.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.General, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Datos.General.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.General.SqlAzure, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Datos.General.SqlAzure.dll</HintPath>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.Oferta, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Datos.Oferta.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.Oferta.Historico, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Datos.Oferta.Historico.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.Oferta.SqlAzure, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Datos.Oferta.SqlAzure.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.SAP, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.Empresa.2.5.0.14\lib\net452\RUV.RUVPP.Datos.SAP.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.SAP.SqlAzure, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.Empresa.2.5.0.14\lib\net452\RUV.RUVPP.Datos.SAP.SqlAzure.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.Ubicador, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Datos.Ubicador.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Datos.Ubicador.SqlAzure, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Entidades.Empresa.2.5.0.11\lib\net452\RUV.RUVPP.Datos.Ubicador.SqlAzure.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Entidades.Ayuntamiento, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Entidades.Ayuntamiento.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Entidades.Comun, Version=1.0.0.15, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Entidades.Comun.1.0.0.15\lib\net452\RUV.RUVPP.Entidades.Comun.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Entidades.Empresa, Version=2.5.0.12, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\RUV.RUVPP.Entidades.Empresa.2.5.0.11\lib\net452\RUV.RUVPP.Entidades.Empresa.dll</HintPath>
    </Reference>
    <Reference Include="RUV.RUVPP.Entidades.Entorno, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Entidades.Entorno.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Entidades.General.Bam, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Entidades.General.Bam.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Entidades.General.Documentos, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Entidades.General.Documentos.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Entidades.General.Indicadores, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Entidades.General.Indicadores.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Entidades.General.Notificaciones, Version=2.5.1.33, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Entidades.General.Notificaciones.2.5.1.33\lib\net452\RUV.RUVPP.Entidades.General.Notificaciones.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Entidades.General.OrdenServicio, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Entidades.General.OrdenServicio.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Entidades.General.Seguridad, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Entidades.General.Seguridad.dll</HintPath>
    </Reference>
    <Reference Include="RUV.RUVPP.Entidades.General.Tareas, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Entidades.General.Tareas.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Entidades.General.Tarificador, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Entidades.General.Tarificador.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Entidades.General.Tutoriales, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Entidades.General.Tutoriales.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Entidades.Oferta, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Entidades.Oferta.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Entidades.SAP, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Entidades.SAP.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Entidades.Ubicador, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Entidades.Ubicador.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Generales.Datos, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Generales.Domino.1.0.0.37\lib\net452\RUV.RUVPP.Generales.Datos.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Generales.Domino, Version=1.0.0.37, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Generales.Domino.1.0.0.37\lib\net452\RUV.RUVPP.Generales.Domino.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Generales.Interop, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Generales.Domino.1.0.0.37\lib\net452\RUV.RUVPP.Generales.Interop.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Generales.Modelo, Version=1.0.0.1, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Generales.Domino.1.0.0.37\lib\net452\RUV.RUVPP.Generales.Modelo.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Integracion.Procesos.Empresa, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.Empresa.2.5.0.14\lib\net452\RUV.RUVPP.Integracion.Procesos.Empresa.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Negocio.Comun.ProductosServicios, Version=2.5.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Negocio.Comun.ProductosServicios.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Negocio.Empresa, Version=2.5.0.14, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.Empresa.2.5.0.14\lib\net452\RUV.RUVPP.Negocio.Empresa.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Negocio.General.Bam, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.Empresa.2.5.0.14\lib\net452\RUV.RUVPP.Negocio.General.Bam.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Negocio.General.Documentos, Version=2.5.2.1, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Documentos.2.5.2.1\lib\net452\RUV.RUVPP.Negocio.General.Documentos.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Negocio.General.Notificaciones, Version=2.5.1.23, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Notificaciones.2.5.1.23\lib\net452\RUV.RUVPP.Negocio.General.Notificaciones.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Negocio.General.OrdenServicio, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.Empresa.2.5.0.14\lib\net452\RUV.RUVPP.Negocio.General.OrdenServicio.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Negocio.General.OrdenTrabajo, Version=2.5.0.7, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.OrdenTrabajo.2.5.0.7\lib\net452\RUV.RUVPP.Negocio.General.OrdenTrabajo.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Negocio.General.Seguridad, Version=2.5.0.15, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Seguridad.2.5.0.15\lib\net452\RUV.RUVPP.Negocio.General.Seguridad.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RUV.RUVPP.Negocio.General.Tarificador, Version=2.5.2.77, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\RUV.RUVPP.Negocio.General.Tarificador.2.5.2.77\lib\net452\RUV.RUVPP.Negocio.General.Tarificador.dll</HintPath>
    </Reference>
    <Reference Include="StackExchange.Redis, Version=1.0.316.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\StackExchange.Redis.1.0.394\lib\net45\StackExchange.Redis.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Swashbuckle.Core, Version=*******, Culture=neutral, PublicKeyToken=cd1bb07a5ac7c7bc, processorArchitecture=MSIL">
      <HintPath>..\packages\Swashbuckle.Core.5.5.3\lib\net40\Swashbuckle.Core.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Configuration.Abstractions, Version=2.0.2.26, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Configuration.Abstractions.2.0.2.26\lib\net45\System.Configuration.Abstractions.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.IdentityModel" />
    <Reference Include="System.IdentityModel.Tokens.Jwt, Version=4.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IdentityModel.Tokens.Jwt.4.0.1\lib\net45\System.IdentityModel.Tokens.Jwt.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Net" />
    <Reference Include="System.Spatial, Version=5.6.4.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Spatial.5.6.4\lib\net40\System.Spatial.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Cors, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Cors.5.2.3\lib\net45\System.Web.Cors.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Web.Http.SelfHost, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.SelfHost.5.2.3\lib\net45\System.Web.Http.SelfHost.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Abstractions" />
    <Reference Include="System.Web.Routing" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="Microsoft.Web.Infrastructure, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.Web.Infrastructure.*******\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http">
    </Reference>
    <Reference Include="System.Net.Http.Formatting, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Client.5.2.3\lib\net45\System.Net.Http.Formatting.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.WebRequest">
    </Reference>
    <Reference Include="System.Web.Helpers, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.3\lib\net45\System.Web.Helpers.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Core.5.2.3\lib\net45\System.Web.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http.WebHost, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.WebHost.5.2.3\lib\net45\System.Web.Http.WebHost.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Mvc, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Web.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Optimization">
      <HintPath>..\packages\Microsoft.AspNet.Web.Optimization.1.1.3\lib\net40\System.Web.Optimization.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Razor, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.Razor.3.2.3\lib\net45\System.Web.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.3\lib\net45\System.Web.WebPages.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Deployment, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.3\lib\net45\System.Web.WebPages.Deployment.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Razor, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.3\lib\net45\System.Web.WebPages.Razor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.WebApi, Version=*******, Culture=neutral, PublicKeyToken=43da31bc42a85347, processorArchitecture=MSIL">
      <HintPath>..\packages\Unity.WebAPI.5.2.3\lib\net45\Unity.WebApi.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="WebActivatorEx, Version=2.0.0.0, Culture=neutral, PublicKeyToken=7b26dc2a43f6a0d4, processorArchitecture=MSIL">
      <HintPath>..\packages\WebActivatorEx.2.2.0\lib\net40\WebActivatorEx.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="WebGrease, Version=1.6.5135.21930, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\WebGrease.1.6.0\lib\WebGrease.dll</HintPath>
      <Private>True</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Reference Include="EntityFramework">
      <HintPath>..\packages\EntityFramework.6.1.3\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer">
      <HintPath>..\packages\EntityFramework.6.1.3\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="Owin">
      <HintPath>..\packages\Owin.1.0\lib\net40\Owin.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin">
      <HintPath>..\packages\Microsoft.Owin.3.0.1\lib\net45\Microsoft.Owin.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Host.SystemWeb">
      <HintPath>..\packages\Microsoft.Owin.Host.SystemWeb.3.0.1\lib\net45\Microsoft.Owin.Host.SystemWeb.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security">
      <HintPath>..\packages\Microsoft.Owin.Security.3.0.1\lib\net45\Microsoft.Owin.Security.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.OAuth">
      <HintPath>..\packages\Microsoft.Owin.Security.OAuth.3.0.1\lib\net45\Microsoft.Owin.Security.OAuth.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http.Owin">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Owin.5.2.3\lib\net45\System.Web.Http.Owin.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="App_Start\BundleConfig.cs" />
    <Compile Include="App_Start\FilterConfig.cs" />
    <Compile Include="App_Start\RouteConfig.cs" />
    <Compile Include="App_Start\Startup.Auth.cs" />
    <Compile Include="App_Start\SwaggerConfig.cs" />
    <Compile Include="App_Start\UnityConfig.cs" />
    <Compile Include="App_Start\WebApiConfig.cs" />
    <Compile Include="Controllers\Externo\AvaluosController.cs" />
    <Compile Include="Controllers\Externo\OfertaViviendaController.cs" />
    <Compile Include="Controllers\Externo\SeguroCalidadController.cs" />
    <Compile Include="Controllers\Externo\SeguroCalidadGeneralesController.cs" />
    <Compile Include="Controllers\Interno\CuentaController.cs" />
    <Compile Include="Controllers\Interno\Generales\EmpresaController.cs" />
    <Compile Include="Controllers\Interno\Generales\ProductoServicioController.cs" />
    <Compile Include="Controllers\Interno\MedicionesController.cs" />
    <Compile Include="Controllers\Interno\OfertaController.cs" />
    <Compile Include="Controllers\Interno\OrdenVerificacionController.cs" />
    <Compile Include="Controllers\Interno\PromotorController.cs" />
    <Compile Include="Controllers\Interno\PrototiposController.cs" />
    <Compile Include="Controllers\Interno\Generales\OrdenTrabajoController.cs" />
    <Compile Include="Controllers\Interno\ProyectosController.cs" />
    <Compile Include="Controllers\Interno\ViviendasController.cs" />
    <Compile Include="FiltroExcepcionesRedOracleApiAttribute.cs" />
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Recursos\Mensajes.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Mensajes.resx</DependentUpon>
    </Compile>
    <Compile Include="SqlServerTypes\Loader.cs" />
    <Compile Include="Startup.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="App_Data\empresa-dictaminacion-config.json" />
    <Content Include="App_Data\Esquemas\RegistroProyectoSchema.xml" />
    <Content Include="App_Data\Esquemas\SDFSchema.xml" />
    <Content Include="App_Data\proyectos-configuracion-secciones.json" />
    <Content Include="App_Data\empresa-reglas.json" />
    <Content Include="App_Data\proyectos-reglas.json" />
    <Content Include="App_Data\ofertas-reglas.json" />
    <None Include="App_Data\prototipos-configuracion-secciones.json" />
    <None Include="App_Data\empresa-estatus-secciones.json" />
    <None Include="App_Data\ofertas-configuracion-secciones.json" />
    <Content Include="favicon.ico" />
    <Content Include="Global.asax" />
    <Content Include="ApplicationInsights.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="App_Data\empresa-configuracion-secciones.json" />
    <Content Include="App_Data\demo-configuracion-secciones.json" />
    <Content Include="Recursos\FichaPago.html" />
    <Content Include="Recursos\FichaPago2.html" />
    <Content Include="SqlServerTypes\readme.htm" />
    <Content Include="SqlServerTypes\x64\msvcr120.dll" />
    <Content Include="SqlServerTypes\x64\SqlServerSpatial140.dll" />
    <Content Include="SqlServerTypes\x86\msvcr120.dll" />
    <Content Include="SqlServerTypes\x86\SqlServerSpatial140.dll" />
    <Content Include="Views\02072019.txt" />
    <Content Include="Web.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Service References\Application Insights\ConnectedService.json" />
    <Content Include="App_Data\SDF_Temporal\READMO.md" />
    <None Include="Properties\PublishProfiles\OfertaAPI.pubxml" />
    <None Include="Properties\PublishProfiles\ruvpp-oferta-api-dev - FTP.pubxml" />
    <None Include="Properties\PublishProfiles\ruvpp-oferta-api-dev - ReadOnly - FTP.pubxml" />
    <None Include="Properties\PublishProfiles\ruvpp-oferta-api-dev - Web Deploy %282%29.pubxml" />
    <None Include="Properties\PublishProfiles\ruvpp-oferta-api-dev - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\ruvpp-oferta-api-qa - FTP %282%29.pubxml" />
    <None Include="Properties\PublishProfiles\ruvpp-oferta-api-qa - ReadOnly - FTP %282%29.pubxml" />
    <None Include="Properties\PublishProfiles\ruvpp-oferta-api-qa - Web Deploy %282%29.pubxml" />
    <None Include="Properties\PublishProfiles\RUVPP-OFERTA-API-stagging - FTP.pubxml" />
    <None Include="Properties\PublishProfiles\RUVPP-OFERTA-API-stagging - ReadOnly - FTP.pubxml" />
    <None Include="Properties\PublishProfiles\RUVPP-OFERTA-API-stagging - Web Deploy.pubxml" />
    <None Include="Web.Integracion.config">
      <DependentUpon>Web.config</DependentUpon>
      <SubType>Designer</SubType>
    </None>
    <None Include="Web.Produccion.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
    <None Include="Web.QA.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Views\Bancos\" />
    <Folder Include="Views\Generales\" />
  </ItemGroup>
  <ItemGroup>
    <Service Include="{4A0DDDB5-7A95-4FBF-97CC-616D07737A77}" />
  </ItemGroup>
  <ItemGroup>
    <TypeScriptCompile Include="Scripts\utils\validaciones-formularios.ts" />
    <TypeScriptCompile Include="Scripts\validacion-modelos.ts" />
    <TypeScriptCompile Include="Scripts\modelo\argumentoValidacion.ts" />
    <TypeScriptCompile Include="Scripts\modelo\reglaControl.ts" />
    <TypeScriptCompile Include="Scripts\modelo\validacion.ts" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="packages.config" />
    <Content Include="App_Data\prototipos-reglas.json" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\RUV.RUVPP.Oferta.Datos\RUV.RUVPP.Oferta.Datos.csproj">
      <Project>{9a22925d-5f26-47b6-8777-b8d87a412ed8}</Project>
      <Name>RUV.RUVPP.Oferta.Datos</Name>
    </ProjectReference>
    <ProjectReference Include="..\RUV.RUVPP.Oferta.Dominio\RUV.RUVPP.Oferta.Dominio.csproj">
      <Project>{bdedfab0-f491-43b0-8dc4-8fb85a772425}</Project>
      <Name>RUV.RUVPP.Oferta.Dominio</Name>
    </ProjectReference>
    <ProjectReference Include="..\RUV.RUVPP.Oferta.GeneralesInterop\RUV.RUVPP.Oferta.GeneralesInterop.csproj">
      <Project>{a429aec1-ba17-4f6f-96c7-d4a52f760f09}</Project>
      <Name>RUV.RUVPP.Oferta.GeneralesInterop</Name>
    </ProjectReference>
    <ProjectReference Include="..\RUV.RUVPP.Oferta.Modelo\RUV.RUVPP.Oferta.Modelo.csproj">
      <Project>{6266a8ca-7163-4580-8bb8-7a0fa604e5d3}</Project>
      <Name>RUV.RUVPP.Oferta.Modelo</Name>
    </ProjectReference>
    <ProjectReference Include="..\RUV.RUVPP.Oferta.Reportes\RUV.RUVPP.Oferta.Reportes.csproj">
      <Project>{ca0945aa-9314-4816-85f8-5f3f6a61fdfc}</Project>
      <Name>RUV.RUVPP.Oferta.Reportes</Name>
    </ProjectReference>
    <ProjectReference Include="..\RUV.RUVPP.Oferta.SeguridadUsuarioAsis.WSC\RUV.RUVPP.Oferta.SeguridadUsuarioAsis.WSC.csproj">
      <Project>{B26B18E1-650D-49F0-93F7-67728189527B}</Project>
      <Name>RUV.RUVPP.Oferta.SeguridadUsuarioAsis.WSC</Name>
    </ProjectReference>
    <ProjectReference Include="..\RUV.RUVPP.RuvAutorizacionJWT\RUV.Comun.Seguridad.JWT.csproj">
      <Project>{da1e3130-674d-4beb-94f6-a069e664438f}</Project>
      <Name>RUV.Comun.Seguridad.JWT</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Service References\" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Recursos\Mensajes.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Mensajes.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Integracion|AnyCPU'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DocumentationFile>bin\RUV.RUVPP.Oferta.Api.XML</DocumentationFile>
    <DebugType>full</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'QA|AnyCPU'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DocumentationFile>bin\RUV.RUVPP.Oferta.Api.XML</DocumentationFile>
    <DebugType>full</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Produccion|AnyCPU'">
    <OutputPath>bin\</OutputPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.targets" Condition="Exists('$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.targets')" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <Target Name="MvcBuildViews" AfterTargets="AfterBuild" Condition="'$(MvcBuildViews)'=='true'">
    <AspNetCompiler VirtualPath="temp" PhysicalPath="$(WebProjectOutputDir)" />
  </Target>
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>55498</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:5900/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Microsoft.Net.Compilers.1.3.2\build\Microsoft.Net.Compilers.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.Net.Compilers.1.3.2\build\Microsoft.Net.Compilers.props'))" />
    <Error Condition="!Exists('..\packages\Microsoft.Bcl.Build.1.0.21\build\Microsoft.Bcl.Build.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.Bcl.Build.1.0.21\build\Microsoft.Bcl.Build.targets'))" />
    <Error Condition="!Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.1.0.2\build\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.1.0.2\build\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props'))" />
  </Target>
  <Import Project="..\packages\Microsoft.Bcl.Build.1.0.21\build\Microsoft.Bcl.Build.targets" Condition="Exists('..\packages\Microsoft.Bcl.Build.1.0.21\build\Microsoft.Bcl.Build.targets')" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target> -->
</Project>