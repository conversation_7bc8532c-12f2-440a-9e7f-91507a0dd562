﻿using System;

namespace RUV.RUVPP.Oferta.Modelo.Oferta.Data
{
    public class OfertaVivienda
    {
        public string nombreFrente { get; set; }
        public string nombreProyecto { get; set; }
        public int idOfertaVivienda { get; set; }
        public int idProyecto { get; set; }
        public int idEmpresa { get; set; }
        public int idConstructor { get; set; }
        public int? idPromotor { get; set; }
        public int? idVendedor { get; set; }
        public int idDomicilioGeografico { get; set; }
        public int idEstatusOfertaVivienda { get; set; }
        public string estatusOferta { get; set; }
        public int idLicenciaFactibilidad { get; set; }
        public int idTipoSociedadHipotecaria { get; set; }
        public bool esZonaRiesgo { get; set; }
        public string claveOfertaVivienda { get; set; }
        public bool esMAI { get; set; }
        public bool cuentaConMinistraciones { get; set; }
        public string empresaOtorgaMinistraciones { get; set; }
        public bool cuentaConCreditoPuente { get; set; }
        public string empresaOtorgaCreditoPuente { get; set; }
        public decimal areaTerreno { get; set; }
        public decimal superficieConstruida { get; set; }
        public decimal metrosFrente { get; set; }
        public int numeroCatastralLote { get; set; }
        public string descripcionActualInmueble { get; set; }
        public string descripcionAccionVivienda { get; set; }
        public bool aceptacionCartaResponsabilidad { get; set; }
        public DateTime fechaRegistro { get; set; }
        public DateTime? fechaActualizacion { get; set; }
        public DateTime? fechaAceptacion { get; set; }
        public string temporalJSON { get; set; }
        public bool activo { get; set; }        
    }
}