﻿using RUV.RUVPP.Oferta.Modelo.AgenteServicio;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Datos.ConvivenciaAsIs
{
    /// <summary>
    /// Contiene operaciones para integrar los servicios web de convivienca de RUV As Is.
    /// </summary>
    public interface IAgenteServicioConvivenciaASIS
    {
        /// <summary>
        /// Realiza una llamada genérica al servicio web del RUV AS IS.
        /// </summary>
        /// <typeparam name="M">Tipo del modelo a enviar.</typeparam>
        /// <typeparam name="R">Tipo del resultado.</typeparam>
        /// <param name="modelo">Modelo a enviar en la petición.</param>
        /// <param name="urlAccion">Url de la acción a ejecutar.</param>
        /// <returns></returns>
        Task<R> LlamarOperacion<M, R>(M modelo, string urlAccion) where R : new();
    }
}
