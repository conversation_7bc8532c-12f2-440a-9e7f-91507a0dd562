﻿{
  "idProducto": 2,
  "producto": "Oferta",
  "idServicio": 35,
  "servicio": "Alta de Proyecto",
  "secciones": [
    {
      "idSeccion": 1,
      "nombre": "Plano",
      "mostrarEnVyD": true,
      "validable": true,
      "secciones": [],
      "elementos": [
        {
          "idElemento": "nombrePlano",
          "nombre": "Nombre Plano",
          "mostrarEnVyD": true,
          "validable": true
        },
        {
          "idElemento": "plano",
          "nombre": "Cargar plano",
          "mostrarEnVyD": true,
          "validable": true
        }
      ]
    },
    {
      "idSeccion": 2,
      "nombre": "Sembrado",
      "mostrarEnVyD": false,
      "validable": false,
      "secciones": [ ],
      "elementos": [
        {
          "idElemento": "folioSEPLADE",
          "nombre": "Folio SEPLADE",
          "mostrarEnVyD": true,
          "validable": true
        },
        {
          "idElemento": "folioAyto",
          "nombre": "Folio Ayuntamiento Tijuana",
          "mostrarEnVyD": true,
          "validable": true
        },
        {
          "idElemento": "erroresGrid",
          "nombre": "Error en Grid Sembrado",
          "mostrarEnVyD": true,
          "validable": true
        }
      ]
    },
    {
      "idSeccion": 3,
      "nombre": "DatosGenerales",
      "mostrarEnVyD": true,
      "validable": false,
      "secciones": [
        {
          "idSeccion": 4,
          "nombre": "Propietario del Terreno",
          "mostrarEnVyD": true,
          "validable": false,
          "secciones": [],
          "elementos": [
            {
              "idElemento": "nombrePropietario",
              "nombre": "Nombre",
              "validable": false
            },
            {
              "idElemento": "tomo",
              "nombre": "Tomo",
              "validable": false
            },
            {
              "idElemento": "numeroEscritura",
              "nombre": "No. de escritura",
              "validable": false
            },
            {
              "idElemento": "numeroCatastral",
              "nombre": "No. catastral del lote",
              "validable": false
            },
            {
              "idElemento": "fechaEscrituracion",
              "nombre": "Fecha de escrituración",
              "validable": false
            },
            {
              "idElemento": "volumen",
              "nombre": "Volumen",
              "validable": false
            },
            {
              "idElemento": "areaTerreno",
              "nombre": "Área del terreno escriturado",
              "validable": false
            },
            {
              "idElemento": "numeroNotario",
              "nombre": "No. de notario",
              "validable": false
            },
            {
              "idElemento": "numeroRPP",
              "nombre": "No. de RPP",
              "validable": false
            }
          ]
        },
        {
          "idSeccion": 5,
          "nombre": "Director responsable de obra",
          "mostrarEnVyD": true,
          "validable": false,
          "secciones": [],
          "elementos": [
            {
              "idElemento": "nombreDirector",
              "nombre": "Nombre director",
              "validable": false
            },
            {
              "idElemento": "apellidoMaterno",
              "nombre": "Apellido materno",
              "validable": false
            },
            {
              "idElemento": "apellidoPaterno",
              "nombre": "Apellido paterno",
              "validable": false
            },
            {
              "idElemento": "numeroPerito",
              "nombre": "Número perito",
              "validable": false
            },
            {
              "idElemento": "fechaVigencia",
              "nombre": "Fecha vigencia",
              "validable": false
            },
            {
              "idElemento": "licenciaDRO",
              "nombre": "Licencia",
              "validable": false
            },
            {
              "idElemento": "identificacionDRO",
              "nombre": "Identificación oficial",
              "validable": true
            }
          ]
        },
        {
          "idSeccion": 6,
          "nombre": "Datos Constructor/ Promotor",
          "mostrarEnVyD": true,
          "validable": false,
          "secciones": [],
          "elementos": [
            {
              "idElemento": "noRegistroRUVConstructor",
              "nombre": "Número registro RUV",
              "validable": false
            },
            {
              "idElemento": "rfcPromotor",
              "nombre": "Rfc",
              "validable": false
            },
            {
              "idElemento": "nombreRazonSocialPromotor",
              "nombre": "Nombre o razón social",
              "validable": false
            },
            {
              "idElemento": "telefonoPromotor",
              "nombre": "Teléfono",
              "validable": false
            },
            {
              "idElemento": "correoElectronicoPromotor",
              "nombre": "Correo electrónico",
              "validable": false
            },
            {
              "idElemento": "rfcVendedor",
              "nombre": "Rfc",
              "validable": false
            },
            {
              "idElemento": "nombreRazonSocialVendedor",
              "nombre": "Nombre o razón social",
              "validable": false
            },
            {
              "idElemento": "telefonoVendedor",
              "nombre": "Teléfono",
              "validable": false
            },
            {
              "idElemento": "correoElectronicoVendedor",
              "nombre": "Correo electrónico",
              "validable": false
            }
          ]
        }
      ]
    },
    {
      "idSeccion": 7,
      "nombre": "Zona de riesgo",
      "mostrarEnVyD": false,
      "validable": true,
      "secciones": [],
      "elementos": [
        {
          "idElemento": "esZonaRiesgo",
          "nombre": "¿Proyecto en zona de riesgo?",
          "validable": true
        },
        {
          "idElemento": "zonaRiesgo",
          "nombre": "Zona de riesgo",
          "validable": true,
          "enLista": true
        },
        {
          "idElemento": "dictamenRiesgo",
          "nombre": "Dictamen de riesgo",
          "validable": true
        }
      ]
    },
    {
      "idSeccion": 8,
      "nombre": "Carta de responsabilidad",
      "mostrarEnVyD": false,
      "validable": false,
      "secciones": [],
      "elementos": [
        {
          "idElemento": "carta",
          "nombre": "Carta",
          "validable": true
        }
      ]
    }
  ]
}