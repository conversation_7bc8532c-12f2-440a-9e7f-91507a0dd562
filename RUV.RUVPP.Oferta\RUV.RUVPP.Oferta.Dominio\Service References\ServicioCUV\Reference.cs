﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace RUV.RUVPP.Oferta.Dominio.ServicioCUV {
    using System.Runtime.Serialization;
    using System;
    
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Punto", Namespace="http://schemas.datacontract.org/2004/07/SIG.CUV.Entidades")]
    [System.SerializableAttribute()]
    public partial class Punto : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string LatitudField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string LongitudField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private int NivelField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string RadioField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Latitud {
            get {
                return this.LatitudField;
            }
            set {
                if ((object.ReferenceEquals(this.LatitudField, value) != true)) {
                    this.LatitudField = value;
                    this.RaisePropertyChanged("Latitud");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Longitud {
            get {
                return this.LongitudField;
            }
            set {
                if ((object.ReferenceEquals(this.LongitudField, value) != true)) {
                    this.LongitudField = value;
                    this.RaisePropertyChanged("Longitud");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Nivel {
            get {
                return this.NivelField;
            }
            set {
                if ((this.NivelField.Equals(value) != true)) {
                    this.NivelField = value;
                    this.RaisePropertyChanged("Nivel");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Radio {
            get {
                return this.RadioField;
            }
            set {
                if ((object.ReferenceEquals(this.RadioField, value) != true)) {
                    this.RadioField = value;
                    this.RaisePropertyChanged("Radio");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ResultadoCuv", Namespace="http://schemas.datacontract.org/2004/07/SIG.CUV.Entidades")]
    [System.SerializableAttribute()]
    public partial class ResultadoCuv : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string ClaveField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private bool CreadoField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string DescripcionField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Clave {
            get {
                return this.ClaveField;
            }
            set {
                if ((object.ReferenceEquals(this.ClaveField, value) != true)) {
                    this.ClaveField = value;
                    this.RaisePropertyChanged("Clave");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool Creado {
            get {
                return this.CreadoField;
            }
            set {
                if ((this.CreadoField.Equals(value) != true)) {
                    this.CreadoField = value;
                    this.RaisePropertyChanged("Creado");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Descripcion {
            get {
                return this.DescripcionField;
            }
            set {
                if ((object.ReferenceEquals(this.DescripcionField, value) != true)) {
                    this.DescripcionField = value;
                    this.RaisePropertyChanged("Descripcion");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Geometria", Namespace="http://schemas.datacontract.org/2004/07/SIG.CUV.Entidades")]
    [System.SerializableAttribute()]
    public partial class Geometria : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string EsquemaField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string GeometriaNombreCampoField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string IdNombreCampoField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private int IdValorCampoField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private int NivelField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string NombreTablaField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private int TipoServicioField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Esquema {
            get {
                return this.EsquemaField;
            }
            set {
                if ((object.ReferenceEquals(this.EsquemaField, value) != true)) {
                    this.EsquemaField = value;
                    this.RaisePropertyChanged("Esquema");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string GeometriaNombreCampo {
            get {
                return this.GeometriaNombreCampoField;
            }
            set {
                if ((object.ReferenceEquals(this.GeometriaNombreCampoField, value) != true)) {
                    this.GeometriaNombreCampoField = value;
                    this.RaisePropertyChanged("GeometriaNombreCampo");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string IdNombreCampo {
            get {
                return this.IdNombreCampoField;
            }
            set {
                if ((object.ReferenceEquals(this.IdNombreCampoField, value) != true)) {
                    this.IdNombreCampoField = value;
                    this.RaisePropertyChanged("IdNombreCampo");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IdValorCampo {
            get {
                return this.IdValorCampoField;
            }
            set {
                if ((this.IdValorCampoField.Equals(value) != true)) {
                    this.IdValorCampoField = value;
                    this.RaisePropertyChanged("IdValorCampo");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Nivel {
            get {
                return this.NivelField;
            }
            set {
                if ((this.NivelField.Equals(value) != true)) {
                    this.NivelField = value;
                    this.RaisePropertyChanged("Nivel");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string NombreTabla {
            get {
                return this.NombreTablaField;
            }
            set {
                if ((object.ReferenceEquals(this.NombreTablaField, value) != true)) {
                    this.NombreTablaField = value;
                    this.RaisePropertyChanged("NombreTabla");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int TipoServicio {
            get {
                return this.TipoServicioField;
            }
            set {
                if ((this.TipoServicioField.Equals(value) != true)) {
                    this.TipoServicioField = value;
                    this.RaisePropertyChanged("TipoServicio");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Cuv", Namespace="http://schemas.datacontract.org/2004/07/SIG.CUV.Entidades")]
    [System.SerializableAttribute()]
    public partial class Cuv : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string EsquemaField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string GeometriaNombreCampoField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string IdNombreCampoField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private int IdServicioField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.Nullable<decimal> IdValorCampoField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private int NivelDecimalField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string NombreTablaField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.Nullable<decimal> TipoGeometriaField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string TipologiaField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Esquema {
            get {
                return this.EsquemaField;
            }
            set {
                if ((object.ReferenceEquals(this.EsquemaField, value) != true)) {
                    this.EsquemaField = value;
                    this.RaisePropertyChanged("Esquema");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string GeometriaNombreCampo {
            get {
                return this.GeometriaNombreCampoField;
            }
            set {
                if ((object.ReferenceEquals(this.GeometriaNombreCampoField, value) != true)) {
                    this.GeometriaNombreCampoField = value;
                    this.RaisePropertyChanged("GeometriaNombreCampo");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string IdNombreCampo {
            get {
                return this.IdNombreCampoField;
            }
            set {
                if ((object.ReferenceEquals(this.IdNombreCampoField, value) != true)) {
                    this.IdNombreCampoField = value;
                    this.RaisePropertyChanged("IdNombreCampo");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IdServicio {
            get {
                return this.IdServicioField;
            }
            set {
                if ((this.IdServicioField.Equals(value) != true)) {
                    this.IdServicioField = value;
                    this.RaisePropertyChanged("IdServicio");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<decimal> IdValorCampo {
            get {
                return this.IdValorCampoField;
            }
            set {
                if ((this.IdValorCampoField.Equals(value) != true)) {
                    this.IdValorCampoField = value;
                    this.RaisePropertyChanged("IdValorCampo");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int NivelDecimal {
            get {
                return this.NivelDecimalField;
            }
            set {
                if ((this.NivelDecimalField.Equals(value) != true)) {
                    this.NivelDecimalField = value;
                    this.RaisePropertyChanged("NivelDecimal");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string NombreTabla {
            get {
                return this.NombreTablaField;
            }
            set {
                if ((object.ReferenceEquals(this.NombreTablaField, value) != true)) {
                    this.NombreTablaField = value;
                    this.RaisePropertyChanged("NombreTabla");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<decimal> TipoGeometria {
            get {
                return this.TipoGeometriaField;
            }
            set {
                if ((this.TipoGeometriaField.Equals(value) != true)) {
                    this.TipoGeometriaField = value;
                    this.RaisePropertyChanged("TipoGeometria");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Tipologia {
            get {
                return this.TipologiaField;
            }
            set {
                if ((object.ReferenceEquals(this.TipologiaField, value) != true)) {
                    this.TipologiaField = value;
                    this.RaisePropertyChanged("Tipologia");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Vertice", Namespace="http://schemas.datacontract.org/2004/07/SIG.CUV.Entidades")]
    [System.SerializableAttribute()]
    public partial class Vertice : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private RUV.RUVPP.Oferta.Dominio.ServicioCUV.Vertices[] VerticesPoligonoField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public RUV.RUVPP.Oferta.Dominio.ServicioCUV.Vertices[] VerticesPoligono {
            get {
                return this.VerticesPoligonoField;
            }
            set {
                if ((object.ReferenceEquals(this.VerticesPoligonoField, value) != true)) {
                    this.VerticesPoligonoField = value;
                    this.RaisePropertyChanged("VerticesPoligono");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Vertices", Namespace="http://schemas.datacontract.org/2004/07/SIG.CUV.Entidades")]
    [System.SerializableAttribute()]
    public partial class Vertices : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string IdOrdenField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string LatitudField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string LongitudField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string IdOrden {
            get {
                return this.IdOrdenField;
            }
            set {
                if ((object.ReferenceEquals(this.IdOrdenField, value) != true)) {
                    this.IdOrdenField = value;
                    this.RaisePropertyChanged("IdOrden");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Latitud {
            get {
                return this.LatitudField;
            }
            set {
                if ((object.ReferenceEquals(this.LatitudField, value) != true)) {
                    this.LatitudField = value;
                    this.RaisePropertyChanged("Latitud");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Longitud {
            get {
                return this.LongitudField;
            }
            set {
                if ((object.ReferenceEquals(this.LongitudField, value) != true)) {
                    this.LongitudField = value;
                    this.RaisePropertyChanged("Longitud");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Consulta", Namespace="http://schemas.datacontract.org/2004/07/SIG.CUV.Entidades")]
    [System.SerializableAttribute()]
    public partial class Consulta : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string EsquemaField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string IdNombreCampoField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string IdValorCampoField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private int NivelDecimalField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string NombreTablaField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Esquema {
            get {
                return this.EsquemaField;
            }
            set {
                if ((object.ReferenceEquals(this.EsquemaField, value) != true)) {
                    this.EsquemaField = value;
                    this.RaisePropertyChanged("Esquema");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string IdNombreCampo {
            get {
                return this.IdNombreCampoField;
            }
            set {
                if ((object.ReferenceEquals(this.IdNombreCampoField, value) != true)) {
                    this.IdNombreCampoField = value;
                    this.RaisePropertyChanged("IdNombreCampo");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string IdValorCampo {
            get {
                return this.IdValorCampoField;
            }
            set {
                if ((object.ReferenceEquals(this.IdValorCampoField, value) != true)) {
                    this.IdValorCampoField = value;
                    this.RaisePropertyChanged("IdValorCampo");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int NivelDecimal {
            get {
                return this.NivelDecimalField;
            }
            set {
                if ((this.NivelDecimalField.Equals(value) != true)) {
                    this.NivelDecimalField = value;
                    this.RaisePropertyChanged("NivelDecimal");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string NombreTabla {
            get {
                return this.NombreTablaField;
            }
            set {
                if ((object.ReferenceEquals(this.NombreTablaField, value) != true)) {
                    this.NombreTablaField = value;
                    this.RaisePropertyChanged("NombreTabla");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ConsultaBitacora", Namespace="http://schemas.datacontract.org/2004/07/SIG.CUV.Entidades")]
    [System.SerializableAttribute()]
    public partial class ConsultaBitacora : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string CampoGeometricoField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string CampoIdField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string ClaveGradiculaField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private int ConsecutivoField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string ConsecutivoHField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string CuvField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string EsquemaField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private int IdRegistroField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private int IdServicioField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string LatitudField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string LongitudField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private int NivelDField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string NivelHField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string OrigenTablaGeometricaField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string ValorIdField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CampoGeometrico {
            get {
                return this.CampoGeometricoField;
            }
            set {
                if ((object.ReferenceEquals(this.CampoGeometricoField, value) != true)) {
                    this.CampoGeometricoField = value;
                    this.RaisePropertyChanged("CampoGeometrico");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CampoId {
            get {
                return this.CampoIdField;
            }
            set {
                if ((object.ReferenceEquals(this.CampoIdField, value) != true)) {
                    this.CampoIdField = value;
                    this.RaisePropertyChanged("CampoId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ClaveGradicula {
            get {
                return this.ClaveGradiculaField;
            }
            set {
                if ((object.ReferenceEquals(this.ClaveGradiculaField, value) != true)) {
                    this.ClaveGradiculaField = value;
                    this.RaisePropertyChanged("ClaveGradicula");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Consecutivo {
            get {
                return this.ConsecutivoField;
            }
            set {
                if ((this.ConsecutivoField.Equals(value) != true)) {
                    this.ConsecutivoField = value;
                    this.RaisePropertyChanged("Consecutivo");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ConsecutivoH {
            get {
                return this.ConsecutivoHField;
            }
            set {
                if ((object.ReferenceEquals(this.ConsecutivoHField, value) != true)) {
                    this.ConsecutivoHField = value;
                    this.RaisePropertyChanged("ConsecutivoH");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Cuv {
            get {
                return this.CuvField;
            }
            set {
                if ((object.ReferenceEquals(this.CuvField, value) != true)) {
                    this.CuvField = value;
                    this.RaisePropertyChanged("Cuv");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Esquema {
            get {
                return this.EsquemaField;
            }
            set {
                if ((object.ReferenceEquals(this.EsquemaField, value) != true)) {
                    this.EsquemaField = value;
                    this.RaisePropertyChanged("Esquema");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IdRegistro {
            get {
                return this.IdRegistroField;
            }
            set {
                if ((this.IdRegistroField.Equals(value) != true)) {
                    this.IdRegistroField = value;
                    this.RaisePropertyChanged("IdRegistro");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IdServicio {
            get {
                return this.IdServicioField;
            }
            set {
                if ((this.IdServicioField.Equals(value) != true)) {
                    this.IdServicioField = value;
                    this.RaisePropertyChanged("IdServicio");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Latitud {
            get {
                return this.LatitudField;
            }
            set {
                if ((object.ReferenceEquals(this.LatitudField, value) != true)) {
                    this.LatitudField = value;
                    this.RaisePropertyChanged("Latitud");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Longitud {
            get {
                return this.LongitudField;
            }
            set {
                if ((object.ReferenceEquals(this.LongitudField, value) != true)) {
                    this.LongitudField = value;
                    this.RaisePropertyChanged("Longitud");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int NivelD {
            get {
                return this.NivelDField;
            }
            set {
                if ((this.NivelDField.Equals(value) != true)) {
                    this.NivelDField = value;
                    this.RaisePropertyChanged("NivelD");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string NivelH {
            get {
                return this.NivelHField;
            }
            set {
                if ((object.ReferenceEquals(this.NivelHField, value) != true)) {
                    this.NivelHField = value;
                    this.RaisePropertyChanged("NivelH");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string OrigenTablaGeometrica {
            get {
                return this.OrigenTablaGeometricaField;
            }
            set {
                if ((object.ReferenceEquals(this.OrigenTablaGeometricaField, value) != true)) {
                    this.OrigenTablaGeometricaField = value;
                    this.RaisePropertyChanged("OrigenTablaGeometrica");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ValorId {
            get {
                return this.ValorIdField;
            }
            set {
                if ((object.ReferenceEquals(this.ValorIdField, value) != true)) {
                    this.ValorIdField = value;
                    this.RaisePropertyChanged("ValorId");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.ServiceContractAttribute(ConfigurationName="ServicioCUV.IServicioCUV")]
    public interface IServicioCUV {
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IServicioCUV/ConsultarPreClavePunto", ReplyAction="http://tempuri.org/IServicioCUV/ConsultarPreClavePuntoResponse")]
        RUV.RUVPP.Oferta.Dominio.ServicioCUV.ResultadoCuv ConsultarPreClavePunto(RUV.RUVPP.Oferta.Dominio.ServicioCUV.Punto punto);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IServicioCUV/ConsultarPreClavePunto", ReplyAction="http://tempuri.org/IServicioCUV/ConsultarPreClavePuntoResponse")]
        System.Threading.Tasks.Task<RUV.RUVPP.Oferta.Dominio.ServicioCUV.ResultadoCuv> ConsultarPreClavePuntoAsync(RUV.RUVPP.Oferta.Dominio.ServicioCUV.Punto punto);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IServicioCUV/ConsultarPreClaveGeometria", ReplyAction="http://tempuri.org/IServicioCUV/ConsultarPreClaveGeometriaResponse")]
        RUV.RUVPP.Oferta.Dominio.ServicioCUV.ResultadoCuv ConsultarPreClaveGeometria(RUV.RUVPP.Oferta.Dominio.ServicioCUV.Geometria geometria);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IServicioCUV/ConsultarPreClaveGeometria", ReplyAction="http://tempuri.org/IServicioCUV/ConsultarPreClaveGeometriaResponse")]
        System.Threading.Tasks.Task<RUV.RUVPP.Oferta.Dominio.ServicioCUV.ResultadoCuv> ConsultarPreClaveGeometriaAsync(RUV.RUVPP.Oferta.Dominio.ServicioCUV.Geometria geometria);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IServicioCUV/CrearCuv", ReplyAction="http://tempuri.org/IServicioCUV/CrearCuvResponse")]
        RUV.RUVPP.Oferta.Dominio.ServicioCUV.ResultadoCuv CrearCuv(RUV.RUVPP.Oferta.Dominio.ServicioCUV.Cuv cuv);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IServicioCUV/CrearCuv", ReplyAction="http://tempuri.org/IServicioCUV/CrearCuvResponse")]
        System.Threading.Tasks.Task<RUV.RUVPP.Oferta.Dominio.ServicioCUV.ResultadoCuv> CrearCuvAsync(RUV.RUVPP.Oferta.Dominio.ServicioCUV.Cuv cuv);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IServicioCUV/ObtenerVertices", ReplyAction="http://tempuri.org/IServicioCUV/ObtenerVerticesResponse")]
        RUV.RUVPP.Oferta.Dominio.ServicioCUV.Vertice ObtenerVertices(RUV.RUVPP.Oferta.Dominio.ServicioCUV.Cuv cuv);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IServicioCUV/ObtenerVertices", ReplyAction="http://tempuri.org/IServicioCUV/ObtenerVerticesResponse")]
        System.Threading.Tasks.Task<RUV.RUVPP.Oferta.Dominio.ServicioCUV.Vertice> ObtenerVerticesAsync(RUV.RUVPP.Oferta.Dominio.ServicioCUV.Cuv cuv);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IServicioCUV/ConsultaCuv", ReplyAction="http://tempuri.org/IServicioCUV/ConsultaCuvResponse")]
        RUV.RUVPP.Oferta.Dominio.ServicioCUV.ConsultaBitacora ConsultaCuv(RUV.RUVPP.Oferta.Dominio.ServicioCUV.Consulta consulta);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IServicioCUV/ConsultaCuv", ReplyAction="http://tempuri.org/IServicioCUV/ConsultaCuvResponse")]
        System.Threading.Tasks.Task<RUV.RUVPP.Oferta.Dominio.ServicioCUV.ConsultaBitacora> ConsultaCuvAsync(RUV.RUVPP.Oferta.Dominio.ServicioCUV.Consulta consulta);
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    public interface IServicioCUVChannel : RUV.RUVPP.Oferta.Dominio.ServicioCUV.IServicioCUV, System.ServiceModel.IClientChannel {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    public partial class ServicioCUVClient : System.ServiceModel.ClientBase<RUV.RUVPP.Oferta.Dominio.ServicioCUV.IServicioCUV>, RUV.RUVPP.Oferta.Dominio.ServicioCUV.IServicioCUV {
        
        public ServicioCUVClient() {
        }
        
        public ServicioCUVClient(string endpointConfigurationName) : 
                base(endpointConfigurationName) {
        }
        
        public ServicioCUVClient(string endpointConfigurationName, string remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public ServicioCUVClient(string endpointConfigurationName, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public ServicioCUVClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress) {
        }
        
        public RUV.RUVPP.Oferta.Dominio.ServicioCUV.ResultadoCuv ConsultarPreClavePunto(RUV.RUVPP.Oferta.Dominio.ServicioCUV.Punto punto) {
            return base.Channel.ConsultarPreClavePunto(punto);
        }
        
        public System.Threading.Tasks.Task<RUV.RUVPP.Oferta.Dominio.ServicioCUV.ResultadoCuv> ConsultarPreClavePuntoAsync(RUV.RUVPP.Oferta.Dominio.ServicioCUV.Punto punto) {
            return base.Channel.ConsultarPreClavePuntoAsync(punto);
        }
        
        public RUV.RUVPP.Oferta.Dominio.ServicioCUV.ResultadoCuv ConsultarPreClaveGeometria(RUV.RUVPP.Oferta.Dominio.ServicioCUV.Geometria geometria) {
            return base.Channel.ConsultarPreClaveGeometria(geometria);
        }
        
        public System.Threading.Tasks.Task<RUV.RUVPP.Oferta.Dominio.ServicioCUV.ResultadoCuv> ConsultarPreClaveGeometriaAsync(RUV.RUVPP.Oferta.Dominio.ServicioCUV.Geometria geometria) {
            return base.Channel.ConsultarPreClaveGeometriaAsync(geometria);
        }
        
        public RUV.RUVPP.Oferta.Dominio.ServicioCUV.ResultadoCuv CrearCuv(RUV.RUVPP.Oferta.Dominio.ServicioCUV.Cuv cuv) {
            return base.Channel.CrearCuv(cuv);
        }
        
        public System.Threading.Tasks.Task<RUV.RUVPP.Oferta.Dominio.ServicioCUV.ResultadoCuv> CrearCuvAsync(RUV.RUVPP.Oferta.Dominio.ServicioCUV.Cuv cuv) {
            return base.Channel.CrearCuvAsync(cuv);
        }
        
        public RUV.RUVPP.Oferta.Dominio.ServicioCUV.Vertice ObtenerVertices(RUV.RUVPP.Oferta.Dominio.ServicioCUV.Cuv cuv) {
            return base.Channel.ObtenerVertices(cuv);
        }
        
        public System.Threading.Tasks.Task<RUV.RUVPP.Oferta.Dominio.ServicioCUV.Vertice> ObtenerVerticesAsync(RUV.RUVPP.Oferta.Dominio.ServicioCUV.Cuv cuv) {
            return base.Channel.ObtenerVerticesAsync(cuv);
        }
        
        public RUV.RUVPP.Oferta.Dominio.ServicioCUV.ConsultaBitacora ConsultaCuv(RUV.RUVPP.Oferta.Dominio.ServicioCUV.Consulta consulta) {
            return base.Channel.ConsultaCuv(consulta);
        }
        
        public System.Threading.Tasks.Task<RUV.RUVPP.Oferta.Dominio.ServicioCUV.ConsultaBitacora> ConsultaCuvAsync(RUV.RUVPP.Oferta.Dominio.ServicioCUV.Consulta consulta) {
            return base.Channel.ConsultaCuvAsync(consulta);
        }
    }
}
