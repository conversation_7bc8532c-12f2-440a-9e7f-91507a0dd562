﻿<?xml version="1.0" encoding="utf-8"?>
<!--
Este archivo es utilizado por el proceso de paquete/publicación de nuestro proyecto Web. Puede personalizar el comportamiento de este proceso
editando este archivo MSBuild. Para conocer más acerca de esto, visite http://go.microsoft.com/fwlink/?LinkID=208121. 
-->
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <WebPublishMethod>MSDeploy</WebPublishMethod>
    <PublishProvider>AzureWebSite</PublishProvider>
    <LastUsedBuildConfiguration>Release</LastUsedBuildConfiguration>
    <LastUsedPlatform>Any CPU</LastUsedPlatform>
    <SiteUrlToLaunchAfterPublish>http://ruvpp-oferta-api-dev.azurewebsites.net</SiteUrlToLaunchAfterPublish>
    <LaunchSiteAfterPublish>True</LaunchSiteAfterPublish>
    <ExcludeApp_Data>False</ExcludeApp_Data>
    <MSDeployServiceURL>ruvpp-oferta-api-dev.scm.azurewebsites.net:443</MSDeployServiceURL>
    <DeployIisAppPath>ruvpp-oferta-api-dev</DeployIisAppPath>
    <RemoteSitePhysicalPath />
    <SkipExtraFilesOnServer>True</SkipExtraFilesOnServer>
    <MSDeployPublishMethod>WMSVC</MSDeployPublishMethod>
    <EnableMSDeployBackup>False</EnableMSDeployBackup>
    <UserName>$ruvpp-oferta-api-dev</UserName>
    <_SavePWD>True</_SavePWD>
    <_DestinationType>AzureWebSite</_DestinationType>
    <PublishDatabaseSettings>
      <Objects>
        <ObjectGroup Name="NotificacionesStorageConnectionString" Order="1" Enabled="False">
          <Destination Path="" />
          <Object Type="DbDacFx">
            <PreSource Path="DefaultEndpointsProtocol=https;AccountName=ruvnotificacionescorreo;AccountKey=****************************************************************************************" includeData="False" />
            <Source Path="$(IntermediateOutputPath)AutoScripts\NotificacionesStorageConnectionString_IncrementalSchemaOnly.dacpac" dacpacAction="Deploy" />
          </Object>
          <UpdateFrom Type="Web.Config">
            <Source MatchValue="DefaultEndpointsProtocol=https;AccountName=ruvnotificacionescorreo;AccountKey=****************************************************************************************" MatchAttributes="$(UpdateFromConnectionStringAttributes)" />
          </UpdateFrom>
        </ObjectGroup>
        <ObjectGroup Name="BitacoraStorageConnectionString" Order="2" Enabled="False">
          <Destination Path="" />
          <Object Type="DbDacFx">
            <PreSource Path="DefaultEndpointsProtocol=https;AccountName=ruvcomun;AccountKey=****************************************************************************************" includeData="False" />
            <Source Path="$(IntermediateOutputPath)AutoScripts\BitacoraStorageConnectionString_IncrementalSchemaOnly.dacpac" dacpacAction="Deploy" />
          </Object>
          <UpdateFrom Type="Web.Config">
            <Source MatchValue="DefaultEndpointsProtocol=https;AccountName=ruvcomun;AccountKey=****************************************************************************************" MatchAttributes="$(UpdateFromConnectionStringAttributes)" />
          </UpdateFrom>
        </ObjectGroup>
        <ObjectGroup Name="GeneralesStorageConnection" Order="3" Enabled="False">
          <Destination Path="" />
          <Object Type="DbDacFx">
            <PreSource Path="DefaultEndpointsProtocol=https;AccountName=ruvarchivosempresa;AccountKey=****************************************************************************************" includeData="False" />
            <Source Path="$(IntermediateOutputPath)AutoScripts\GeneralesStorageConnection_IncrementalSchemaOnly.dacpac" dacpacAction="Deploy" />
          </Object>
          <UpdateFrom Type="Web.Config">
            <Source MatchValue="DefaultEndpointsProtocol=https;AccountName=ruvarchivosempresa;AccountKey=****************************************************************************************" MatchAttributes="$(UpdateFromConnectionStringAttributes)" />
          </UpdateFrom>
        </ObjectGroup>
        <ObjectGroup Name="HistoricosConnection" Order="4" Enabled="False">
          <Destination Path="" />
          <Object Type="DbDacFx">
            <PreSource Path="Data Source=bd-qadev02-sd;Initial Catalog=RUVPlus_DEV;Persist Security Info=True;User ID=3XeAPPrUVM45Devs%3M;Password=*********$RF(" includeData="False" />
            <Source Path="$(IntermediateOutputPath)AutoScripts\HistoricosConnection_IncrementalSchemaOnly.dacpac" dacpacAction="Deploy" />
          </Object>
          <UpdateFrom Type="Web.Config">
            <Source MatchValue="data source=bd-qadev02-sd;initial catalog=RUVPlus_DEV;persist security info=True;user id=3XeAPPrUVM45Devs%3M;password=*********$RF(;multipleactiveresultsets=True" MatchAttributes="$(UpdateFromConnectionStringAttributes)" />
          </UpdateFrom>
        </ObjectGroup>
        <ObjectGroup Name="GeneralesConnection" Order="5" Enabled="False">
          <Destination Path="" />
          <Object Type="DbDacFx">
            <PreSource Path="Data Source=bd-qadev02-sd;Initial Catalog=RUVPlus_DEV;Persist Security Info=True;User ID=3XeAPPrUVM45Devs%3M;Password=*********$RF(" includeData="False" />
            <Source Path="$(IntermediateOutputPath)AutoScripts\GeneralesConnection_IncrementalSchemaOnly.dacpac" dacpacAction="Deploy" />
          </Object>
          <UpdateFrom Type="Web.Config">
            <Source MatchValue="data source=bd-qadev02-sd;initial catalog=RUVPlus_DEV;persist security info=True;user id=3XeAPPrUVM45Devs%3M;password=*********$RF(;multipleactiveresultsets=True" MatchAttributes="$(UpdateFromConnectionStringAttributes)" />
          </UpdateFrom>
        </ObjectGroup>
        <ObjectGroup Name="RuvAvaluosBitacora" Order="6" Enabled="False">
          <Destination Path="" />
          <Object Type="DbDacFx">
            <PreSource Path="Data Source=bd-qadev02-sd;Initial Catalog=RUV_DEV;Persist Security Info=True;User ID=EX34ppRuvAs1sDevnT5);Password=*****;*****************;" includeData="False" />
            <Source Path="$(IntermediateOutputPath)AutoScripts\RuvAvaluosBitacora_IncrementalSchemaOnly.dacpac" dacpacAction="Deploy" />
          </Object>
          <UpdateFrom Type="Web.Config">
            <Source MatchValue="data source=bd-qadev02-sd;initial catalog=RUV_DEV;persist security info=True;user id=EX34ppRuvAs1sDevnT5);password=************;multipleactiveresultsets=True" MatchAttributes="$(UpdateFromConnectionStringAttributes)" />
          </UpdateFrom>
        </ObjectGroup>
        <ObjectGroup Name="RuvAsisConnection" Order="7" Enabled="False">
          <Destination Path="" />
          <Object Type="DbDacFx">
            <PreSource Path="Data Source=bd-qadev02-sd;Initial Catalog=RUV_DEV;Persist Security Info=True;User ID=EX34ppRuvAs1sDevnT5);Password=*****;*****************;" includeData="False" />
            <Source Path="$(IntermediateOutputPath)AutoScripts\RuvAsisConnection_IncrementalSchemaOnly.dacpac" dacpacAction="Deploy" />
          </Object>
          <UpdateFrom Type="Web.Config">
            <Source MatchValue="data source=bd-qadev02-sd;initial catalog=RUV_DEV;persist security info=True;user id=EX34ppRuvAs1sDevnT5);password=************;multipleactiveresultsets=True" MatchAttributes="$(UpdateFromConnectionStringAttributes)" />
          </UpdateFrom>
        </ObjectGroup>
        <ObjectGroup Name="OfertaConnection" Order="8" Enabled="False">
          <Destination Path="" />
          <Object Type="DbDacFx">
            <PreSource Path="Data Source=bd-qadev02-sd;Initial Catalog=RUVPlus_DEV;Persist Security Info=True;User ID=3XeAPPrUVM45Devs%3M;Password=*********$RF(" includeData="False" />
            <Source Path="$(IntermediateOutputPath)AutoScripts\OfertaConnection_IncrementalSchemaOnly.dacpac" dacpacAction="Deploy" />
          </Object>
          <UpdateFrom Type="Web.Config">
            <Source MatchValue="data source=bd-qadev02-sd;initial catalog=RUVPlus_DEV;persist security info=True;user id=3XeAPPrUVM45Devs%3M;password=*********$RF(;multipleactiveresultsets=True" MatchAttributes="$(UpdateFromConnectionStringAttributes)" />
          </UpdateFrom>
        </ObjectGroup>
        <ObjectGroup Name="OracleConnection" Order="9" Enabled="False">
          <Destination Path="" />
          <Object Type="DbDacFx">
            <PreSource Path="DATA SOURCE=map-q2-sd.cloudapp.net:25450/DSIGRUV;PERSIST SECURITY INFO=True;USER ID=OFERTADEV;PASSWORD=**$$w0rdDEV" includeData="False" />
            <Source Path="$(IntermediateOutputPath)AutoScripts\OracleConnection_IncrementalSchemaOnly.dacpac" dacpacAction="Deploy" />
          </Object>
          <UpdateFrom Type="Web.Config">
            <Source MatchValue="DATA SOURCE=map-q2-sd.cloudapp.net:25450/DSIGRUV;PERSIST SECURITY INFO=True;USER ID=OFERTADEV;PASSWORD=**$$w0rdDEV" MatchAttributes="$(UpdateFromConnectionStringAttributes)" />
          </UpdateFrom>
        </ObjectGroup>
        <ObjectGroup Name="RUV_LOG" Order="10" Enabled="False" xmlns="">
          <Destination Path="" />
          <Object Type="DbDacFx">
            <PreSource Path="Data Source=bd-sap02-qa.eastus.cloudapp.azure.com;Initial Catalog=RUV_LOG;User ID=sa;Password=***********" includeData="False" />
            <Source Path="$(IntermediateOutputPath)AutoScripts\RUV_LOG_IncrementalSchemaOnly.dacpac" dacpacAction="Deploy" />
          </Object>
          <UpdateFrom Type="Web.Config">
            <Source MatchValue="Data Source=bd-sap02-qa.eastus.cloudapp.azure.com;Initial Catalog=RUV_LOG;User ID=sa;Password=***********" MatchAttributes="$(UpdateFromConnectionStringAttributes)" />
          </UpdateFrom>
        </ObjectGroup>
      </Objects>
    </PublishDatabaseSettings>
    <ADUsesOwinOrOpenIdConnect>False</ADUsesOwinOrOpenIdConnect>
    <EnableMsDeployAppOffline>False</EnableMsDeployAppOffline>
  </PropertyGroup>
  <ItemGroup>
    <MSDeployParameterValue Include="$(DeployParameterPrefix)BitacoraStorageConnectionString-Web.config Connection String" />
    <MSDeployParameterValue Include="$(DeployParameterPrefix)GeneralesConnection-Web.config Connection String" />
    <MSDeployParameterValue Include="$(DeployParameterPrefix)GeneralesStorageConnection-Web.config Connection String" />
    <MSDeployParameterValue Include="$(DeployParameterPrefix)HistoricosConnection-Web.config Connection String" />
    <MSDeployParameterValue Include="$(DeployParameterPrefix)NotificacionesStorageConnectionString-Web.config Connection String" />
    <MSDeployParameterValue Include="$(DeployParameterPrefix)OfertaConnection-Web.config Connection String" />
    <MSDeployParameterValue Include="$(DeployParameterPrefix)OracleConnection-Web.config Connection String" />
    <MSDeployParameterValue Include="$(DeployParameterPrefix)RuvAsisConnection-Web.config Connection String" />
    <MSDeployParameterValue Include="$(DeployParameterPrefix)RuvAvaluosBitacora-Web.config Connection String" />
    <MSDeployParameterValue Include="$(DeployParameterPrefix)RUV_LOG-Web.config Connection String" />
  </ItemGroup>
</Project>