﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.Promotor.Api.Servicio
{
   public class Empresa
    {
        public string numeroEmpresa { get; set; }
        public int tipoCategoria { get; set; }
        public string nombre { get; set; }
        public string apellidoPaterno { get; set; }
        public string apellidoMaterno { get; set; }
        public string rfc { get; set; }
        public string nrp { get; set; }
        public string direccion { get; set; }
        public List<Telefono> telefonos { get; set; }
        public List<Correo> correos { get; set; }
        public string objetoSocial { get; set; }
        public List<Accionista> accionistas { get; set; }
        public List<Promotore> promotores { get; set; }
    }
}
