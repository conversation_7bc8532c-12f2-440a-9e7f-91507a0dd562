﻿using Microsoft.ApplicationInsights;
using RUV.RUVPP.Oferta.Datos.Prototipos;
using RUV.Comun.Negocio;
using RUV.RUVPP.Oferta.Modelo.Prototipos.Api;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Dominio.Prototipo.Implementacion
{
    /// <summary>
    /// 
    /// </summary>
    public class ServicioPrototipoCatalogos : ServicioDominioBase, IServicioPrototipoCatalogos
    {
        #region Campos

        /// <summary>
        /// 
        /// </summary>
        private readonly ICatalogosPrototiposDataMapper _catalogosPM;

        #endregion

        #region Constructor

        /// <summary>
        /// 
        /// </summary>
        /// <param name="clienteTelemetria"></param>
        /// <param name="catalogosPM"></param>
        public ServicioPrototipoCatalogos(ICatalogosPrototiposDataMapper catalogosPM)
            : base()
        {
            this._catalogosPM = catalogosPM;
        }

        #endregion

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public async Task<List<TipologiaVivienda>> ObtenerCatalogoDeTipologiaViviendaAsync()
        {
            return await this._catalogosPM.ObtenerCatalogoDeTipologiaViviendaAsync();
        }
        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public async Task<List<ClasificacionVivienda>> ObtenerCatalogoDeClasificacionViviendaAsync()
        {
            return await this._catalogosPM.ObtenerCatalogoDeClasificacionViviendaAsync();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public async Task<List<TipoDimensionAreaVivienda>> ObtenerCatalogoTipoDimensionAreaViviendaAsync()
        {
            return await this._catalogosPM.ObtenerCatalogoTipoDimensionAreaViviendaAsync();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public async Task<List<AreaVivienda>> ObtenerCatalogoAreaViviendaAsync()
        {
            return await this._catalogosPM.ObtenerCatalogoAreaViviendaAsync();
        }


        #region Metodos sobreescritos

        protected override void DisposeManagedResources()
        {
            this._catalogosPM.Dispose();
        }

        #endregion
    }
}
