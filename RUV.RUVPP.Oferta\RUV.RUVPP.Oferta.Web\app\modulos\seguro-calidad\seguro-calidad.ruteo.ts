﻿import { Routes, RouterModule } from '@angular/router';

import { ComponentePadronAseguradoras } from './modulos/padron-aseguradoras/padron-aseguradoras.componente';
import { ComponenteGestionAseguradoras } from './modulos/gestion-aseguradoras/gestion-aseguradoras.componente';
import { ComponenteGestionIncidencias } from './modulos/gestion-incidencias/gestion-incidencias.componente';
// import { ComponenteGestionIncidenciasInterno } from './modulos/gestion-incidencias-interno/gestion-incidencias-interno.componente';
import { ComponenteValidarRelacionComercial } from './modulos/validar-relacion-comercial/validar-relacion-comercial.componente';
import { ComponenteSeleccionAseguradora } from './modulos/seleccion-aseguradora/seleccion-aseguradora.componente';
import { ComponenteSolicitarPoliza } from './modulos/solicitar-poliza/solicitar-poliza.componente';
import { ComponenteNuevoEstadoCuenta } from './modulos/nuevo-estado-cuenta/nuevo-estado-cuenta.componente';
import { ComponenteConsultaPoliza } from './modulos/consulta-poliza/consulta-poliza.componente';
import { ComponenteConsultaAseguradoras } from './modulos/consulta-aseguradoras/consulta-aseguradoras.componente';
import { ComponenteConsultaAsignacionDesarrollador } from './modulos/consulta-asignacion-desarrollador/consulta-asignacion-desarrollador.componente';
import { ComponenteConsultaAsignacionAseguradora } from './modulos/consulta-asignacion-aseguradora/consulta-asignacion-aseguradora.componente';
import { ComponentePagoDiferenciaOV } from './modulos/pago-diferencia-ov/pago-diferencia-ov.componente';
import { ComponenteConsultaPagoDiferenciaOV } from './modulos/consulta-pago-diferencia-ov/consulta-pago-diferencia-ov.componente';
import { ComponenteSolicitarDevolucionDesarrollador } from './modulos/solicitar-devolucion-desarrollador/solicitar-devolucion-desarrollador.componente';
import { ComponenteRegistrarSiniestro } from './modulos/registrar-siniestro/registrar-siniestro.componente';
import { ComponenteConsultaSiniestro } from './modulos/consulta-siniestro/consulta-siniestro.componente';
import { ComponenteConsultaPagosAseguradora } from './modulos/consulta-pagos-aseguradora/consulta-pagos-aseguradora.componente';

import { ComponenteReportePagosAseguradora } from './modulos/reporte-pagos-aseguradora/reporte-pagos-aseguradora.componente';
import { ComponenteReportePagoPlataformaSeguro } from './modulos/reporte-pago-plataforma-seguro/reporte-pago-plataforma-seguro.componente';

import { ComponenteConsultaDevoluciones } from './modulos/consulta-devoluciones/consulta-devoluciones.componente';
// import { ComponenteConsultaDevolucionesInterno } from './modulos/consulta-devoluciones-interno/consulta-devoluciones-interno.componente';
import { ComponenteSolicitudDevolucionEvaluacion } from './modulos/solicitud-devolucion-evaluacion/solicitud-devolucion-evaluacion.componente';
// import { ComponenteSolicitudDevolucionPoliza } from './modulos/solicitud-devolucion-poliza/solicitud-devolucion-poliza.componente';

import { ComponenteSolicitarPagoAseguradora } from './modulos/solicitar-pagos-aseguradora/solicitar-pagos-aseguradora.componente';
import { GenerarFichaPagoDiferenciaCuvComponente } from './componentes/generar-ficha-pago-diferencia-cuv.componente';

export const ParametroIdIncidencia = "idIncidencia";
export const ParametroCuv = "cuv";

/**
 * Lista de rutas del modulo de seguro de calidad.
 * @type {{path: string, component: ComponenteProyectos}[]}
 */

export const RutasSeguroCalidad = [
    {
        path: 'Padron-Aseguradoras',
        component: ComponentePadronAseguradoras
    },
    {
        path: 'Gestion-Aseguradoras',
        component: ComponenteGestionAseguradoras
    },
    {
        path: 'Gestion-Incidencias',
        component: ComponenteGestionIncidencias
    },
    /*{
        path: 'Gestion-Incidencias-Interno',
        component: ComponenteGestionIncidenciasInterno
    },*/
    {
        path: `Gestion-Incidencias/idIncidencia/:${ParametroIdIncidencia}`,
        component: ComponenteGestionIncidencias
    },
    {
        path: 'Validar-Relacion-Comercial',
        component: ComponenteValidarRelacionComercial
    },
    {
        path: 'Seleccion-Aseguradora',
        component: ComponenteSeleccionAseguradora
    },
    {
        path: 'Solicitar-Poliza',
        component: ComponenteSolicitarPoliza
    },
    {
        path: 'Nuevo-Estado-Cuenta',
        component: ComponenteNuevoEstadoCuenta
    },
    {
        path: 'Consulta-Poliza',
        component: ComponenteConsultaPoliza
    },
    {
        path: 'Consulta-Aseguradoras',
        component: ComponenteConsultaAseguradoras
    },
    {
        path: 'Consulta-Asignacion-Desarrollador',
        component: ComponenteConsultaAsignacionDesarrollador
    },
    {
        path: 'Consulta-Asignacion-Aseguradoras',
        component: ComponenteConsultaAsignacionAseguradora
    },
    {
        path: 'Pago-Diferencia-OV',
        component: ComponentePagoDiferenciaOV
    },
    {
        path: 'Solicitar-Devolucion-Desarrollador',
        component: ComponenteSolicitarDevolucionDesarrollador
    },
    {
        path: 'Consulta-Pago-Diferencia-OV',
        component: ComponenteConsultaPagoDiferenciaOV
    },
    {
        path: 'Registrar-Siniestro',
        component: ComponenteRegistrarSiniestro
    },
    {
        path: 'Consulta-Siniestro',
        component: ComponenteConsultaSiniestro
    },
    {
        path: `Consulta-Siniestro/cuv/:${ParametroCuv}`,
        component: ComponenteConsultaSiniestro
    },
    {
        path: `Consulta-Pagos-Aseguradora`,
        component: ComponenteConsultaPagosAseguradora
    },
    {

        path: `Reporte-Pagos-Aseguradora`,
        component: ComponenteReportePagosAseguradora

    },
    {

        path: `Consulta-Devoluciones`,
        component: ComponenteConsultaDevoluciones

    },
    /*{

        path: `Consulta-Devoluciones-Interno`,
        component: ComponenteConsultaDevolucionesInterno

    },*/
    {

        path: `Solicitud-Devolucion-Evaluacion`,
        component: ComponenteSolicitudDevolucionEvaluacion

    },
    /*{

        path: `Solicitud-Devolucion-Poliza`,
        component: ComponenteSolicitudDevolucionPoliza

    },*/
    {

        path: `Reporte-Pago-Plataforma-Seguro`,
        component: ComponenteReportePagoPlataformaSeguro

    },

    {

        path: `Solicitar-Pago-Aseguradora`,
        component: ComponenteSolicitarPagoAseguradora

    },
    {
        path: 'Generar-Ficha-Pago-Diferencia-CUV',
        component: GenerarFichaPagoDiferenciaCuvComponente
    },
];

/**
 * Modulo de rutas del modulo de registro de proyectos.
 */

export const ruteo = RouterModule.forChild(RutasSeguroCalidad);