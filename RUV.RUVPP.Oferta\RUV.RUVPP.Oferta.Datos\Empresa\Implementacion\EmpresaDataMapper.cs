﻿using RUV.Comun.Datos.Mapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.ApplicationInsights;
using RUV.RUVPP.Oferta.Modelo.Proyectos.Data;
using Dapper;
using System.Data;
using RUV.Comun.Datos.Mapper;
using RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api;

namespace RUV.RUVPP.Oferta.Datos.Empresa.Implementacion
{
    public class EmpresaDataMapper : SqlDataMapperBase, IEmpresaDataMapper
    {
        public EmpresaDataMapper(string nombreCadenaConexion, GestorConexiones gestorConexiones = null) : base(nombreCadenaConexion, gestorConexiones)
        {
        }

        public async Task<DatosCPV> ConsultaEmpresaPorNRRUV(int? idEmpresa, string nrruv)
        {
            var parameters = new DynamicParameters();
            parameters.Add("@idEmpresa", idEmpresa);
            parameters.Add("@nrruv", nrruv);

            var reader = await this._conexion.QueryMultipleAsync("empresa.ObtenerEmpresaPorNRRUV", parameters, commandType: CommandType.StoredProcedure);

            var resultado = await reader.ReadAsync<DatosCPV>();

            return resultado.FirstOrDefault();
        }

        public async Task<DatosCPV> ConsultaEmpresaInterno(int idProyecto, int idOferta, string cuv)
        {
            var parameters = new DynamicParameters();
            if (idProyecto != 0) {
                parameters.Add("@idProyecto", idProyecto);
            }
            if (idOferta != 0)
            {
                parameters.Add("@idOferta", idOferta);
            }
            if (cuv != null && !cuv.Equals("x"))
            {
                parameters.Add("@cuv", cuv);
            }
            
            var reader = await this._conexion.QueryMultipleAsync("empresa.ObtenerEmpresaInterno", parameters, commandType: CommandType.StoredProcedure);

            var resultado = await reader.ReadAsync<DatosCPV>();

            return resultado.FirstOrDefault();
        }

        public async Task<string> ObtenerNumeroRegistroRUV(int idEmpresa)
        {
            var parameters = new DynamicParameters();
            parameters.Add("@idEmpresa", idEmpresa);

            var reader = await this._conexion.QueryMultipleAsync("empresa.usp_ObtenerNumeroRegistroRUV", parameters, commandType: CommandType.StoredProcedure, commandTimeout: 160);

            var registro = await reader.ReadAsync<string>();

            return registro.FirstOrDefault();
        }

        public async Task<DatosGeneralesEmpresa> ConsultarDatosGeneralesEmpresa(int idRuvAsIs) {
            var parameters = new DynamicParameters();
            parameters.Add("@idRUVAsis", idRuvAsIs);

            var reader = await this._conexion.QueryMultipleAsync("empresa.ConsultarDatosEmpresa", parameters, commandType: CommandType.StoredProcedure, commandTimeout: 160);

            var registro = await reader.ReadAsync<DatosGeneralesEmpresa>();

            return registro.FirstOrDefault();
        }

        public async Task<DatosGeneralesEmpresa> ObtenerEmpresaxUsuario(int idUsuario)
        {
            var parameters = new DynamicParameters();
            parameters.Add("@idUsuario", idUsuario);

            var reader = await this._conexion.QueryMultipleAsync("empresa.ConsultarDatosEmpresaxUsuario", parameters, commandType: CommandType.StoredProcedure, commandTimeout: 160);

            var registro = await reader.ReadAsync<DatosGeneralesEmpresa>();

            return registro.FirstOrDefault();
        }
    }
}
