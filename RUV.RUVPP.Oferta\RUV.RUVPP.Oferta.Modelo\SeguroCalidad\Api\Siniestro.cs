﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api
{
    public class Siniestro
    {
        public int? idSiniestro { get; set; }        
        public string claveAseguradora { get; set; }
        public string descripcion { get; set; }
        public DateTime? fechaRegistro { get; set; }
        public DateTime? fechaActualizacion { get; set; }
        public bool? activo { get; set; }
        public DateTime? fechaSiniestro { get; set; }
        public DateTime? fechaAtencion { get; set; }
        public byte? idTipoSiniestro { get; set; }
        public byte? idTipoCoberturaSiniestro { get; set; }
        public byte? idestatusSiniestro { get; set; }
        public int? idPoliza { get; set; }
        public int? idUsuarioRegistro { get; set; }

        public DateTime? fechaRecepcionAseguradora { get; set; }        

    }
}
