﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.Mediciones
{
    /// <summary>
    /// Clase que modela la informacion de consulta de una CUV
    /// </summary>
    public class ConsultaCuv
    {
        /// <summary>
        /// CUV
        /// </summary>
        public string cuv { get; set; }
        /// <summary>
        /// Identificador del proyecto
        /// </summary>
        public int idProyecto { get; set; }
        /// <summary>
        /// Nombre del proyecto
        /// </summary>
        public string nombreProyecto { get; set; }
        /// <summary>
        /// Identificador de la oferta
        /// </summary>
        public int idOfertaVivienda { get; set; }
        /// <summary>
        /// Nombre de frente
        /// </summary>
        public string nombreFrente { get; set; }
        /// <summary>
        /// Orden Verificacion
        /// </summary>
        public string ordenVerificacion { get; set; }
        /// <summary>
        /// Identificador de la vivienda
        /// </summary>
        public int idVivienda { get; set; }
        /// <summary>
        /// Identificador del estatus de la vivienda
        /// </summary>
        public int idEstatusVivienda { get; set; }
        /// <summary>
        /// Estatus de la vivienda
        /// </summary>
        public string estatusVivienda { get; set; }
        /// <summary>
        /// Direccion
        /// </summary>
        public string direccion { get; set; }
        /// <summary>
        /// Identificador de la entidad federativa
        /// </summary>
        public int idEstado { get; set; }
        /// <summary>
        /// Nombre de la entidad federativa
        /// </summary>
        public string nombreEstado { get; set; }
        /// <summary>
        /// Porcentaje de avance de obra (0-100)
        /// </summary>
        public int? porcentajeAvanceObra { get; set; }
    }
}
