﻿using System;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using System.Threading.Tasks;
using System.Collections.Generic;
using Microsoft.ApplicationInsights;

namespace RUV.RUVPP.Oferta.Dominio.Tests
{
    [TestClass]
    public class ServicioDominoDesarrolloTests
    {
        //[TestMethod]
        //public async Task ObtenerDesarrollosFiltradosyPaginadoAsync_RegresaDatosDesdeBD()
        //{
        //    //Arrange
        //    int tamanioPagina = 11;
        //    int paginaActual = 1;
        //    int idEmpresa = 72;
        //    int totalRegistros = 20;

        //    var clienteTelemetria = new TelemetryClient();
        //    var mockDesarrolloDataMapper = new Mock<IDesarrolloDataMapper>();

        //    mockDesarrolloDataMapper
        //        .Setup(dm => dm.ObtenerDesarrollosFiltradosyPaginadoAsync(It.IsAny<int>(), It.IsAny<int>(), It.IsAny<int>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<int?>(), It.IsAny<int?>(), It.IsAny<int?>(), It.IsAny<int?>()))
        //        .Returns(async () => Tuple.Create(totalRegistros, new List<DesarrolloConsulta>()));

        //    var servicioDominio = new ServicioDominoDesarrollo(clienteTelemetria, mockDesarrolloDataMapper.Object);

        //    //Act
        //    var resultado = await servicioDominio.ObtenerDesarrollosFiltradosyPaginadoAsync(tamanioPagina, paginaActual, idEmpresa);

        //    //Assert
        //    mockDesarrolloDataMapper.Verify(dm => dm.ObtenerDesarrollosFiltradosyPaginadoAsync(tamanioPagina, paginaActual, idEmpresa, null, null, null, null, null, null));
        //}

        //[TestMethod]
        //public async Task ObtenerDesarrollosFiltradosyPaginadoAsync_CalculaTotalPaginasConBaseaRegistrosyTamanioPagina()
        //{
        //    //Arrange
        //    int tamanioPagina = 11;
        //    int paginaActual = 1;
        //    int idEmpresa = 72;
        //    int totalRegistros = 20;

        //    var clienteTelemetria = new TelemetryClient();
        //    var mockDesarrolloDataMapper = new Mock<IDesarrolloDataMapper>();

        //    mockDesarrolloDataMapper
        //        .Setup(dm => dm.ObtenerDesarrollosFiltradosyPaginadoAsync(It.IsAny<int>(), It.IsAny<int>(), It.IsAny<int>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<int?>(), It.IsAny<int?>(), It.IsAny<int?>(), It.IsAny<int?>()))
        //        .Returns(async () => Tuple.Create(totalRegistros, new List<DesarrolloConsulta>()));

        //    var servicioDominio = new ServicioDominoDesarrollo(clienteTelemetria, mockDesarrolloDataMapper.Object);

        //    //Act
        //    var resultado = await servicioDominio.ObtenerDesarrollosFiltradosyPaginadoAsync(tamanioPagina, paginaActual, idEmpresa);

        //    //Assert
        //    Assert.IsTrue(resultado.TotalPaginas == (int)Math.Ceiling((double)(totalRegistros / tamanioPagina)));
        //}
    }
}
