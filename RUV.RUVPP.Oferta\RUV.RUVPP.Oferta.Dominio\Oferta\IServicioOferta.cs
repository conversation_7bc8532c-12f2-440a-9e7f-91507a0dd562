﻿using RUV.Comun.Seguridad.JWT;
using RUV.Comun.Utilerias;
using RUV.RUVPP.Oferta.Comun.Modelo;
using RUV.RUVPP.Oferta.Modelo.AgenteServicio;
using RUV.RUVPP.Oferta.Modelo.Dictaminacion;
using RUV.RUVPP.Oferta.Modelo.Historico.Data;
using RUV.RUVPP.Oferta.Modelo.Mediciones;
using RUV.RUVPP.Oferta.Modelo.Oferta.Api;
using RUV.RUVPP.Oferta.Modelo.Oferta.Data;
using RUV.RUVPP.Oferta.Modelo.Proyectos.Data;
using RUV.RUVPP.Oferta.Modelo.Viviendas;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Dominio.Oferta
{
    public interface IServicioOferta : IDisposable
    {
        Task<int> GuardarOfertaAsync(int idProyecto, string nombre, string proyecto, CustomUserRuv usuario);
        Task<int> GuardarOfertaDetalleAsync(Modelo.Oferta.Api.Oferta oferta, CustomUserRuv usuario);
        Task<int> ActualizarOfertaAsync(OfertaVivienda ofertaVivienda, string oferta, bool fueronModificadosDatosSensibles);
        Task<int> ActualizarOfertaDetalleAsync(Modelo.Oferta.Api.Oferta oferta, CustomUserRuv usuario);
        Task EliminarOfertaParcialAsync(int idParcialOferta);
        Task<bool> EliminarOfertaAsync(int idOferta);

        Task<Vivienda> ObtenerDetalleViviendaAsisAsync(Vivienda vivienda);

        Task<List<Vivienda>> ObtenerViviendasFiltroAsync(int? idEmpresa, string noRuv, string nombreProyecto, int? idProyecto, int? idOferta, string claveOferta, int? idVivienda, string cuv);

        Task<ResultadoPaginado<List<Vivienda>>> ObtenerViviendasFiltroConPaginadoAsync(int tamanioPagina, int pagina, int? idEmpresa, string noRuv, string nombreProyecto, int? idProyecto, int? idOferta, int? idVivienda, string cuv);

        Task<List<Vivienda>> ObtenerViviendasPorIdProyectoAsync(int idProyecto);

        Task<ResultadoPaginado<List<ViviendaSinAsignar>>> ObtenerViviendasSinAsingarOfertaAsync(int idProyecto, int idEstatusOferta, int tamanioPagina, int pagina, int? idOferta);

        Task<List<ViviendaPrototipo>> ObtenerViviendasPrototiposAsync(int idProyecto, int? idOferta, int idEstatusOferta);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="idOferta"></param>
        /// <returns></returns>
        Task<Modelo.Oferta.Api.Oferta> ObtenerOfertaPorIdAsync(int idOferta);

        Task<Modelo.Oferta.Api.Oferta> ObtenerOfertasFiltroAsync(int? idEmpresa, string noRuv, string nombreProyecto, int? idProyecto, int? idOferta, string claveOferta);

        Task<ResultadoPaginado<List<Modelo.Oferta.Api.Oferta>>> ObtenerOfertasFiltroConPaginadoAsync(int tamanioPagina, int pagina, int? idEmpresa, string noRuv, string nombreProyecto, int? idProyecto, int? idOferta, int? idEstatus);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="idOferta"></param>
        /// <param name="nombreProyecto"></param>
        /// <param name="idEmpresa"></param>
        /// <returns></returns>
        Task<ResultadoPaginado<List<Modelo.Oferta.Api.Oferta>>> ObtenerOfertasFiltradoAsync(int tamanioPagina, int pagina, string claveOferta, string nombreProyecto, int? idEmpresa);
        Task<Modelo.Oferta.Api.Oferta> ObtenerDatosGeneralesAsync(int idOferta);
        Task<Modelo.Oferta.Api.Oferta> ObtenerOfertaAsync(int idOferta);
        Task<OfertaVivienda> ObtenerOfertaDetalleAsync(int idOferta);
        Task<Dictionary<int, string>> ObtenerProyectosEmpresaAsync(int idEmpresa);
        Task<Modelo.Oferta.Api.Oferta> ObtenerOfertaxProyectoAsync(int idProyecto);

        Task<List<Modelo.Oferta.Api.Oferta>> ObtenerOfertasxProyectoAsync(int idProyecto);

        Task<List<Modelo.Oferta.Data.PrototipoCatalogo>> ObtenerCatalogoPrototipoAsyn();
        Task<List<Modelo.Oferta.Data.Documento>> ObtenerCatalogoDocumentosAsyn();
        Task<FichaPago> ObtenerFichaPagoAsync(int idOferta);
        /// <summary>
        /// Actualiza el estatus de oferta vivienda
        /// </summary>
        /// <param name="idOfertaVivienda">Identificador de la oferta vivienda</param>
        /// <param name="idEstatus">Identificador del estatus</param>
        /// <returns></returns>
        Task<bool> ActualizarEstatusOfertaViviendaAsync(int idOfertaVivienda, int idEstatus, bool actualizarFechaAceptacion = false);
        Task<int> DesvincularViviendaAsync(int idOfertaVivienda);
        Task<int> ObtenerEstatusViviendaAsIs(string cuv);

        /// <summary>
        /// Guarda/Actualiza la dictaminación, actualiza el estatus de la oferta y atiende la ODT.
        /// </summary>
        /// <param name="idOrdenTrabajo">Identificador de la orden de trabajo</param>
        /// <param name="idServicio">Identificador del servicio</param>
        /// <param name="idRegistro">Identificador del registro</param>
        /// <param name="parametrosDictaminacion">Objeto que contiene los siguientes parametros:
        /// seActualizaDictaminacion - FALSE si se inserta la dictaminacion, TRUE si se actualiza
        /// DictaminacionJSON -  cadena que contiene el JSON de dictaminacion</param>
        /// aceptacion - TRUE indica que se acepta la oferta, FALSE que se rechaza la oferta<returns></returns>
        Task<bool> EnviarDictaminacionOfertaVivienda(int idOrdenTrabajo, short idServicio, int idRegistro, ParametroBodyDictaminacion parametrosDictaminacion, CustomUserRuv usuario, bool esDictaminacionAsincrona = false);

        /// <summary>
        /// Obtiene el listado de ordenes de trabajo para una oferta por idOferta
        /// </summary>
        /// <param name="idOfertaVivienda">Identificador de la oferta vivienda</param>
        /// <param name="tamanioPagina">Tamaño de la pagina</param>
        /// <param name="pagina">Pagina</param>
        /// <returns></returns>
        Task<ResultadoPaginado<List<OrdenTrabajoHistoricoValidacion>>> ObtenerOrdenesTrabajoPorOfertaPaginadoAsync(int idOfertaVivienda, int tamanioPagina, int pagina);

        Task<List<Vivienda>> ObtenerViviendasPorOfertaVivienda(int idOfertaVivienda);

        Task<List<Vivienda>> ObtenerViviendasReportePorOfertaVivienda(int idOfertaVivienda);

        /// <summary>
        /// Obtiene el listado viviendas para una oferta por idOferta
        /// </summary>
        /// <param name="idOfertaVivienda">Identificador de la oferta vivienda</param>
        /// <param name="tamanioPagina">Tamaño de la pagina</param>
        /// <param name="pagina">Pagina</param>
        /// <returns></returns>
        Task<ResultadoPaginado<List<Vivienda>>> ObtenerViviendasOfertaPaginadoAsync(int idOfertaVivienda, int tamanioPagina, int pagina);

        Task<List<int>> ObtenerViviendasAsignadasPorListaAsync(int idOferta, string listaIdsViviendas);

        /// <summary>
        /// Obtiene la documentación ligada al idOferta introducido.
        /// </summary>
        /// <param name="idOferta">Identificador de la oferta.</param>
        /// <returns></returns>
        Task<ResultadoPaginado<Modelo.Oferta.Api.Oferta>> ObtenerDocumentosOfertaAsync(int idOferta, int pagina, int tamPag, int num);

        /// <summary>
        /// Obtiene el catalogo de estatus de vivienda
        /// </summary>        
        Task<List<Modelo.Oferta.Data.EstatusVivienda>> ObtenerCatalogoEstatusViviendaAsync();

        /// <summary>
        /// Obtienes los ids y clave de oferta con estatus pertenecientes a un idProyecto
        /// </summary>
        /// <param name="idProyecto">Identificador del proyecto</param>        
        Task<List<ClaveOferta>> ObtenerIdsOfertaPorProyectoAsync(int idProyecto, int idEstatusOferta);

        #region Pago Oferta
        /// <summary>
        /// Metodo para confirmar el pago en SAP una vez que éste sea pagado
        /// </summary>
        /// <param name="fichaPagoDTO">id de la oferta de vivienda</param>
        /// <returns>Regresa true si el proceso se ejecuta correctamente</returns>
        Task<bool> ConfirmarPagoPorIdAsync(FichaPagoDTO fichaPagoDTO);

        /// <summary>
        ///Metodo para calcular el monto de las viviendas seleccionadas
        /// </summary>
        /// <param name="viviendasProyecto">Viviendas del proyecto</param>
        /// /// <param name="idProyecto">Id del proyecto donde se va a generar la oferta de vivienda</param>
        /// <returns>Regresa el monto total de la oferta</returns>
        Task<decimal> ObtenerMontoOfertaAsync(List<ViviendaPrototipo> viviendasProyecto, int idProyecto);
        #endregion

        #region Puntajes

        Task<Puntajes> ObtenerConceptosViviendaAsync(int idCuv, int idTipoPuntaje);
        Task<Puntajes> ObtenerElementosViviendaAsync(int idCuv);
        Task<ResultadoPaginado<List<Vivienda>>> ObtenerPuntajesViviendasPaginadoAsync(int tamanioPagina, int pagina, int? idProyecto, int? idOferta, int? idOrdenVerificacion, string idCuv);

        #endregion

        /// <summary>
        /// Envía un idOferta para obtener la lista de viviendas con sus estatus respectivos.
        /// </summary>
        /// <param name="idOferta">Identificador de la oferta.</param>        
        Task<List<Tuple<int, int, int>>> ObtenerViviendasPorIdOfertaAsync(int idOferta);

        /// <summary>
        /// Envía un idVivienda y obtiene el idOfertaVivienda que tiene asignado.
        /// </summary>
        /// <param name="idVivienda">Identificador de la vivienda para obtener la oferta a la que pertenece.</param>        
        Task<List<Tuple<int, int?>>> ObtenerIdOfertaPorIdViviendaAsync(int[] idVivienda);

        /// <summary>
        /// Envía un idVivienda y obtiene el idOfertaVivienda que tiene asignado.
        /// </summary>
        /// <param name="idVivienda">Identificador de la vivienda para obtener la oferta a la que pertenece.</param>        
        Task<bool> IndividualizarViviendasMasivo(List<ViviendaIndividualizable> arregloViviendas, CustomUserRuv usuario);

        Task<List<PrototipoVivienda>> ObtenerPrototipoConsultaGeneral(int? idProyecto, int? idOferta, string cuv = null);
        /// <summary>
        /// Obtiene la clave oferta de una cuv
        /// </summary>
        /// <param name="cuv">Clave única de vivienda</param>
        /// <returns></returns>
        Task<string> ObtenerClaveOfertaxCuvAsync(string cuv);

        /// <summary>
        /// Actualiza el estatus de las CUV a Disponible.
        /// </summary>
        /// <param name="listaIdVivienda">Arreglo de Identificadores de Vivienda.</param>
        /// <param name="listaIdDocumento">Arreglo de Identificadores de Documentos Asociados.</param>
        /// <param name="usuario">Usuario que realiza el acción.</param>
        Task<bool> DisponibilizarListaCuv(int[] listaIdVivienda, int[] listaIdDocumento, CustomUserRuv usuario);

        /// <summary>
        /// Actualiza el estatus de las CUV a Individualizada.
        /// </summary>
        /// <param name="idsVivienda">Identificadores de vivienda como 'string' separados por comas.</param>
        /// <param name="fecha">Fecha de Individualizar.</param>
        /// <param name="medioIndividualizacion">Identificador del Medio de Individualizacion.</param>
        /// <param name="tipoIndividualizacion">Identificador del Tipo de Individualizacion.</param>
        /// <param name="usuario">Usuario que realiza el acción.</param>
        Task<bool> ActualizaCuvsAIndividualizadaPorIdsViviendaAsync(int[] idsVivienda, string fecha, int medioIndividualizacion, int? tipoIndividualizacion, CustomUserRuv usuario);

        /// <summary>
        /// Elimina las cuvs especificadas.
        /// </summary>
        /// <param name="idsVivienda">Identificadores de las viviendas.</param>
        /// <param name="usuario">Usuario que realiza la eliminación.</param>
        Task<bool> EliminarCuvsPorIdsViviendaAsync(int[] idsVivienda, CustomUserRuv usuario);

        /// <summary>
        /// Obtiene los documentos asociados a un Evento Vivienda.
        /// </summary>
        /// <param name="idEventoVivienda">Identificadores de las viviendas.</param>
        Task<List<DocumentoEventoVivienda>> ObtenerDocumentosEventoVivienda(int idEventoVivienda);

        /// <summary>
        /// Inserta valores en la tabla de Documentos por Cambio de Estatus de Vivienda.
        /// </summary>
        /// <param name="idDocumento">Identificador del Documento.</param>
        /// <param name="idVivienda">Identificador de la Vivienda.</param>
        /// <param name="descripcionArchivo">Descripción del Documento.</param>
        /// <returns></returns>
        Task<bool> InsertarValoresDocumentoPorEstatusVivienda(int idDocumento, int idVivienda, string descripcionArchivo);

        /// <summary>
        /// Obtiene documentos de acuerdo a los identificadores enviados.
        /// </summary>
        /// <param name="idDocumentos">Identificadores de los Documentos.</param>
        /// <returns></returns>
        Task<List<DocumentoEventoVivienda>> ObtenerDocumentosPorIdentificadores(int[] idDocumentos);

        /// <summary>
        /// Procedimiento Almacenado para Eliminar un registro de la tabla de Descripciones de Documentos por Evento Vivienda.
        /// </summary>
        /// <param name="idDocumento">Identificador del Documento.</param>
        /// <returns></returns>
        Task<bool> EliminarRegistrosDocumentoPorEventoVivienda(int idDocumento);

        /// <summary>
        /// Método para obtener los medios de individualización junto con un campo extra de Banco.
        /// </summary>
        /// <returns></returns>
        Task<List<Tuple<int, string>>> ObtenerMedioIndividualizacion();

        /// <summary>
        /// Método para obtener los bancos como medio de individualización.
        /// </summary>
        /// <returns></returns>
        Task<List<Tuple<int, string>>> ObtenerBancosIndividualizacion();

        /// <summary>
        /// Método para obtener los tipos de individualización de acuerdo a un medio introducido.
        /// </summary>
        /// <param name="idMedioIndividualizacion">Identificador del Medio seleccionado.</param>
        /// <returns></returns>
        Task<List<Tuple<int, string>>> ObtenerTipoIndividualizacion(int idMedioIndividualizacion);

        /// <summary>
        /// Método para insertar valores en la tabla de Individualización de Vivienda.
        /// </summary>
        /// <param name="idVivienda">Identificador de la Vivienda.</param>
        /// <param name="idMedioIndividualizacion">Identificador del Medio seleccionado.</param>
        /// <param name="idTipoIndividualizacion">Identificador del Tipo de Individualización. El dato es opcional.</param>
        /// <param name="fechaIndividualizacion">Fecha seleccionada.</param>
        /// <returns></returns>
        Task<bool> InsertarIndividualizacion(int idVivienda, int idMedioIndividualizacion, int? idTipoIndividualizacion, DateTime fechaIndividualizacion);

        /// <summary>
        /// Método para eliminar valores en la tabla de Individualización de Vivienda.
        /// </summary>
        /// <param name="idVivienda">Identificador de la Vivienda.</param>
        /// <returns></returns>
        Task<bool> EliminarIndividualizacion(int idVivienda);

        Task NotificarOfertaNuevaAsIs(Modelo.Oferta.Api.Oferta ofertaVivienda);

        Task<ResultadoPaginado<List<DatosSuspension>>> ObtenerDatosSuspensionAsync(int tamanioPagina, int pagina, string ordenVerificacion);
        Task<string> GenerarPDFCuvs(string idOfertaVivienda, bool cuvsValidadas, int idEmpresa);
    }
}