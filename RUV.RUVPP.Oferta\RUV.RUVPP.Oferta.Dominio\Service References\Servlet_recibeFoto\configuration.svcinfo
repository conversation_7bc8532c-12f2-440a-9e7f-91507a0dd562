﻿<?xml version="1.0" encoding="utf-8"?>
<configurationSnapshot xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="urn:schemas-microsoft-com:xml-wcfconfigurationsnapshot">
  <behaviors />
  <bindings>
    <binding digest="System.ServiceModel.Configuration.BasicHttpBindingElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089:&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data name=&quot;SI_recibeFoto_SOBinding&quot;&gt;&lt;security mode=&quot;Transport&quot; /&gt;&lt;/Data&gt;" bindingType="basicHttpBinding" name="SI_recibeFoto_SOBinding" />
    <binding digest="System.ServiceModel.Configuration.BasicHttpBindingElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089:&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data name=&quot;SI_recibeFoto_SOBinding1&quot;&gt;&lt;security mode=&quot;Transport&quot; /&gt;&lt;/Data&gt;" bindingType="basicHttpBinding" name="SI_recibeFoto_SOBinding1" />
    <binding digest="System.ServiceModel.Configuration.BasicHttpBindingElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089:&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data name=&quot;SI_recibeFoto_SOBinding2&quot; /&gt;" bindingType="basicHttpBinding" name="SI_recibeFoto_SOBinding2" />
  </bindings>
  <endpoints>
    <endpoint normalizedDigest="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data address=&quot;https://************:8997/XISOAPAdapter/MessageServlet_recibeFoto_QA&quot; binding=&quot;basicHttpBinding&quot; bindingConfiguration=&quot;SI_recibeFoto_SOBinding&quot; contract=&quot;Servlet_recibeFoto.SI_recibeFoto_SO&quot; name=&quot;HTTP_Port&quot; /&gt;" digest="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data address=&quot;https://************:8997/XISOAPAdapter/MessageServlet_recibeFoto_QA&quot; binding=&quot;basicHttpBinding&quot; bindingConfiguration=&quot;SI_recibeFoto_SOBinding&quot; contract=&quot;Servlet_recibeFoto.SI_recibeFoto_SO&quot; name=&quot;HTTP_Port&quot; /&gt;" contractName="Servlet_recibeFoto.SI_recibeFoto_SO" name="HTTP_Port" />
    <endpoint normalizedDigest="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data address=&quot;https://************:8997/XISOAPAdapter/MessageServlet_recibeFotoSec1_QA&quot; binding=&quot;basicHttpBinding&quot; bindingConfiguration=&quot;SI_recibeFoto_SOBinding1&quot; contract=&quot;Servlet_recibeFoto.SI_recibeFoto_SO&quot; name=&quot;HTTPS_Port&quot; /&gt;" digest="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data address=&quot;https://************:8997/XISOAPAdapter/MessageServlet_recibeFotoSec1_QA&quot; binding=&quot;basicHttpBinding&quot; bindingConfiguration=&quot;SI_recibeFoto_SOBinding1&quot; contract=&quot;Servlet_recibeFoto.SI_recibeFoto_SO&quot; name=&quot;HTTPS_Port&quot; /&gt;" contractName="Servlet_recibeFoto.SI_recibeFoto_SO" name="HTTPS_Port" />
  </endpoints>
</configurationSnapshot>