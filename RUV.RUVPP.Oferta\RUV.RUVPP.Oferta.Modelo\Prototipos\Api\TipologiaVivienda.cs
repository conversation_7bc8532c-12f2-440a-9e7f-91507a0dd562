﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.Prototipos.Api
{
    public class TipologiaVivienda
    {
        public byte? idTipologiaVivienda { get; set; }

        public string clave { get; set; }

        public string nombre { get; set; }

        public DateTime? fechaRegistro { get; set; }

        public DateTime? fechaActualizacion { get; set; }

        public bool? activo { get; set; }
    }
}
