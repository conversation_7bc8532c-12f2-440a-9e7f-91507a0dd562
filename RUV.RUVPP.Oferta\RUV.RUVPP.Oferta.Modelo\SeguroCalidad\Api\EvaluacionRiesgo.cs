﻿using RUV.RUVPP.Oferta.Comun.Modelo;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api
{
    public class EvaluacionRiesgo
    {
        public int? IdEvaluacionRiesgoxVivienda { get; set; }

        public int? IdEvaluacionRiesgo { get; set; }

        public int? IdVvGeneral { get; set; }

        public DateTime? FechaRegistro { get; set; }

        public bool? TieneRiesgo { get; set; }

        public string TituloIncidencia { get; set; }

        public string FolioAseguradora { get; set; }

        public string OpcionesMitigacion { get; set; }

        public string Descripcion { get; set; }

        public string ordenVerificacion { get; set; }

        public int? idGrupoIncidencia { get; set; }

        public int? IdUsuario { get; set; }
        public List<DocumentoRuv> Documentos { get; set; }

        public List<CuvSeleccionada> CuvsSeleccionadas { get; set; }
    }
}
