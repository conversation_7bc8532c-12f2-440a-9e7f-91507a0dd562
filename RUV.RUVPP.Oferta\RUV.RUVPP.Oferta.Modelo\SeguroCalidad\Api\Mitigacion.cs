﻿using RUV.RUVPP.Oferta.Comun.Modelo;
using RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api
{
    public class Mitigacion
    {
        public int? ClaveIncidencia { get; set; }

        public string OrdenVerificacion { get; set; }

        public string Cuv { get; set; }

        public DateTime? FechaRegistro { get; set; }

        public string NombreFrente { get; set; }

        public string Aseguradora { get; set; }

        public string OpcionesMitigacion { get; set; }

        public string MitigacionIncidencia { get; set; }

        public string tipoIncidencia { get; set; }
        public string tituloIncidencia { get; set; }

        public string idGrupoIncidencia { get; set; }

        public List<DocumentoRuv> documentos { get; set; }

        public List<IncidenciaGestion> listaIncidencias { get; set; }
    }
}
