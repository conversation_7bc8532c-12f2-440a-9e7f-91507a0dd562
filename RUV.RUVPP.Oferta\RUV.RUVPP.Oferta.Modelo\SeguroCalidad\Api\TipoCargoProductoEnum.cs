﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api
{
    public enum TipoCargoProductoEnum
    {
        PagoOfertaVivienda = 1,
        PagoOrdenVerificacion = 2,
        PagoDiferenciasSeguro = 3,
        PagoEvaluacionRiesgo = 4,
        PagoPolizaSeguro = 5,
        PagoOrdenVerificacionAmpliada = 6,
        PagoSISEVIVE = 7,
        DevolucionPagoPólizas = 8,
        DevolucionEvaluacionRiesgos = 9,
        CargoUsoPlataforma = 10,
        PagoHeevi = 11,
        AbonoPrepago = 12,
        PagoUsoPlataforma = 13,
        PagoVentanillaUnica = 14
    }
}
