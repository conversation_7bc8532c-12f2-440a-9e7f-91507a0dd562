﻿[
  {
    "idElemento": "nombrePlano",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function () { return validaciones.formatoCampo(modelo.nombre, 'NT', 1, 100); })()",
        "mensajeError": "Se requiere un dato alfanumérico"
      }
    ]
  },
  {
    "idElemento": "plano",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function () { return (modelo.plano.idDocumento > 0); })()",
        "mensajeError": "Este dato es requerido"
      }
    ]
  },
  {
    "idElemento": "folioSEPLADE",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function () { if(modelo.esAguascalientes === true){ return validaciones.formatoCampo(modelo.folioSEPLADE, 'NT', 2, 16); } else if(modelo.esAguascalientes === false){ return true; } })()",
        "mensajeError": "Se requiere un dato alfanumérico"
      }
    ]
  },
  {
    "idElemento": "folioAyto",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function () { if(modelo.esTijuana === true){ return validaciones.formatoCampo(modelo.folioAyto, 'NT', 16, 16); } else if(modelo.esTijuana === false){ return true; } })()",
        "mensajeError": "Se requiere un dato alfanumérico"
      }
    ]
  },
  {
    "idElemento": "nombrePropietario",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function () { return validaciones.formatoCampo(modelo.propietarioTerreno.nombrePropietario, 'NT', 1, 50); })()",
        "mensajeError": "Se requiere un dato alfanumérico"
      }
    ]
  },
  {
    "idElemento": "tomo",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function () { return validaciones.formatoCampo(modelo.propietarioTerreno.tomo, 'NT', 1, 10); })()",
        "mensajeError": "Se requiere un dato alfanumérico"
      }
    ]
  },
  {
    "idElemento": "numeroEscritura",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function () { return validaciones.formatoCampo(modelo.propietarioTerreno.noEscritura, 'N', 1, 15); })()",
        "mensajeError": "Se requiere un dato numérico"
      }
    ]
  },
  {
    "idElemento": "numeroCatastral",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function () { return validaciones.formatoCampo(modelo.propietarioTerreno.noCatastral, 'N', 1, 30); })()",
        "mensajeError": "Se requiere un dato numérico"
      }
    ]
  },
  {
    "idElemento": "volumen",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function () { return validaciones.formatoCampo(modelo.propietarioTerreno.volumen, 'NT', 1, 15 ); })()",
        "mensajeError": "Se requiere un dato numérico"
      }
    ]
  },
  {
    "idElemento": "areaTerreno",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function () { return validaciones.formatoCampo(modelo.propietarioTerreno.areaTerrenoEscriturado, 'N', 1, 6 ); })()",
        "mensajeError": "Se requiere un dato numérico"
      }
    ]
  },
  {
    "idElemento": "numeroNotario",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function () { return validaciones.formatoCampo(modelo.propietarioTerreno.noNotario, 'N', 1, 5 ); })()",
        "mensajeError": "Se requiere un dato numérico"
      }
    ]
  },
  {
    "idElemento": "numeroRPP",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function () { return validaciones.formatoCampo(modelo.propietarioTerreno.noRPP, 'N', 1, 8 ); })()",
        "mensajeError": "Se requiere un dato numérico"
      }
    ]
  },
  {
    "idElemento": "fechaEscrituracion",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function () { return modelo.propietarioTerreno.fechaEscrituracion != ''; })()",
        "mensajeError": "Este dato es requerido"
      },
      {
        "nombre": "Fecha invalida",
        "orden": 2,
        "funcion": "(function () { var fecha = new Date(); var fechaSeleccionada = new Date(modelo.propietarioTerreno.fechaEscrituracion); return ( fechaSeleccionada <= fecha ); })()",
        "mensajeError": "La fecha de escrituración debe ser menor a la fecha actual."
      }
    ]
  },
  {
    "idElemento": "nombreDirector",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function () { return validaciones.formatoCampo(modelo.dro.nombreDRO, 'NT', 1, 50); })()",
        "mensajeError": "Se requiere un dato alfanumérico"
      }
    ],
    "reglasDependientes": [
      {
        "idElemento": "apellidoMaterno"
      }
    ]
  },
  {
    "idElemento": "apellidoPaterno",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function () { return validaciones.formatoCampo(modelo.dro.apellidoPaterno, 'NT', 1, 50); })()",
        "mensajeError": "Se requiere un dato alfanumérico"
      }
    ],
    "reglasDependientes": [
      {
        "idElemento": "apellidoMaterno"
      }
    ]
  },
  {
    "idElemento": "apellidoMaterno",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function () { return validaciones.formatoCampo(modelo.dro.apellidoMaterno, 'NT', 0, 50); })()",
        "mensajeError": "Se requiere un dato alfanumérico"
      }
    ]
  },
  {
    "idElemento": "numeroPerito",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function () { return validaciones.formatoCampo(modelo.dro.noPerito, 'NT', 1, 50); })()",
        "mensajeError": "Se requiere un dato alfanumérico"
      }
    ]
  },
  {
    "idElemento": "fechaVigencia",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function () { return modelo.dro.fechaVigencia != ''; })()",
        "mensajeError": "Este dato es requerido"
      },
      {
        "nombre": "Fecha invalida",
        "orden": 2,
        "funcion": "(function () { var fecha = new Date(); var fechaSeleccionada = new Date(modelo.dro.fechaVigencia); return ( fechaSeleccionada >= fecha ); })()",
        "mensajeError": "La fecha de vigencia debe ser mayor a la fecha actual."
      }
    ]
  },
  {
    "idElemento": "licenciaDRO",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function () { return (modelo.dro.licencia.idDocumento > 0); })()",
        "mensajeError": "Este dato es requerido"
      }
    ]
  },
  {
    "idElemento": "identificacionDRO",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function () { return (modelo.dro.identificacionOficial.idDocumento > 0); })()",
        "mensajeError": "Este dato es requerido"
      }
    ]
  },
  {
    "idElemento": "esConstructor",
    "validaciones": [
      {
        "nombre": "Existe constructor",
        "orden": 1,
        "funcion": "(function() { if (modelo.constructor.esElMismo === true || (modelo.constructor.esElMismo === false && modelo.constructor.noRegistroRUV != '')) { return true; } else { return false; } })() ",
        "mensajeError": "Debe introducir el número de registro ruv."
      }
    ],
    "reglasDependientes": [
      {
        "idElemento": "noRegistroRUVConstructor"
      }
    ]
  },
  {
    "idElemento": "noRegistroRUVConstructor",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function() { if (modelo.constructor.esElMismo === true || (modelo.constructor.esElMismo === false && modelo.constructor.noRegistroRUV != '')) { return true; } else { return false; } })()",
        "mensajeError": "Este dato es requerido"
      },
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function() { if (modelo.constructor.esElMismo === true || (modelo.constructor.esElMismo === false && validaciones.formatoCampo(modelo.constructor.noRegistroRUV, 'N', 8, 8 ))) { return true; } else { return false; } })()",
        "mensajeError": "Se requiere un número de 8 digitos"
      }
    ]
  },
  {
    "idElemento": "esPromotor",
    "validaciones": [
      {
        "nombre": "Existe promotor",
        "orden": 1,
        "funcion": "(function() { if (modelo.promotor.esElMismo === true || (modelo.promotor.esElMismo === false && modelo.promotor.rfc != '' && modelo.promotor.nombreRazonSocial != '' && modelo.promotor.lada != '' && modelo.promotor.numeroTelefono != '' && modelo.promotor.correoElectronico != '')) { return true; } else { return false; } })()",
        "mensajeError": "Debe introducir todos los datos del promotor."
      }
    ],
    "reglasDependientes": [
      {
        "idElemento": "rfcPromotor"
      },
      {
        "idElemento": "nombreRazonSocialPromotor"
      },
      {
        "idElemento": "telefonoPromotor"
      },
      {
        "idElemento": "correoElectronicoPromotor"
      }
    ]
  },
  {
    "idElemento": "rfcPromotor",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function() { if (modelo.promotor.esElMismo === true || (modelo.promotor.esElMismo === false && modelo.promotor.rfc != '')) { return true; } else { return false; } })()",
        "mensajeError": "Este dato es requerido"
      },
      {
        "nombre": "RFC Valido",
        "orden": 2,
        "funcion": "(function() { if (modelo.promotor.esElMismo === true || (modelo.promotor.esElMismo === false && validaciones.esRfcValido(modelo.promotor.rfc))) { return true; } else { return false; } })()",
        "mensajeError": "El formato del RFC no es el correcto."
      },
      {
        "nombre": "Longitud maxima",
        "orden": 3,
        "funcion": "(function() { if (modelo.promotor.esElMismo === true || (modelo.promotor.esElMismo === false && modelo.promotor.rfc.length <= 13)) { return true; } else { return false; } })()",
        "mensajeError": "El RFC no debe de ser mayor a 13 caracters."
      }
    ]
  },
  {
    "idElemento": "nombreRazonSocialPromotor",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function() { if (modelo.promotor.esElMismo === true || (modelo.promotor.esElMismo === false && validaciones.formatoCampo(modelo.promotor.nombreRazonSocial, 'NT', 1, 150 ))) { return true; } else { return false; } })()",
        "mensajeError": "Este dato es requerido"
      }
    ]
  },
  {
    "idElemento": "telefonoPromotor",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function() { if (modelo.promotor.esElMismo === true || (modelo.promotor.esElMismo === false && modelo.promotor.numeroTelefono != '')) { return true; } else { return false; } })() ",
        "mensajeError": "Este dato es requerido"
      },
      {
        "nombre": "Dato requerido",
        "orden": 2,
        "funcion": "(function() { if (modelo.promotor.esElMismo === true || (modelo.promotor.esElMismo === false && validaciones.esTelefonoValido(modelo.promotor.numeroTelefono))) { return true; } else { return false; } })()",
        "mensajeError": "El teléfono debe de tener de 10 a 11 dígitos"
      }
    ]
  },
  {
    "idElemento": "correoElectronicoPromotor",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function() { if (modelo.promotor.esElMismo === true || (modelo.promotor.esElMismo === false && modelo.promotor.correoElectronico != '')) { return true; } else { return false; } })()",
        "mensajeError": "Este dato es requerido"
      },
      {
        "nombre": "Dato requerido",
        "orden": 2,
        "funcion": "(function() { if (modelo.promotor.esElMismo === true || (modelo.promotor.esElMismo === false && validaciones.esCorreoValido(modelo.promotor.correoElectronico))) { return true; } else { return false; } })()",
        "mensajeError": "Debe introducir un correo electrónico valido, debe introducir solo letras minúsculas."
      }
    ]
  },
  {
    "idElemento": "esVendedor",
    "validaciones": [
      {
        "nombre": "Existe vendedor",
        "orden": 1,
        "funcion": "(function() { if (modelo.vendedor.esElMismo === true || (modelo.vendedor.esElMismo === false && modelo.vendedor.rfc != '' && modelo.vendedor.nombreRazonSocial != '' && modelo.vendedor.numeroTelefono != '' && modelo.vendedor.correoElectronico != '')) { return true; } else { return false; } })()",
        "mensajeError": "Debe introducir todos los datos del vendedor."
      }
    ],
    "reglasDependientes": [
      {
        "idElemento": "rfcVendedor"
      },
      {
        "idElemento": "nombreRazonSocialVendedor"
      },
      {
        "idElemento": "telefonoVendedor"
      },
      {
        "idElemento": "correoElectronicoVendedor"
      }
    ]
  },
  {
    "idElemento": "rfcVendedor",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function() { if (modelo.vendedor.esElMismo === true || (modelo.vendedor.esElMismo === false && modelo.vendedor.rfc != '')) { return true; } else { return false; } })()",
        "mensajeError": "Este dato es requerido"
      },
      {
        "nombre": "RFC Valido",
        "orden": 2,
        "funcion": "(function() { if (modelo.vendedor.esElMismo === true || (modelo.vendedor.esElMismo === false && validaciones.esRfcValido(modelo.vendedor.rfc))) { return true; } else { return false; } })()",
        "mensajeError": "El formato del RFC no es el correcto."
      },
      {
        "nombre": "Longitud maxima",
        "orden": 3,
        "funcion": "(function() { if (modelo.vendedor.esElMismo === true || (modelo.vendedor.esElMismo === false && modelo.vendedor.rfc.length <= 13)) { return true; } else { return false; } })()",
        "mensajeError": "El RFC no debe de ser mayor a 13 caracters."
      }
    ]
  },
  {
    "idElemento": "nombreRazonSocialVendedor",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function() { if (modelo.vendedor.esElMismo === true || (modelo.vendedor.esElMismo === false && validaciones.formatoCampo(modelo.vendedor.nombreRazonSocial, 'NT', 1, 150 ))) { return true; } else { return false; } })()",
        "mensajeError": "Este dato es requerido"
      }
    ]
  },
  {
    "idElemento": "telefonoVendedor",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function() { if (modelo.vendedor.esElMismo === true || (modelo.vendedor.esElMismo === false && modelo.vendedor.numeroTelefono != '')) { return true; } else { return false; } })()",
        "mensajeError": "El teléfono debe de tener de 8 a 10 dígitos"
      },
      {
        "nombre": "Dato requerido",
        "orden": 2,
        "funcion": "(function() { if (modelo.vendedor.esElMismo === true || (modelo.vendedor.esElMismo === false && validaciones.esTelefonoValido(modelo.vendedor.numeroTelefono))) { return true; } else { return false; } })()",
        "mensajeError": "El teléfono debe de tener de 10 a 11 dígitos"
      }
    ]
  },
  {
    "idElemento": "correoElectronicoVendedor",
    "validaciones": [
      {
        "nombre": "Dato requerido",
        "orden": 1,
        "funcion": "(function() { if (modelo.vendedor.esElMismo === true || (modelo.vendedor.esElMismo === false && modelo.vendedor.correoElectronico != '')) { return true; } else { return false; } })()",
        "mensajeError": "Este dato es requerido"
      },
      {
        "nombre": "Dato requerido",
        "orden": 2,
        "funcion": "(function() { if (modelo.vendedor.esElMismo === true || (modelo.vendedor.esElMismo === false && validaciones.esCorreoValido(modelo.vendedor.correoElectronico))) { return true; } else { return false; } })()",
        "mensajeError": "Debe introducir un correo electrónico valido, debe introducir solo letras minúsculas."
      }
    ]
  },
  {
    "idElemento": "esZonaRiesgo",
    "validaciones": [
      {
        "nombre": "Hay zonas riego declaradas",
        "orden": 1,
        "funcion": "(function() { if (modelo.esZonaRiesgo === true) { return R.any(function(z) { return z.esSeleccionada == true }, modelo.zonasRiesgo); } else if (modelo.esZonaRiesgo === false) { return true; } })()",
        "mensajeError": "Es necesario indicar por lo menos una zona de riesgo."
      }
    ],
    "reglasDependientes": [
      {
        "idElemento": "zonaRiesgo"
      }
    ]
  },
  {
    "idElemento": "zonaRiesgo",
    "funcionLongitudIndice": "(function() { return modelo.zonasRiesgo.length; })()",
    "validaciones": [
      {
        "nombre": "Cuenta con solución",
        "orden": 1,
        "funcion": "(function () { var zona = modelo.zonasRiesgo[indice]; if(zona.esSeleccionada) { return zona.solucionMitigarRiesgo !== null && zona.solucionMitigarRiesgo !== ''; } else { return true; } })()",
        "mensajeError": "Es necesario colocar una solución para la zona de riesgo."
      },
      {
        "nombre": "Longitud de campo",
        "orden": 2,
        "funcion": "(function () { var zona = modelo.zonasRiesgo[indice]; if(zona.esSeleccionada) { return zona.solucionMitigarRiesgo && zona.solucionMitigarRiesgo.length <= 500; } else { return true; } })()",
        "mensajeError": "La solución para mitigar el riesgo solo admite 500 caracteres."
      }
    ],
    "reglasDependientes": [
      {
        "idElemento": "esZonaRiesgo"
      }
    ]
  },
  {
    "idElemento": "carta",
    "validaciones": [
      {
        "nombre": "Aceptó carta",
        "orden": 1,
        "funcion": "(function() { if (modelo.aceptacionCartaResponsabilidad === true) { return true; } else { return false; } })()",
        "mensajeError": "Debe seleccionar la casilla de aceptación de carta de responsabilidad."
      }
    ]
  }
]