﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.Proyectos.Data
{
    public class Vialidad
    {
        public int idVialidad { get; set; }

        public decimal? idVialidadOF { get; set; }

        public string idEstado { get; set; }

        public string idmunicipio { get; set; }

        public string idLocalidad { get; set; }

        public int idPeriodo { get; set; }

        public string nombreVialidad { get; set; }

        public int idTipoVialidad { get; set; }

        public int idAmbito { get; set; }

        public bool agregado { get; set; }

        public bool agregadoRUV { get; set; }
    }
}
