﻿using RUV.Comun.Seguridad.JWT;
using RUV.Comun.Utilerias;
using RUV.Comun.Web.Http;
using RUV.RUVPP.Negocio.Empresa.Comun;
using RUV.RUVPP.Negocio.Empresa.EmpresaConsulta;
using RUV.RUVPP.Oferta.Dominio.Oferta;
using RUV.RUVPP.Oferta.Dominio.Promotor;
using RUV.RUVPP.Oferta.Modelo.Promotor.Api;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;
using System.Web.Http.Description;

namespace RUV.RUVPP.Oferta.Api.Controllers.Interno
{
    [RoutePrefix("interno/api/promotor")]
    public class PromotorController : ApiControllerBase
    {

        private readonly IServicioPromotor _servicioPromotor;


        /// <summary>
        /// 
        /// </summary>
        /// <param name="_servicioPromotor"></param>
        public PromotorController(IServicioPromotor _servicioPromotor) : base()
        {
            this._servicioPromotor = _servicioPromotor;
        }


        #region Promotor


        /// <summary>
        /// 
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="idOferente"></param>
        /// <param name="idOfertaVivienda"></param>
        /// <param name="nombreFrente"></param>
        /// <returns></returns>
        [HttpGet, Route("ObtenerOfertaViviendaPaginado")]
        [ResponseType(typeof(ResultadoPaginado<List<RUV.RUVPP.Oferta.Modelo.Promotor.Data.OfertaViviendaAsis>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerOfertaViviendaPaginado(int tamanioPagina, int pagina, int idOferente, string idOfertaVivienda, string nombreFrente)
        {
            var result = await this._servicioPromotor.ObtenerOfertaViviendaPaginado(tamanioPagina, pagina, new RUV.RUVPP.Oferta.Modelo.Promotor.Data.OfertaViviendaAsis() { idOferente = idOferente, idOfertaVivienda = idOfertaVivienda, nombreFrente = nombreFrente });

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("GuardarPromotor")]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> GuardarPromotor(RUV.RUVPP.Oferta.Modelo.Promotor.Api.PromotorDTO model)
        {
            var result = await this._servicioPromotor.GuardarPromotor(model);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        [HttpPost, Route("EliminarPromotorXCobertura")]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> EliminarPromotorXCobertura(PromotorXCoberturaDTO dto)
        {
            var result = await this._servicioPromotor.EliminarPromotorXCobertura(dto);
            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="idPromotor"></param>
        /// <returns></returns>
        [HttpGet, Route("ObtenerListadoPromotorXCobertura")]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        [ResponseType(typeof(List<RUV.RUVPP.Oferta.Modelo.Promotor.Api.PromotorXCobertura>))]
        public async Task<HttpResponseMessage> ObtenerListadoPromotorXCobertura(int idPromotor)
        {
            List<RUV.RUVPP.Oferta.Modelo.Promotor.Api.PromotorXCobertura> lista = await this._servicioPromotor.ObtenerListadoPromotorXCobertura(idPromotor);
            return Request.CreateResponse(HttpStatusCode.OK, lista);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("ActualizarPromotor")]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ActualizarPromotor(RUV.RUVPP.Oferta.Modelo.Promotor.Api.PromotorDTO model)
        {
            var result = await this._servicioPromotor.ActualizarPromotor(model);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="idOferente"></param>
        /// <param name="nombre"></param>
        /// <param name="rfc"></param>
        /// <returns></returns>
        [HttpGet, Route("ObtenerPromotorPaginado")]
        [ResponseType(typeof(ResultadoPaginado<List<RUV.RUVPP.Oferta.Modelo.Promotor.Api.Promotor>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerPromotorPaginado(int tamanioPagina, int pagina, int idOferente, string nombre, string rfc)
        {
            var result = await this._servicioPromotor.ObtenerPromotorPaginado(tamanioPagina, pagina, new Modelo.Promotor.Api.Promotor() { idOferente = idOferente, nombre = nombre, rfc = rfc });

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="idPromotor"></param>
        /// <returns></returns>
        [HttpGet, Route("ObtenerPromotor")]
        [ResponseType(typeof(RUV.RUVPP.Oferta.Modelo.Promotor.Api.Promotor))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerPromotor(int idPromotor)
        {
            var entity = await this._servicioPromotor.ObtenerPromotor(new Modelo.Promotor.Api.Promotor() { idPromotor = idPromotor });
            return Request.CreateResponse(HttpStatusCode.OK, entity);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        [HttpPost, Route("GuardarOfertaxPromotor")]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> GuardarOfertaxPromotor(OfertaxPromotorDTO dto)
        {
            var result = await this._servicioPromotor.GuardarOfertaxPromotor(dto);
            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        [HttpPost, Route("EliminarOfertaxPromotor")]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> EliminarOfertaxPromotor(OfertaxPromotorDTO dto)
        {
            var result = await this._servicioPromotor.EliminarOfertaxPromotor(dto);
            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        [HttpPost, Route("ActualizarOfertaxPromotor")]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ActualizarOfertaxPromotor(OfertaxPromotorDTO dto)
        {
            var result = await this._servicioPromotor.ActualizarOfertaxPromotor(dto);
            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="idOfertaVivienda"></param>
        /// <returns></returns>
        [HttpGet, Route("ObtenerListadoOfertaxPromotor")]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        [ResponseType(typeof(List<RUV.RUVPP.Oferta.Modelo.Promotor.Api.OfertaxPromotor>))]
        public async Task<HttpResponseMessage> ObtenerListadoOfertaxPromotor(string idOfertaVivienda)
        {
            List<RUV.RUVPP.Oferta.Modelo.Promotor.Api.OfertaxPromotor> lista = await this._servicioPromotor.ObtenerListadoOfertaxPromotor(new Modelo.Promotor.Api.OfertaxPromotor() { idOfertaVivienda = idOfertaVivienda });
            return Request.CreateResponse(HttpStatusCode.OK, lista);
        }

       /// <summary>
       /// 
       /// </summary>
       /// <param name="paginado"></param>
       /// <returns></returns>
        [HttpPost, Route("ObtenerOfertaxPromotorPaginado")]
        [ResponseType(typeof(ResultadoPaginado<List<RUV.RUVPP.Oferta.Modelo.Promotor.Api.Promotor>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerOfertaxPromotorPaginado(OfertaxPromotorPaginado paginado)
        {
            var result = await this._servicioPromotor.ObtenerOfertaxPromotorPaginado(paginado.tamanioPagina, paginado.pagina, paginado.idOfertaVivienda, new Modelo.Promotor.Api.Promotor() { idOferente = paginado.idOferente, nombre = paginado.nombre, rfc = paginado.rfc });

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("GuardarDocumento")]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> GuardarDocumento(RUV.RUVPP.Oferta.Modelo.Promotor.Api.Documentos model)
        {
            var result = await this._servicioPromotor.GuardarDocumento(model);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="idDocumentos"></param>
        /// <returns></returns>
        [HttpPost, Route("ObtenerListadoDocumentos")]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        [ResponseType(typeof(List<RUV.RUVPP.Oferta.Modelo.Promotor.Api.Documentos>))]
        public async Task<HttpResponseMessage> ObtenerListadoDocumentos(int[] idDocumentos)
        {
            List<RUV.RUVPP.Oferta.Modelo.Promotor.Api.Documentos> lista = await this._servicioPromotor.ObtenerListadoDocumentos(idDocumentos);
            return Request.CreateResponse(HttpStatusCode.OK, lista);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet, Route("ObtenerListadoCatEstado")]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        [ResponseType(typeof(List<RUV.RUVPP.Oferta.Modelo.Promotor.Api.CatEstado>))]
        public async Task<HttpResponseMessage> ObtenerListadoCatEstado()
        {
            List<RUV.RUVPP.Oferta.Modelo.Promotor.Api.CatEstado> lista = await this._servicioPromotor.ObtenerListadoCatEstado();
            return Request.CreateResponse(HttpStatusCode.OK, lista);
        }

        [HttpPost, Route("ReEnviarJson")]
        public async Task<HttpResponseMessage> ReEnviarJson()
        {
            bool result = await this._servicioPromotor.reEnviarJSON();
            return Request.CreateResponse(HttpStatusCode.OK, result);
        }



        [HttpPost, Route("registrarPromotor")]
        public async Task<HttpResponseMessage> registrarPromotor()
        {
            bool result = await this._servicioPromotor.registrarPromotor();
            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        #endregion Promotor

    }
}
