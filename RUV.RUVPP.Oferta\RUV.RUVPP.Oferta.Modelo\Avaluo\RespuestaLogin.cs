﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.Avaluo
{
    public class RespuestaLogin
    {
        public bool acceso { get; set; }
        public int? accesosFallidos { get; set; }
        public int? estatus { get; set; }
        public string idMensaje { get; set; }
        public string idPerfil { get; set; }
        public string idPertenencia { get; set; }
        public string idUsuario { get; set; }
        public string materno { get; set; }
        public string mensaje { get; set; }
        public string nombre { get; set; }
        public string paterno { get; set; }
        public string perfil { get; set; }
        public string pertenencia { get; set; }
        public string correoE { get; set; }
        public int? idBitacoraAvaluo { get; set; }
    }
}
