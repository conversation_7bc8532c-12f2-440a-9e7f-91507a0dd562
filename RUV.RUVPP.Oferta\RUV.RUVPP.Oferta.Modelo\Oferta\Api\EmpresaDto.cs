﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.Oferta.Api
{
    public class EmpresaDto
    {
        public string nombreRazonSocial { get; set; }
        public string apellidoPaterno { get; set; }
        public string apellidoMaterno { get; set; }
        public string rfc { get; set; }
        public byte? idEntidad { get; set; }
        public byte[] idEntidadesAsociadas { get; set; }
        public byte? idSubEntidad { get; set; }
        public byte? idRegimenFiscal { get; set; }
        public string curp { get; set; }
        public string numeroRegistroPatronal { get; set; }
        public string paginaWeb { get; set; }
        public int? numeroEmpleados { get; set; }
        public DateTime? fechaInicioActividades { get; set; }
        public string actividadesPrincipales { get; set; }
        public DateTime? fechaConstitucion { get; set; }
        public DateTime? fechaInscripcionRPP { get; set; }
        public string nombreNotario { get; set; }
        public string numeroEscrituraPublica { get; set; }
        public string numeroDeNotarioPublico { get; set; }
        public string numeroVolumenEscritura { get; set; }
        public string objetoSocialEmpresa { get; set; }
        public string nombreContenedorDocumentos { get; set; }
        public DateTime? fechaRegistro { get; set; }
        public int? IdRegistro { get; set; }
        public string IdEmpresaInst { get; set; }
        public byte? IdEstatusEmpresa { get; set; }
        public string numeroAcreedorSAP { get; set; }
        public DateTime? fechaAceptacion { get; set; }
        public DateTime? fechaAtencion { get; set; }
        public int? idDomicilioGeografico { get; set; }
        public int? idEmpresa { get; set; }
        public int? idEmpresaOrigen { get; set; }
        public byte? sistemaOrigen { get; set; }
        public int? idDomicilioFiscal { get; set; }
        public DateTime? fechaActualizacion { get; set; }
        public DateTime? fechaValidacion { get; set; }
        public int? idConsorcio { get; set; }
        public int? idRUVAsis { get; set; }
        public string motivoBaja { get; set; }
        public DateTime? fechaBaja { get; set; }

        public bool? operaConBanco { get; set; }
        public EmpresaDto clone { get; set; }
        public string numeroAcreedorSAPProvedor { get; set; }
        public int? idAcreedorERP { get; set; }
        public short? idRegimen { get; set; }
    }
}
