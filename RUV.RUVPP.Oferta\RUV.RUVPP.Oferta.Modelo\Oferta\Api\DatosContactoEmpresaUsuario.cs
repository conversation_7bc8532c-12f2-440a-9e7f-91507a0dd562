﻿using RUV.RUVPP.Entidades.Comun;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.Oferta.Api
{
    public class DatosContactoEmpresaUsuario
    {
        public int idUsuario { get; set; }
        public string nombreUsuario { get; set; }
        public string apellidoPaterno { get; set; }
        public string apellidoMaterno { get; set; }
        public short idPerfil { get; set; }
        public bool usuarioActivo { get; set; }
        public int idEmpresa { get; set; }
        public string correoElectronico { get; set; }
        public bool esPrincipal { get; set; }
        public bool correoEmpresaActivo { get; set; }
    }
}
