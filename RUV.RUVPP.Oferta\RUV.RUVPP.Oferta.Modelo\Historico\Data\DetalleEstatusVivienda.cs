﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.Historico.Data
{
    public class DetalleEstatusVivienda
    {
        /// <summary>
        /// Construye un objeto de la clase DetalleEstatusVivienda.
        /// </summary>
        /// <param name="idCampoAdicional">Identificador del campo adicional.</param>
        /// <param name="nombreDelCampo">Nombre del campo.</param>
        /// <param name="descripcion">Descripción del campo.</param>
        /// <param name="tipoDato">Tipo del dato del campo.</param>
        /// <param name="activo">Si está activo.</param>
        public DetalleEstatusVivienda(int idEstatusProyecto, EnumCampoAdicional idCampoAdicional, string valor)
        {
            this.idEstatusProyecto = idEstatusProyecto;
            this.idCampoAdicional = (int)idCampoAdicional;
            this.valorCampoAdicional = valor;
        }
        public int idDetalleEstatusProyecto { get; set; }
        public int idEstatusProyecto { get; set; }
        public int idCampoAdicional { get; set; }
        public string valorCampoAdicional { get; set; }
    }
}
