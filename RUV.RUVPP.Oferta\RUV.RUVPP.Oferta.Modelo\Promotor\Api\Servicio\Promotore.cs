﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.Promotor.Api.Servicio
{
   public class Promotore
    {

        public string idPromotor { get; set; }
        public string tipoPromotor { get; set; }
        public List<EstadosOperacion> estadosOperacion { get; set; }
        public string nombre { get; set; }
        public string apellidoPaterno { get; set; }
        public string apellidoMaterno { get; set; }
        public string certificado { get; set; }
        public string curp { get; set; }
        public string telefono { get; set; }
        public string correo { get; set; }
    }
}
