﻿using Microsoft.ApplicationInsights;
using RUV.RUVPP.Oferta.Dominio.Prototipo;
using RUV.RUVPP.Oferta.Modelo.Prototipos.Api;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;
using System.Web.Http.Description;
using RUV.Comun.Web.Http;
using RUV.RUVPP.Oferta.Comun.Modelo;
using RUV.RUVPP.Oferta.Modelo.Proyectos.Data;
using RUV.Comun.Utilerias;
using RUV.Comun.Seguridad.JWT;

namespace RUV.RUVPP.Oferta.Api.Controllers.Interno
{    
    /// <summary>
    /// 
    /// </summary>
    [RoutePrefix("interno/api/prototipos")]
    [AuthorizeApi(RoleClaims.IsRuvUser)]
    public class PrototiposController : ApiControllerBase
    {
        private readonly IServicioPrototipo _servicioPrototipo;
        private readonly IServicioPrototipoCatalogos _servicioPrototipoCatalogos;        

        /// <summary>
        /// 
        /// </summary>
        /// <param name="clienteTelemetria"></param>
        /// <param name="servicioPrototipo"></param>
        public PrototiposController(IServicioPrototipo servicioPrototipo, IServicioPrototipoCatalogos servicioPrototipoCatalogos)
            : base()
        {
            this._servicioPrototipo = servicioPrototipo;
            this._servicioPrototipoCatalogos = servicioPrototipoCatalogos;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet, Route("catalogos/tipologia-vivienda")]
        [ResponseType(typeof(List<TipologiaVivienda>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerCatalogoTipologiaViviendaAsync()
        {
            var result = await this._servicioPrototipoCatalogos.ObtenerCatalogoDeTipologiaViviendaAsync();

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="idEmpresa"></param>
        /// <param name="nombre"></param>
        /// <param name="idPrototipo"></param>
        /// <returns></returns>
        [HttpGet, Route("nombre/{nombre}")]
        [ResponseType(typeof(int))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ExistePrototipoAsync(int idEmpresa, string nombre = null, int? idPrototipo = null)
        {
            var result = 0;

            if (nombre != null)
                result = await this._servicioPrototipo.ExistePrototipoPorNombreAsync(nombre, idEmpresa, idPrototipo);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("{idPrototipo}/validar-asis")]
        [ResponseType(typeof(bool))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ValidarEstatusAsisAsync(int idEmpresa, int idPrototipo)
        {
            var result = await this._servicioPrototipo.ValidarEstatusAsisAsync(idEmpresa, idPrototipo);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet, Route("catalogos/clasificacion-vivienda")]
        [ResponseType(typeof(List<TipologiaVivienda>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerCatalogoClasificacionViviendaAsync()
        {
            var result = await this._servicioPrototipoCatalogos.ObtenerCatalogoDeClasificacionViviendaAsync();

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }


        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet, Route("catalogos/tipo-dimension-area-vivienda")]
        [ResponseType(typeof(List<TipoDimensionAreaVivienda>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerCatalogoTipoDimensionAreaViviendaAsync()
        {
            var result = await this._servicioPrototipoCatalogos.ObtenerCatalogoTipoDimensionAreaViviendaAsync();

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet, Route("catalogos/clasificacion-area-vivienda")]
        [ResponseType(typeof(List<AreaVivienda>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerCatalogoAreaViviendaAsync()
        {
            var result = await this._servicioPrototipoCatalogos.ObtenerCatalogoAreaViviendaAsync();

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="prototipo"></param>
        /// <param name="esGuardadoTemporal"></param>
        /// <returns></returns>
        [HttpPost, Route("")]
        [ResponseType(typeof(Prototipo))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> GuardarPrototipoAsync(Prototipo prototipo, [FromUri] bool esGuardadoTemporal)
        {            
            var result = await this._servicioPrototipo.GuardarPrototipoAsync(prototipo, esGuardadoTemporal);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }        

        /// <summary>
        /// 
        /// </summary>
        /// <param name="idPrototipo"></param>
        /// <param name="nombrePrototipo"></param>
        /// <returns></returns>
        [HttpPost, Route("{idPrototipo}/duplicar/{nombrePrototipo}")]
        [ResponseType(typeof(Prototipo))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> DuplicarPrototipoAsync(int idPrototipo, string nombrePrototipo)
        {
            var result = await this._servicioPrototipo.DuplicarPrototipoAsync(idPrototipo, nombrePrototipo);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="prototipo"></param>
        /// <param name="esGuardadoTemporal"></param>
        /// <returns></returns>
        [HttpPut, Route("{idPrototipo}")]
        [ResponseType(typeof(Prototipo))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage>ActualizarPrototipoAsync(Prototipo prototipo, [FromUri] bool esGuardadoTemporal)
        {
            var result = await this._servicioPrototipo.ActualizarPrototipoAsync(prototipo, esGuardadoTemporal);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="idEmpresa"></param>
        /// <param name="identificadorPrototipo"></param>
        /// <param name="precioDesde"></param>
        /// <param name="precioHasta"></param>
        /// <param name="idTipologiaVivienda"></param>
        /// <param name="nombre"></param>
        /// <param name="oferente"></param>
        /// <param name="recamaras"></param>
        /// <returns></returns>
        [HttpGet, Route("")]
        [ResponseType(typeof(ResultadoPaginado<List<Prototipo>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerPrototiposFiltradoAsync(int tamanioPagina, int pagina, int? idEmpresa = null, string identificadorPrototipo = null, float? precioDesde = null, float? precioHasta = null, int? idTipologiaVivienda = null, string nombre = null, string oferente = null, int? recamaras = null)
        {
            var result = await this._servicioPrototipo.ObtenerPrototiposFiltradoAsync(tamanioPagina, pagina, idEmpresa, identificadorPrototipo, precioDesde, precioHasta, idTipologiaVivienda, nombre, oferente, recamaras);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        [HttpGet, Route("prototipos-asociados")]
        [ResponseType(typeof(ResultadoPaginado<List<Prototipo>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerPrototiposAsociados(int pagina, int tamPag, int? idProyecto = null, int? idOferta = null)
        {
            var result = await this._servicioPrototipo.ObtenerPrototiposAsociados(pagina, tamPag, idProyecto, idOferta);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="idPrototipo"></param>
        /// <param name="soloJSON"></param>
        /// <returns></returns>
        [HttpDelete, Route("{idPrototipo}")]
        [ResponseType(typeof(bool))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> EliminarPrototipoAsync(int idPrototipo, [FromUri] bool soloJSON)
        {
            var result = await this._servicioPrototipo.EliminarPrototipoAsync(idPrototipo, soloJSON);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="idPrototipo"></param>
        /// <returns></returns>
        [HttpGet, Route("{idPrototipo}")]
        [ResponseType(typeof(ResultadoPaginado<Prototipo>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerPrototipoPorIdAsync(int idPrototipo)
        {
            var usuario = (CustomUserRuv)this.User;
            var result = await this._servicioPrototipo.ObtenerPrototipoPorIdAsync(idPrototipo);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="idPrototipo"></param>
        /// <param name="idVivienda"></param>
        /// <returns></returns>
        [HttpGet, Route("viviendas")]
        [ResponseType(typeof(ResultadoPaginado<List<Vivienda>>))]
        [AuthorizeApi(RoleClaims.IsRuvUser)]
        public async Task<HttpResponseMessage> ObtenerViviendasPorPrototipoIdAsync(int tamanioPagina, int pagina, int idPrototipo, int? idVivienda = null)
        {
            var result = await this._servicioPrototipo.ObtenerViviendasPorPrototipoIdAsync(tamanioPagina, pagina, idPrototipo, idVivienda);

            return Request.CreateResponse(HttpStatusCode.OK, result);
        }

        #region Disposible

        protected override void Dispose(bool disposing)
        {
            base.Dispose(disposing);

            this._servicioPrototipo.Dispose();
            this._servicioPrototipoCatalogos.Dispose();
        }

        #endregion
    }
}