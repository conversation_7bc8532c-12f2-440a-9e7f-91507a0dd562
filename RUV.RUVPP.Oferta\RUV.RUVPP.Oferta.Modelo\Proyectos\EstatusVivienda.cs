﻿using System.ComponentModel;

namespace RUV.RUVPP.Oferta.Modelo.Proyectos
{
    public enum EstatusVivienda
    {
        [Description("Inactiva")]
        Inactiva = 1,
        [Description("Sin validar")]
        SinValidar = 2,
        [Description("Disponible")]
        Disponible = 3,
        [Description("En solicitud de crédito")]
        EnSolicitudCredito = 4,
        [Description("Individualizada")]
        Individualizada = 5,
        [Description("En proceso de verificación")]
        EnProcesoVerficacion = 6,
        [Description("Cancelada")]
        Cancelada = 7,
        [Description("Eliminada")]
        Eliminada = 8
    }
}
