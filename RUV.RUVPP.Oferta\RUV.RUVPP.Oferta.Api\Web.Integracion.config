<?xml version="1.0" encoding="utf-8"?>

<!-- For more information on using web.config transformation visit http://go.microsoft.com/fwlink/?LinkId=125889 -->

<configuration xmlns:xdt="http://schemas.microsoft.com/XML-Document-Transform">
  <!--
    In the example below, the "SetAttributes" transform will change the value of 
    "connectionString" to use "ReleaseSQLServer" only when the "Match" locator 
    finds an attribute "name" that has a value of "MyDB".
    
    <connectionStrings>
      <add name="MyDB" 
        connectionString="Data Source=ReleaseSQLServer;Initial Catalog=MyReleaseDB;Integrated Security=True" 
        xdt:Transform="SetAttributes" xdt:Locator="Match(name)"/>
    </connectionStrings>
  -->
  <connectionStrings>
    <add name="RuvAsisConnection" connectionString="data source=************,35000;initial catalog=RUVdev;persist security info=True;user id=RUvPLu$Pr4pP04#;password=&amp;hT7G75%&amp;n2D$;multipleactiveresultsets=True" providerName="System.Data.SqlClient" xdt:Transform="SetAttributes" xdt:Locator="Match(name)" />
    <add name="GeneralesConnection" connectionString="data source=ruvpp-sql-dev.eastus.cloudapp.azure.com,1433;initial catalog=ruvPlusDesarrollo;persist security info=True;user id=RUVd3S4RR0LLo;password=**********;multipleactiveresultsets=True" providerName="System.Data.SqlClient" xdt:Transform="SetAttributes" xdt:Locator="Match(name)" />
    <add name="OfertaConnection" connectionString="data source=ruvpp-sql-dev.eastus.cloudapp.azure.com,1433;initial catalog=ruvPlusDesarrollo;persist security info=True;user id=RUVd3S4RR0LLo;password=**********;multipleactiveresultsets=True" providerName="System.Data.SqlClient" xdt:Transform="SetAttributes" xdt:Locator="Match(name)" />
    <add name="HistoricosConnection" connectionString="data source=ruvpp-sql-dev.eastus.cloudapp.azure.com,1433;initial catalog=ruvPlusDesarrolloHistoricos;persist security info=True;user id=RUVd3S4RR0LLo;password=**********;multipleactiveresultsets=True" providerName="System.Data.SqlClient" xdt:Transform="SetAttributes" xdt:Locator="Match(name)" />
    <add name="OracleConnection" connectionString="DATA SOURCE=map-q2-sd.cloudapp.net:25450/DSIGRUV;PERSIST SECURITY INFO=True;USER ID=OFERTADEV;PASSWORD=**$$w0rdDEV" providerName="Oracle.DataAccess.Client" xdt:Transform="SetAttributes" xdt:Locator="Match(name)" />
    <add name="GeneralesStorageConnection" connectionString="DefaultEndpointsProtocol=https;AccountName=ruvarchivosempresa;AccountKey=****************************************************************************************" xdt:Transform="SetAttributes" xdt:Locator="Match(name)" />
    <add name="NotificacionesStorageConnectionString" connectionString="DefaultEndpointsProtocol=https;AccountName=ruvnotificacionescorreo;AccountKey=a5EJuUhcacgScjgecuCMos9L3cbq+ztK4K**33yQWCscMr3EcYWDKjgzDtfVmAr4h3jhmPdjbQO9V9E+qH3szg==" xdt:Transform="SetAttributes" xdt:Locator="Match(name)" />
    <add name="BitacoraStorageConnectionString" connectionString="DefaultEndpointsProtocol=https;AccountName=ruvcomun;AccountKey=****************************************************************************************" xdt:Transform="SetAttributes" xdt:Locator="Match(name)" />
  </connectionStrings>
  <appSettings>
    <add key="CorsOrigins" value="https://ruvpp-oferta-web-dev.azurewebsites.net" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="InstrumentationKey" value="2e902491-9ab2-4f55-81a6-a077d8ad07be" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="LogBookInterceptor.Enable" value="true" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="Documentos.numeroIntentos" value="1" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="Documentos.blockDataStorage" value="10" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="Documentos.tiempoEsperaSegundos" value="30" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="Documentos.tamañomaximo" value="5" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="RUV.RUVPP.UI.Usuarios.Suscripcion" value="http://ruvruppdevwebapi.cloudapp.net/api/{0}" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="Notificaciones.QueueName" value="notificacionescorreo" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="Notificaciones.TableName" value="plantillastipocorreo" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="Notificaciones.BlobName" value="adjuntoscorreo" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="Notificaciones.NumeroIntentos" value="1" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="Notificaciones.TiempoEspera" value="1" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="Notificaciones.TamanoBloque" value="1" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="Notificaciones.TiempoEsperaWR" value="60" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="RUV.RUVPP.UI.Usuarios.Suscripcion" value="http://ruvruppdevwebapi.cloudapp.net/" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="Notificaciones.QueueAlerta" value="notificacionesalerta" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="Notificaciones.TokenAlerta" value="0D472D28-C528-4D12-935D-34214395140E" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="SDFschema" value="Schema1" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="OracleUser" value="OFERTADEV" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="OraclePassword" value="**$$w0rdDEV" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="OracleUrl" value="map-q2-sd.cloudapp.net:25450/DSIGRUV" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="OracleSchema" value="OFERTADEV" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="ActivarIntegracionSIG" value="1" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="UrlSigCuvGeografica" value="http://map-q3-w2k8r2.cloudapp.net/ws/cuvdev/ServicioCUV.svc" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="CuvGeografica.Esquema" value="OFERTADEV" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="ActivarConvivenciaAsIs" value="1" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="UrlBaseConvivencia" value="http://ruvintmigradeve.cloudapp.net/" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="UserASIS" value="IN094146" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="PasswordASIS" value="ANGEL079" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
  </appSettings>
  <system.web>
    <!--<compilation xdt:Transform="RemoveAttributes(debug)" />-->
    <!--
      In the example below, the "Replace" transform will replace the entire 
      <customErrors> section of your web.config file.
      Note that because there is only one customErrors section under the 
      <system.web> node, there is no need to use the "xdt:Locator" attribute.
      
      <customErrors defaultRedirect="GenericError.htm"
        mode="RemoteOnly" xdt:Transform="Replace">
        <error statusCode="500" redirect="InternalError.htm"/>
      </customErrors>
    -->
  </system.web>
</configuration>