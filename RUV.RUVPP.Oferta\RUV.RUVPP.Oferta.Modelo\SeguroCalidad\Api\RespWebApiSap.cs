﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api
{
    public class RespWebApiSap
    {
        public int NumPagoEfectuado { get; set; }
        public int DocEntry_OrdenCompra { get; set; }
        public int NumOrdenCompra { get; set; }
        public int DocEntry_Reconciliacion { get; set; }
        public string NumReconciliacion { get; set; }
        public int DocEntry_Asiento { get; set; }
        public string NumAsiento { get; set; }
        public int DocEntry_OrdenVenta { get; set; }
        public int DocEntry_Factura { get; set; }
        public int NumOrdenVenta { get; set; }
        public int NumFactura { get; set; }
        public string CardCode { get; set; }
        public string RetKey { get; set; }
        public string DescripError { get; set; }
        public int CodigoError { get; set; }
        public int DocEntry_PagoEfectuado { get; set; }
        public string Proyecto { get; set; }
    }
}
