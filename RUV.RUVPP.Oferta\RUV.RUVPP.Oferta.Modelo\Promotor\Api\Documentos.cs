﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.Promotor.Api
{
    public class Documentos
    {
        public int? idDocumento { get; set; }
        public string rutaArchivo { get; set; }
        public string nombreArchivo { get; set; }
        public DateTime? fechaRegistro { get; set; }
        public DateTime? fechaActualizacion { get; set; }
        public bool? activo { get; set; }
        public byte? numeroVersion { get; set; }
        public int? idUsuarioCarga { get; set; }
        public short? idCatalogoDocumento { get; set; }
    }

    public class DocumentoDTO : Documentos
    {
        public int[] idDocumentos { get; set; }
    }
}
