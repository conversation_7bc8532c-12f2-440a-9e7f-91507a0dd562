﻿using RUV.Comun.Utilerias;
using RUV.RUVPP.Oferta.Datos.SeguroCalidad;
using RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api;
using RUV.RUVPP.Negocio.General.Seguridad;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Transactions;
using RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Data;
using RUV.RUVPP.Negocio.Empresa.EmpresaConsulta;
using RUV.RUVPP.Entidades.Empresa;
using RUV.RUVPP.Oferta.Modelo.Comun;
using RUV.RUVPP.Entidades.Comun.Enums;
using System.Configuration;
using RUV.RUVPP.Oferta.Comun.Modelo;
using RUV.RUVPP.Generales.Dominio.Generales;
using RUV.RUVPP.Generales.Modelo.Generales;
using RUV.RUVPP.Generales.Modelo.Generales.Enum;
using RUV.Comun.Seguridad.JWT;
using RUV.RUVPP.Negocio.Empresa.Comun;
using RUV.RUVPP.Entidades.General.Notificaciones;
using RUV.RUVPP.Entidades.General.Notificaciones.Enum;
using RUV.RUVPP.Negocio.General.Notificaciones.Interfaces;
using RUV.RUVPP.Entidades.General.Seguridad;
using System.IO;
using iTextSharp.text;
using iTextSharp.text.pdf;
using RUV.RUVPP.Oferta.Datos.Empresa;
using System.Net;
using System.Runtime.Serialization.Json;
using System.Web.Script.Serialization;
using RUV.RUVPP.Oferta.Reportes;
using RUV.RUVPP.Oferta.Modelo.Reportes.SeguroCalidad;
using RUV.Comun.Servicios.Parameters.da;
using RUV.RUVPP.Oferta.Modelo.Oferta.Api;

namespace RUV.RUVPP.Oferta.Dominio.SeguroCalidad.Implementacion
{
    /// <summary>
    /// Servicio Seguro de Calidad
    /// </summary>
    public class ServicioSeguroCalidad : IServicioSeguroCalidad
    {

        #region Constantes


        private const string PARAMETRO_IVA = "IVA";
        private const string PARAMETRO_TRUNCAR_DECIMALS = "TRUNDECIMALS";
        private const string PARAMETRO_COSTO_ORDEN = "COSTO_ORDEN";
        private const string PARAMETRO_COSTO_POLIZA = "COSTO_POLIZA";
        private const string PARAMETRO_COSTO_EVALUACION = "COSTO_EVALUACION";
        private const string PARAMETRO_URL_EVALUACION = "URLAPIEVAL";
        private const string PARAMETRO_URL_DIFERENCIAS = "URLAPIDIF";
        private const string PARAMETRO_URL_POLIZA = "URLAPIPOL";
        private const string PARAMETRO_URL_ASEGURADORA = "URLAPIASEG";


        #endregion

        #region Propiedades

        private readonly ISeguroCalidadDataMapper _seguroCalidadDM;
        private readonly ISeguroCalidadDataMapper__ _seguroCalidadDM__;
        private readonly IServicioSeguridad _servicioSeguridad;
        private readonly IServicioEmpresaConsulta _servicioEmpresaConsulta;
        private readonly IServicioEmpresaComun _servicioEmpresaComun;
        private readonly TransactionOptions _opcionesTransaccion;
        private readonly IServicioGenerales _servicioGenerales;
        private readonly INotificacion<MensajeDto> _servicioNotificaciones;
        private readonly IEmpresaDataMapper _empresaDM;
        private readonly IServicioReportes _servicioReportes;
        #endregion

        #region Constructor

        /// <summary>
        /// Constructor de la clase
        /// </summary>
        /// <param name="seguroCalidadDM"></param>
        /// <param name="servicioSeguridad"></param>
        /// <param name="servicioEmpresaConsulta"></param>
        public ServicioSeguroCalidad(ISeguroCalidadDataMapper seguroCalidadDM, ISeguroCalidadDataMapper__ seguroCalidadDM__, IServicioSeguridad servicioSeguridad,
            IServicioEmpresaConsulta servicioEmpresaConsulta, IServicioGenerales servicioGenerales,
            IServicioEmpresaComun servicioEmpresaComun, INotificacion<MensajeDto> servicioNotificaciones, IEmpresaDataMapper empresaDM, IServicioReportes servicioReportes)
            : base()
        {
            this._seguroCalidadDM = seguroCalidadDM;
            this._servicioSeguridad = servicioSeguridad;
            this._seguroCalidadDM__ = seguroCalidadDM__;
            this._servicioGenerales = servicioGenerales;
            this._servicioEmpresaConsulta = servicioEmpresaConsulta;
            this._servicioEmpresaComun = servicioEmpresaComun;
            this._servicioNotificaciones = servicioNotificaciones;
            var timeoutTransaccion = ConfigurationManager.AppSettings["RUV.RUVPP.Transaccion.Timeout"];
            this._opcionesTransaccion = new TransactionOptions { IsolationLevel = IsolationLevel.ReadCommitted, Timeout = TimeSpan.Parse(timeoutTransaccion) };
            this._empresaDM = empresaDM;
            this._servicioReportes = servicioReportes;
        }

        #endregion

        #region Padron Aseguradoras
        /// <summary>
        /// Obtiene una lista de aseguradoras con su relacion comercial
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="razonSocial"></param>
        /// <param name="noRegistroRUV"></param>
        /// <returns></returns>
        public async Task<ResultadoPaginado<List<Aseguradora>>> ObtenerAseguradoraConPaginadoAsync(int tamanioPagina, int pagina, string razonSocial, string noRegistroRUV)
        {
            ResultadoPaginado<List<Aseguradora>> resultado = new ResultadoPaginado<List<Aseguradora>>()
            {
                TamanioPagina = tamanioPagina,
                PaginaActual = pagina
            };

            var data = await this._seguroCalidadDM.ObtenerAseguradoraConPaginadoAsync(tamanioPagina, pagina, razonSocial, noRegistroRUV);

            if (data.Item1 > 0)
            {
                resultado.TotalRegistros = data.Item1;
                resultado.TotalPaginas = (int)Math.Ceiling((double)(resultado.TotalRegistros / tamanioPagina));
            }

            var aseguradoras = data.Item2;

            List<string> listaAseguradoras = new List<string>();

            foreach (var aseguradora in aseguradoras)
            {

                listaAseguradoras.Add(aseguradora.noRegistroRUV);

                var sanciones = await ObtenerSancionesAsync(null, aseguradora.idAseguradora);

                if (sanciones.Count() > 0)
                {
                    aseguradora.sancionVigente = sanciones.Where(s => s.activo == true).FirstOrDefault();

                    aseguradora.sancionesVencidas = sanciones.Where(s => s.activo == false).ToList();

                    if (aseguradora.sancionVigente != null && aseguradora.sancionVigente.idUsuarioSanciono != null)
                    {
                        var usuarioSanciono = _servicioSeguridad.ObtenerUsuarioDto(aseguradora.sancionVigente.idUsuarioSanciono.Value);

                        if (usuarioSanciono != null)
                            aseguradora.sancionVigente.usuarioSanciono = string.Format("{0} {1} {2}", usuarioSanciono.nombreUsuario, usuarioSanciono.apellidoPaterno, usuarioSanciono.apellidoMaterno != null ? usuarioSanciono.apellidoMaterno : "");
                    }

                    if (aseguradora.sancionesVencidas != null && aseguradora.sancionesVencidas.Count > 0)
                    {
                        foreach (var sancion in aseguradora.sancionesVencidas)
                        {
                            var usuarioSanciono = _servicioSeguridad.ObtenerUsuarioDto(sancion.idUsuarioSanciono.Value);

                            if (usuarioSanciono != null)
                                sancion.usuarioSanciono = string.Format("{0} {1} {2}", usuarioSanciono.nombreUsuario, usuarioSanciono.apellidoPaterno, usuarioSanciono.apellidoMaterno != null ? usuarioSanciono.apellidoMaterno : "");

                            if (sancion.idUsuarioRevoco != null)
                            {
                                var usuarioRevoco = _servicioSeguridad.ObtenerUsuarioDto(sancion.idUsuarioRevoco.Value);

                                sancion.usuarioRevoco = string.Format("{0} {1} {2}", usuarioRevoco.nombreUsuario, usuarioRevoco.apellidoPaterno, usuarioRevoco.apellidoMaterno != null ? usuarioRevoco.apellidoMaterno : "");
                            }
                        }
                    }
                }

            }

            //Aqui obtiene el domicilio Geografico de la lista de id de Empresa

            if (listaAseguradoras.Count > 0)
            {
                var listaEmpresas = string.Join(",", listaAseguradoras.ToArray());

                List<DomicilioGeografico> listaDomicilio = await _seguroCalidadDM__.ObtenerDomicilioGeograficoAsync(null, listaEmpresas);

                List<DatosContactoEmpresa> listaDatosContacto = await _seguroCalidadDM__.ObtenerDatosContactoStringAsync(null, listaEmpresas);

                foreach (var domicilio in listaDomicilio)
                {
                    aseguradoras.Where(a => a.noRegistroRUV == domicilio.idEmpresaInst).FirstOrDefault().domicilio = domicilio.Domicilio;
                }

                foreach (var datoscontacto in listaDatosContacto)
                {
                    aseguradoras.Where(a => a.noRegistroRUV == datoscontacto.idEmpresaInst).FirstOrDefault().email = datoscontacto.Correos;
                    aseguradoras.Where(a => a.noRegistroRUV == datoscontacto.idEmpresaInst).FirstOrDefault().telefono = datoscontacto.Telefonos;
                }

            }
            resultado.Resultado = aseguradoras;

            return resultado;
        }

        public async Task<ResultadoPaginado<List<Avaluo>>> ObtenerListaAvaluosAsync(int tamanioPagina, int pagina, string cuv)
        {
            ResultadoPaginado<List<Avaluo>> resultado = new ResultadoPaginado<List<Avaluo>>()
            {
                TamanioPagina = tamanioPagina,
                PaginaActual = pagina
            };

            var data = await this._seguroCalidadDM.ObtenerListaAvaluosAsync(tamanioPagina, pagina, cuv);

            if (data.Item1 > 0)
            {
                resultado.TotalRegistros = data.Item1;
                resultado.TotalPaginas = (int)Math.Ceiling((double)(resultado.TotalRegistros / tamanioPagina));
            }

            var avaluos = data.Item2;           

            resultado.Resultado = avaluos;

            return resultado;            
        }

        public async Task<Aseguradora> ObtenerAseguradoraCompletaAsync(int idAseguradora)
        {
            var aseguradora = await this._seguroCalidadDM.ObtenerAseguradoraCompletaAsync(idAseguradora);
           
            var relacion = await ObtenerRelacionComercialAsync(null, aseguradora.idAseguradora, 1, null);

            if (relacion != null)
                aseguradora.relacionComercial = relacion;
            

            //Aqui obtiene el domicilio Geografico de la lista de id de Empresa

            //List<DomicilioGeografico> listaDomicilio = await _seguroCalidadDM__.ObtenerDomicilioGeograficoAsync(null, aseguradora.noRegistroRUV);

            //List<DatosContactoEmpresa> listaDatosContacto = await _seguroCalidadDM__.ObtenerDatosContactoStringAsync(null, aseguradora.noRegistroRUV);

            //aseguradora.domicilio = listaDomicilio.FirstOrDefault().Domicilio;            
          
            //aseguradora.email = listaDatosContacto.FirstOrDefault().Correos;
            //aseguradora.telefono = listaDatosContacto.FirstOrDefault().Telefonos;                           

            return aseguradora;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="listaAseguradoras"></param>
        /// <param name="enPadronAseguradora"></param>
        /// <param name="idUsuario"></param>
        /// <returns></returns>
        public async Task<bool> PasarAseguradoraPadronAsync(List<int> listaAseguradoras, int enPadronAseguradora, int idUsuario)
        {
            var resultado = false;

            var oferentesParaTarea = new List<string>();
            List<RelacionesComercialesOferente> oferentesRLAseguradora = new List<RelacionesComercialesOferente>();
            var relacionesComerciales = new List<RelacionesComercialesOferente>();

            ////////////////// Aqui trae el numero de relaciones comerciales por desarrollador
            relacionesComerciales = await ObtenerRelacionesComercialesXDesarrolladorAsync();

            using (var transaccion = new TransactionScope(TransactionScopeOption.RequiresNew, TransactionScopeAsyncFlowOption.Enabled))
            {
                var LiteralAseguradoras = string.Join(",", listaAseguradoras.ToArray());                

                resultado = await this._seguroCalidadDM.PasarAseguradoraPadronAsync(LiteralAseguradoras, enPadronAseguradora);

                //Aqui significa que estas quitando aseguradoras del padron, por lo cual se debe quitar la relacion comercial
                if (enPadronAseguradora == 0)
                {
                    await this._seguroCalidadDM.ActualizarRCXSancionOFueraPadronAsync(LiteralAseguradoras, enPadronAseguradora);                    

                    //Aqui se hace un for con todas las aseguradoras que se estan quitando para saber cuales tienen relaciones con desarrolladores                    
                    foreach (int idAseguradora in listaAseguradoras)
                    {
                        var oferenteEncontrado = relacionesComerciales.Where(r => r.idAseguradora == idAseguradora.ToString()).ToList();

                        foreach (RelacionesComercialesOferente RL in oferenteEncontrado)
                        {
                            if (oferentesRLAseguradora.Find(r => r.idOferente == RL.idOferente) == null)
                                oferentesRLAseguradora.Add(RL);                        
                        }
                        
                    }                    

                    for (int i = 0; i <= oferentesRLAseguradora.Count() - 1; i++)
                    {
                        if (relacionesComerciales.Where(r => r.idOferente == oferentesRLAseguradora[i].idOferente).Count() == 1)
                        {
                            var oferente = await _seguroCalidadDM.ObtenerOferenteAsync(oferentesRLAseguradora[i].idOferente);
                            oferentesParaTarea.Add(oferente.noRegistroRUV);
                        }
                    }
                    //////////////////

                }

                using (var transaccionNotificaciones = new TransactionScope(TransactionScopeOption.RequiresNew, TransactionScopeAsyncFlowOption.Enabled))
                {
                    await this.EnviarCorreoPadronAseguradoras(listaAseguradoras, enPadronAseguradora);

                    //Aqui revisar si se esta sacando del padron ya que si si entonces revisamos si habia desarrolladores con una sola relacion comercial y estaba esa aseguradora
                    if (enPadronAseguradora == 0)
                    {

                        //Aqui mandar correo a oferentes que tenian relacion comercial con la aseguradora que se quito
                        foreach (int idAseguradora in listaAseguradoras)
                        {
                            var oferenteEncontrado = relacionesComerciales.Where(r => r.idAseguradora == idAseguradora.ToString()).ToList();

                            await this.EnviarCorreoBajaPadronAseguradoras(oferenteEncontrado);
                           
                        }

                        //Aqui revisar si hay aseguradoras para tareas
                        if (oferentesParaTarea.Count() > 0)
                        {
                            foreach (var noRegistroRuv in oferentesParaTarea)
                            {
                                var idEmpresaOferente = _servicioEmpresaComun.ObtenerEmpresa(noRegistroRuv)?.idEmpresa;

                                //asignar tarea a esas desarrolladoras
                                var idTareaAgregada = await _servicioGenerales.AgregarTareaDto(new Tarea
                                {
                                    IdEstatusTarea = (int)EstatusTareaEnum.Pendiente,
                                    IdTipoTarea = (int)TipoTareaEnum.ReasignacionAseguradora,
                                    IdEmpresa = idEmpresaOferente,
                                    IdUsuarioCreaTarea = idUsuario,
                                    Descripcion = "Seleccionar nueva aseguradora para relación comercial",
                                    FechaRegistro = AzureDateTime.Now,
                                    Activo = true
                                });

                                //Aqui se agrega la tarea en sancion x tarea
                                await _servicioGenerales.AgregarPadronXTarea(idEmpresaOferente.Value, idTareaAgregada);

                            }
                        }
                    }



                    transaccionNotificaciones.Complete();
                }


                transaccion.Complete();
            }

            return resultado;
        }

        #endregion

        #region Sanciones

        /// <summary>
        /// Obtiene las sanciones de una aseguradora
        /// </summary>
        /// <param name="idSancion"></param>
        /// <param name="idAseguradora"></param>
        /// <returns></returns>
        public async Task<List<Sancion>> ObtenerSancionesAsync(int? idSancion, int? idAseguradora)
        {
            var resultado = await this._seguroCalidadDM.ObtenerSancionesAsync(idSancion, idAseguradora);

            if (resultado != null)
            {
                foreach (var sancion in resultado)
                {
                    sancion.documentos = await this._seguroCalidadDM.ObtenerDocumentosAsync(sancion.idSancion.Value);
                }
            }

            return resultado;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="sancion"></param>
        /// <param name="idUsuario"></param>
        /// <returns></returns>
        public async Task<int> GuardarSancionAsync(Sancion sancion, int? idUsuario)
        {
            var idSancion = 0;

            //Revisar si solo habia una relacion comercial para un desarrollador
            var relacionesComerciales = await ObtenerRelacionesComercialesXDesarrolladorAsync();

            var oferentesRLAseguradora = relacionesComerciales.Where(r => r.idAseguradora == sancion.idAseguradora.Value.ToString()).ToList();

            var oferentesParaTarea = new List<string>();    

            for (int i = 0; i <= oferentesRLAseguradora.Count() -1; i++)
            {
                if (relacionesComerciales.Where(r => r.idOferente == oferentesRLAseguradora[i].idOferente).Count() == 1)
                {
                    var oferente = await _seguroCalidadDM.ObtenerOferenteAsync(oferentesRLAseguradora[i].idOferente);
                    oferentesParaTarea.Add(oferente.noRegistroRUV);
                }                    
            }

            //Hacer for para revisar que desarrolladores tienen solo una relacion comercial

            using (var transaccion = new TransactionScope(TransactionScopeOption.RequiresNew, TransactionScopeAsyncFlowOption.Enabled))
            {
                idSancion = await this._seguroCalidadDM.GuardarSancionAsync(sancion);

                foreach (var documento in sancion.documentos)
                {
                    documento.idSancion = idSancion;

                    var idDocumento = await this._seguroCalidadDM.GuardarDocumentoComunAsync(documento.UrlArchivo, documento.NombreArchivo, documento.idUsuarioCarga, documento.IdCatalogoDocumento.Value);

                    var idDocumentoxSancion = await this._seguroCalidadDM.GuardarDocumentoXSancionAsync(idDocumento, idSancion);
                }

                //Aqui va a cambiar el status de la relacion comercial porque se esta haciendo sancion                
                //await this._seguroCalidadDM.ActualizarRCXSancionOFueraPadronAsync(sancion.idAseguradora.ToString(), 0);                


                var aseguradoraSancionada = await this._seguroCalidadDM.ObtenerAseguradoraCompletaAsync(sancion.idAseguradora.Value);

                //Aqui mandar correo de sancion
                using (var transaccionNotificacion = new TransactionScope(TransactionScopeOption.RequiresNew, TransactionScopeAsyncFlowOption.Enabled))
                {
                    var idEmpresa = _servicioEmpresaComun.ObtenerEmpresa(aseguradoraSancionada.noRegistroRUV)?.idEmpresa;

                    //Aqui revisar si hay aseguradoras para tareas
                    if (oferentesParaTarea.Count() > 0)
                    {
                        foreach (var noRegistroRuv in oferentesParaTarea)
                        {
                            var idEmpresaOferente = _servicioEmpresaComun.ObtenerEmpresa(noRegistroRuv)?.idEmpresa;

                            //asignar tarea a esas desarrolladoras
                            var idTareaAgregada = await _servicioGenerales.AgregarTareaDto(new Tarea
                            {
                                IdEstatusTarea = (int)EstatusTareaEnum.Pendiente,
                                IdTipoTarea = (int)TipoTareaEnum.ReasignacionAseguradora,
                                IdEmpresa = idEmpresaOferente,
                                IdUsuarioCreaTarea = idUsuario,
                                Descripcion = "Seleccionar nueva aseguradora para relación comercial",
                                FechaRegistro = AzureDateTime.Now,
                                Activo = true
                            });

                            //Aqui se agrega la tarea en sancion x tarea
                            await _servicioGenerales.AgregarSancionXTarea(idSancion, idTareaAgregada);

                        }                        
                    }

                    await this.EnviarCorreoSancionAseguradora(aseguradoraSancionada, sancion, idEmpresa.Value);
                   
                    transaccionNotificacion.Complete();
                }

                transaccion.Complete();
            }

            return idSancion;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="sancion"></param>
        /// <param name="idUsuario"></param>
        /// <returns></returns>
        public async Task<int> ActualizarSancionAsync(Sancion sancion, int idUsuario)
        {
            using (var transaccion = new TransactionScope(TransactionScopeOption.RequiresNew, TransactionScopeAsyncFlowOption.Enabled))
            {
                await this._seguroCalidadDM.ActualizarSancionAsync(sancion);

                foreach (var documento in sancion.documentos)
                {
                    documento.idSancion = sancion.idSancion.Value;

                    var idDocumento = await this._seguroCalidadDM.GuardarDocumentoComunAsync(documento.UrlArchivo, documento.NombreArchivo, documento.idUsuarioCarga, documento.IdCatalogoDocumento.Value);

                    var idDocumentoxSancion = await this._seguroCalidadDM.GuardarDocumentoXSancionAsync(idDocumento, sancion.idSancion.Value);
                }

                //Aqui va a cambiar el status de la relacion comercial porque se esta revocando la sancion                
                //await this._seguroCalidadDM.ActualizarRCXSancionOFueraPadronAsync(sancion.idAseguradora.ToString(), Convert.ToInt32(sancion.activo.Value));

                var aseguradoraSancionada = await this._seguroCalidadDM.ObtenerAseguradoraCompletaAsync(sancion.idAseguradora.Value);

                //Aqui mandar correo de sancion
                using (var transaccionNotificacion = new TransactionScope(TransactionScopeOption.RequiresNew, TransactionScopeAsyncFlowOption.Enabled))
                {
                    var idEmpresa = _servicioEmpresaComun.ObtenerEmpresa(aseguradoraSancionada.noRegistroRUV)?.idEmpresa;

                    await this.EnviarCorreoRevocarSancionAseguradora(aseguradoraSancionada, sancion, idEmpresa.Value);

                    //Aqui cerrar la tarea pendiente que se asigno por la sancion
                    var idTarea = await _servicioGenerales.ObtenerSancionXTarea(sancion.idSancion.Value);

                    if (idTarea != 0)
                    {
                        await _servicioGenerales.ActualizarTarea(new TareaBancos
                        {
                            IdTarea = idTarea,
                            IdEstatusTarea = (int)EstatusTareaEnum.Atendida,
                            IdUsuarioAtiendeTarea = idUsuario
                        });
                    }


                    transaccionNotificacion.Complete();
                }

                transaccion.Complete();
            }

            return sancion.idSancion.Value;
        }

        #endregion

        #region Relacion Comercial

        public async Task<ResultadoPaginado<List<Aseguradora>>> ObtenerAseguradoraRelacionComercialConPaginadoAsync(int tamanioPagina, int pagina, string razonSocial, string noRegistroRUV, int? idDesarrollador, int? idAseguradora)
        {
            ResultadoPaginado<List<Aseguradora>> resultado = new ResultadoPaginado<List<Aseguradora>>()
            {
                TamanioPagina = tamanioPagina,
                PaginaActual = pagina
            };

            var data = await this._seguroCalidadDM.ObtenerAseguradoraRelacionComercialConPaginadoAsync(tamanioPagina, pagina, razonSocial, noRegistroRUV, idDesarrollador, idAseguradora);

            if (data.Item1 > 0)
            {
                resultado.TotalRegistros = data.Item1;
                resultado.TotalPaginas = (int)Math.Ceiling((double)(resultado.TotalRegistros / tamanioPagina));
            }

            var aseguradoras = data.Item2;

            List<string> listaAseguradoras = new List<string>();

            foreach (var aseguradora in aseguradoras)
            {
                listaAseguradoras.Add(aseguradora.noRegistroRUV);

                var relacion = await ObtenerRelacionComercialAsync(null, null, null, aseguradora.idRelacionComercial);

                if (relacion != null)
                    aseguradora.relacionComercial = relacion;
            }

            //Aqui obtiene el domicilio Geografico de la lista de id de Empresa

            if (listaAseguradoras.Count > 0)
            {

                var listaEmpresas = string.Join(",", listaAseguradoras.ToArray());

                List<DomicilioGeografico> listaDomicilio = await _seguroCalidadDM__.ObtenerDomicilioGeograficoAsync(null, listaEmpresas);

                List<DatosContactoEmpresa> listaDatosContacto = await _seguroCalidadDM__.ObtenerDatosContactoStringAsync(null, listaEmpresas);

                foreach (var domicilio in listaDomicilio)
                {
                    aseguradoras.Where(a => a.noRegistroRUV == domicilio.idEmpresaInst).FirstOrDefault().domicilio = domicilio.Domicilio;
                }

                foreach (var datoscontacto in listaDatosContacto)
                {
                    aseguradoras.Where(a => a.noRegistroRUV == datoscontacto.idEmpresaInst).FirstOrDefault().email = datoscontacto.Correos;
                    aseguradoras.Where(a => a.noRegistroRUV == datoscontacto.idEmpresaInst).FirstOrDefault().telefono = datoscontacto.Telefonos;
                }

            }
            resultado.Resultado = aseguradoras;

            return resultado;
        }

        public async Task<ResultadoPaginado<List<DesarrolladorRelacion>>> ObtenerDesarrolladoresRelacionComercialConPaginadoAsync(int tamanioPagina, int pagina, string razonSocial, string noRegistroRUV, string ordenVerificacion, string estatusCosto, int? idDesarrollador, int? idAseguradora)
        {
            ResultadoPaginado<List<DesarrolladorRelacion>> resultado = new ResultadoPaginado<List<DesarrolladorRelacion>>()
            {
                TamanioPagina = tamanioPagina,
                PaginaActual = pagina
            };

            var data = await this._seguroCalidadDM.ObtenerDesarrolladoresRelacionComercialConPaginadoAsync(tamanioPagina, pagina, razonSocial, noRegistroRUV, ordenVerificacion, estatusCosto, idDesarrollador, idAseguradora);

            if (data.Item1 > 0)
            {
                resultado.TotalRegistros = data.Item1;
                resultado.TotalPaginas = (int)Math.Ceiling((double)(resultado.TotalRegistros / tamanioPagina));
            }

            var desarrolladores = data.Item2;

            List<string> listaAseguradoras = new List<string>();

            foreach (var desarrollador in desarrolladores)
            {
                listaAseguradoras.Add(desarrollador.noRegistroRUV);

                var relacion = await ObtenerRelacionComercialAsync(null, null, null, desarrollador.idRelacionComercial);

                if (relacion != null)
                    desarrollador.relacionComercial = relacion;

                if (desarrollador.relacionComercial.idDocumentoContrato != null)
                {
                    var documentoContrato = await _seguroCalidadDM.ObtenerDocumentosComunAsync(desarrollador.relacionComercial.idDocumentoContrato.Value);

                    desarrollador.relacionComercial.contrato = documentoContrato;
                }
            }

            //Aqui obtiene el domicilio Geografico de la lista de id de Empresa
            if (listaAseguradoras.Count > 0)
            {

                var listaEmpresas = string.Join(",", listaAseguradoras.ToArray());

                List<DomicilioGeografico> listaDomicilio = await _seguroCalidadDM__.ObtenerDomicilioGeograficoAsync(null, listaEmpresas);

                List<DatosContactoEmpresa> listaDatosContacto = await _seguroCalidadDM__.ObtenerDatosContactoStringAsync(null, listaEmpresas);

                foreach (var domicilio in listaDomicilio)
                {
                    desarrolladores.Where(a => a.noRegistroRUV == domicilio.idEmpresaInst).FirstOrDefault().domicilio = domicilio.Domicilio;
                }

                foreach (var datoscontacto in listaDatosContacto)
                {
                    desarrolladores.Where(a => a.noRegistroRUV == datoscontacto.idEmpresaInst).FirstOrDefault().email = datoscontacto.Correos;
                    desarrolladores.Where(a => a.noRegistroRUV == datoscontacto.idEmpresaInst).FirstOrDefault().telefono = datoscontacto.Telefonos;
                }

            }

            resultado.Resultado = desarrolladores;

            return resultado;
        }

        public async Task<ResultadoPaginado<List<Aseguradora>>> ObtenerAseguradoraEnPadronSinOferenteEnRelacionComercialConPaginadoAsync(int tamanioPagina, int pagina, string razonSocial, string noRegistroRUV, int? idDesarrollador)
        {
            ResultadoPaginado<List<Aseguradora>> resultado = new ResultadoPaginado<List<Aseguradora>>()
            {
                TamanioPagina = tamanioPagina,
                PaginaActual = pagina
            };

            var data = await this._seguroCalidadDM.ObtenerAseguradoraEnPadronSinOferenteEnRelacionComercialConPaginadoAsync(tamanioPagina, pagina, razonSocial, noRegistroRUV, idDesarrollador);

            if (data.Item1 > 0)
            {
                resultado.TotalRegistros = data.Item1;
                resultado.TotalPaginas = (int)Math.Ceiling((double)(resultado.TotalRegistros / tamanioPagina));
            }

            var aseguradoras = data.Item2;

            List<string> listaAseguradoras = new List<string>();

            foreach (var aseguradora in aseguradoras)
            {
                listaAseguradoras.Add(aseguradora.noRegistroRUV);

                if (idDesarrollador == null)
                {
                    var relacion = await ObtenerRelacionComercialAsync(null, null, null, aseguradora.idRelacionComercial);

                    if (relacion != null)
                        aseguradora.relacionComercial = relacion;
                }                
            }            

            //Aqui obtiene el domicilio Geografico de la lista de id de Empresa

            if (listaAseguradoras.Count > 0)
            {

                var listaEmpresas = string.Join(",", listaAseguradoras.ToArray());

                List<DomicilioGeografico> listaDomicilio = await _seguroCalidadDM__.ObtenerDomicilioGeograficoAsync(null, listaEmpresas);

                List<DatosContactoEmpresa> listaDatosContacto = await _seguroCalidadDM__.ObtenerDatosContactoStringAsync(null, listaEmpresas);

                foreach (var domicilio in listaDomicilio)
                {
                    aseguradoras.Where(a => a.noRegistroRUV == domicilio.idEmpresaInst).FirstOrDefault().domicilio = domicilio.Domicilio;
                }

                foreach (var datoscontacto in listaDatosContacto)
                {
                    aseguradoras.Where(a => a.noRegistroRUV == datoscontacto.idEmpresaInst).FirstOrDefault().email = datoscontacto.Correos;
                    aseguradoras.Where(a => a.noRegistroRUV == datoscontacto.idEmpresaInst).FirstOrDefault().telefono = datoscontacto.Telefonos;
                }

            }

            resultado.Resultado = aseguradoras;

            return resultado;
        }

        public async Task<Aseguradora> ObtenerAseguradoraEnPadronSinOferenteXDefectoAsync(int idDesarrollador)
        {                       
            var aseguradorasResultado = await this._seguroCalidadDM.ObtenerAseguradoraEnPadronSinOferenteXDefectoAsync(idDesarrollador);            

            ////Aqui revisar cual seria la aseguradora por defecto

            //Aseguradora aseguradoraXDefecto = aseguradorasResultado.OrderBy(a => a.porDefecto).ThenBy(a=> a.idAseguradora).FirstOrDefault();            
            
            //Aqui obtiene el domicilio Geografico de la lista de id de Empresa
                        
            List<DomicilioGeografico> listaDomicilio = await _seguroCalidadDM__.ObtenerDomicilioGeograficoAsync(null, aseguradorasResultado.noRegistroRUV);

            List<DatosContactoEmpresa> listaDatosContacto = await _seguroCalidadDM__.ObtenerDatosContactoStringAsync(null, aseguradorasResultado.noRegistroRUV);

            aseguradorasResultado.domicilio = listaDomicilio.FirstOrDefault()?.Domicilio;

            aseguradorasResultado.email = listaDatosContacto.FirstOrDefault().Correos;
            aseguradorasResultado.telefono = listaDatosContacto.FirstOrDefault().Telefonos;                      

            return aseguradorasResultado;
        }

        public async Task<ResultadoPaginado<List<Aseguradora>>> ObtenerAseguradoraEnPadronConPaginadoAsync(int tamanioPagina, int pagina, string razonSocial, string noRegistroRUV)
        {
            ResultadoPaginado<List<Aseguradora>> resultado = new ResultadoPaginado<List<Aseguradora>>()
            {
                TamanioPagina = tamanioPagina,
                PaginaActual = pagina
            };

            var data = await this._seguroCalidadDM.ObtenerAseguradoraEnPadronConPaginadoAsync(tamanioPagina, pagina, razonSocial, noRegistroRUV);

            if (data.Item1 > 0)
            {
                resultado.TotalRegistros = data.Item1;
                resultado.TotalPaginas = (int)Math.Ceiling((double)(resultado.TotalRegistros / tamanioPagina));
            }

            var aseguradoras = data.Item2;

            List<string> listaAseguradoras = new List<string>();

            foreach (var aseguradora in aseguradoras)
            {
                listaAseguradoras.Add(aseguradora.noRegistroRUV);

                var sanciones = await ObtenerSancionesAsync(null, aseguradora.idAseguradora);

                if (sanciones.Count() > 0)
                {
                    aseguradora.sancionVigente = sanciones.Where(s => s.activo == true).FirstOrDefault();

                    aseguradora.sancionesVencidas = sanciones.Where(s => s.activo == false).ToList();

                    if (aseguradora.sancionVigente != null && aseguradora.sancionVigente.idUsuarioSanciono != null)
                    {
                        var usuarioSanciono = _servicioSeguridad.ObtenerUsuarioDto(aseguradora.sancionVigente.idUsuarioSanciono.Value);

                        if (usuarioSanciono != null)                                     
                            aseguradora.sancionVigente.usuarioSanciono = string.Format("{0} {1} {2}", usuarioSanciono.nombreUsuario, usuarioSanciono.apellidoPaterno, usuarioSanciono.apellidoMaterno);                        
                    }

                    if (aseguradora.sancionesVencidas != null && aseguradora.sancionesVencidas.Count > 0)
                    {
                        foreach (var sancion in aseguradora.sancionesVencidas)
                        {
                            var usuarioSanciono = _servicioSeguridad.ObtenerUsuarioDto(sancion.idUsuarioSanciono.Value);

                            if (usuarioSanciono != null)
                                sancion.usuarioSanciono = string.Format("{0} {1} {2}", usuarioSanciono.nombreUsuario, usuarioSanciono.apellidoPaterno, usuarioSanciono.apellidoMaterno);

                            if (sancion.idUsuarioRevoco != null)
                            {
                                var usuarioRevoco = _servicioSeguridad.ObtenerUsuarioDto(sancion.idUsuarioRevoco.Value);

                                sancion.usuarioRevoco = string.Format("{0} {1} {2}", usuarioRevoco.nombreUsuario, usuarioRevoco.apellidoPaterno, usuarioRevoco.apellidoMaterno);
                            }
                        }
                    }
                }
            }

            //Aqui obtiene el domicilio Geografico y datos de contacto de la lista de id de Empresa
            if (listaAseguradoras.Count > 0)
            {

                var listaEmpresas = string.Join(",", listaAseguradoras.ToArray());

                List<DomicilioGeografico> listaDomicilio = await _seguroCalidadDM__.ObtenerDomicilioGeograficoAsync(null, listaEmpresas);

                List<DatosContactoEmpresa> listaDatosContacto = await _seguroCalidadDM__.ObtenerDatosContactoStringAsync(null, listaEmpresas);

                foreach (var domicilio in listaDomicilio)
                {
                    aseguradoras.Where(a => a.noRegistroRUV == domicilio.idEmpresaInst).FirstOrDefault().domicilio = domicilio.Domicilio;
                }

                foreach (var datoscontacto in listaDatosContacto)
                {
                    aseguradoras.Where(a => a.noRegistroRUV == datoscontacto.idEmpresaInst).FirstOrDefault().email = datoscontacto.Correos;
                    aseguradoras.Where(a => a.noRegistroRUV == datoscontacto.idEmpresaInst).FirstOrDefault().telefono = datoscontacto.Telefonos;
                }

            }
            resultado.Resultado = aseguradoras;

            return resultado;
        }

        public async Task<RelacionComercial> ObtenerRelacionComercialAsync(int? idDesarrollador, int? idAseguradora, int? activo, int? idrelacionComercial)
        {
            var resultado = await this._seguroCalidadDM.ObtenerRelacionComercialAsync(idDesarrollador, idAseguradora, activo, idrelacionComercial);

            return resultado;
        }

        #endregion

        public async Task<DesarrolladorRelacion> ObtenerOferenteAsync(string idDesarrollador)
        {
            var desarrolladorRelacion = await this._seguroCalidadDM.ObtenerOferenteAsync(idDesarrollador);

            return desarrolladorRelacion;
        }

        public async Task<bool> RevisarParametros90Dias()
        {
            var parametros = await ObtenerParametrosConfigs90DiasAsync(new List<string> { "FechaInicio90Dias", "FechaFin90Dias", "Dias90Dias", "Estatus90Dias" });

            if (parametros.Where(p => p.clave == "FechaInicio90Dias").FirstOrDefault().valor == null || parametros.Where(p => p.clave == "FechaFin90Dias").FirstOrDefault().valor == null)
            {
                return false;
            }
            else
            {
                if (DateTime.Now > Convert.ToDateTime(parametros.Where(p => p.clave == "FechaFin90Dias").FirstOrDefault().valor))
                {
                    parametros.Where(p => p.clave == "FechaFin90Dias").FirstOrDefault().valor =
                        (Convert.ToDateTime(parametros.Where(p => p.clave == "FechaFin90Dias").FirstOrDefault().valor).AddDays(Convert.ToInt32(parametros.Where(p => p.clave == "Dias90Dias").FirstOrDefault().valor))).ToShortDateString();

                    if (parametros.Where(p => p.clave == "Estatus90Dias").FirstOrDefault().valor == "0")
                        parametros.Where(p => p.clave == "Estatus90Dias").FirstOrDefault().valor = "1";
                    else
                        parametros.Where(p => p.clave == "Estatus90Dias").FirstOrDefault().valor = "0";


                    await ActualizarParametrosConfigs90DiasAsync(parametros);                  
                }

                return true;
            }
        }

        public async Task<bool> ActualizarParametrosConfigs90DiasAsync(List<ParametrosConfigs> listaParametros)
        {
            using (var transaccion = new TransactionScope(TransactionScopeOption.RequiresNew, TransactionScopeAsyncFlowOption.Enabled))
            {
                foreach (var parametros in listaParametros)
                {
                    await this._seguroCalidadDM.ActualizarParametrosConfigs90DiasAsync(parametros.clave, parametros.valor);
                }                

                transaccion.Complete();
            }

            return true;
        }

        public async Task<List<ParametrosConfigs>> ObtenerParametrosConfigs90DiasAsync(List<string> listaClaves)
        {
            var ListaClaves = string.Join(",", listaClaves.ToArray());
            var resultado = await this._seguroCalidadDM.ObtenerParametrosConfigs90DiasAsync(ListaClaves);            

            return resultado;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="cuv"></param>
        /// <param name="ordenVerificacion"></param>
        /// <returns></returns>
        public async Task<OrdenDeVerificacion> ObtenerInfoOrdenVerificacionAsync(string ordenVerificacion)
        {            
            var ordenVerificacionR = await this._seguroCalidadDM.ObtenerInfoOrdenVerificacionAsync(ordenVerificacion);

            if (ordenVerificacionR != null)
            {
                ordenVerificacionR.fechaInicioVerificacion = Convert.ToDateTime(ordenVerificacionR.fechaInicioVerificacion).ToString("dd/MM/yyyy");
                ordenVerificacionR.fechaFinVerificacion = Convert.ToDateTime(ordenVerificacionR.fechaFinVerificacion).ToString("dd/MM/yyyy");
                ordenVerificacionR.fechaInicioPrestacion = Convert.ToDateTime(ordenVerificacionR.fechaInicioPrestacion).ToString("dd/MM/yyyy");
            }

            return ordenVerificacionR;
        }

       /// <summary>
       /// 
       /// </summary>
       /// <param name="tamanioPagina"></param>
       /// <param name="pagina"></param>
       /// <param name="noRegistroRUV"></param>
       /// <param name="razonSocial"></param>
       /// <param name="ordenVerificacion"></param>
       /// <param name="noContrato"></param>
       /// <param name="fechaInicial"></param>
       /// <param name="fechaFinal"></param>
       /// <param name="fechaAceptacionInicio"></param>
       /// <param name="fechaAceptacionFinal"></param>
       /// <param name="idTipoAsignacion"></param>
       /// <param name="idOferenteExterno"></param>
       /// <param name="idAseguradoraExterno"></param>
       /// <returns></returns>
        public async Task<ResultadoPaginado<List<OrdenDeVerificacion>>> ObtenerConsultaAseguradoraConPaginadoAsync(int tamanioPagina, int pagina, string noRegistroRUV, string razonSocial, string ordenVerificacion, string noContrato, string fechaInicial, string fechaFinal, string fechaAceptacionInicio, string fechaAceptacionFinal, int? idTipoAsignacion, int? idOferenteExterno, int? idAseguradoraExterno, int? idEstatusPagoEvaluacionRiesgo)
        {
            ResultadoPaginado<List<OrdenDeVerificacion>> resultado = new ResultadoPaginado<List<OrdenDeVerificacion>>()
            {
                TamanioPagina = tamanioPagina,
                PaginaActual = pagina
            };

            var idEntidad = 0;

            if (noRegistroRUV != null || razonSocial != null)
            {
                ConsultaEmpresas empresa = new ConsultaEmpresas { NRRuv = noRegistroRUV, RazonSocial = razonSocial };

                var datosEmpresa = _servicioEmpresaConsulta.ObtenerEmpresasConsulta(empresa, null);

                var idEntidadPrueba = datosEmpresa.ListaEntidad.FirstOrDefault()?.identidad;

                if(idEntidadPrueba != null)
                    idEntidad = Convert.ToInt32(idEntidadPrueba);     
                           
            }            

            var data = await this._seguroCalidadDM.ObtenerConsultaAseguradoraConPaginadoAsync(tamanioPagina, pagina, noRegistroRUV, razonSocial, ordenVerificacion, noContrato, fechaInicial, fechaFinal, fechaAceptacionInicio, fechaAceptacionFinal, idTipoAsignacion, idEntidad, idOferenteExterno, idAseguradoraExterno, idEstatusPagoEvaluacionRiesgo);

            if (data.Item1 > 0)
            {
                resultado.TotalRegistros = data.Item1;
                resultado.TotalPaginas = (int)Math.Ceiling((double)(resultado.TotalRegistros / tamanioPagina));
            }

            var listaCuvs = data.Item2;

            // var listaEmpresas = string.Join(",", listaCuvs.Select(l => l.idEmpresaInstAseguradora).ToArray());
            var listaEmpresas = "";

            //Obtener Domicilio


            if (idOferenteExterno == null && idAseguradoraExterno != null) {
                listaEmpresas =  string.Join(",", listaCuvs.Select(l => l.idEmpresaInstDesarrollador).ToArray());
            }

            if (idOferenteExterno != null && idAseguradoraExterno == null)
            {
                listaEmpresas = string.Join(",", listaCuvs.Select(l => l.idEmpresaInstAseguradora).ToArray());
            }

            if (idOferenteExterno == null && idAseguradoraExterno == null)
            {
                listaEmpresas = string.Join(",", listaCuvs.Select(l => l.idEmpresaInstAseguradora).ToArray());

                //listaEmpresas += string.Join(",", listaCuvs.Select(l => l.idEmpresaInstDesarrollador).ToArray());
            }

            List<DomicilioGeografico> listaDomicilio = await _seguroCalidadDM__.ObtenerDomicilioGeograficoAsync(null, listaEmpresas);
            //List<DatosContactoEmpresa> listaDatosContacto = await _seguroCalidadDM__.ObtenerDatosContactoStringAsync(null, listaEmpresas);

            // foreach (var domicilio in listaDomicilio)
            // {
            //listaCuvs.Where(a => a.idEmpresaInstAseguradora == domicilio.idEmpresaInst).FirstOrDefault().domicilio = domicilio.Domicilio;
            //}
            
            foreach (var cuvSinPoliza in listaCuvs)
            {
                cuvSinPoliza.fechaAsignacion = Convert.ToDateTime(cuvSinPoliza.fechaAsignacion).ToString("dd/MM/yyyy");

                cuvSinPoliza.fechaAceptacion = Convert.ToDateTime(cuvSinPoliza.fechaAceptacion).ToString("dd/MM/yyyy");


                if( cuvSinPoliza.domicilio==null ){
                    //List<DomicilioGeografico> listaDomicilio = await _seguroCalidadDM__.ObtenerDomicilioGeograficoAsync(null, cuvSinPoliza.idEmpresaInstAseguradora);
                    if (idOferenteExterno == null && idAseguradoraExterno != null)
                    {
                        DomicilioGeografico domiclio = listaDomicilio.Where(a => a.idEmpresaInst == cuvSinPoliza.idEmpresaInstDesarrollador).FirstOrDefault();

                        if(domiclio != null && domiclio.Domicilio != null)
                            cuvSinPoliza.domicilio = domiclio.Domicilio;
                    }
                    if (idOferenteExterno != null && idAseguradoraExterno == null)
                    {
                        DomicilioGeografico domiclio = listaDomicilio.Where(a => a.idEmpresaInst == cuvSinPoliza.idEmpresaInstAseguradora).FirstOrDefault();

                        if (domiclio != null && domiclio.Domicilio != null)
                            cuvSinPoliza.domicilio = domiclio.Domicilio;
                    }
                }
            }

            resultado.Resultado = listaCuvs;

            return resultado;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="cuv"></param>
        /// <param name="ordenVerificacion"></param>
        /// <param name="idOferente"></param>
        /// <param name="cambioValorAvaluo"></param>
        /// <returns></returns>
        public async Task<ResultadoPaginado<List<CuvPoliza>>> ObtenerCuvSinPolizaConPaginadoAsync(int tamanioPagina, int pagina, string cuv, string ordenVerificacion, int? idOferente, bool? cambioValorAvaluo)
        {
            ResultadoPaginado<List<CuvPoliza>> resultado = new ResultadoPaginado<List<CuvPoliza>>()
            {
                TamanioPagina = tamanioPagina,
                PaginaActual = pagina
            };

            var data = await this._seguroCalidadDM.ObtenerCuvSinPolizaConPaginadoAsync(tamanioPagina, pagina, cuv, ordenVerificacion, idOferente, cambioValorAvaluo);

            if (data.Item1 > 0)
            {
                resultado.TotalRegistros = data.Item1;
                resultado.TotalPaginas = (int)Math.Ceiling((double)(resultado.TotalRegistros / tamanioPagina));
            }

            var listaCuvs = data.Item2;

            var listaCuvsVistas = new List<string>();

            foreach (var cuvSinPoliza in listaCuvs)
            {
                cuvSinPoliza.fechaVigenciaAvaluo = false;

                if (cuvSinPoliza.fechaVigenciaAvaluoFecha != "" && cuvSinPoliza.fechaVigenciaAvaluoFecha != null)
                {
                    DateTime Fecha1 = Convert.ToDateTime(cuvSinPoliza.fechaVigenciaAvaluoFecha);

                    DateTime Fecha2 = DateTime.Now;
                   
                    cuvSinPoliza.fechaVigenciaAvaluo = Fecha1.Date >= Fecha2.Date ? true : false;
                }

                //Aqui revisar si algunos tienen 1, para esos cambiarlos a 0
                if (cambioValorAvaluo != null && cambioValorAvaluo.Value && cuvSinPoliza.cambioValorAvaluo != null && cuvSinPoliza.cambioValorAvaluo.Value)
                {
                    listaCuvsVistas.Add(cuvSinPoliza.cuv);
                }
                else
                {
                    if (cambioValorAvaluo == null)
                        cuvSinPoliza.cambioValorAvaluo = null;
                }
                    
            }

            if (listaCuvsVistas.Any())
            {               
                var result = await this._seguroCalidadDM.ActualizarCuvsCambioAvaluoAsync(listaCuvsVistas, 0);
            }
            
            resultado.Resultado = listaCuvs;

            return resultado;
        }        

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="cuv"></param>
        /// <param name="ordenVerificacion"></param>
        /// <param name="idOferente"></param>
        /// <returns></returns>
        public async Task<ResultadoPaginado<List<CuvPoliza>>> ObtenerCuvConPolizaConPaginadoAsync(int tamanioPagina, int pagina, string cuv, string ordenVerificacion, int? idOferente)
        {
            ResultadoPaginado<List<CuvPoliza>> resultado = new ResultadoPaginado<List<CuvPoliza>>()
            {
                TamanioPagina = tamanioPagina,
                PaginaActual = pagina
            };

            var data = await this._seguroCalidadDM.ObtenerCuvConPolizaConPaginadoAsync(tamanioPagina, pagina, cuv, ordenVerificacion, idOferente);

            if (data.Item1 > 0)
            {
                resultado.TotalRegistros = data.Item1;
                resultado.TotalPaginas = (int)Math.Ceiling((double)(resultado.TotalRegistros / tamanioPagina));
            }

            var listaPolizas = data.Item2;

            foreach(var poliza in listaPolizas)
            {
                if(poliza.fechaactualizacionavaluo != null)
                    poliza.fechaactualizacionavaluo = Convert.ToDateTime(poliza.fechaactualizacionavaluo).ToString("dd/MM/yyyy");
            }

            resultado.Resultado = listaPolizas;

            return resultado;
        }       

        /// <summary>
        /// 
        /// </summary>
        /// <param name="listaCuvPoliza"></param>
        /// <returns></returns>
        public async Task<List<int>> AgregarPolizaAsync(List<CuvPoliza> listaCuvPoliza)
        {
            List<int> listaResultadosCuvPoliza = new List<int>();
            int idRuvAsIs = 0;

            List<VvGeneral> listaVVGeneral = await this._seguroCalidadDM.BuscarxCuvAsync(listaCuvPoliza.FirstOrDefault().cuv, null);

            int idGrupoPoliza = await this._seguroCalidadDM.ObtenerUltimoGrupoPolizaAsync();
            decimal costoRelacion = 0;
            using (var transaccion = new TransactionScope(TransactionScopeOption.RequiresNew, TransactionScopeAsyncFlowOption.Enabled))
            {
                foreach (var cuv in listaCuvPoliza)
                {
                    CriteriosVivienda criteriosViviendas = await this._seguroCalidadDM.ObteneCriteriosCuvsAsync(cuv.cuv);
                    decimal costoPoliza = await ObtenerCostoPoliza(criteriosViviendas.costoEvaluacionRiesgo, criteriosViviendas.montoAvaluo, criteriosViviendas.costoRelacion, criteriosViviendas.criterioSinInicioObra == 1 ? true : false);
                    costoRelacion = criteriosViviendas.costoRelacion;
                    cuv.costoPoliza = costoPoliza;
                    cuv.idGrupoPoliza = idGrupoPoliza;
                    var idPoliza = await this._seguroCalidadDM.AgregarPolizaAsync(cuv);
                    idRuvAsIs = cuv.idOferente.Value;
                    
                    listaResultadosCuvPoliza.Add(idPoliza);
                }

                //Aseguradora aseguradoraCompleta = await ObtenerAseguradoraCompletaAsync(Convert.ToInt32(listaCuvPoliza.FirstOrDefault().idAseguradora));

                //DesarrolladorRelacion desarrollador = await ObtenerOferenteAsync(listaCuvPoliza.FirstOrDefault().idOferente.Value.ToString());

                //using (var transaccionNotificaciones = new TransactionScope(TransactionScopeOption.RequiresNew, TransactionScopeAsyncFlowOption.Enabled))
                //{
                //    //Aqui mandar correo

                //    await this.EnviarCorreoSolicitudPoliza(listaCuvPoliza, aseguradoraCompleta, desarrollador);

                //    transaccionNotificaciones.Complete();
                //}


                transaccion.Complete();
            }


            List<PreciosxId> listaPrecios = await ObtenerListadoViviendas(listaCuvPoliza, listaVVGeneral.FirstOrDefault().ordenVerificacion);

            FichaPagoRiesgo fichaPagoRiesgo = new FichaPagoRiesgo();

            //List<PrecioViviendas> listadoViviendasOrden = await this._seguroCalidadDM.ObtenerViviendasOrdenAsync(vvGeneral.ordenVerificacion);
            fichaPagoRiesgo.listaViviendas = listaPrecios;
            fichaPagoRiesgo.idOrdenVerificacion = listaVVGeneral.FirstOrDefault().ordenVerificacion;
            fichaPagoRiesgo.idEmpresaAsIs = idRuvAsIs;
            fichaPagoRiesgo.idCuvsSolicitadas = idGrupoPoliza;
            fichaPagoRiesgo.costoRelacion = costoRelacion;
            var regreso = await this.GenerarFichaPago(fichaPagoRiesgo, (int)ServicioProducto.SolicitarPoliza);

            var regresoD = await this.GenerarFichaPago(fichaPagoRiesgo, (int)ServicioProducto.PagoDiferencias);
            
            //fichaGenerada = this._servicioFicha.CrearFichaPago(1, null, null, ServicioProducto.SolicitarPoliza, Producto.SeguroCalidad, idRuvAsIs, false, listaPrecios.Count(), listaPrecios, vvGeneral.ordenVerificacion, false);

            //fichaGenerada = this._servicioFicha.CrearFichaPago(1, null, null, ServicioProducto.PagoDiferencias, Producto.SeguroCalidad, idRuvAsIs, false, listaPrecios.Count(), listaPrecios, vvGeneral.ordenVerificacion, false);

            return listaResultadosCuvPoliza;

        }

        private async Task<List<PreciosxId>> ObtenerListadoViviendas(List<CuvPoliza> listaCuvPoliza, string ordenVerificacion)
        {

            List<PreciosxId> listaPreciosViviendas = new List<PreciosxId>();

            List<VvGeneral> listaViviendasGeneral = await this._seguroCalidadDM.BuscarxCuvAsync(null, ordenVerificacion);

            foreach (CuvPoliza cuvs in listaCuvPoliza)
            {
                VvGeneral vvGeneral = listaViviendasGeneral.FirstOrDefault(c => c.cuv == cuvs.cuv);
                //CreditoVivienda creditoVivienda = await this._seguroCalidadDM.ConsultarAvaluoxCuvAsync(cuvs.cuv);
                ReporteViviendaInicioObraUnificado reporteVivienda = await this._seguroCalidadDM.ObteneCriterioSinInicioObraAsync(cuvs.cuv);
                ViviendasCostoxOrden viviendasCostoxOrden = await this._seguroCalidadDM.ConsultarCostoEvaluacionxCuvAsync(cuvs.cuv, 1);

                if(vvGeneral != null && reporteVivienda != null && viviendasCostoxOrden != null) { 
                    PreciosxId preciosxId = new PreciosxId();
                    preciosxId.id = vvGeneral.identificadorVivienda;
                    preciosxId.precio = viviendasCostoxOrden.montoAvaluo.Value;
                    preciosxId.criterioSinInicioObra = reporteVivienda.criterioSinInicioObra != null ? reporteVivienda.criterioSinInicioObra.Value : false;
                    preciosxId.montoAvaluo = viviendasCostoxOrden.montoAvaluo.Value;
                    preciosxId.costoOrdenVerificacion = viviendasCostoxOrden.costoViviendaOrden;
                    preciosxId.precio = viviendasCostoxOrden.costoVivienda;
                    preciosxId.cuv = viviendasCostoxOrden.cuv;
                    preciosxId.costoPoliza = viviendasCostoxOrden.costoPoliza != null? viviendasCostoxOrden.costoPoliza.Value : 0;
                    preciosxId.precioEvaluacion = viviendasCostoxOrden.costoEvaluacionRiesgo != null ? viviendasCostoxOrden.costoEvaluacionRiesgo.Value : 0;
                    preciosxId.diferenciaOv = viviendasCostoxOrden.costoDiferenciaOrden != null ? viviendasCostoxOrden.costoDiferenciaOrden.Value : 0;
                    listaPreciosViviendas.Add(preciosxId);
                }
            }

            return listaPreciosViviendas;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="listaAseguradora"></param>
        /// <param name="idUsuario"></param>
        /// <param name="idEmpresainst"></param>
        /// <returns></returns>
        public async Task<List<int>> GuardarRelacionComercialAsync(List<Aseguradora> listaAseguradora, int? idUsuario, string idEmpresainst)
        {
            List<int> listaIdRelacion = new List<int>();

            using (var transaccion = new TransactionScope(TransactionScopeOption.RequiresNew, TransactionScopeAsyncFlowOption.Enabled))
            {
                foreach (var aseguradora in listaAseguradora)
                {
                    var idRelacion = await this._seguroCalidadDM.GuardarRelacionComercialAsync(aseguradora.relacionComercial);

                    using (var transaccionNotificacion = new TransactionScope(TransactionScopeOption.RequiresNew, TransactionScopeAsyncFlowOption.Enabled))
                    {
                        await this.EnviarCorreoRelacionComercial(aseguradora, idEmpresainst);

                        var idEmpresa = _servicioEmpresaComun.ObtenerEmpresa(aseguradora.noRegistroRUV)?.idEmpresa;

                        var idTareaAgregada = await _servicioGenerales.AgregarTareaDto(new Tarea
                        {
                            IdEstatusTarea = (int)EstatusTareaEnum.Pendiente,
                            IdTipoTarea = (int)TipoTareaEnum.ValidarRelacionComercialPago,
                            IdEmpresa = idEmpresa,
                            IdUsuarioCreaTarea = idUsuario,
                            Descripcion = "Validar relación comercial Pago",
                            FechaRegistro = AzureDateTime.Now,
                            Activo = true
                        });

                        var idTareaRelacion = await _servicioGenerales.AgregarRelacionComercialXTarea(idRelacion, idTareaAgregada);

                        idTareaAgregada = await _servicioGenerales.AgregarTareaDto(new Tarea
                        {
                            IdEstatusTarea = (int)EstatusTareaEnum.Pendiente,
                            IdTipoTarea = (int)TipoTareaEnum.ValidarRelacionComercialEvaluacionRiesgo,
                            IdEmpresa = idEmpresa,
                            IdUsuarioCreaTarea = idUsuario,
                            Descripcion = "Validar relación comercial evaluación de riesgo",
                            FechaRegistro = AzureDateTime.Now,
                            Activo = true
                        });

                        var idTareaRelacion_ = await _servicioGenerales.AgregarRelacionComercialXTarea(idRelacion, idTareaAgregada);

                        transaccionNotificacion.Complete();
                    }


                    listaIdRelacion.Add(idRelacion);
                }

                transaccion.Complete();
            }

            return listaIdRelacion;
        }

        public async Task<int> ActualizarPagoDiferenciaOVAsync(PagoDiferenciaOV pagoDiferenciaOV)
        {
            var resultado = 0;

            using (var transaccion = new TransactionScope(TransactionScopeOption.RequiresNew, TransactionScopeAsyncFlowOption.Enabled))
            {
                resultado = await this._seguroCalidadDM.ActualizarPagoDiferenciaOVAsync(pagoDiferenciaOV);

                transaccion.Complete();
            }

            return resultado;
        }

        /// <summary>
        /// Actualiza la relacion comercial en todo los sentidos, puede cambiar de estatus los pagos y las evaluaciones de riesgo
        /// </summary>
        /// <param name="relacion"></param>
        /// <param name="idUsuario"></param>
        /// <returns></returns>
        public async Task<int> ActualizarRelacionComercialAsync(RelacionComercial relacion, int? idUsuario)
        {
            using (var transaccion = new TransactionScope(TransactionScopeOption.RequiresNew, TransactionScopeAsyncFlowOption.Enabled))
            {
                //Primero antes que todo, revisar si la relacion comercial que mando aun esta activa, si no manda error y hay que refrescar
                var relacionComercial = await this._seguroCalidadDM.ObtenerRelacionComercialAsync(null, null, null, relacion.idRelacionComercial);

                if (relacionComercial.activo.Value || (!relacionComercial.activo.Value && (relacion.contrato != null && relacion.contrato.IdDocumento != 0)))
                {
                    //Revisar si trae id documento ya que si si, hay que guardar el documento primero y agregarlo al objeto relacion comercial
                    if (relacion.contrato != null && relacion.contrato.IdDocumento != 0)
                    {
                        var idDocumento = await this._seguroCalidadDM.GuardarDocumentoComunAsync(relacion.contrato.UrlArchivo, relacion.contrato.NombreArchivo, 0, relacion.contrato.IdCatalogoDocumento.Value);

                        relacion.idDocumentoContrato = idDocumento;
                    }

                    if (relacion.fechaRechazo != null)
                    {
                        relacion.fechaRechazo = DateTime.Now;
                    }

                    if (relacion.fechaRechazoevaluacion != null)
                    {
                        relacion.fechaRechazoevaluacion = DateTime.Now;
                    }

                    if (relacion.fechaAceptacion != null)
                    {
                        relacion.fechaAceptacion = DateTime.Now;
                    }

                    await this._seguroCalidadDM.ActualizarRelacionComercialAsync(relacion);

                    Aseguradora aseguradoraCompleta = await ObtenerAseguradoraCompletaAsync(relacion.idAseguradora.Value);

                    int? idEmpresa = null;
                    int? idAseguradoraMasMas = null;

                    using (var transaccionNotificacion = new TransactionScope(TransactionScopeOption.RequiresNew, TransactionScopeAsyncFlowOption.Enabled))
                    {
                        idEmpresa = _servicioEmpresaComun.ObtenerEmpresa(relacion.idEmpresaInstMasMas)?.idEmpresa;
                        idAseguradoraMasMas = _servicioEmpresaComun.ObtenerEmpresa(aseguradoraCompleta.noRegistroRUV)?.idEmpresa;

                        if (relacion.idEstatusPagoEvaluacionRiesgo != null && relacion.idEstatusPagoEvaluacionRiesgo == (int)EstatusPagoEvaluacionRiesgo.Rechazado || relacion.idEstatusPagoEvaluacionRiesgo == (int)EstatusPagoEvaluacionRiesgo.Aceptado)
                        {
                            await this.EnviarCorreoValidacionEvaluacion(relacion, aseguradoraCompleta, relacion.idEstatusPagoEvaluacionRiesgo == (int)EstatusPagoEvaluacionRiesgo.Aceptado);
                        }

                        if (relacion.idEstatusRelacionComercial != null && relacion.idEstatusRelacionComercial == (int)EstatusRelacionComercial.Rechazado || relacion.idEstatusRelacionComercial == (int)EstatusRelacionComercial.Vigente)
                        {
                            await this.EnviarCorreoValidacionPago(relacion, aseguradoraCompleta, relacion.idEstatusRelacionComercial == (int)EstatusRelacionComercial.Vigente);
                        }

                        transaccionNotificacion.Complete();
                    }

                    using (var transaccionGenerales = new TransactionScope(TransactionScopeOption.RequiresNew, TransactionScopeAsyncFlowOption.Enabled))
                    {
                        if (idEmpresa != null && idAseguradoraMasMas != null)
                        {

                            //Si la relacionComercial se quita, entonces hay que cerrar todas las tareas que tengan que ver con esa relacion comercial
                            if (relacion.activo == false)
                            {
                                //Primero cerramos la tarea pendiente de validacion pago
                                var listaTareasCerrar = await _servicioGenerales.ObtenerListaRelacionComercialXTarea(relacion.idRelacionComercial, null, null);

                                foreach (var idTareas in listaTareasCerrar)
                                {
                                    var idTareaAactualizada = await _servicioGenerales.ActualizarTarea(new TareaBancos
                                    {
                                        IdTarea = idTareas.idTarea,
                                        IdEstatusTarea = (int)EstatusTareaEnum.Atendida,
                                        IdUsuarioAtiendeTarea = idUsuario
                                    });
                                }

                            }

                            if (relacion.idEstatusPagoEvaluacionRiesgo == (int)EstatusPagoEvaluacionRiesgo.Aceptado)
                            {
                                //Primero cerramos la tarea pendiente de validacion pago
                                var idTareaACerrar = await _servicioGenerales.ObtenerRelacionComercialXTarea(relacion.idRelacionComercial, null, (int)TipoTareaEnum.ValidarRelacionComercialEvaluacionRiesgo);

                                if (idTareaACerrar != null)
                                {
                                    var idTareaAactualizada = await _servicioGenerales.ActualizarTarea(new TareaBancos
                                    {
                                        IdTarea = idTareaACerrar.idTarea,
                                        IdEstatusTarea = (int)EstatusTareaEnum.Atendida,
                                        IdUsuarioAtiendeTarea = idUsuario
                                    });
                                }
                                
                            }

                            if (relacion.idEstatusRelacionComercial == (int)EstatusRelacionComercial.Vigente)
                            {
                                //Primero cerramos la tarea pendiente de validacion pago
                                var idTareaACerrar = await _servicioGenerales.ObtenerRelacionComercialXTarea(relacion.idRelacionComercial, null, (int)TipoTareaEnum.ValidarRelacionComercialPago);

                                if (idTareaACerrar != null)
                                {
                                    var idTareaAactualizada = await _servicioGenerales.ActualizarTarea(new TareaBancos
                                    {
                                        IdTarea = idTareaACerrar.idTarea,
                                        IdEstatusTarea = (int)EstatusTareaEnum.Atendida,
                                        IdUsuarioAtiendeTarea = idUsuario
                                    });
                                }                                    
                            }

                            //AQui cerrar tarea por sancion aseguradora
                            if (relacion.idEstatusPagoEvaluacionRiesgo == (int)EstatusPagoEvaluacionRiesgo.Aceptado && relacion.idEstatusRelacionComercial == (int)EstatusRelacionComercial.Vigente)
                            {
                                if (aseguradoraCompleta.sancionVigente != null)
                                {
                                    var idTarea = await _servicioGenerales.ObtenerSancionXTarea(aseguradoraCompleta.sancionVigente.idSancion.Value);

                                    if (idTarea != 0)
                                    {
                                        var idTareaAactualizada = await _servicioGenerales.ActualizarTarea(new TareaBancos
                                        {
                                            IdTarea = idTarea,
                                            IdEstatusTarea = (int)EstatusTareaEnum.Atendida,
                                            IdUsuarioAtiendeTarea = idUsuario
                                        });
                                    }

                                    idTarea = await _servicioGenerales.ObtenerPadronXTarea(idEmpresa.Value);

                                    if (idTarea != 0)
                                    {
                                        var idTareaAactualizada = await _servicioGenerales.ActualizarTarea(new TareaBancos
                                        {
                                            IdTarea = idTarea,
                                            IdEstatusTarea = (int)EstatusTareaEnum.Atendida,
                                            IdUsuarioAtiendeTarea = idUsuario
                                        });
                                    }

                                }
                            }

                            if (relacion.idEstatusPagoEvaluacionRiesgo == (int)EstatusPagoEvaluacionRiesgo.Rechazado)
                            {
                                //Primero cerramos la tarea pendiente de validacion pago
                                var idTareaACerrar = await _servicioGenerales.ObtenerRelacionComercialXTarea(relacion.idRelacionComercial, null, (int)TipoTareaEnum.ValidarRelacionComercialEvaluacionRiesgo);

                                if (idTareaACerrar != null)
                                {
                                    var idTareaAactualizada = await _servicioGenerales.ActualizarTarea(new TareaBancos
                                    {
                                        IdTarea = idTareaACerrar.idTarea,
                                        IdEstatusTarea = (int)EstatusTareaEnum.Atendida,
                                        IdUsuarioAtiendeTarea = idUsuario
                                    });
                                }

                                //Aqui creamos la tarea nueva
                                var idTareaAgregada = await _servicioGenerales.AgregarTareaDto(new Tarea
                                {
                                    IdEstatusTarea = (int)EstatusTareaEnum.Pendiente,
                                    IdTipoTarea = (int)TipoTareaEnum.RechazoEvaluacionRiesgo,
                                    IdEmpresa = idEmpresa,
                                    IdUsuarioCreaTarea = idUsuario,
                                    Descripcion = "Se rechazó evaluación comercial",
                                    FechaRegistro = AzureDateTime.Now,
                                    Activo = true
                                });                                

                                var idTareaRelacion = await _servicioGenerales.AgregarRelacionComercialXTarea(relacion.idRelacionComercial.Value, idTareaAgregada);

                            }

                            if (relacion.idEstatusRelacionComercial == (int)EstatusRelacionComercial.Rechazado)
                            {
                                //Primero cerramos la tarea pendiente de validacion pago
                                var idTareaACerrar = await _servicioGenerales.ObtenerRelacionComercialXTarea(relacion.idRelacionComercial, null, (int)TipoTareaEnum.ValidarRelacionComercialPago);

                                if (idTareaACerrar != null)
                                {
                                    var idTareaAactualizada = await _servicioGenerales.ActualizarTarea(new TareaBancos
                                    {
                                        IdTarea = idTareaACerrar.idTarea,
                                        IdEstatusTarea = (int)EstatusTareaEnum.Atendida,
                                        IdUsuarioAtiendeTarea = idUsuario
                                    });
                                }

                                //Aqui creamos la tarea nueva
                                var idTareaAgregada = await _servicioGenerales.AgregarTareaDto(new Tarea
                                {
                                    IdEstatusTarea = (int)EstatusTareaEnum.Pendiente,
                                    IdTipoTarea = (int)TipoTareaEnum.RechazoPagoEvaluacionRiesgo,
                                    IdEmpresa = idEmpresa,
                                    IdUsuarioCreaTarea = idUsuario,
                                    Descripcion = "Se rechazó pago",
                                    FechaRegistro = AzureDateTime.Now,
                                    Activo = true
                                });

                                var idTareaRelacion = await _servicioGenerales.AgregarRelacionComercialXTarea(relacion.idRelacionComercial.Value, idTareaAgregada);
                            }

                            if (relacion.idEstatusPagoEvaluacionRiesgo == (int)EstatusPagoEvaluacionRiesgo.ValidacionPorActualizacion)
                            {

                                //obtenemos la tarea a cerrar
                                var idTareaACerrar = await _servicioGenerales.ObtenerRelacionComercialXTarea(relacion.idRelacionComercial, null, (int)TipoTareaEnum.RechazoEvaluacionRiesgo);

                                if (idTareaACerrar != null)
                                {
                                    var idTareaActualizada = await _servicioGenerales.ActualizarTarea(new TareaBancos
                                    {
                                        IdTarea = idTareaACerrar.idTarea,
                                        IdEstatusTarea = (int)EstatusTareaEnum.Atendida,
                                        IdUsuarioAtiendeTarea = idUsuario
                                    });
                                }

                                // Aqui creamos la tarea nueva
                                var idTareaAgregada = await _servicioGenerales.AgregarTareaDto(new Tarea
                                {
                                    IdEstatusTarea = (int)EstatusTareaEnum.Pendiente,
                                    IdTipoTarea = (int)TipoTareaEnum.ValidarRelacionComercialEvaluacionRiesgo,
                                    IdEmpresa = idAseguradoraMasMas,
                                    IdUsuarioCreaTarea = idUsuario,
                                    Descripcion = "Se envia validación de evaluación comercial",
                                    FechaRegistro = AzureDateTime.Now,
                                    Activo = true
                                });

                                var idTareaRelacion = await _servicioGenerales.AgregarRelacionComercialXTarea(relacion.idRelacionComercial.Value, idTareaAgregada);

                            }

                            if (relacion.idEstatusRelacionComercial == (int)EstatusRelacionComercial.ValidaciónPorActualización)
                            {
                                //obtenemos la tarea a cerrar
                                var idTareaACerrar = await _servicioGenerales.ObtenerRelacionComercialXTarea(relacion.idRelacionComercial, null, (int)TipoTareaEnum.RechazoPagoEvaluacionRiesgo);

                                if (idTareaACerrar != null)
                                {
                                    var idTareaActualizado = await _servicioGenerales.ActualizarTarea(new TareaBancos
                                    {
                                        IdTarea = idTareaACerrar.idTarea,
                                        IdEstatusTarea = (int)EstatusTareaEnum.Atendida,
                                        IdUsuarioAtiendeTarea = idUsuario
                                    });
                                }

                                // Aqui creamos la tarea nueva
                                var idTareaAgregada = await _servicioGenerales.AgregarTareaDto(new Tarea
                                {
                                    IdEstatusTarea = (int)EstatusTareaEnum.Pendiente,
                                    IdTipoTarea = (int)TipoTareaEnum.ValidarRelacionComercialPago,
                                    IdEmpresa = idAseguradoraMasMas,
                                    IdUsuarioCreaTarea = idUsuario,
                                    Descripcion = "Se envia validación de pago",
                                    FechaRegistro = AzureDateTime.Now,
                                    Activo = true
                                });                                

                                var idTareaRelacion = await _servicioGenerales.AgregarRelacionComercialXTarea(relacion.idRelacionComercial.Value, idTareaAgregada);
                            }
                        }
                        else
                        {
                            return -1;
                        }

                        transaccionGenerales.Complete();
                    }
                }
                else
                {
                    return 0;
                }

                transaccion.Complete();
            }

            return relacion.idRelacionComercial.Value;
        }

        public async Task<bool> ActualizarAseguradoraOrdenPadronAsync(List<Aseguradora> listaAseguradora)
        {           
            using (var transaccion = new TransactionScope(TransactionScopeOption.RequiresNew, TransactionScopeAsyncFlowOption.Enabled))
            {
                foreach (var aseguradora in listaAseguradora)
                {
                    await this._seguroCalidadDM.ActualizarAseguradoraOrdenPadronAsync(aseguradora);
                }                                

                transaccion.Complete();
            }

            return true;
        }

        /// <summary>
        /// Actualiza la relacion comercial en todo los sentidos, puede cambiar de estatus los pagos y las evaluaciones de riesgo
        /// </summary>
        /// <param name="relacion"></param>
        /// <param name="idUsuario"></param>
        /// <returns></returns>
        public async Task<int> ActualizarRelacionComercialXRechazoAsync(RelacionComercial relacion, int? idUsuario)
        {
            using (var transaccion = new TransactionScope(TransactionScopeOption.RequiresNew, TransactionScopeAsyncFlowOption.Enabled))
            {
                //Primero antes que todo, revisar si la relacion comercial que mando aun esta activa, si no manda error y hay que refrescar
                var relacionComercial = await this._seguroCalidadDM.ObtenerRelacionComercialAsync(null, null, null, relacion.idRelacionComercial);

                if (relacionComercial.activo.Value || (!relacionComercial.activo.Value && (relacion.contrato != null && relacion.contrato.IdDocumento != 0)))
                {
                    //Actualizamos con los datos que mando
                    await this._seguroCalidadDM.ActualizarRelacionComercialAsync(relacion);

                    Aseguradora aseguradoraCompleta = await ObtenerAseguradoraCompletaAsync(relacion.idAseguradora.Value);

                    int? idEmpresa = null;
                    int? idAseguradoraMasMas = null;

                    //Aqui son las notificaciones
                    using (var transaccionNotificacion = new TransactionScope(TransactionScopeOption.RequiresNew, TransactionScopeAsyncFlowOption.Enabled))
                    {
                        idEmpresa = _servicioEmpresaComun.ObtenerEmpresa(relacion.idEmpresaInstMasMas)?.idEmpresa;
                        idAseguradoraMasMas = _servicioEmpresaComun.ObtenerEmpresa(aseguradoraCompleta.noRegistroRUV)?.idEmpresa;

                        //if (relacion.idEstatusPagoEvaluacionRiesgo != null && relacion.idEstatusPagoEvaluacionRiesgo == (int)EstatusPagoEvaluacionRiesgo.Rechazado || relacion.idEstatusPagoEvaluacionRiesgo == (int)EstatusPagoEvaluacionRiesgo.Aceptado)
                        //{
                        //    await this.EnviarCorreoValidacionEvaluacion(relacion, aseguradoraCompleta, relacion.idEstatusPagoEvaluacionRiesgo == (int)EstatusPagoEvaluacionRiesgo.Aceptado);
                        //}

                        //if (relacion.idEstatusRelacionComercial != null && relacion.idEstatusRelacionComercial == (int)EstatusRelacionComercial.Rechazado || relacion.idEstatusRelacionComercial == (int)EstatusRelacionComercial.Vigente)
                        //{
                        //    await this.EnviarCorreoValidacionPago(relacion, aseguradoraCompleta, relacion.idEstatusRelacionComercial == (int)EstatusRelacionComercial.Vigente);
                        //}

                        transaccionNotificacion.Complete();
                    }

                    //Aqui se haran las tareas
                    using (var transaccionGenerales = new TransactionScope(TransactionScopeOption.RequiresNew, TransactionScopeAsyncFlowOption.Enabled))
                    {
                        if (idEmpresa != null && idAseguradoraMasMas != null)
                        {                                                                                   
                            if (relacion.idEstatusPagoEvaluacionRiesgo == (int)EstatusPagoEvaluacionRiesgo.ValidacionPorActualizacion)
                            {
                                //obtenemos la tarea a cerrar
                                var idTareaACerrar = await _servicioGenerales.ObtenerRelacionComercialXTarea(relacion.idRelacionComercial, null, (int)TipoTareaEnum.RechazoPagoEvaluacionRiesgo);

                                if (idTareaACerrar != null)
                                {
                                    var idTareaActualizada = await _servicioGenerales.ActualizarTarea(new TareaBancos
                                    {
                                        IdTarea = idTareaACerrar.idTarea,
                                        IdEstatusTarea = (int)EstatusTareaEnum.Atendida,
                                        IdUsuarioAtiendeTarea = idUsuario
                                    });
                                }

                                // Aqui creamos la tarea nueva
                                var idTareaAgregada = await _servicioGenerales.AgregarTareaDto(new Tarea
                                {
                                    IdEstatusTarea = (int)EstatusTareaEnum.Pendiente,
                                    IdTipoTarea = (int)TipoTareaEnum.ValidarRelacionComercialPago,
                                    IdEmpresa = idAseguradoraMasMas,
                                    IdUsuarioCreaTarea = idUsuario,
                                    Descripcion = "Se envía validación de pago",
                                    FechaRegistro = AzureDateTime.Now,
                                    Activo = true
                                });

                                var idTareaRelacion = await _servicioGenerales.AgregarRelacionComercialXTarea(relacion.idRelacionComercial.Value, idTareaAgregada);

                            }

                            if (relacion.idEstatusRelacionComercial == (int)EstatusRelacionComercial.ValidaciónPorActualización)
                            {
                                //obtenemos la tarea a cerrar
                                var idTareaACerrar = await _servicioGenerales.ObtenerRelacionComercialXTarea(relacion.idRelacionComercial, null, (int)TipoTareaEnum.RechazoEvaluacionRiesgo);

                                if (idTareaACerrar != null)
                                {
                                    var idTareaActualizado = await _servicioGenerales.ActualizarTarea(new TareaBancos
                                    {
                                        IdTarea = idTareaACerrar.idTarea,
                                        IdEstatusTarea = (int)EstatusTareaEnum.Atendida,
                                        IdUsuarioAtiendeTarea = idUsuario
                                    });
                                }


                                // Aqui creamos la tarea nueva
                                var idTareaAgregada = await _servicioGenerales.AgregarTareaDto(new Tarea
                                {
                                    IdEstatusTarea = (int)EstatusTareaEnum.Pendiente,
                                    IdTipoTarea = (int)TipoTareaEnum.ValidarRelacionComercialEvaluacionRiesgo,
                                    IdEmpresa = idAseguradoraMasMas,
                                    IdUsuarioCreaTarea = idUsuario,
                                    Descripcion = "Se envía validación de evaluación comercial",
                                    FechaRegistro = AzureDateTime.Now,
                                    Activo = true
                                });

                                var idTareaRelacion = await _servicioGenerales.AgregarRelacionComercialXTarea(relacion.idRelacionComercial.Value, idTareaAgregada);
                            }
                        }
                        else
                        {
                            return -1;
                        }

                        transaccionGenerales.Complete();
                    }
                }
                else
                {
                    return 0;
                }

                transaccion.Complete();
            }

            return relacion.idRelacionComercial.Value;
        }

        public async Task<List<OpcionCatalogoSeguroCalidad>> ObtenerEstatusPagoEvaluacionRiesgoAsync()
        {
            return await this._seguroCalidadDM.ObtenerEstatusPagoEvaluacionRiesgoAsync();
        }

        public async Task<List<RelacionesComercialesOferente>> ObtenerRelacionesComercialesXDesarrolladorAsync()
        {
            return await this._seguroCalidadDM.ObtenerRelacionesComercialesXDesarrolladorAsync();
        }

        #region ServiciosINFONAVIT

        public async Task<bool> GuardarDatosTitulacionAsync(DatosTitulacionINFONAVIT datosTitulacion)
        {
            var resultado = await this._seguroCalidadDM.GuardarDatosTitulacionAsync(datosTitulacion);

            return resultado;
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="datosValorAvaluo"></param>
        /// <returns></returns>
        public async Task<ResultadoPeticion> GuardarDatosValorAvaluo(DatosValorAvaluo datosValorAvaluo)
        {
            try
            {
                ResultadoPeticion resultadoPeticion = await this.ValidarDatosAvaluo(datosValorAvaluo);

                if (resultadoPeticion != null)
                {
                    return resultadoPeticion;
                }
               

                using (var transaccion = new TransactionScope(TransactionScopeOption.RequiresNew, TransactionScopeAsyncFlowOption.Enabled))
                {
                    await this._seguroCalidadDM.GuardarDatosValorAvaluoAsync(datosValorAvaluo);
                   
                    await this.EliminarPoliza(datosValorAvaluo.cuv);

                    OrdenDeVerificacion ordenDeVerificaicon = await this._seguroCalidadDM.ObteneOrdenVeirificacionAsync(datosValorAvaluo.cuv);

                    await this.GenerarCostoPolizaAsync(datosValorAvaluo.cuv, ordenDeVerificaicon.ordenVerificacion);

                    resultadoPeticion = new ResultadoPeticion() { resultado = "OK", descripcion = "Registro guardado exitosamente." };

                    transaccion.Complete();
                }

                return resultadoPeticion;

            }
            catch (Exception exc)
            {
                ResultadoPeticion resultadoPeticion = new ResultadoPeticion() { resultado = "OK", descripcion = "No se guardado el registro, favor de intentarlo más tarde." };

                return resultadoPeticion;
            }
            
        }

        /// <summary>
        /// Se recibe el valor avaluo y se elimina la poliza en caso de 
        /// </summary>
        /// <param name="Cuv"></param>
        /// <returns></returns>
        public async Task<bool> EliminarPoliza(string Cuv)
        {
            VvGeneral vvGeneral = (await this._seguroCalidadDM.BuscarxCuvAsync(Cuv, null)).FirstOrDefault();
            if (vvGeneral != null) {
                await this._seguroCalidadDM.EliminarPolizaVigenteAsync(vvGeneral.idvvGeneral);
            }
            return true;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="datosVivienda"></param>
        /// <returns></returns>
        public async Task<ViviendaAsegurada> ValidarSeguroVivienda(DatosVivienda datosVivienda) {

            ViviendaAsegurada viviendaAsegurada;

            if (datosVivienda == null)
            {
                viviendaAsegurada = new ViviendaAsegurada();
                viviendaAsegurada.estatus = 0;
                viviendaAsegurada.resultado = "NOK";
                viviendaAsegurada.descripcion = "Los datos recibidos no son validos.";
                return viviendaAsegurada;
            }

            if (datosVivienda.cuv == null || datosVivienda.cuv.Length != 16)
            {
                viviendaAsegurada = new ViviendaAsegurada();
                viviendaAsegurada.estatus = 0;
                viviendaAsegurada.resultado = "NOK";
                viviendaAsegurada.descripcion = "La Cuv no es válida.";
                return viviendaAsegurada;
            }
            else
            {
                
                DatosValorAvaluo exiteCuv = await this._seguroCalidadDM.BuscarCuv(datosVivienda.cuv);

                if (exiteCuv == null && exiteCuv.cuv == null)
                {
                    viviendaAsegurada = new ViviendaAsegurada();
                    viviendaAsegurada.estatus = 0;
                    viviendaAsegurada.resultado = "NOK";
                    viviendaAsegurada.descripcion = "La Cuv no existe.";
                    return viviendaAsegurada;
                }

            }


            DatosValorAvaluo datosValorAvaluo = await this._seguroCalidadDM.BuscarTieneSeguroCalidad(datosVivienda.cuv);

            if (datosValorAvaluo != null)
            {
                if (datosValorAvaluo.idEstatusPoliza == 3) {
                    viviendaAsegurada = new ViviendaAsegurada();
                    viviendaAsegurada.estatus = 2;
                    viviendaAsegurada.resultado = "OK";
                    viviendaAsegurada.descripcion = "La Cuv tiene seguro calidad.";
                    return viviendaAsegurada;
                }else{
                    viviendaAsegurada = new ViviendaAsegurada();
                    viviendaAsegurada.estatus = 0;
                    viviendaAsegurada.resultado = "NOK";
                    viviendaAsegurada.descripcion = "La Cuv no cuenta con seguro de calidad.";
                    return viviendaAsegurada;
                }
            }


            bool tieneCobertura = await this._seguroCalidadDM.BuscarTieneCobertura(datosVivienda.cuv);

            if (tieneCobertura)
            {
                viviendaAsegurada = new ViviendaAsegurada();
                viviendaAsegurada.estatus = 1;
                viviendaAsegurada.resultado = "OK";
                viviendaAsegurada.descripcion = "La Cuv tiene cobertura calidad.";
                return viviendaAsegurada;
            }

            viviendaAsegurada = new ViviendaAsegurada();
            viviendaAsegurada.estatus = 0;
            viviendaAsegurada.resultado = "NOK";
            viviendaAsegurada.descripcion = "La Cuv no existe.";
            return viviendaAsegurada;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="datosValorAvaluo"></param>
        /// <returns></returns>
        private async Task<ResultadoPeticion> ValidarDatosAvaluo(DatosValorAvaluo datosValorAvaluo)
        {
            ResultadoPeticion resultadoPeticion = null;
            try
            {
                if (datosValorAvaluo == null)
                {
                    resultadoPeticion = new ResultadoPeticion();
                    resultadoPeticion.resultado = "NOK";
                    resultadoPeticion.descripcion = "Los datos recibidos no son validos";
                    return resultadoPeticion;
                }

                //if (datosValorAvaluo.usuario == null)
                //{
                //    resultadoPeticion = new ResultadoPeticion();
                //    resultadoPeticion.resultado = "NOK";
                //    resultadoPeticion.descripcion = "El usuario ingresado no es valido";
                //    return resultadoPeticion;
                //}

                //if (datosValorAvaluo.passsword == null)
                //{
                //    resultadoPeticion = new ResultadoPeticion();
                //    resultadoPeticion.resultado = "NOK";
                //    resultadoPeticion.descripcion = "El passsword ingresado no es valido";
                //    return resultadoPeticion;
                //}

                if (datosValorAvaluo.cuv == null || datosValorAvaluo.cuv.Length != 16)
                {
                    resultadoPeticion = new ResultadoPeticion();
                    resultadoPeticion.resultado = "NOK";
                    resultadoPeticion.descripcion = "La Cuv no es válida";
                    return resultadoPeticion;
                }
                else
                {
                    DatosValorAvaluo exiteCuv = await this._seguroCalidadDM.BuscarCuv(datosValorAvaluo.cuv);

                    if (exiteCuv == null || exiteCuv.cuv == null)
                    {
                        resultadoPeticion = new ResultadoPeticion();
                        resultadoPeticion.resultado = "NOK";
                        resultadoPeticion.descripcion = "La Cuv no existe";
                        return resultadoPeticion;
                    }

                }

                if (datosValorAvaluo.fechaCierreAvaluo == null)
                {


                    resultadoPeticion = new ResultadoPeticion();
                    resultadoPeticion.resultado = "NOK";
                    resultadoPeticion.descripcion = "La fecha de cierre del Avaluó no es valida(dd-mm-yyyy HH:mm)";
                    return resultadoPeticion;
                }
                else
                {

                    try
                    {
                        DateTime myDate = DateTime.ParseExact(datosValorAvaluo.fechaCierreAvaluo, "dd-MM-yyyy HH:mm",
                                          System.Globalization.CultureInfo.InvariantCulture);

                        string fecha = myDate.ToString("yyyy/MM/dd HH:mm");

                        datosValorAvaluo.fechaCierreAvaluo = fecha;

                        if (myDate == null)
                        {
                            resultadoPeticion = new ResultadoPeticion();
                            resultadoPeticion.resultado = "NOK";
                            resultadoPeticion.descripcion = "La fecha de cierre del Avaluó no es valida(dd-mm-yyyy HH:mm)";
                            return resultadoPeticion;
                        }
                    }
                    catch (Exception exc)
                    {
                        resultadoPeticion = new ResultadoPeticion();
                        resultadoPeticion.resultado = "NOK";
                        resultadoPeticion.descripcion = "La fecha de cierre del Avaluó no es valida(dd-mm-yyyy HH:mm)";
                        return resultadoPeticion;
                    }
                }

                if (datosValorAvaluo.fechaVigenciaAvaluo == null)
                {
                    resultadoPeticion = new ResultadoPeticion();
                    resultadoPeticion.resultado = "NOK";
                    resultadoPeticion.descripcion = "La fecha de vigencia del Avaluó no es valida(dd-mm-yyyy HH:mm)";
                    return resultadoPeticion;
                }
                else
                {
                    try
                    {
                        DateTime myDate = DateTime.ParseExact(datosValorAvaluo.fechaVigenciaAvaluo, "dd-MM-yyyy HH:mm",
                                          System.Globalization.CultureInfo.InvariantCulture);

                        string fecha = myDate.ToString("yyyy/MM/dd HH:mm");

                        datosValorAvaluo.fechaVigenciaAvaluo = fecha;

                        if (myDate == null)
                        {
                            resultadoPeticion = new ResultadoPeticion();
                            resultadoPeticion.resultado = "NOK";
                            resultadoPeticion.descripcion = "La fecha de vigencia del Avaluó no es valida(dd-mm-yyyy HH:mm)";
                            return resultadoPeticion;
                        }
                    }
                    catch (Exception exc)
                    {
                        resultadoPeticion = new ResultadoPeticion();
                        resultadoPeticion.resultado = "NOK";
                        resultadoPeticion.descripcion = "La fecha de vigencia del Avaluó no es valida(dd-mm-yyyy HH:mm)";
                        return resultadoPeticion;
                    }

                }

                if (datosValorAvaluo.valorAvaluo <= 0)
                {
                    resultadoPeticion = new ResultadoPeticion();
                    resultadoPeticion.resultado = "NOK";
                    resultadoPeticion.descripcion = "El valor Avaluó es invalido";
                    return resultadoPeticion;
                }
            }
            catch (Exception exc)
            {
                resultadoPeticion = new ResultadoPeticion();
                resultadoPeticion.resultado = "NOK";
                resultadoPeticion.descripcion = "Los datos recibidos no son validos";
                return resultadoPeticion;
            }

            return resultadoPeticion;
        }

        #endregion

        #region Incidencias

        public async Task<List<OpcionCatalogoSeguroCalidad>> ObtenerTipoIncidenciasAsync()
        {
            return await this._seguroCalidadDM.ObtenerTipoIncidenciasAsync();
        }

        public async Task<List<OpcionCatalogoSeguroCalidad>> ObtenerEstatusIncidenciasAsync()
        {
            return await this._seguroCalidadDM.ObtenerEstatusIncidenciasAsync();
        }

        public async Task<List<OpcionCatalogoSeguroCalidad>> ObtenerClasificacionIncidenciasAsync()
        {
            return await this._seguroCalidadDM.ObtenerClasificacionIncidenciasAsync();
        }

        public async Task<ResultadoPaginado<List<IncidenciaGestion>>> ObtenerIncidenciasGestionAsync(int tamanioPagina, int pagina, string ordenVerificacion = null, string cuv = null, string desarrollador = null, string claveIncidencia = null, string idTipoIncidencia = null, string idEstatusIncidencia = null, string idClasificacionIncidencia = null, bool? esFechaRegistro = default(bool?), bool? esFechaAtencion = default(bool?), DateTime? fechaInicial = default(DateTime?), DateTime? fechaFinal = default(DateTime?), string idEntidad = null, string idEmpresa = null, string conRiesgo = null)
        {
            int? idVerificador = null;
            int? idAseguradora = null;
            string idDesarrolladorInst = null;

            var datosEmpresa = this._servicioEmpresaConsulta.ObtenerEmpresaDto(Convert.ToInt32(idEmpresa));


            if (Convert.ToInt32(idEntidad) == (int)EnumEntidadEmpresa.Verificador)
            {
                idVerificador = await this._seguroCalidadDM.ObtenerIdVerificadorAsync(datosEmpresa.IdEmpresaInst);
            }
            else if (Convert.ToInt32(idEntidad) == (int)EnumEntidadEmpresa.Aseguradora)
            {
                idAseguradora = await this._seguroCalidadDM.ObtenerIdAseguradoraAsync(datosEmpresa.IdEmpresaInst);
            }
            else if (Convert.ToInt32(idEntidad) == (int)EnumEntidadEmpresa.Desarrollador)
            {
                idDesarrolladorInst = datosEmpresa.IdEmpresaInst;
            }

            ResultadoPaginado<List<IncidenciaGestion>> resultado = new ResultadoPaginado<List<IncidenciaGestion>>()
            {
                TamanioPagina = tamanioPagina,
                PaginaActual = pagina
            };

            var data = await this._seguroCalidadDM.ObtenerIncidenciasGestionAsync(tamanioPagina, pagina, ordenVerificacion, cuv, desarrollador, claveIncidencia, idTipoIncidencia, idEstatusIncidencia, idClasificacionIncidencia, esFechaRegistro, esFechaAtencion, fechaInicial, fechaFinal, idVerificador, idAseguradora, idDesarrolladorInst, conRiesgo);

            if (data.Item1 > 0)
            {
                resultado.TotalRegistros = data.Item1;
                resultado.TotalPaginas = (int)Math.Ceiling((double)(resultado.TotalRegistros / tamanioPagina));
            }

            var incidencias = data.Item2;

            resultado.Resultado = incidencias;

            if (resultado.Resultado != null && resultado.Resultado.Any())
            {
                foreach (var item in resultado.Resultado)
                {
                    item.CUV = $"{item.CUV} ";
                }
            }

            return resultado;
        }

        public async Task<List<IncidenciaGestion>> ObtenerIncidenciasGestionSinPaginadoAsync(string ordenVerificacion = null, string cuv = null, string desarrollador = null, string claveIncidencia = null, string idTipoIncidencia = null, string idEstatusIncidencia = null, string idClasificacionIncidencia = null, bool? esFechaRegistro = default(bool?), bool? esFechaAtencion = default(bool?), DateTime? fechaInicial = default(DateTime?), DateTime? fechaFinal = default(DateTime?), string idEntidad = null, string idEmpresa = null, string conRiesgo = null)
        {
            int? idVerificador = null;
            int? idAseguradora = null;
            string idDesarrolladorInst = null;

            var datosEmpresa = this._servicioEmpresaConsulta.ObtenerEmpresaDto(Convert.ToInt32(idEmpresa));

            if (Convert.ToInt32(idEntidad) == (int)EnumEntidadEmpresa.Verificador)
            {
                idVerificador = await this._seguroCalidadDM.ObtenerIdVerificadorAsync(datosEmpresa.IdEmpresaInst);
            }
            else if (Convert.ToInt32(idEntidad) == (int)EnumEntidadEmpresa.Aseguradora)
            {
                idAseguradora = await this._seguroCalidadDM.ObtenerIdAseguradoraAsync(datosEmpresa.IdEmpresaInst);
            }
            else if (Convert.ToInt32(idEntidad) == (int)EnumEntidadEmpresa.Desarrollador)
            {
                idDesarrolladorInst = datosEmpresa.IdEmpresaInst;
            }

            var resultado = await this._seguroCalidadDM.ObtenerIncidenciasGestionSinPaginadoAsync(ordenVerificacion, cuv, desarrollador, claveIncidencia, idTipoIncidencia, idEstatusIncidencia, idClasificacionIncidencia, esFechaRegistro, esFechaAtencion, fechaInicial, fechaFinal, idVerificador, idAseguradora, idDesarrolladorInst, conRiesgo);

            if (resultado != null && resultado.Any())
            {
                foreach (var item in resultado)
                {
                    item.CUV = $"{item.CUV} ";
                }
            }

            return resultado;
        }

        public async Task<List<OpcionCatalogoSeguroCalidad>> ObtenerOrdenesVerificacionAsync(string idEntidad, string idEmpresa)
        {
            int? idVerificador = null;
            int? idAseguradora = null;

            var datosEmpresa = this._servicioEmpresaConsulta.ObtenerEmpresaDto(Convert.ToInt32(idEmpresa));


            if (Convert.ToInt32(idEntidad) == (int)EnumEntidadEmpresa.Verificador)
            {
                idVerificador = await this._seguroCalidadDM.ObtenerIdVerificadorAsync(datosEmpresa.IdEmpresaInst);
            }
            else if (Convert.ToInt32(idEntidad) == (int)EnumEntidadEmpresa.Aseguradora)
            {
                idAseguradora = await this._seguroCalidadDM.ObtenerIdAseguradoraAsync(datosEmpresa.IdEmpresaInst);
            }

            var ordenesVerificacion = await this._seguroCalidadDM.ObtenerOrdenesVerificacionAsync(idAseguradora, idVerificador, null, null, null, null);

            return ordenesVerificacion;
        }

        public async Task<List<OpcionCatalogoSeguroCalidad>> ObtenerOrdenesVerificacionConsultaAsync(string noRuv, string razonSocial, string rfc)
        {            
            ConsultaEmpresas empresa = new ConsultaEmpresas { NRRuv = noRuv, RFC = rfc, RazonSocial = razonSocial };

            var ordenesVerificacion = new List<OpcionCatalogoSeguroCalidad>();

            var datosEmpresa = _servicioEmpresaConsulta.ObtenerEmpresasConsulta(empresa, null);

            if(datosEmpresa.ListaEntidad.Count > 0)
                ordenesVerificacion = await this._seguroCalidadDM.ObtenerOrdenesVerificacionAsync(null, null, noRuv, razonSocial, rfc, Convert.ToInt32(datosEmpresa.ListaEntidad.FirstOrDefault().identidad));           

            return ordenesVerificacion;
        }

        public async Task<InformacionOVNuevaincidencia> ObtenerInformacionOVNuevaIncidenciaAsync(string OrdenVerificacion, string cuv, string idEntidad, string idEmpresa)
        {
            var resultado = await this._seguroCalidadDM.ObtenerInformacionOVNuevaIncidenciaAsync(OrdenVerificacion);

            var datosContactoDesarrollador = await this._seguroCalidadDM__.ObtenerDatosContactoEmpresaAsync(resultado.idDesarrolladorIns.ToString());
            var datosContactoVerificador = await this._seguroCalidadDM__.ObtenerDatosContactoEmpresaAsync(resultado.idVerificadorIns.ToString());

            resultado.TelefonoDesarrollador = datosContactoDesarrollador?.TelefonoPrincipal;
            resultado.CorreoDesarrollador = datosContactoDesarrollador?.CorreoPrincipal;

            resultado.TelefonoVerificador = datosContactoVerificador?.TelefonoPrincipal;
            resultado.CorreoVerificador = datosContactoVerificador?.CorreoPrincipal;

            return resultado;
        }

        public async Task<CuvValida> ValidarCuvValidaAsync(string cuv, int? idEntidad, int? idEmpresa)
        {
            CuvValida resultado = new CuvValida();
            int? idVerificador = null;
            int? idAseguradora = null;

            var datosEmpresa = this._servicioEmpresaConsulta.ObtenerEmpresaDto(idEmpresa.Value);

            if (idEntidad == (int)EnumEntidadEmpresa.Verificador)
            {
                idVerificador = await this._seguroCalidadDM.ObtenerIdVerificadorAsync(datosEmpresa.IdEmpresaInst);
                resultado = await this._seguroCalidadDM.ValidarCuvVerificadorAsync(cuv, idVerificador);

                if (resultado == null)
                {
                    resultado = new CuvValida();
                    resultado.esValida = false;
                    resultado.MensajeValidacion = "CUV no válida para Verificador";
                }
                else
                {
                    if (resultado.claveEstatus != null && resultado.claveEstatus == 3)
                    {
                        resultado.esValida = false;
                        resultado.MensajeValidacion = "CUV no válida, la vivienda se encuentra individualizada";
                    }
                    else if (resultado.idEstatusPoliza != null && (resultado.idEstatusPoliza == 1 || resultado.idEstatusPoliza == 3)) {
                        resultado.esValida = false;
                        resultado.MensajeValidacion = "CUV no válida, la vivienda cuenta con una póliza de seguro vigente";
                    }
                    else
                    {
                        resultado.esValida = true;
                        resultado.MensajeValidacion = "Valida";
                    }
                }
            }
            else if (idEntidad == (int)EnumEntidadEmpresa.Aseguradora)
            {
                idAseguradora = await this._seguroCalidadDM.ObtenerIdAseguradoraAsync(datosEmpresa.IdEmpresaInst);
                resultado = await this._seguroCalidadDM.ValidarCuvAseguradoraAsync(cuv, idAseguradora);

                if (resultado == null)
                {
                    resultado = new CuvValida();
                    resultado.esValida = false;
                    resultado.MensajeValidacion = "CUV no válida para Aseguradora";
                }
                else
                {
                    if (resultado.claveEstatus != null && resultado.claveEstatus == 3)
                    {
                        resultado.esValida = false;
                        resultado.MensajeValidacion = "CUV no válida, la vivienda se encuentra individualizada";
                    }
                    else if (resultado.idEstatusPoliza != null && (resultado.idEstatusPoliza == 1 || resultado.idEstatusPoliza == 3))
                    {
                        resultado.esValida = false;
                        resultado.MensajeValidacion = "CUV no válida, la vivienda cuenta con una póliza de seguro vigente";
                    }
                    else
                    {
                        resultado.esValida = true;
                        resultado.MensajeValidacion = "Valida";
                    }
                }
            }

            return resultado;
        }

        public async Task<List<CuvSeleccionada>> ObtenerCuvsXOVAsync(string ordenVerificacion, string cuv)
        {
            return await this._seguroCalidadDM.ObtenerCuvsXOVAsync(ordenVerificacion, cuv);
        }

        public async Task<ResultadoPaginado<List<CuvSeleccionada>>> ObtenerCuvsXOVPaginadoAsync(int tamanioPagina, int pagina, string ordenVerificacion = null, string cuv = null)
        {
            ResultadoPaginado<List<CuvSeleccionada>> resultado = new ResultadoPaginado<List<CuvSeleccionada>>()
            {
                TamanioPagina = tamanioPagina,
                PaginaActual = pagina
            };

            var data = await this._seguroCalidadDM.ObtenerCuvsXOVPaginadoAsync(tamanioPagina, pagina, ordenVerificacion, cuv);

            if (data.Item1 > 0)
            {
                resultado.TotalRegistros = data.Item1;
                resultado.TotalPaginas = (int)Math.Ceiling((double)(resultado.TotalRegistros / tamanioPagina));
            }

            var cuvs = data.Item2;

            resultado.Resultado = cuvs;

            return resultado;
        }

        public async Task<List<OpcionCatalogoSeguroCalidad>> ObtenerCatalogoCoberturaAfectadaAsync()
        {
            return await this._seguroCalidadDM.ObtenerCatalogoCoberturaAfectadaAsync();
        }

        public async Task<List<OpcionCatalogoSeguroCalidad>> ObtenerCatalogoClasificacioRiesgoAsync()
        {
            return await this._seguroCalidadDM.ObtenerCatalogoClasificacioRiesgoAsync();
        }

        public async Task<bool> GuardarIncidenciaNotificacionAsync(Incidencia incidencia, int idEntidad)
        {
            List<int> idsDocumentosGuardados = new List<int>();
            int idDocumentoGuardado = 0;
            int idIncidenciaNotificacionGuardada = 0;
            bool resultado = false;
            var informacionOV = new InformacionOVNuevaincidencia();
            var aseguradora = new EmpresaDto();
            var verificador = new EmpresaDto();
            var desarrollador = new EmpresaDto();

            informacionOV = await this._seguroCalidadDM.ObtenerInformacionOVNuevaIncidenciaAsync(incidencia.OrdenVerificacion);

            using (var transaccionEmpresa = new TransactionScope(TransactionScopeOption.RequiresNew, this._opcionesTransaccion, TransactionScopeAsyncFlowOption.Enabled))
            {
                aseguradora = this._servicioEmpresaComun.ObtenerEmpresa(informacionOV.idAseguradoraIns);
                verificador = this._servicioEmpresaComun.ObtenerEmpresa(informacionOV.idVerificadorIns);
                desarrollador = this._servicioEmpresaComun.ObtenerEmpresa(informacionOV.idDesarrolladorIns);
                transaccionEmpresa.Complete();
            }

            using (var transaccion = new TransactionScope(TransactionScopeOption.RequiresNew, this._opcionesTransaccion, TransactionScopeAsyncFlowOption.Enabled))
            {
                incidencia.idEstatusIncidencia = (int)EnumEstatusIncidencia.Abierto;

                if (incidencia.IdTipoIncidenciaRUV == (int)EnumTipoIncidencia.Incidencia)
                {
                    //Guardar documentos en comun
                    foreach (var documento in incidencia.DocumentosIncidencia)
                    {
                        if (documento.EsNuevaCarga == null || documento.EsNuevaCarga == false)
                        {
                            idsDocumentosGuardados.Add(documento.IdDocumento);
                        }
                        else
                        {
                            idDocumentoGuardado = await this._seguroCalidadDM.GuardarDocumentoComunAsync(documento.UrlArchivo, documento.NombreArchivo, documento.idUsuarioCarga.Value, (int)EnumCatalogoDocumento.EvidenciaIncidencia);
                            idsDocumentosGuardados.Add(idDocumentoGuardado);
                        }
                    }
                }
                else if (incidencia.IdTipoIncidenciaRUV == (int)EnumTipoIncidencia.Notificacion)
                {
                    //Guardar documentos en comun
                    foreach (var documento in incidencia.DocumentosNotificacion)
                    {
                        idDocumentoGuardado = await this._seguroCalidadDM.GuardarDocumentoComunAsync(documento.UrlArchivo, documento.NombreArchivo, documento.idUsuarioCarga.Value, (int)EnumCatalogoDocumento.EvidenciaNotificacion);
                        idsDocumentosGuardados.Add(idDocumentoGuardado);
                    }
                }
                else if (incidencia.IdTipoIncidenciaRUV == (int)EnumTipoIncidencia.EvaluacionRiesgo)
                {
                    //Guardar documentos en comun
                    foreach (var documento in incidencia.DocumentosEvaluacion)
                    {
                        idDocumentoGuardado = await this._seguroCalidadDM.GuardarDocumentoComunAsync(documento.UrlArchivo, documento.NombreArchivo, documento.idUsuarioCarga.Value, (int)EnumCatalogoDocumento.EvidenciaNotificacion);
                        idsDocumentosGuardados.Add(idDocumentoGuardado);
                    }
                }

                var tipo = "";

                switch (incidencia.IdTipoIncidenciaRUV)
                {
                    case 1:
                        tipo = "incidencia";
                        break;
                    case 2:
                        tipo = "notificación";
                        break;
                    case 3:
                        tipo = "evaluación de riesgo";
                        break;
                }

                using (var transaccionEmpresa = new TransactionScope(TransactionScopeOption.RequiresNew, this._opcionesTransaccion, TransactionScopeAsyncFlowOption.Enabled))
                {
                    desarrollador = this._servicioEmpresaComun.ObtenerEmpresa(informacionOV.idDesarrolladorIns);
                    transaccionEmpresa.Complete();
                }

                // Guarda una notificacion por cada Cuv seleccionada
                foreach (var cuv in incidencia.ListaCuvsSeleccionadas)
                {
                    idIncidenciaNotificacionGuardada = await this._seguroCalidadDM.GuardarIncidenciaNotificacionAsync(incidencia, cuv);

                    cuv.ClaveIncidencia = idIncidenciaNotificacionGuardada.ToString();

                    //Guarda la relacion de los documentos cargados por cada incidencia
                    foreach (var documentoRelacion in idsDocumentosGuardados)
                    {
                        await this._seguroCalidadDM.GuardarDocumentoXIncidenciaAsync(documentoRelacion, idIncidenciaNotificacionGuardada, Convert.ToInt32(cuv.IdVVGeneral));
                    }
                    
                    if (desarrollador != null && desarrollador.idEmpresa != null)
                    {
                        using (var transaccionGenerales = new TransactionScope(TransactionScopeOption.RequiresNew, TransactionScopeAsyncFlowOption.Enabled))
                        {
                            var idTareaAgregada = await _servicioGenerales.AgregarTareaDto(new Tarea
                            {
                                IdEstatusTarea = (int)EstatusTareaEnum.Pendiente,
                                IdTipoTarea = (int)TipoTareaEnum.RegistrarIncidencia,
                                IdEmpresa = desarrollador.idEmpresa,
                                IdUsuarioCreaTarea = incidencia.IdUsuario,
                                Descripcion = $"Se registró una {tipo} con id {idIncidenciaNotificacionGuardada}",
                                FechaRegistro = AzureDateTime.Now,
                                Activo = true
                            });

                            var idTareaRelacion = await _servicioGenerales.AgregarIncidenciaXTarea(idIncidenciaNotificacionGuardada, idTareaAgregada);

                            transaccionGenerales.Complete();
                        }
                    }
                    if (incidencia.ListaCuvsSeleccionadas != null && incidencia.ListaCuvsSeleccionadas.Count == 1 )
                    {
                        using (var transaccionNotificaciones = new TransactionScope(TransactionScopeOption.RequiresNew, this._opcionesTransaccion, TransactionScopeAsyncFlowOption.Enabled))
                        {
                            if (idEntidad == (int)EnumEntidadEmpresa.Aseguradora)
                            {
                                //Verificador
                                await this.EnviarCorreoNuevaIncidenciaVerificadorAseguradora(idIncidenciaNotificacionGuardada, incidencia.OrdenVerificacion, aseguradora, verificador, tipo, true);

                                //Desarrollador
                                await this.EnviarCorreoNuevaIncidenciaDesarrollador(idIncidenciaNotificacionGuardada, incidencia.OrdenVerificacion, aseguradora, verificador, desarrollador, tipo, true);

                                //A el mismo
                                await this.EnviarCorreoNuevaIncidenciaAElMismo(idIncidenciaNotificacionGuardada, incidencia.OrdenVerificacion, aseguradora, verificador, desarrollador, tipo, true);
                            }
                            else if (idEntidad == (int)EnumEntidadEmpresa.Verificador)
                            {
                                //Aseguradora
                                await this.EnviarCorreoNuevaIncidenciaVerificadorAseguradora(idIncidenciaNotificacionGuardada, incidencia.OrdenVerificacion, aseguradora, verificador, tipo, false);

                                //Desarrollador
                                await this.EnviarCorreoNuevaIncidenciaDesarrollador(idIncidenciaNotificacionGuardada, incidencia.OrdenVerificacion, aseguradora, verificador, desarrollador, tipo, false);

                                //A el mismo
                                await this.EnviarCorreoNuevaIncidenciaAElMismo(idIncidenciaNotificacionGuardada, incidencia.OrdenVerificacion, aseguradora, verificador, desarrollador, tipo, false);
                            }

                            transaccionNotificaciones.Complete();
                        }
                    }
                }

                //Se registra la evaluacion de riesgo
                if (incidencia.IdTipoIncidenciaRUV == (int)EnumTipoIncidencia.EvaluacionRiesgo)
                {
                    await this.GuardarEvaluacionRiesgoAsync(new EvaluacionRiesgo() { ordenVerificacion = incidencia.OrdenVerificacion, CuvsSeleccionadas = incidencia.ListaCuvsSeleccionadas, TituloIncidencia = incidencia.TituloIncidencia,
                    FolioAseguradora = incidencia.FolioAseguradora, OpcionesMitigacion = incidencia.OpcionesMitigacion, Descripcion = incidencia.Descripcion, TieneRiesgo = true, IdUsuario = incidencia.IdUsuario});
                }

                var urlArchivo =  await this.GenerarDocumentoIncidenciaNotificacionCorreo(incidencia.ListaCuvsSeleccionadas, tipo);

                if(incidencia.ListaCuvsSeleccionadas != null && incidencia.ListaCuvsSeleccionadas.Count > 1) { 
                    using (var transaccionNotificaciones = new TransactionScope(TransactionScopeOption.RequiresNew, this._opcionesTransaccion, TransactionScopeAsyncFlowOption.Enabled))
                    {
                        if (idEntidad == (int)EnumEntidadEmpresa.Aseguradora)
                        {
                            //Verificador
                            await this.EnviarCorreoNuevaIncidenciaVerificadorAseguradora(idIncidenciaNotificacionGuardada, incidencia.OrdenVerificacion, aseguradora, verificador, tipo, true, urlArchivo);

                            //Desarrollador
                            await this.EnviarCorreoNuevaIncidenciaDesarrollador(idIncidenciaNotificacionGuardada, incidencia.OrdenVerificacion, aseguradora, verificador, desarrollador, tipo, true, urlArchivo);

                            //A el mismo
                            await this.EnviarCorreoNuevaIncidenciaAElMismo(idIncidenciaNotificacionGuardada, incidencia.OrdenVerificacion, aseguradora, verificador, desarrollador, tipo, true, urlArchivo);
                        }
                        else if (idEntidad == (int)EnumEntidadEmpresa.Verificador)
                        {
                            //Aseguradora
                            await this.EnviarCorreoNuevaIncidenciaVerificadorAseguradora(idIncidenciaNotificacionGuardada, incidencia.OrdenVerificacion, aseguradora, verificador, tipo, false, urlArchivo);

                            //Desarrollador
                            await this.EnviarCorreoNuevaIncidenciaDesarrollador(idIncidenciaNotificacionGuardada, incidencia.OrdenVerificacion, aseguradora, verificador, desarrollador, tipo, false, urlArchivo);

                            //A el mismo
                            await this.EnviarCorreoNuevaIncidenciaAElMismo(idIncidenciaNotificacionGuardada, incidencia.OrdenVerificacion, aseguradora, verificador, desarrollador, tipo, false, urlArchivo);
                        }

                        transaccionNotificaciones.Complete();
                    }
                }


                transaccion.Complete();

                resultado = true;
            }



            return resultado;
        }

        private async Task EnviarCorreoSancionAseguradora(Aseguradora aseguradora, Sancion sancion, int idEmpresa)
        {
            MensajeDto mensaje = null;
            List<string> correo = new List<string>();

            correo = this._servicioEmpresaComun.ObtenerCorreosContacto(idEmpresa).ToList();

            //correo.Add("<EMAIL>");

            var documentos = "";

            for (int i = 0; i <= sancion.documentos.Count - 1; i++)
            {
                documentos += "<li><a href='" + sancion.documentos[i].UrlArchivo + "'>Archivo " + (i + 1) + " </a></li>";
            }

            if (sancion.idTipoSancion == 1)
            {                              
                Dictionary<string, string> parametrosCorreo = new Dictionary<string, string>
                {
                    {"0", aseguradora.RazonSocial},
                    {"1", aseguradora.rfc},
                    {"2", aseguradora.noRegistroRUV},
                    {"3", sancion.fechaInicioSancion.Value.ToString("dd/MM/yyyy")},
                    {"4", sancion.fechaFinSancion.Value.ToString("dd/MM/yyyy")},
                    {"5", sancion.comentario},
                    {"6", documentos}
                };

                mensaje = new MensajeDto("Sanción",
                    correo,
                    eTipoCorreo.ContenidoHtmlMailNotifSancionTemp,
                    eTipoMensaje.InformativoComunicacion,
                    null,
                    parametrosCorreo);
            }
            else
            {
                Dictionary<string, string> parametrosCorreo = new Dictionary<string, string>
                {
                    {"0", aseguradora.RazonSocial},
                    {"1", aseguradora.rfc},
                    {"2", aseguradora.noRegistroRUV},
                    {"3", sancion.fechaInicioSancion.Value.ToString("dd/MM/yyyy")},                    
                    {"4", sancion.comentario},
                    {"5", documentos}
                };


                mensaje = new MensajeDto("Sanción",
                    correo,
                    eTipoCorreo.ContenidoHtmlMailNotifSancionDef,
                    eTipoMensaje.InformativoComunicacion,
                    null,
                    parametrosCorreo);
            }            

            _servicioNotificaciones.AgregarNotificacion(mensaje);
        }

        private async Task EnviarCorreoRevocarSancionAseguradora(Aseguradora aseguradora, Sancion sancion, int idEmpresa)
        {
            MensajeDto mensaje = null;
            List<string> correo = new List<string>();

            correo = this._servicioEmpresaComun.ObtenerCorreosContacto(idEmpresa).ToList();

            //correo.Add("<EMAIL>");

            var documentos = "";

            for (int i = 0; i <= sancion.documentos.Count - 1; i++)
            {
                documentos += "<li><a href='" + sancion.documentos[i].UrlArchivo + "'>Archivo " + (i + 1) + " </a></li>";
            }
            
            Dictionary<string, string> parametrosCorreo = new Dictionary<string, string>
            {
                {"0", aseguradora.RazonSocial},
                {"1", sancion.fechaActualizacionString},
                {"2", sancion.comentarioRevocacion},                
                {"3", documentos}
            };

            mensaje = new MensajeDto("Revocación de Sanción",
                correo,
                eTipoCorreo.ContenidoHtmlMailNotifRevocaSancion,
                eTipoMensaje.InformativoComunicacion,
                null,
                parametrosCorreo);
            

            _servicioNotificaciones.AgregarNotificacion(mensaje);
        }

        private async Task EnviarCorreoRelacionComercial(Aseguradora aseguradora, string idEmpresainst)
        {
            //Agregar notificacion, se debe revisar si es para meter al padron o para quitar            

            var empresa = _servicioEmpresaComun.ObtenerEmpresa(idEmpresainst);
            var aseguradoraDatos = _servicioEmpresaComun.ObtenerEmpresa(aseguradora.noRegistroRUV);

            MensajeDto mensaje = null;
            List<string> correoEmpresa = new List<string>();
            List<string> correoAseguradora = new List<string>();

            correoEmpresa = this._servicioEmpresaComun.ObtenerCorreosContacto(empresa.idEmpresa.Value).ToList();
            correoAseguradora = this._servicioEmpresaComun.ObtenerCorreosContacto(aseguradoraDatos.idEmpresa.Value).ToList();

            ///correoAseguradora.Add("<EMAIL>");
            //correoEmpresa.Add("<EMAIL>");

            if (aseguradora.relacionComercial.idTipoAsignacion == 1)
            {

                Dictionary<string, string> parametrosCorreo = new Dictionary<string, string>();

                if (aseguradora.relacionComercial.evaluacionRiesgo == 1)
                {
                    parametrosCorreo = new Dictionary<string, string>
                    {
                    {"0", aseguradora.RazonSocial},
                    {"1", String.Format("{0} {1} {2}", empresa.nombreRazonSocial, empresa.apellidoPaterno, empresa.apellidoMaterno)},
                    {"2", empresa.rfc},
                    {"3", empresa.IdEmpresaInst},
                    {"4", aseguradora.relacionComercial.costoRelacion},
                    {"5", ", así mismo ha solicitado un pago anticipado por Evaluación de Riesgo"}
                    };
                }
                else
                {
                    parametrosCorreo = new Dictionary<string, string>
                    {
                    {"0", aseguradora.RazonSocial},
                    {"1", String.Format("{0} {1} {2}", empresa.nombreRazonSocial, empresa.apellidoPaterno, empresa.apellidoMaterno)},
                    {"2", empresa.rfc},
                    {"3", empresa.IdEmpresaInst},
                    {"4", aseguradora.relacionComercial.costoRelacion},                    
                    };
                }
                              

                mensaje = new MensajeDto("Nueva relación comercial",
                correoAseguradora,
                eTipoCorreo.ContenidoHtmlMailNotifAsignAsegu,
                eTipoMensaje.InformativoComunicacion,
                null,
                parametrosCorreo);
            }
            else
            {
                Dictionary<string, string> parametrosCorreo = new Dictionary<string, string>
                {
                    {"0", aseguradora.RazonSocial},
                    {"1", aseguradora.rfc},
                    {"2", aseguradora.noRegistroRUV},
                    {"3", String.Format("{0} {1} {2}", empresa.nombreRazonSocial, empresa.apellidoPaterno, empresa.apellidoMaterno)},                    
                };                

                mensaje = new MensajeDto("Nueva relación comercial",
                correoAseguradora,
                eTipoCorreo.ContenidoHtmlMailNotifDesarAsign,
                eTipoMensaje.InformativoComunicacion,
                null,
                parametrosCorreo);
            }

            _servicioNotificaciones.AgregarNotificacion(mensaje);

            if (aseguradora.relacionComercial.idTipoAsignacion == 1)
            {
                //Aqui se envia correo al desarrollador
                Dictionary<string, string> parametrosCorreoDes = new Dictionary<string, string>
                {
                    {"0", String.Format("{0} {1} {2}", empresa.nombreRazonSocial, empresa.apellidoPaterno, empresa.apellidoMaterno)},
                    {"1", aseguradora.RazonSocial},
                    {"2", aseguradora.rfc},
                    {"3", aseguradora.noRegistroRUV},
                    {"4", aseguradora.RazonSocial },
                    {"5", aseguradora.domicilio},
                    {"6", aseguradora.email},
                    {"7", aseguradora.telefono},
                };

                mensaje = new MensajeDto("Nueva relación comercial",
                correoEmpresa,
                eTipoCorreo.ContenidoHtmlMailNotifAsignEleccDesar,
                eTipoMensaje.InformativoComunicacion,
                null,
                parametrosCorreoDes);
            }
            else
            {
                //Aqui se envia correo al desarrollador
                Dictionary<string, string> parametrosCorreoDes = new Dictionary<string, string>
                {
                    {"0", String.Format("{0} {1} {2}", empresa.nombreRazonSocial, empresa.apellidoPaterno, empresa.apellidoMaterno)},
                    {"1", aseguradora.RazonSocial},
                    {"2", aseguradora.rfc},
                    {"3", aseguradora.noRegistroRUV},           
                    {"4", aseguradora.domicilio},
                    {"5", aseguradora.email},
                    {"6", aseguradora.telefono},
                };

                mensaje = new MensajeDto("Nueva relación comercial",
                correoEmpresa,
                eTipoCorreo.ContenidoHtmlMailNotifDesarEstru,
                eTipoMensaje.InformativoComunicacion,
                null,
                parametrosCorreoDes);
            }

                

            _servicioNotificaciones.AgregarNotificacion(mensaje);
            
        }

        private async Task EnviarCorreoPadronAseguradoras(List<int> listaAseguradoras, int enPadronAseguradora)
        {
            //Agregar notificacion, se debe revisar si es para meter al padron o para quitar
            //Primero for para obtener la informacion de la aseguradora completa
            foreach (int aseguradora in listaAseguradoras)
            {
                Aseguradora aseguradoraCompleta = await ObtenerAseguradoraCompletaAsync(aseguradora);
               
                var idEmpresa = _servicioEmpresaComun.ObtenerEmpresa(aseguradoraCompleta.noRegistroRUV)?.idEmpresa;

                MensajeDto mensaje = null;
                List<string> correo = new List<string>();

                correo = this._servicioEmpresaComun.ObtenerCorreosContacto(idEmpresa.Value).ToList();

                //correo.Add("<EMAIL>");

                Dictionary<string, string> parametrosCorreo = new Dictionary<string, string>
                    {
                        {"0", String.Format("{0}", aseguradoraCompleta.RazonSocial)}
                    };

                //correo.Add("<EMAIL>");

                if (enPadronAseguradora == 0)
                {
                    mensaje = new MensajeDto("Baja de aseguradora del padrón",
                    correo,
                    eTipoCorreo.ContenidoHtmlMailNotifBajaPadro,
                    eTipoMensaje.InformativoComunicacion,
                    null,
                    parametrosCorreo);
                }
                else
                {
                    mensaje = new MensajeDto("Alta de aseguradora en padrón",
                        correo,
                        eTipoCorreo.ContenidoHtmlMailNotifAltaPadro,
                        eTipoMensaje.InformativoComunicacion,
                        null,
                        parametrosCorreo);
                }

                _servicioNotificaciones.AgregarNotificacion(mensaje);
                                  
            }
        }

        private async Task EnviarCorreoSolicitudPoliza(List<CuvPoliza> listaCuvs, Aseguradora aseguradoraCompleta, DesarrolladorRelacion desarrollador)
        {            
            var idAseguradora = _servicioEmpresaComun.ObtenerEmpresa(aseguradoraCompleta.noRegistroRUV)?.idEmpresa;

            var idEmpresa = _servicioEmpresaComun.ObtenerEmpresa(desarrollador.noRegistroRUV)?.idEmpresa;

            MensajeDto mensaje = null;
            List<string> correo = new List<string>();

            correo = this._servicioEmpresaComun.ObtenerCorreosContacto(idEmpresa.Value).ToList();            

            Dictionary<string, string> parametrosCorreo = new Dictionary<string, string>
                    {
                        {"0", desarrollador.RazonSocial},
                        {"1", DateTime.Now.ToString("dd/MM/yyyy")},
                        {"2", listaCuvs.FirstOrDefault().ordenVerificacion},
                        {"3", "$ " + listaCuvs.Sum(l => l.montoPolizaCuv).ToString()}
                    };

            //correo.Add("<EMAIL>");

            //Documento

            MemoryStream adjuntoPorCorreo;

            List<CuvPolizaReporte> listaCuvsReporte = new List<CuvPolizaReporte>();

            foreach (var cuv in listaCuvs)
            {
                listaCuvsReporte.Add(new CuvPolizaReporte { cuv = cuv.cuv, direccion =  cuv.direccion, montoPolizaCuv = cuv.montoPolizaCuv.Value, total = listaCuvs.Sum(l => l.montoPolizaCuv).Value });
            }

            MemoryStream adjunto = this._servicioReportes.GenerarReporteCuvsSolicitudPolizaPdf(listaCuvsReporte);

            mensaje = new MensajeDto("Solicitud de póliza",
            correo,
            eTipoCorreo.ContenidoHtmlMailNotifSolicitaPoliza,
            eTipoMensaje.InformativoComunicacion,
            null,
            parametrosCorreo);

            adjunto.Position = 0;
            adjuntoPorCorreo = new MemoryStream();
            await adjunto.CopyToAsync(adjuntoPorCorreo);
            adjuntoPorCorreo.Position = 0;
            string urlAdjunto = this._servicioNotificaciones.CargarBlob(adjuntoPorCorreo, "SolicitudPoliza.pdf", false);
            mensaje.AttachmentBlobName = "SolicitudPoliza.pdf";

            _servicioNotificaciones.AgregarNotificacion(mensaje);
        }

        private async Task EnviarCorreoBajaPadronAseguradoras(List<RelacionesComercialesOferente> listaRL)
        {
            //Agregar notificacion, se debe revisar si es para meter al padron o para quitar
            //Primero for para obtener la informacion de la aseguradora completa
            foreach (RelacionesComercialesOferente RL in listaRL)
            {
                Aseguradora aseguradoraCompleta = await ObtenerAseguradoraCompletaAsync(Convert.ToInt32(RL.idAseguradora));

                DesarrolladorRelacion desarrollador = await ObtenerOferenteAsync(RL.idOferente);

                var idAseguradora = _servicioEmpresaComun.ObtenerEmpresa(aseguradoraCompleta.noRegistroRUV)?.idEmpresa;

                var idEmpresa = _servicioEmpresaComun.ObtenerEmpresa(desarrollador.noRegistroRUV)?.idEmpresa;

                MensajeDto mensaje = null;
                List<string> correo = new List<string>();

                correo = this._servicioEmpresaComun.ObtenerCorreosContacto(idEmpresa.Value).ToList();

                //correo.Add("<EMAIL>");

                Dictionary<string, string> parametrosCorreo = new Dictionary<string, string>
                    {
                        {"0", String.Format("{0}", desarrollador.RazonSocial)},                        
                        {"1", aseguradoraCompleta.RazonSocial},
                        {"2", aseguradoraCompleta.rfc},
                        {"3", aseguradoraCompleta.noRegistroRUV}                       
                    };

                //correo.Add("<EMAIL>");
                
                mensaje = new MensajeDto("Baja de aseguradora del padrón",
                correo,
                eTipoCorreo.ContenidoHtmlMailNotifBajaDesar,
                eTipoMensaje.InformativoComunicacion,
                null,
                parametrosCorreo);
                

                _servicioNotificaciones.AgregarNotificacion(mensaje);

            }
        }

        private async Task EnviarCorreoValidacionPago(RelacionComercial relacionComercial, Aseguradora aseguradoraCompleta, bool esAceptacion)
        {
            MensajeDto mensaje = null;
            List<string> correo = new List<string>();

            var empresa = this._servicioEmpresaComun.ObtenerEmpresa(relacionComercial.idEmpresaInstMasMas);                   

            correo = this._servicioEmpresaComun.ObtenerCorreosContacto(empresa.idEmpresa.Value).ToList();

            //correo.Add("<EMAIL>");

            if (esAceptacion)
            {
                Dictionary<string, string> parametrosCorreo = new Dictionary<string, string>
                {
                    {"0", String.Format("{0} {1} {2}", empresa.nombreRazonSocial, empresa.apellidoPaterno, empresa.apellidoMaterno)},
                    {"1", aseguradoraCompleta.RazonSocial},
                    {"2", aseguradoraCompleta.rfc},
                    {"3", aseguradoraCompleta.noRegistroRUV},
                    {"4", relacionComercial.costoRelacion},
                    {"5", relacionComercial.fechaRegistro.Value.ToString("dd/MM/yyyy")}
                };

                mensaje = new MensajeDto("Aceptación costo póliza para relación comercial",
                correo,
                eTipoCorreo.ContenidoHtmlMailNotifAceptCosto,
                eTipoMensaje.InformativoComunicacion,
                null,
                parametrosCorreo);
            }
            else
            {
                Dictionary<string, string> parametrosCorreo = new Dictionary<string, string>
                {
                    {"0", String.Format("{0} {1} {2}", empresa.nombreRazonSocial, empresa.apellidoPaterno, empresa.apellidoMaterno)},
                    {"1", aseguradoraCompleta.RazonSocial},
                    {"2", aseguradoraCompleta.rfc},
                    {"3", aseguradoraCompleta.noRegistroRUV},
                    {"4", relacionComercial.fechaRechazo.Value.ToString("dd/MM/yyyy")},                    
                };

                mensaje = new MensajeDto("Rechazo costo póliza para relación comercial",
                correo,
                eTipoCorreo.ContenidoHtmlMailEstruNotifRecha,
                eTipoMensaje.InformativoComunicacion,
                null,
                parametrosCorreo);
            }            

            _servicioNotificaciones.AgregarNotificacion(mensaje);
        }

        private async Task EnviarCorreoValidacionEvaluacion(RelacionComercial relacionComercial, Aseguradora aseguradoraCompleta, bool esAceptacion)
        {
            MensajeDto mensaje = null;
            List<string> correo = new List<string>();

            var empresa = this._servicioEmpresaComun.ObtenerEmpresa(relacionComercial.idEmpresaInstMasMas);

            correo = this._servicioEmpresaComun.ObtenerCorreosContacto(empresa.idEmpresa.Value).ToList();

            //correo.Add("<EMAIL>");

            if (esAceptacion)
            {
                Dictionary<string, string> parametrosCorreo = new Dictionary<string, string>
                {
                    {"0", String.Format("{0} {1} {2}", empresa.nombreRazonSocial, empresa.apellidoPaterno, empresa.apellidoMaterno)},
                    {"1", aseguradoraCompleta.RazonSocial},
                    {"2", aseguradoraCompleta.rfc},
                    {"3", aseguradoraCompleta.noRegistroRUV}
                };

                mensaje = new MensajeDto("Aceptación evaluación de riesgo",
                correo,
                eTipoCorreo.ContenidoHtmlMailNotifAceptEvaluRiesg,
                eTipoMensaje.InformativoComunicacion,
                null,
                parametrosCorreo);
            }
            else
            {
                Dictionary<string, string> parametrosCorreo = new Dictionary<string, string>
                {
                    {"0", String.Format("{0} {1} {2}", empresa.nombreRazonSocial, empresa.apellidoPaterno, empresa.apellidoMaterno)},
                    {"1", aseguradoraCompleta.RazonSocial},
                    {"2", aseguradoraCompleta.rfc},
                    {"3", aseguradoraCompleta.noRegistroRUV},
                    {"4", relacionComercial.fechaRechazoevaluacion.Value.ToString("dd/MM/yyyy")},
                };

                mensaje = new MensajeDto("Rechazo evaluación de riesgo",
                correo,
                eTipoCorreo.ContenidoHtmlMailNotifRechaEvaluRiesg,
                eTipoMensaje.InformativoComunicacion,
                null,
                parametrosCorreo);
            }

            _servicioNotificaciones.AgregarNotificacion(mensaje);
        }

        /// <summary>
        /// Envia una notificacion al verificador indicando el registro de una nueva incidencia
        /// </summary>
        /// <param name="idIncidencia"></param>
        /// <param name="ordenVerificacion"></param>
        /// <param name="aseguradora"></param>
        /// <param name="verificador"></param>
        /// <returns></returns>
        private async Task EnviarCorreoNuevaIncidenciaVerificadorAseguradora(int idIncidencia, string ordenVerificacion, EmpresaDto aseguradora, EmpresaDto verificador, string tipo, bool esAseguradora, string urlArchivo = null)
        {
            MensajeDto mensaje = null;
            List<string> correo = new List<string>();
            Dictionary<string, string> parametrosCorreo = new Dictionary<string, string>();
            
            if (esAseguradora)
            {
                correo = this._servicioEmpresaComun.ObtenerCorreosContacto(verificador.idEmpresa.Value).ToList();

                if (urlArchivo == null)
                {
                    parametrosCorreo = new Dictionary<string, string>
                    {
                        {"0", String.Format("{0} {1} {2}", verificador.nombreRazonSocial, verificador.apellidoPaterno, verificador.apellidoMaterno)},
                        {"1", "la aseguradora" },
                        {"2", String.Format("{0} {1} {2}", aseguradora.nombreRazonSocial, aseguradora.apellidoPaterno, aseguradora.apellidoMaterno)},
                        {"3", aseguradora.rfc},
                        {"4", aseguradora.IdEmpresaInst},
                        {"5", tipo},
                        {"6", idIncidencia.ToString()},
                        {"7", ordenVerificacion}
                    };
                }else
                {
                    parametrosCorreo = new Dictionary<string, string>
                    {
                        {"0", String.Format("{0} {1} {2}", verificador.nombreRazonSocial, verificador.apellidoPaterno, verificador.apellidoMaterno)},
                        {"1", "la aseguradora" },
                        {"2", String.Format("{0} {1} {2}", aseguradora.nombreRazonSocial, aseguradora.apellidoPaterno, aseguradora.apellidoMaterno)},
                        {"3", aseguradora.rfc},
                        {"4", aseguradora.IdEmpresaInst},
                        {"5", tipo},
                        {"6", ordenVerificacion}
                    };
                } 
            }
            else
            {
                correo = this._servicioEmpresaComun.ObtenerCorreosContacto(aseguradora.idEmpresa.Value).ToList();

                if (urlArchivo == null)
                {
                    parametrosCorreo = new Dictionary<string, string>
                    {
                        {"0", String.Format("{0} {1} {2}", aseguradora.nombreRazonSocial, aseguradora.apellidoPaterno, aseguradora.apellidoMaterno)},
                        {"1", "el verificador" },
                        {"2", String.Format("{0} {1} {2}", verificador.nombreRazonSocial, verificador.apellidoPaterno, verificador.apellidoMaterno)},
                        {"3", verificador.rfc},
                        {"4", verificador.IdEmpresaInst},
                        {"5", tipo},
                        {"6", idIncidencia.ToString()},
                        {"7", ordenVerificacion}
                    };
                }else
                {
                    parametrosCorreo = new Dictionary<string, string>
                    {
                        {"0", String.Format("{0} {1} {2}", aseguradora.nombreRazonSocial, aseguradora.apellidoPaterno, aseguradora.apellidoMaterno)},
                        {"1", "el verificador" },
                        {"2", String.Format("{0} {1} {2}", verificador.nombreRazonSocial, verificador.apellidoPaterno, verificador.apellidoMaterno)},
                        {"3", verificador.rfc},
                        {"4", verificador.IdEmpresaInst},
                        {"5", tipo},
                        {"6", ordenVerificacion}
                    };
                }
                    
            }

            //correo.Add("<EMAIL>");
            
            if(urlArchivo == null) { 
                mensaje = new MensajeDto("Registro de " + (tipo),
                correo,
                eTipoCorreo.ContenidoHtmlMailNotifIncidVerif,
                eTipoMensaje.InformativoComunicacion,
                null,
                parametrosCorreo);
                
            }else
            {
                mensaje = new MensajeDto("Registro de " + (tipo),
                correo,
                eTipoCorreo.ContenidoHtmlMailNotifIncidVerifMasivo,
                eTipoMensaje.InformativoComunicacion,
                null,
                parametrosCorreo,
                urlArchivo);
            }
            _servicioNotificaciones.AgregarNotificacion(mensaje);
        }

        /// <summary>
        /// Envia una notificacion a la aseguradora indicando el registro de una nueva incidencia
        /// </summary>
        /// <param name="idIncidencia"></param>
        /// <param name="ordenVerificacion"></param>
        /// <param name="aseguradora"></param>
        /// <param name="desarrollador"></param>
        /// <returns></returns>
        private async Task EnviarCorreoNuevaIncidenciaAElMismo(int idIncidencia, string ordenVerificacion, EmpresaDto aseguradora, EmpresaDto verificador, EmpresaDto desarrollador, string tipo, bool esAseguradora, string rutaArchivo = null)
        {
            MensajeDto mensaje = null;
            List<string> correo = new List<string>();
            Dictionary<string, string> parametrosCorreo = new Dictionary<string, string>();

            if (esAseguradora)
            {
                correo = this._servicioEmpresaComun.ObtenerCorreosContacto(aseguradora.idEmpresa.Value).ToList();
                if (rutaArchivo == null)
                {
                    parametrosCorreo = new Dictionary<string, string>
                    {
                        {"0", String.Format("{0} {1} {2}", aseguradora.nombreRazonSocial, aseguradora.apellidoPaterno, aseguradora.apellidoMaterno)},
                        {"1", tipo},
                        {"2", idIncidencia.ToString()},
                        {"3", ordenVerificacion},
                        {"4", String.Format("{0} {1} {2}", desarrollador.nombreRazonSocial, desarrollador.apellidoPaterno, desarrollador.apellidoMaterno)},
                        {"5", desarrollador.rfc},
                        {"6", desarrollador.IdEmpresaInst}
                    };
                }
                else {
                    parametrosCorreo = new Dictionary<string, string>
                    {
                        {"0", String.Format("{0} {1} {2}", aseguradora.nombreRazonSocial, aseguradora.apellidoPaterno, aseguradora.apellidoMaterno)},
                        {"1", tipo},
                        {"2", ordenVerificacion},
                        {"3", String.Format("{0} {1} {2}", desarrollador.nombreRazonSocial, desarrollador.apellidoPaterno, desarrollador.apellidoMaterno)},
                        {"4", desarrollador.rfc},
                        {"5", desarrollador.IdEmpresaInst}
                    };
                }
                    
            }
            else
            {
                correo = this._servicioEmpresaComun.ObtenerCorreosContacto(verificador.idEmpresa.Value).ToList();
                if (rutaArchivo == null)
                {
                    parametrosCorreo = new Dictionary<string, string>
                    {
                        {"0", String.Format("{0} {1} {2}", verificador.nombreRazonSocial, verificador.apellidoPaterno, verificador.apellidoMaterno)},
                        {"1", tipo},
                        {"2", idIncidencia.ToString()},
                        {"3", ordenVerificacion},
                        {"4", String.Format("{0} {1} {2}", desarrollador.nombreRazonSocial, desarrollador.apellidoPaterno, desarrollador.apellidoMaterno)},
                        {"5", desarrollador.rfc},
                        {"6", desarrollador.IdEmpresaInst}
                    };
                }else
                {
                    parametrosCorreo = new Dictionary<string, string>
                    {
                        {"0", String.Format("{0} {1} {2}", verificador.nombreRazonSocial, verificador.apellidoPaterno, verificador.apellidoMaterno)},
                        {"1", tipo},
                        {"2", ordenVerificacion},
                        {"3", String.Format("{0} {1} {2}", desarrollador.nombreRazonSocial, desarrollador.apellidoPaterno, desarrollador.apellidoMaterno)},
                        {"4", desarrollador.rfc},
                        {"5", desarrollador.IdEmpresaInst}
                    };
                }
                    
            }

            //correo.Add("<EMAIL>");

            if (rutaArchivo == null)
            {
                
                mensaje = new MensajeDto("Registro de " + (tipo),
                correo,
                eTipoCorreo.ContenidoHtmlMailNotifIncidAsegu,
                eTipoMensaje.InformativoComunicacion,
                null,
                parametrosCorreo);

                _servicioNotificaciones.AgregarNotificacion(mensaje);
            }
            else
            {
                mensaje = new MensajeDto("Registro de " + (tipo),
                    correo,
                    eTipoCorreo.ContenidoHtmlMailNotifIncidAseguMasivo,
                    eTipoMensaje.InformativoComunicacion,
                    null,
                    parametrosCorreo,
                    rutaArchivo);

                _servicioNotificaciones.AgregarNotificacion(mensaje);
            }
        }

        /// <summary>
        /// Envia una notificacion al desarrollador indicando el registro de una nueva incidencia
        /// </summary>
        /// <param name="idIncidencia"></param>
        /// <param name="ordenVerificacion"></param>
        /// <param name="aseguradora"></param>
        /// <param name="desarrollador"></param>
        /// <returns></returns>
        private async Task EnviarCorreoNuevaIncidenciaDesarrollador(int idIncidencia, string ordenVerificacion, EmpresaDto aseguradora, EmpresaDto verificador, EmpresaDto desarrollador, string tipo, bool esAseguradora, string rutaArchivo = null)
        {
            MensajeDto mensaje = null;
            List<string> correo = new List<string>();
            Dictionary<string, string> parametrosCorreo = new Dictionary<string, string>();

            correo = this._servicioEmpresaComun.ObtenerCorreosContacto(desarrollador.idEmpresa.Value).ToList();

            if (esAseguradora)
            {
                if (rutaArchivo == null)
                {
                    parametrosCorreo = new Dictionary<string, string>
                    {
                        {"0", String.Format("{0} {1} {2}", desarrollador.nombreRazonSocial, desarrollador.apellidoPaterno, desarrollador.apellidoMaterno)},
                        {"1", "la aseguradora" },
                        {"2", String.Format("{0} {1} {2}", aseguradora.nombreRazonSocial, aseguradora.apellidoPaterno, aseguradora.apellidoMaterno)},
                        {"3", aseguradora.rfc},
                        {"4", aseguradora.IdEmpresaInst},
                        {"5", tipo},
                        {"6", idIncidencia.ToString()},
                        {"7", ordenVerificacion}
                    };
                }else
                {
                    parametrosCorreo = new Dictionary<string, string>
                    {
                        {"0", String.Format("{0} {1} {2}", desarrollador.nombreRazonSocial, desarrollador.apellidoPaterno, desarrollador.apellidoMaterno)},
                        {"1", "la aseguradora" },
                        {"2", String.Format("{0} {1} {2}", aseguradora.nombreRazonSocial, aseguradora.apellidoPaterno, aseguradora.apellidoMaterno)},
                        {"3", aseguradora.rfc},
                        {"4", aseguradora.IdEmpresaInst},
                        {"5", tipo},
                        {"6", ordenVerificacion}
                    };
                }
                    
            }
            else
            {
                if (rutaArchivo == null)
                {
                    parametrosCorreo = new Dictionary<string, string>
                    {
                        {"0", String.Format("{0} {1} {2}", desarrollador.nombreRazonSocial, desarrollador.apellidoPaterno, desarrollador.apellidoMaterno)},
                        {"1", "el verificador" },
                        {"2", String.Format("{0} {1} {2}", verificador.nombreRazonSocial, verificador.apellidoPaterno, verificador.apellidoMaterno)},
                        {"3", verificador.rfc},
                        {"4", verificador.IdEmpresaInst},
                        {"5", tipo},
                        {"6", idIncidencia.ToString()},
                        {"7", ordenVerificacion}
                    };
                }else
                {
                    parametrosCorreo = new Dictionary<string, string>
                    {
                        {"0", String.Format("{0} {1} {2}", desarrollador.nombreRazonSocial, desarrollador.apellidoPaterno, desarrollador.apellidoMaterno)},
                        {"1", "el verificador" },
                        {"2", String.Format("{0} {1} {2}", verificador.nombreRazonSocial, verificador.apellidoPaterno, verificador.apellidoMaterno)},
                        {"3", verificador.rfc},
                        {"4", verificador.IdEmpresaInst},
                        {"5", tipo},
                        {"6", ordenVerificacion}
                    };
                }
                    
            }

            //correo.Add("<EMAIL>");

            if (rutaArchivo == null)
            {
                mensaje = new MensajeDto("Registro de " + (tipo),
                correo,
                eTipoCorreo.ContenidoHtmlMailNotifIncidDesar,
                eTipoMensaje.InformativoComunicacion,
                null,
                parametrosCorreo);

                _servicioNotificaciones.AgregarNotificacion(mensaje);
            }else
            {
                mensaje = new MensajeDto("Registro de " + (tipo),
                correo,
                eTipoCorreo.ContenidoHtmlMailNotifIncidDesarMasivo,
                eTipoMensaje.InformativoComunicacion,
                null,
                parametrosCorreo,
                rutaArchivo);

                _servicioNotificaciones.AgregarNotificacion(mensaje);
            }
            
        }

        /// <summary>
        /// Envia una notificacion al desarrollador indicando el cierre de una incidencia
        /// </summary>
        /// <param name="idIncidencia"></param>
        /// <param name="ordenVerificacion"></param>
        /// <param name="aseguradora"></param>
        /// <param name="desarrollador"></param>
        /// <param name="idEntidad"></param>
        /// <returns></returns>
        private async Task EnviarCorreoCierreIncidenciaDesarrollador(int idIncidencia, string ordenVerificacion, EmpresaDto aseguradora, EmpresaDto desarrollador, int idEntidad)
        {
            MensajeDto mensaje = null;
            List<string> correo = new List<string>();

            correo = this._servicioEmpresaComun.ObtenerCorreosContacto(desarrollador.idEmpresa.Value).ToList();

            Dictionary<string, string> parametrosCorreo = new Dictionary<string, string>
                {
                    {"0", String.Format("{0} {1} {2}", desarrollador.nombreRazonSocial, desarrollador.apellidoPaterno, desarrollador.apellidoMaterno)},
                    {"1", idEntidad == 13 ? "Aseguradora" : "Verificadora"},
                    {"2", String.Format("{0} {1} {2}", aseguradora.nombreRazonSocial, aseguradora.apellidoPaterno, aseguradora.apellidoMaterno)},
                    {"3", aseguradora.rfc},
                    {"4", aseguradora.IdEmpresaInst},
                    {"5", idIncidencia.ToString()},
                    {"6", ordenVerificacion}
                };
            

            mensaje = new MensajeDto("Cierre de incidencia",
                correo,
                eTipoCorreo.ContenidoHtmlMailNotifCierrDesar,
                eTipoMensaje.InformativoComunicacion,
                null,
                parametrosCorreo);

            _servicioNotificaciones.AgregarNotificacion(mensaje);
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="ordenVerificacion"></param>
        /// <param name="verificador"></param>
        /// <param name="desarrollador"></param>
        /// <param name="idIncidencia"></param>
        /// <returns></returns>
        private async Task EnviarCorreoMitigacionVerificador(string ordenVerificacion, EmpresaDto verificador, EmpresaDto desarrollador, int idIncidencia)
        {
            MensajeDto mensaje = null;
            List<string> correo = new List<string>();

            correo = this._servicioEmpresaComun.ObtenerCorreosContacto(verificador.idEmpresa.Value).ToList();

            Dictionary<string, string> parametrosCorreo = new Dictionary<string, string>
                {
                    {"0", String.Format("{0} {1} {2}", verificador.nombreRazonSocial, verificador.apellidoPaterno, verificador.apellidoMaterno)},
                    {"1", String.Format("{0} {1} {2}", desarrollador.nombreRazonSocial, desarrollador.apellidoPaterno, desarrollador.apellidoMaterno)},
                    {"2", desarrollador.rfc},
                    {"3", desarrollador.IdEmpresaInst},
                    {"4", idIncidencia.ToString()},
                    {"5", ordenVerificacion}
                };


            mensaje = new MensajeDto("Mitigación de notificación",
                correo,
                eTipoCorreo.ContenidoHtmlMailNotiMitigarVerif,
                eTipoMensaje.InformativoComunicacion,
                null,
                parametrosCorreo);

            _servicioNotificaciones.AgregarNotificacion(mensaje);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="ordenVerificacion"></param>
        /// <param name="aseguradora"></param>
        /// <param name="desarrollador"></param>
        /// <param name="idIncidencia"></param>
        /// <param name="tipoIncidencia"></param>
        /// <returns></returns>
        private async Task EnviarCorreoMitigacionAseguradora(string ordenVerificacion, EmpresaDto aseguradora, EmpresaDto desarrollador, int idIncidencia, string tipoIncidencia)
        {
            MensajeDto mensaje = null;
            List<string> correo = new List<string>();

            correo = this._servicioEmpresaComun.ObtenerCorreosContacto(aseguradora.idEmpresa.Value).ToList();

            Dictionary<string, string> parametrosCorreo = new Dictionary<string, string>
                {
                    {"0", String.Format("{0} {1} {2}", aseguradora.nombreRazonSocial, aseguradora.apellidoPaterno, aseguradora.apellidoMaterno)},
                    {"1", String.Format("{0} {1} {2}", desarrollador.nombreRazonSocial, desarrollador.apellidoPaterno, desarrollador.apellidoMaterno)},
                    {"2", desarrollador.rfc},
                    {"3", desarrollador.IdEmpresaInst},
                    {"4", tipoIncidencia},
                    {"5", idIncidencia.ToString()},
                    {"6", ordenVerificacion},
                    {"7", tipoIncidencia}
                };


            mensaje = new MensajeDto($"Mitigación de {tipoIncidencia}",
                correo,
                eTipoCorreo.ContenidoHtmlNotiMitigarAsegu,
                eTipoMensaje.InformativoComunicacion,
                null,
                parametrosCorreo);

            _servicioNotificaciones.AgregarNotificacion(mensaje);
        }

        /// <summary>
        /// Envia una notificacion al desarrollador indicando el rechazo de una incidencia
        /// </summary>
        /// <param name="idIncidencia"></param>
        /// <param name="ordenVerificacion"></param>
        /// <param name="aseguradoraVerificador"></param>
        /// <param name="desarrollador"></param>
        /// <returns></returns>
        private async Task EnviarCorreoRechazoIncidenciaDesarrollador(int idIncidencia, string ordenVerificacion, EmpresaDto aseguradoraVerificador, EmpresaDto desarrollador)
        {
            MensajeDto mensaje = null;
            List<string> correo = new List<string>();

            correo = this._servicioEmpresaComun.ObtenerCorreosContacto(desarrollador.idEmpresa.Value).ToList();

            string rfc = "";

            Dictionary<string, string> parametrosCorreo = new Dictionary<string, string>
                {
             
                    {"0", String.Format("{0} {1} {2}", desarrollador.nombreRazonSocial, desarrollador.apellidoPaterno, desarrollador.apellidoMaterno)},
                    {"1", aseguradoraVerificador.idEntidad == 13 ? "la Aseguradora": " el Verificador" },
                    {"2", String.Format("{0} {1} {2}", aseguradoraVerificador.nombreRazonSocial, aseguradoraVerificador.apellidoPaterno, aseguradoraVerificador.apellidoMaterno)},
                    {"3", aseguradoraVerificador.idEntidad == 13 ? desarrollador.rfc : aseguradoraVerificador.rfc  },
                    {"4", aseguradoraVerificador.idEntidad == 13 ? desarrollador.IdEmpresaInst : aseguradoraVerificador.IdEmpresaInst },
                    {"5", idIncidencia.ToString()},
                    {"6", ordenVerificacion}
                };
            
            mensaje = new MensajeDto("Rechazo de mitigación",
                correo,
                eTipoCorreo.ContenidoHtmlMailNotifRechaDesar,
                eTipoMensaje.InformativoComunicacion,
                null,
                parametrosCorreo);

            _servicioNotificaciones.AgregarNotificacion(mensaje);
        }

        public async Task<InformacionPreviaNotificacion> ObtenerInformacionPreviaNotificacionAsync(int idIncidencia, int idVvGeneral)
        {
            InformacionPreviaNotificacion previa = new InformacionPreviaNotificacion();

            var incidencia = await this._seguroCalidadDM.ObtenerIncidenciaNotificacionAsync(idIncidencia);

            var documentos = await this._seguroCalidadDM.ObtenerDocumentosIncidenciaNotificacionAsync(idIncidencia, idVvGeneral);

            previa.descripcion = incidencia.Descripcion;
            previa.documentos = documentos;

            return previa;
        }

        public async Task<List<CuvSeleccionada>> ObtenerCuvsXOVEvaluacionRiesgosAsync(string ordenVerificacion, string cuv)
        {
            return await this._seguroCalidadDM.ObtenerCuvsXOVEvaluacionRiesgosAsync(ordenVerificacion, cuv);
        }

        public async Task<ResultadoPaginado<List<CuvSeleccionada>>> ObtenerCuvsXOVEvaluacionRiesgosPaginadoAsync(int tamanioPagina, int pagina, string ordenVerificacion = null, string cuv = null)
        {
            ResultadoPaginado<List<CuvSeleccionada>> resultado = new ResultadoPaginado<List<CuvSeleccionada>>()
            {
                TamanioPagina = tamanioPagina,
                PaginaActual = pagina
            };

            var data = await this._seguroCalidadDM.ObtenerCuvsXOVEvaluacionRiesgosPaginadoAsync(tamanioPagina, pagina, ordenVerificacion, cuv);

            if (data.Item1 > 0)
            {
                resultado.TotalRegistros = data.Item1;
                resultado.TotalPaginas = (int)Math.Ceiling((double)(resultado.TotalRegistros / tamanioPagina));
            }

            var cuvs = data.Item2;

            resultado.Resultado = cuvs;

            return resultado;
        }

        public async Task<CuvValida> ValidarCuvValidaEvaluacionRiesgosAsync(string cuv, int? idEntidad, int? idEmpresa)
        {
            CuvValida resultado = new CuvValida();
            int? idAseguradora = null;
            string ov = null;

            var datosEmpresa = this._servicioEmpresaConsulta.ObtenerEmpresaDto(idEmpresa.Value);

            idAseguradora = await this._seguroCalidadDM.ObtenerIdAseguradoraAsync(datosEmpresa.IdEmpresaInst);
            resultado = await this._seguroCalidadDM.ValidarCuvAseguradoraAsync(cuv, idAseguradora);

            if (resultado == null)
            {
                resultado = new CuvValida();
                resultado.esValida = false;
                resultado.MensajeValidacion = "CUV no Valida para Verificador";
            }
            else
            {
                ov = resultado.ordenVerificacion;
                resultado = await this._seguroCalidadDM.ValidarCuvAseguradoraEvaluacionRiesgosAsync(cuv, idAseguradora);

                if (resultado == null)
                {
                    resultado = new CuvValida();
                    resultado.esValida = true;
                    resultado.MensajeValidacion = "Valida";
                    resultado.ordenVerificacion = ov;
                }
                else
                {
                    resultado.esValida = false;
                    resultado.MensajeValidacion = "La Cuv ingresada ya cuenta con un registro de evaluación de riesgo.";
                }
            }


            return resultado;
        }

        public async Task<int> GuardarEvaluacionRiesgoAsync(EvaluacionRiesgo evaluacion)
        {
            List<int> idsDocumentosGuardados = new List<int>();
            int idDocumentoGuardado = 0;
            int idEvaluacionGuardada = 0;
            int resultado = 0;


            //using (var transaccion = new TransactionScope(TransactionScopeOption.RequiresNew, this._opcionesTransaccion, TransactionScopeAsyncFlowOption.Enabled))
            //{
                //Guardar en tabla principal
                var idEvaluacionRiesgo = await this._seguroCalidadDM.GuardarEvaluacionRiesgoAsync(evaluacion.ordenVerificacion);
                evaluacion.IdEvaluacionRiesgo = idEvaluacionRiesgo;

                //Guardar documentos en comun
                //foreach (var documento in evaluacion.Documentos)
                //{
                //    idDocumentoGuardado = await this._seguroCalidadDM.GuardarDocumentoComunAsync(documento.UrlArchivo, documento.NombreArchivo, documento.idUsuarioCarga.Value, (int)EnumCatalogoDocumento.EvidenciaIncidencia);
                //    idsDocumentosGuardados.Add(idDocumentoGuardado);
                //}

                // Guarda una evaluación por cada Cuv seleccionada
                foreach (var cuv in evaluacion.CuvsSeleccionadas)
                {
                    idEvaluacionGuardada = await this._seguroCalidadDM.GuardarEvaluacionRiesgoXViviendaAsync(evaluacion, cuv);

                    //Guarda la relacion de los documentos cargados por cada incidencia
                    //foreach (var documentoRelacion in idsDocumentosGuardados)
                    //{
                    //    await this._seguroCalidadDM.GuardarDocumentoXEvaluacionRiesgoXViviendaAsync(documentoRelacion, idEvaluacionGuardada);
                    //}
                }

                //transaccion.Complete();
                resultado = 1;
            //}

            return resultado;
        }

        public async Task<DetalleIncidencia> ObtenerDetalleIncidenciaAsync(int? idIncidencia, int? idVvGeneral, int? idEntidad, int? idEmpresa)
        {
            var resultado = new DetalleIncidencia();
            var documentos = new List<DocumentoRuv>();
            var informacionContacto = new InformacionOVNuevaincidencia();

            //Obtiene la informacion general
            resultado = await this._seguroCalidadDM.ObtenerDetalleIncidenciaAsync(idIncidencia);
            documentos = await this._seguroCalidadDM.ObtenerDocumentosIncidenciaNotificacionAsync(idIncidencia.Value, idVvGeneral.Value);

            informacionContacto = await this.ObtenerInformacionOVNuevaIncidenciaAsync(resultado.OrdenVerificacion, resultado.Cuv, idEntidad.ToString(), idEmpresa.ToString());

            resultado.DocumentosIncidencia = documentos.Where(d => d.IdCatalogoDocumento == 502 || d.IdCatalogoDocumento == 504).ToList();
            resultado.DocumentosMitigacion = documentos.Where(d => d.IdCatalogoDocumento == 503).ToList();
            resultado.InformacionContacto = informacionContacto;

            return resultado;
        }

        public async Task<Mitigacion> ObtenerDatosMitigacionAsync(int? idIncidencia)
        {
            return await this._seguroCalidadDM.ObtenerDatosMitigacionAsync(idIncidencia);
        }


        public async Task<bool> GuardarMitigacionIncidencia(Mitigacion mitigacion, string idEmpresainst)
        {
            bool resultado = false;
            List<int> listaDoctosGuardados = new List<int>();
            UsuarioDto usuarioRegistroIncidencia = new UsuarioDto();
            var informacionOV = new InformacionOVNuevaincidencia();
            
            informacionOV = await this._seguroCalidadDM.ObtenerInformacionOVNuevaIncidenciaAsync(mitigacion.OrdenVerificacion);
            //Actualizar regsitro de incidencia

            using (var transaccion = new TransactionScope(TransactionScopeOption.RequiresNew, TransactionScopeAsyncFlowOption.Enabled))
            {
                var incidencia = await this._seguroCalidadDM.ObtenerIncidenciaNotificacionAsync(mitigacion.ClaveIncidencia.Value);
                var idIncidenciaOriginal = incidencia.idEstatusIncidencia;
                await this._seguroCalidadDM.GuardarIncidenciaNotificacionBitacoraAsync(incidencia, "Mitigacion");
                incidencia.idEstatusIncidencia = (int)EnumEstatusIncidencia.EnValidacion;
                incidencia.Mitigacion = mitigacion.MitigacionIncidencia;
                incidencia.fechaAtencion = AzureDateTime.Now;

                //Actualizar incidencia
                await this._seguroCalidadDM.ActualizarIncidenciaNotificacion(incidencia);
                
                //Guardar documentos
                foreach (var documento in mitigacion.documentos)
                {
                    listaDoctosGuardados.Add(await this._seguroCalidadDM.GuardarDocumentoComunAsync(documento.UrlArchivo, documento.NombreArchivo, documento.idUsuarioCarga.Value, documento.IdCatalogoDocumento.Value));
                }

                //Guardar relacion de documentos
                foreach (var documento in listaDoctosGuardados)
                {
                    await this._seguroCalidadDM.GuardarDocumentoXIncidenciaAsync(documento, incidencia.IdIncidencia, incidencia.idVvGeneral.Value);
                }

                var desarrollador = new EmpresaDto();
                var verificadorAseguradora = new EmpresaDto();
                var tipo = incidencia.IdTipoIncidenciaRUV == 1 ? "incidencia" : "notificación";

                using (var transaccionEmpresa = new TransactionScope(TransactionScopeOption.RequiresNew, this._opcionesTransaccion, TransactionScopeAsyncFlowOption.Enabled))
                {
                    desarrollador = this._servicioEmpresaComun.ObtenerEmpresa(informacionOV.idDesarrolladorIns);
                    //verificador = this._servicioEmpresaComun.ObtenerEmpresa(mitigacion.Aseguradora);
                    usuarioRegistroIncidencia = _servicioSeguridad.ObtenerUsuarioDto(incidencia.IdUsuario.Value);
                    DatosGeneralesEmpresa datosGeneralesEmpresa = await _empresaDM.ObtenerEmpresaxUsuario(incidencia.IdUsuario.Value);
                    verificadorAseguradora.idEntidad = datosGeneralesEmpresa.idEntidad;
                    verificadorAseguradora.nombreRazonSocial = datosGeneralesEmpresa.nombreRazonSocial;
                    verificadorAseguradora.apellidoPaterno = datosGeneralesEmpresa.apellidoPaterno;
                    verificadorAseguradora.apellidoMaterno = datosGeneralesEmpresa.apellidoMaterno;
                    verificadorAseguradora.IdEmpresaInst = datosGeneralesEmpresa.idEmpresaInst;
                    verificadorAseguradora.idEmpresa = datosGeneralesEmpresa.idEmpresa;
                    
                    transaccionEmpresa.Complete();
                }
                
                if (desarrollador != null && desarrollador.idEmpresa != null)
                {
                    using (var transaccionNotificaciones = new TransactionScope(TransactionScopeOption.RequiresNew, this._opcionesTransaccion, TransactionScopeAsyncFlowOption.Enabled))
                    {
                        //Verificador
                        if(verificadorAseguradora.idEntidad == 8)
                        {
                            await this.EnviarCorreoMitigacionVerificador(mitigacion.OrdenVerificacion, verificadorAseguradora, desarrollador, mitigacion.ClaveIncidencia.Value);
                        }
                        //Aseguradora
                        if (verificadorAseguradora.idEntidad == 13)
                        {
                            await this.EnviarCorreoMitigacionAseguradora(mitigacion.OrdenVerificacion, verificadorAseguradora, desarrollador, mitigacion.ClaveIncidencia.Value, tipo);
                        }

                        transaccionNotificaciones.Complete();
                    }

                    using (var transaccionGenerales = new TransactionScope(TransactionScopeOption.RequiresNew, TransactionScopeAsyncFlowOption.Enabled))
                    {
                        TareaRelacion idTareaACerrar = new TareaRelacion();


                        if (idIncidenciaOriginal == (int)EnumEstatusIncidencia.Abierto)
                        {
                            idTareaACerrar = await _servicioGenerales.ObtenerIncidenciaXTarea(incidencia.IdIncidencia, null, (int)TipoTareaEnum.RegistrarIncidencia);
                        }
                        else if (idIncidenciaOriginal == (int)EnumEstatusIncidencia.Rechazada)
                        {
                            idTareaACerrar = await _servicioGenerales.ObtenerIncidenciaXTarea(incidencia.IdIncidencia, null, (int)TipoTareaEnum.RechazoMitigacionIncidencia);
                        }
                        
                        if(idTareaACerrar != null)
                        {
                            var idTareaAactualizada = await _servicioGenerales.ActualizarTarea(new TareaBancos
                            {
                                IdTarea = idTareaACerrar.idTarea,
                                IdEstatusTarea = (int)EstatusTareaEnum.Atendida,
                                IdUsuarioAtiendeTarea = incidencia.IdUsuario
                            });
                        }
                        

                        var idTareaAgregada = await _servicioGenerales.AgregarTareaDto(new Tarea
                        {
                            IdEstatusTarea = (int)EstatusTareaEnum.Pendiente,
                            IdTipoTarea = (int)TipoTareaEnum.AtenderIncidencia,
                            IdEmpresa = usuarioRegistroIncidencia.idEmpresa,
                            IdUsuarioCreaTarea = incidencia.IdUsuario,
                            Descripcion = $"Se atendió la {tipo} con id {mitigacion.ClaveIncidencia}",
                            FechaRegistro = AzureDateTime.Now,
                            Activo = true
                        });

                        var idTareaRelacion = await _servicioGenerales.AgregarIncidenciaXTarea(incidencia.IdIncidencia, idTareaAgregada);

                        transaccionGenerales.Complete();
                    }
                }


                transaccion.Complete();

                resultado = true;
            }

            return resultado;
        }

        public async Task<bool> CerrarIncidenciaAsync(int idIncidencia, string ordenVerificacion, int idUsuario, string idEmpresaInst, int idEntidad)
        {
            bool resultado = false;

            var aseguradoraVerificador = new EmpresaDto();
            var desarrollador = new EmpresaDto();

            var incidencia = await this._seguroCalidadDM.ObtenerIncidenciaNotificacionAsync(idIncidencia);
            var informacionOV = await this._seguroCalidadDM.ObtenerInformacionOVNuevaIncidenciaAsync(ordenVerificacion);

            using (var transaccionEmpresa = new TransactionScope(TransactionScopeOption.RequiresNew, this._opcionesTransaccion, TransactionScopeAsyncFlowOption.Enabled))
            {
                aseguradoraVerificador = this._servicioEmpresaComun.ObtenerEmpresa(idEmpresaInst);
                desarrollador = this._servicioEmpresaComun.ObtenerEmpresa(informacionOV.idDesarrolladorIns);
                transaccionEmpresa.Complete();
            }
            
            using (var transaccion = new TransactionScope(TransactionScopeOption.RequiresNew, TransactionScopeAsyncFlowOption.Enabled))
            {
                incidencia.idEstatusIncidencia = (int)EnumEstatusIncidencia.Cerrada;
                
                await this._seguroCalidadDM.ActualizarIncidenciaNotificacion(incidencia);

                //Se elimina evaluacion de riesgo
                if (incidencia.IdTipoIncidenciaRUV == (int)EnumTipoIncidencia.EvaluacionRiesgo)
                {
                    await this._seguroCalidadDM.EliminarEvaluacionRiesgoAsync(incidencia.idVvGeneral);
                }

                using (var transaccionNotificaciones = new TransactionScope(TransactionScopeOption.RequiresNew, this._opcionesTransaccion, TransactionScopeAsyncFlowOption.Enabled))
                {
                    //Desarrollador
                    await this.EnviarCorreoCierreIncidenciaDesarrollador(idIncidencia, ordenVerificacion, aseguradoraVerificador, desarrollador, idEntidad);

                    transaccionNotificaciones.Complete();
                }

                using (var transaccionGenerales = new TransactionScope(TransactionScopeOption.RequiresNew, TransactionScopeAsyncFlowOption.Enabled))
                {
                    // Primero cerramos la tarea pendiente de validacion pago
                    var idTareaACerrar = await _servicioGenerales.ObtenerIncidenciaXTarea(idIncidencia, null, (int)TipoTareaEnum.AtenderIncidencia);

                    if (idTareaACerrar != null)
                    {
                        var idTareaAactualizada = await _servicioGenerales.ActualizarTarea(new TareaBancos
                        {
                            IdTarea = idTareaACerrar.idTarea,
                            IdEstatusTarea = (int)EstatusTareaEnum.Atendida,
                            IdUsuarioAtiendeTarea = idUsuario
                        });
                    }
                    
                    transaccionGenerales.Complete();
                }


                transaccion.Complete();

                resultado = true;
            }

             return resultado;
        }

        public async Task<bool> RechazarMitigacionAsync(Incidencia incidencia)
        {
            bool resultado = false;
            var informacionOV = new InformacionOVNuevaincidencia();
            var incidenciaBD = new Incidencia();

            var verificadorAseguradora = new EmpresaDto();
            var desarrollador = new EmpresaDto();

            informacionOV = await this._seguroCalidadDM.ObtenerInformacionOVNuevaIncidenciaAsync(incidencia.OrdenVerificacion);
            incidenciaBD = await this._seguroCalidadDM.ObtenerIncidenciaNotificacionAsync(incidencia.IdIncidencia);
            await this._seguroCalidadDM.GuardarIncidenciaNotificacionBitacoraAsync(incidenciaBD, "Rechazo");

            using (var transaccionEmpresa = new TransactionScope(TransactionScopeOption.RequiresNew, this._opcionesTransaccion, TransactionScopeAsyncFlowOption.Enabled))
            {
                //verificadorAseguradora = this._servicioEmpresaComun.ObtenerEmpresa(informacionOV.idAseguradoraIns);
                desarrollador = this._servicioEmpresaComun.ObtenerEmpresa(informacionOV.idDesarrolladorIns);
                UsuarioDto usuarioRegistroIncidencia = new UsuarioDto();
                usuarioRegistroIncidencia = _servicioSeguridad.ObtenerUsuarioDto(incidenciaBD.IdUsuario.Value);
                DatosGeneralesEmpresa datosGeneralesEmpresa = await _empresaDM.ObtenerEmpresaxUsuario(incidenciaBD.IdUsuario.Value);
                verificadorAseguradora.idEntidad = datosGeneralesEmpresa.idEntidad;
                verificadorAseguradora.nombreRazonSocial = datosGeneralesEmpresa.nombreRazonSocial;
                verificadorAseguradora.apellidoPaterno = datosGeneralesEmpresa.apellidoPaterno;
                verificadorAseguradora.apellidoMaterno = datosGeneralesEmpresa.apellidoMaterno;
                verificadorAseguradora.IdEmpresaInst = datosGeneralesEmpresa.idEmpresaInst;
                verificadorAseguradora.idEmpresa = datosGeneralesEmpresa.idEmpresa;
                verificadorAseguradora.rfc = datosGeneralesEmpresa.rfc;
                transaccionEmpresa.Complete();
            }

            using (var transaccion = new TransactionScope(TransactionScopeOption.RequiresNew, TransactionScopeAsyncFlowOption.Enabled))
            {

                incidenciaBD.idEstatusIncidencia = (int)EnumEstatusIncidencia.Rechazada;
                incidenciaBD.MotivoRechazo = incidencia.MotivoRechazo;

                await this._seguroCalidadDM.ActualizarIncidenciaNotificacion(incidenciaBD);
                
                var tipo = incidenciaBD.IdTipoIncidenciaRUV == 1 ? "incidencia" : "notificación";
                
                if (desarrollador != null && desarrollador.idEmpresa != null)
                {
                    using (var transaccionGenerales = new TransactionScope(TransactionScopeOption.RequiresNew, TransactionScopeAsyncFlowOption.Enabled))
                    {
                        // Primero cerramos la tarea pendiente de validacion pago
                        var idTareaACerrar = await _servicioGenerales.ObtenerIncidenciaXTarea(incidencia.IdIncidencia, null, (int)TipoTareaEnum.AtenderIncidencia);
                        if(idTareaACerrar != null) { 
                            var idTareaAactualizada = await _servicioGenerales.ActualizarTarea(new TareaBancos
                            {
                                IdTarea = idTareaACerrar.idTarea,
                                IdEstatusTarea = (int)EstatusTareaEnum.Atendida,
                                IdUsuarioAtiendeTarea = incidenciaBD.IdUsuario
                            });
                        }
                        var idTareaAgregada = await _servicioGenerales.AgregarTareaDto(new Tarea
                        {
                            IdEstatusTarea = (int)EstatusTareaEnum.Pendiente,
                            IdTipoTarea = (int)TipoTareaEnum.RechazoMitigacionIncidencia,
                            IdEmpresa = desarrollador.idEmpresa,
                            IdUsuarioCreaTarea = incidenciaBD.IdUsuario,
                            Descripcion = $"Se rechazó la mitigación para la {tipo} con id {incidencia.IdIncidencia}",
                            FechaRegistro = AzureDateTime.Now,
                            Activo = true
                        });

                        var idTareaRelacion = await _servicioGenerales.AgregarIncidenciaXTarea(incidencia.IdIncidencia, idTareaAgregada);

                        
                        transaccionGenerales.Complete();
                    }
                }

                using (var transaccionNotificaciones = new TransactionScope(TransactionScopeOption.RequiresNew, this._opcionesTransaccion, TransactionScopeAsyncFlowOption.Enabled))
                {
                    //Desarrollador
                    await this.EnviarCorreoRechazoIncidenciaDesarrollador(incidencia.IdIncidencia, incidencia.OrdenVerificacion, verificadorAseguradora, desarrollador);

                    transaccionNotificaciones.Complete();
                }


                transaccion.Complete();

                resultado = true;
            }

            return resultado;
        }


        #endregion

        #region Ficha Evaluacion
        /// <summary>
        /// Metodo para la generacion de ficha de pago de la evaluacion de riesgo
        /// </summary>
        /// <param name="fichaPagoRiesgo">Objeto con los datos necesarios para la generacion de la ficha de pago</param>
        /// <returns></returns>
        public async Task<bool> GenerarFichaPagoEvaluacion(FichaPagoRiesgo fichaPagoRiesgo)
        {

            RelacionComercial relacion = await this._seguroCalidadDM.ObtenerRelacionComercialxOrdenAsync(fichaPagoRiesgo.idOrdenVerificacion);
            
            List<PreciosxId> precioViviendas = this.ObtenerListaPrecios(fichaPagoRiesgo.precioViviendas);

            var empresa = _servicioEmpresaComun.ObtenerEmpresaxIdRuvAsIs(fichaPagoRiesgo.idEmpresaAsIs);
            Entidades.General.Tarificador.FichaPagoDTO fichaGenerada = null;
            if (empresa != null)
            {
            //fichaGenerada = this._servicioFicha.CrearFichaPago(1, null, null, ServicioProducto.EvaluacionRiesgo, Producto.SeguroCalidad, fichaPagoRiesgo.idEmpresaAsIs, false, precioViviendas.Count(), precioViviendas, fichaPagoRiesgo.idOrdenVerificacion, false);
            if (relacion != null && relacion.evaluacionRiesgo != null && relacion.evaluacionRiesgo == 1)
            {
                var regreso = await this.GenerarFichaPago(fichaPagoRiesgo, (int)ServicioProducto.EvaluacionRiesgo);
            }
            foreach (PrecioViviendas precioVivienda in fichaPagoRiesgo.precioViviendas)
            {
                ViviendasCostoxOrden viviendasxCostoOrden = new ViviendasCostoxOrden();
                viviendasxCostoOrden.cuv = precioVivienda.cuv;
                viviendasxCostoOrden.ordenVerificacion = fichaPagoRiesgo.idOrdenVerificacion;
                viviendasxCostoOrden.costoVivienda = precioVivienda.precio;
                if (relacion != null && relacion.evaluacionRiesgo != null && relacion.evaluacionRiesgo == 1) {
                    decimal costoEvaluacion = await ObtenerCostoEvaluacion(precioVivienda.precio);
                    viviendasxCostoOrden.costoEvaluacionRiesgo = costoEvaluacion;
                }else{
                    viviendasxCostoOrden.costoEvaluacionRiesgo = 0;
                }
                await this._seguroCalidadDM.InsertarActualizarCostoEvaluacionAsync(viviendasxCostoOrden);
            }
            }

            return fichaGenerada != null ? true : false;
            
        }

        /// <summary>
        /// Metodo para obtener la lista de precios para enviarla en el metodo de generacion de ficha
        /// </summary>
        /// <param name="precioViviendas">Objeto con los datos de precio de vivienda</param>
        /// <returns></returns>
        public List<PreciosxId> ObtenerListaPrecios(List<PrecioViviendas> precioViviendas)
        {
            List<PreciosxId> listaPrecios = new List<PreciosxId>();
            foreach (PrecioViviendas precioVivienda in precioViviendas)
            {
                PreciosxId preciosxId = new PreciosxId();
                preciosxId.id = precioVivienda.id;
                preciosxId.precio = precioVivienda.precio;
                listaPrecios.Add(preciosxId);
            }
            return listaPrecios;
        }


        #endregion

        #region Seleccion Aseguradora
        /// <summary>
        /// 
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="idRuvAsis"></param>
        /// <param name="ordenVerificacion"></param>
        /// <param name="noContrato"></param>
        /// <param name="idEmpresaInstAseguradora"></param>
        /// <param name="idTipoAsignacion"></param>
        /// <param name="razonSocialAseguradora"></param>
        /// <returns></returns>
        public async Task<ResultadoPaginado<List<OrdenDeVerificacion>>> ObtenerOrdenesDeVerificacionAsync(int tamanioPagina, int pagina, int idRuvAsis, string ordenVerificacion = null, string noContrato = null, string idEmpresaInstAseguradora = null, int? idTipoAsignacion = null, string razonSocialAseguradora = null)
        {
            ResultadoPaginado<List<OrdenDeVerificacion>> resultado = new ResultadoPaginado<List<OrdenDeVerificacion>>()
            {
                TamanioPagina = tamanioPagina,
                PaginaActual = pagina
            };

            var data = await this._seguroCalidadDM.ObteneOrdenesVerificacionConPaginadoAsync(tamanioPagina, pagina, idRuvAsis, ordenVerificacion, noContrato, idEmpresaInstAseguradora, idTipoAsignacion, razonSocialAseguradora);

            if (data.Item1 > 0)
            {
                resultado.TotalRegistros = data.Item1;
                resultado.TotalPaginas = (int)Math.Ceiling((double)(resultado.TotalRegistros / tamanioPagina));
            }

            resultado.Resultado = data.Item2;

            return resultado;
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="idRuvAsis"></param>
        /// <param name="idEmpresaInst"></param>
        /// <param name="nombreRazonSocial"></param>
        /// <returns></returns>
        public async Task<ResultadoPaginado<List<RelacionComercial>>> ObtenerRelacionesComercialesAsync(int tamanioPagina, int pagina, int idRuvAsis, string idEmpresaInst = null, string nombreRazonSocial = null)
        {
            ResultadoPaginado<List<RelacionComercial>> resultado = new ResultadoPaginado<List<RelacionComercial>>()
            {
                TamanioPagina = tamanioPagina,
                PaginaActual = pagina
            };

            var data = await this._seguroCalidadDM.ObteneRelacionComercialConPaginadoAsync(tamanioPagina, pagina, idRuvAsis, idEmpresaInst, nombreRazonSocial);

            if (data.Item1 > 0)
            {
                resultado.TotalRegistros = data.Item1;
                resultado.TotalPaginas = (int)Math.Ceiling((double)(resultado.TotalRegistros / tamanioPagina));
            }

            resultado.Resultado = data.Item2;

            return resultado;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="ordenVerificacion"></param>
        /// <returns></returns>
        public async Task<string> ObtenerCostoEvaluacionRiesgo(string ordenVerificacion) {

            List<PrecioViviendas> listaPrecios = await this._seguroCalidadDM.ObtenerViviendasOrdenAsync(ordenVerificacion);
            decimal costoEvaluacionTotal = 0;
            foreach (var lista in listaPrecios)
            {
                decimal costoEvaluacion = await ObtenerCostoEvaluacion(lista.precio);
                costoEvaluacionTotal += costoEvaluacion;
            }
            Parametro parametro = await this._seguroCalidadDM.ObtenerParametroAsIsAsync(PARAMETRO_IVA);

            decimal montoConIva = await AgregarIvaMontoTotalAPagar(costoEvaluacionTotal, decimal.Parse(parametro.valor));

            return montoConIva.ToString("C");
        }

        private async Task<decimal> AgregarIvaMontoTotalAPagar(decimal calculoMontoTotalAPagar, decimal iva)
        {
            decimal montoconIVA = 0;
            decimal montototalconIVA = 0;
            try
            {
                // Se agrega el iva al importe
                montoconIVA = (calculoMontoTotalAPagar * (iva / 100));
                montototalconIVA = calculoMontoTotalAPagar + montoconIVA;  
            }
            catch
            {
                throw;
            }
            return await TruncarDecimales(montototalconIVA);
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="idOrdenVerificacion"></param>
        /// <param name="idAseguradora"></param>
        /// <param name="idRelacionComercial"></param>
        /// <returns></returns>
        public async Task<bool> GuardarRelacionesComercialOrdenAsync(string idOrdenVerificacion, int idAseguradora, int idRelacionComercial)
        {

            var resultado = await this._seguroCalidadDM.ActualizarRelacionComercialOrdenAsync(idOrdenVerificacion, idAseguradora, idRelacionComercial);

            OrdenDeVerificacion ordendeVerificacion = await this._seguroCalidadDM.ObtenerOferentePorOVAsync(idOrdenVerificacion);
            
            RelacionComercial relacion = await this._seguroCalidadDM.ObtenerRelacionComercialxOrdenAsync(idOrdenVerificacion);
            
            FichaPagoRiesgo fichaPagoRiesgo = new FichaPagoRiesgo();

            List<PrecioViviendas> listadoViviendasOrden = await this._seguroCalidadDM.ObtenerViviendasOrdenAsync(idOrdenVerificacion);

            fichaPagoRiesgo.idOrdenVerificacion = idOrdenVerificacion;
            fichaPagoRiesgo.idEmpresaAsIs = ordendeVerificacion.idOferente;
            fichaPagoRiesgo.precioViviendas = listadoViviendasOrden;
        //await this.GenerarFichaPagoEvaluacion(fichaPagoRiesgo);
        
            foreach (PrecioViviendas precioVivienda in fichaPagoRiesgo.precioViviendas)
            {
                ViviendasCostoxOrden viviendasxCostoOrden = new ViviendasCostoxOrden();
                viviendasxCostoOrden.cuv = precioVivienda.cuv;
                viviendasxCostoOrden.ordenVerificacion = fichaPagoRiesgo.idOrdenVerificacion;
                viviendasxCostoOrden.costoVivienda = precioVivienda.precio;
                viviendasxCostoOrden.costoViviendaOrden = precioVivienda.costoViviendaOrden;
                if (relacion != null && relacion.evaluacionRiesgo != null && relacion.evaluacionRiesgo == 1)
                {
                    decimal costoEvaluacion = await ObtenerCostoEvaluacion(precioVivienda.precio);
                    viviendasxCostoOrden.costoEvaluacionRiesgo = costoEvaluacion;
                }
                else {
                    viviendasxCostoOrden.costoEvaluacionRiesgo = 0;
                }
                await this._seguroCalidadDM.InsertarActualizarCostoEvaluacionAsync(viviendasxCostoOrden);
            }


            if (ordendeVerificacion != null && ordendeVerificacion.idEmpresaInstAseguradora != null && ordendeVerificacion.idEmpresaInstDesarrollador != null)
            {
                var ban = await this.EnvioCorreoAseguradoraAsync(new EnvioCorreoConfirmacion() { idOrdenVerificacion = idOrdenVerificacion, idEmpresaInstDesarrollador = ordendeVerificacion.idEmpresaInstDesarrollador, idEmpresaInstAseguradora = ordendeVerificacion.idEmpresaInstAseguradora });
            }

            if (relacion != null && relacion.evaluacionRiesgo != null && relacion.evaluacionRiesgo == 1)
            {
                try
                {
                    var resultado_ = await GenerarFichaPago(fichaPagoRiesgo, (int)ServicioProducto.EvaluacionRiesgo);
                }
                catch (Exception exc)
                {
                    var x = "";
                }
            }


            return resultado;
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="envioCorreoConfirmacion"></param>
        /// <returns></returns>
        public async Task<bool> EnvioCorreoAseguradoraAsync(EnvioCorreoConfirmacion envioCorreoConfirmacion)
        {
            MensajeDto mensaje = null;
            List<string> correo = new List<string>();

            OrdenDeVerificacion ordendeVerificacion = await this._seguroCalidadDM.ObtenerOferentePorOVAsync(envioCorreoConfirmacion.idOrdenVerificacion);

            var empresaAseguradora = _servicioEmpresaComun.ObtenerEmpresa(envioCorreoConfirmacion.idEmpresaInstAseguradora);
            
            var empresaDesarrollador = _servicioEmpresaComun.ObtenerEmpresa(ordendeVerificacion.idEmpresaInstDesarrollador);

            correo = this._servicioEmpresaComun.ObtenerCorreosContacto(empresaAseguradora.idEmpresa.Value).ToList();
            
            var relacion = await ObtenerRelacionComercialAsync(null, null, null, ordendeVerificacion.idRelacionComercial);
            
            var nombre = "{0} {1} {2}";

            Dictionary<string, string> parametrosCorreo = new Dictionary<string, string>
                {
                    {"0", string.Format(nombre, empresaAseguradora.nombreRazonSocial, empresaAseguradora.apellidoPaterno, empresaAseguradora.apellidoMaterno) },
                    {"1", empresaAseguradora.rfc},
                    {"2", empresaAseguradora.IdEmpresaInst},
                    //{"3", string.Format(nombre, empresaDesarrollador.nombreRazonSocial, empresaDesarrollador.apellidoPaterno, empresaDesarrollador.apellidoMaterno) },
                    {"3", envioCorreoConfirmacion.idOrdenVerificacion}
                };

            //correo.Add("<EMAIL>");

            mensaje = new MensajeDto("Asignación de Orden Verificación",
                correo,
                eTipoCorreo.ContenidoHtmlMailNotPagoOV,
                eTipoMensaje.InformativoComunicacion,
                null,
                parametrosCorreo);
            
            _servicioNotificaciones.AgregarNotificacion(mensaje);
            
            return true;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="idOrdenVerificacion"></param>
        /// <returns></returns>
        public async Task<DetalleSeleccionAseguradora> ObtenerRelacionComercialOrdenAsync(string idOrdenVerificacion)
        {
            var resultado = await this._seguroCalidadDM.ObtenerRelacionComercialOrdenAsync(idOrdenVerificacion);

            return resultado;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="idOrdenVerificacion"></param>
        /// <returns></returns>
        public async Task<List<PrecioViviendas>> ObtenerViviendasOrdenAsync(string idOrdenVerificacion)
        {
            var resultado = await this._seguroCalidadDM.ObtenerViviendasOrdenAsync(idOrdenVerificacion);

            return resultado;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="idRuvAsIs"></param>
        /// <returns></returns>
        public async Task<DatosGeneralesEmpresa> ConsultarDatosGeneralesEmpresa(int idRuvAsIs) {
            var resultado = await this._empresaDM.ConsultarDatosGeneralesEmpresa(idRuvAsIs);
            return resultado;
        }

        #endregion

        public async Task<string> GenerarDocumentoIncidenciaNotificacionCorreo(List<CuvSeleccionada> listaCuvs, string tipoIncidenciaNotificacion)
        {

            MemoryStream adjuntoPorCorreo;

            List<CuvIncidenciaReporte> listaCuvsReporte = new List<CuvIncidenciaReporte>();

            foreach (var cuv in listaCuvs)
            {
                listaCuvsReporte.Add(new CuvIncidenciaReporte { cuv = cuv.Cuv, incidencia = cuv.ClaveIncidencia });
            }

            MemoryStream adjunto = this._servicioReportes.GenerarReporteCuvsIncidenciasPdf(listaCuvsReporte);

            adjunto.Position = 0;
            adjuntoPorCorreo = new MemoryStream();
            await adjunto.CopyToAsync(adjuntoPorCorreo);
            adjuntoPorCorreo.Position = 0;
            string urlFile = this._servicioNotificaciones.CargarBlob(adjuntoPorCorreo, "CuvsIncidencias.pdf", false);           

            return urlFile;
        }

        /*
         * INCIDENCIAS INTERNO
         */
        public async Task<ResultadoPaginado<List<IncidenciaGestion>>> ObtenerIncidenciasInternoGestionAsync(int tamanioPagina, int pagina, string ordenVerificacion = null, string cuv = null, string desarrollador = null, string claveIncidencia = null, string idTipoIncidencia = null, string idEstatusIncidencia = null, string idClasificacionIncidencia = null, bool? esFechaRegistro = default(bool?), bool? esFechaAtencion = default(bool?), DateTime? fechaInicial = default(DateTime?), DateTime? fechaFinal = default(DateTime?), string idEntidad = null, string idEmpresa = null, string conRiesgo = null
            , string verificador = null, string aseguradora = null)
        {
            int? idVerificador = null;
            int? idAseguradora = null;
            string idDesarrolladorInst = null;

            var datosEmpresa = this._servicioEmpresaConsulta.ObtenerEmpresaDto(Convert.ToInt32(idEmpresa));


            if (Convert.ToInt32(idEntidad) == (int)EnumEntidadEmpresa.Verificador)
            {
                idVerificador = await this._seguroCalidadDM.ObtenerIdVerificadorAsync(datosEmpresa.IdEmpresaInst);
            }
            else if (Convert.ToInt32(idEntidad) == (int)EnumEntidadEmpresa.Aseguradora)
            {
                idAseguradora = await this._seguroCalidadDM.ObtenerIdAseguradoraAsync(datosEmpresa.IdEmpresaInst);
            }
            else if (Convert.ToInt32(idEntidad) == (int)EnumEntidadEmpresa.Desarrollador)
            {
                idDesarrolladorInst = datosEmpresa.IdEmpresaInst;
            }

            ResultadoPaginado<List<IncidenciaGestion>> resultado = new ResultadoPaginado<List<IncidenciaGestion>>()
            {
                TamanioPagina = tamanioPagina,
                PaginaActual = pagina
            };

            var data = await this._seguroCalidadDM.ObtenerIncidenciasInternoGestionAsync(tamanioPagina, pagina, ordenVerificacion, cuv, desarrollador, claveIncidencia, idTipoIncidencia, idEstatusIncidencia, idClasificacionIncidencia, esFechaRegistro, esFechaAtencion, fechaInicial, fechaFinal, idVerificador, idAseguradora, idDesarrolladorInst, conRiesgo, verificador, aseguradora);

            if (data.Item1 > 0)
            {
                resultado.TotalRegistros = data.Item1;
                resultado.TotalPaginas = (int)Math.Ceiling((double)(resultado.TotalRegistros / tamanioPagina));
            }

            var incidencias = data.Item2;

            resultado.Resultado = incidencias;

            if (resultado.Resultado != null && resultado.Resultado.Any())
            {
                foreach (var item in resultado.Resultado)
                {
                    item.CUV = $"{item.CUV} ";
                }
            }

            return resultado;
        }


        public async Task<List<IncidenciaGestion>> ObtenerIncidenciasInternoGestionSinPaginadoAsync(string ordenVerificacion = null, string cuv = null, string desarrollador = null, string claveIncidencia = null, string idTipoIncidencia = null, string idEstatusIncidencia = null, string idClasificacionIncidencia = null, bool? esFechaRegistro = default(bool?), bool? esFechaAtencion = default(bool?), DateTime? fechaInicial = default(DateTime?), DateTime? fechaFinal = default(DateTime?), string idEntidad = null, string idEmpresa = null, string conRiesgo = null
            , string verificador = null, string aseguradora = null)
        {
            int? idVerificador = null;
            int? idAseguradora = null;
            string idDesarrolladorInst = null;

            var datosEmpresa = this._servicioEmpresaConsulta.ObtenerEmpresaDto(Convert.ToInt32(idEmpresa));

            if (Convert.ToInt32(idEntidad) == (int)EnumEntidadEmpresa.Verificador)
            {
                idVerificador = await this._seguroCalidadDM.ObtenerIdVerificadorAsync(datosEmpresa.IdEmpresaInst);
            }
            else if (Convert.ToInt32(idEntidad) == (int)EnumEntidadEmpresa.Aseguradora)
            {
                idAseguradora = await this._seguroCalidadDM.ObtenerIdAseguradoraAsync(datosEmpresa.IdEmpresaInst);
            }
            else if (Convert.ToInt32(idEntidad) == (int)EnumEntidadEmpresa.Desarrollador)
            {
                idDesarrolladorInst = datosEmpresa.IdEmpresaInst;
            }

            var resultado = await this._seguroCalidadDM.ObtenerIncidenciasInternoGestionSinPaginadoAsync(ordenVerificacion, cuv, desarrollador, claveIncidencia, idTipoIncidencia, idEstatusIncidencia, idClasificacionIncidencia, esFechaRegistro, esFechaAtencion, fechaInicial, fechaFinal, idVerificador, idAseguradora, idDesarrolladorInst, conRiesgo,verificador,aseguradora);

            if (resultado != null && resultado.Any())
            {
                foreach (var item in resultado)
                {
                    item.CUV = $"{item.CUV} ";
                }
            }

            return resultado;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="cuv"></param>
        /// <param name="ordenVerificacion"></param>
        /// <returns></returns>
        private async Task<bool> GenerarCostoPolizaAsync(string cuv, string ordenVerificacion) {
            try { 
            CreditoVivienda creditoVivienda = await this._seguroCalidadDM.ConsultarAvaluoxCuvAsync(cuv);

            var banEvaluacion = false;

            if (creditoVivienda != null && (creditoVivienda.idEstatusPoliza == null || creditoVivienda.idEstatusPoliza != 3)) { 
                ViviendasCostoxOrden viviendasCostoxOrden = await this._seguroCalidadDM.ConsultarCostoEvaluacionxCuvAsync(cuv, 0);
                RelacionComercial relacion = await this._seguroCalidadDM.ObtenerRelacionComercialxOrdenAsync(ordenVerificacion);

                if (relacion != null)
                {
                    ReporteViviendaInicioObraUnificado reporteVivienda = await this._seguroCalidadDM.ObteneCriterioSinInicioObraAsync(cuv);
                    if (reporteVivienda != null && reporteVivienda.criterioSinInicioObra != null)
                    {
                        decimal costoEvaluacion = (viviendasCostoxOrden != null && viviendasCostoxOrden.costoEvaluacionRiesgo != null ? viviendasCostoxOrden.costoEvaluacionRiesgo.Value : Convert.ToDecimal(0));

                        if (relacion.evaluacionRiesgo == 1)
                        {
                            if(costoEvaluacion == 0)
                            {
                                costoEvaluacion = await ObtenerCostoEvaluacion(creditoVivienda.costoVivienda);
                                banEvaluacion = true;
                            }
                        }
                        else
                        {
                            costoEvaluacion = 0;
                        }

                        decimal costoPoliza = await this.ObtenerCostoPoliza(costoEvaluacion, creditoVivienda.montoAvaluo, Convert.ToDecimal(relacion.costoRelacion), reporteVivienda.criterioSinInicioObra ?? false);
                            
                        decimal costoPolizaConIva = await this.AgregarIvaMontoTotalAPagar(costoPoliza);
                            
                        if (viviendasCostoxOrden != null) {
                            if (banEvaluacion)
                            {
                                viviendasCostoxOrden.costoEvaluacionRiesgo = costoEvaluacion;
                            }
                            decimal costoDiferenciaOv = await ObtenerCostoDiferencia(viviendasCostoxOrden.costoViviendaOrden, creditoVivienda.montoAvaluo);
                            viviendasCostoxOrden.costoDiferenciaOrden = costoDiferenciaOv;
                            viviendasCostoxOrden.costoPoliza = costoPolizaConIva < 0 ? 0 : costoPolizaConIva;
                            viviendasCostoxOrden.montoAvaluo = creditoVivienda.montoAvaluo;
                            await this._seguroCalidadDM.ActualizarCostoPolizaAsync(viviendasCostoxOrden);
                        }
                        else{
                            viviendasCostoxOrden = new ViviendasCostoxOrden();
                            if (banEvaluacion) {
                                viviendasCostoxOrden.costoEvaluacionRiesgo = costoEvaluacion;
                            }
                            decimal costoDiferenciaOv = await ObtenerCostoDiferencia(reporteVivienda.costoViviendaOrden, creditoVivienda.montoAvaluo);

                            viviendasCostoxOrden.costoDiferenciaOrden = costoDiferenciaOv;
                            viviendasCostoxOrden.ordenVerificacion = ordenVerificacion;
                            viviendasCostoxOrden.cuv = cuv;
                            viviendasCostoxOrden.costoPoliza = costoPolizaConIva < 0 ? 0 : costoPolizaConIva;
                            viviendasCostoxOrden.montoAvaluo = creditoVivienda.montoAvaluo;
                            viviendasCostoxOrden.costoVivienda = reporteVivienda.costoVivienda;
                            viviendasCostoxOrden.costoViviendaOrden = reporteVivienda.costoViviendaOrden;
                            await this._seguroCalidadDM.InsertarActualizarCostoEvaluacionAsync(viviendasCostoxOrden);
                        }
                        
                        return true;
                    }
                }
            }
            }
            catch (Exception ezx)
            {
                var tt = "";
            }
            return false;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="costoEvaluacionRiesgo"></param>
        /// <param name="costoAvaluo"></param>
        /// <param name="costoRelacion"></param>
        /// <param name="criterioSinInicioObra"></param>
        /// <returns></returns>
        private async Task<decimal> ObtenerCostoPoliza(decimal costoEvaluacionRiesgo, decimal costoAvaluo, decimal costoRelacion, bool criterioSinInicioObra) {
            decimal costoPoliza = 0;

            Parametro parametro = await this._seguroCalidadDM.ObtenerParametroAsIsAsync(PARAMETRO_COSTO_POLIZA);

            if (criterioSinInicioObra) {
                costoPoliza = (costoAvaluo * (costoRelacion / 1000)) - costoEvaluacionRiesgo;
            }else
            {
                costoPoliza = (costoAvaluo * (Convert.ToDecimal(parametro.valor) / 1000)) - costoEvaluacionRiesgo;
            }
            
            return await TruncarDecimales(costoPoliza);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="costoOrdenVerificacion"></param>
        /// <param name="costoAvaluo"></param>
        /// <returns></returns>
        private async Task<decimal> ObtenerCostoDiferencia(decimal costoOrdenVerificacion, decimal costoAvaluo)
        {
            decimal montoOrdenDiferencia = 0;
            
            Parametro parametro = await this._seguroCalidadDM.ObtenerParametroAsIsAsync(PARAMETRO_COSTO_ORDEN);

            montoOrdenDiferencia = (costoAvaluo * (Convert.ToDecimal(parametro.valor)));

            montoOrdenDiferencia = await TruncarDecimales(montoOrdenDiferencia - costoOrdenVerificacion);
            return montoOrdenDiferencia <= 0 ? 0 : montoOrdenDiferencia;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="costoVivienda"></param>
        /// <returns></returns>
        private async Task<decimal> ObtenerCostoEvaluacion(decimal costoVivienda)
        {
            decimal costoEvaluacion = 0;

            Parametro parametro = await this._seguroCalidadDM.ObtenerParametroAsIsAsync(PARAMETRO_COSTO_EVALUACION);

            decimal costoEv = Convert.ToDecimal(parametro.valor);
            costoEvaluacion = (costoVivienda) * costoEv;

            return await TruncarDecimales(costoEvaluacion);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="calculoMontoTotalAPagar"></param>
        /// <returns></returns>
        private async Task<decimal> AgregarIvaMontoTotalAPagar(decimal calculoMontoTotalAPagar)
        {
            Parametro parametro = await this._seguroCalidadDM.ObtenerParametroAsIsAsync(PARAMETRO_IVA);

            decimal iva = decimal.Parse(parametro.valor); 
            //decimal importeTotalIvaTruncado = 0;
            decimal importeTotalIva = 0;
            try
            {
                importeTotalIva = (calculoMontoTotalAPagar * (iva / 100));
                //importeTotalIvaTruncado = await this.TruncarDecimales(importeTotalIva);
            }
            catch
            {
                throw;
            }
            return await TruncarDecimales(importeTotalIva + calculoMontoTotalAPagar);
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="cantidadATruncar"></param>
        /// <returns></returns>
        private async Task<decimal> TruncarDecimales(decimal cantidadATruncar)
        {
            Parametro parametro = await this._seguroCalidadDM.ObtenerParametroAsIsAsync(PARAMETRO_TRUNCAR_DECIMALS);

            byte numeroMaximoDecimales = byte.Parse(parametro.valor);
            if (numeroMaximoDecimales > 0)
            {
                decimal parteFraccionaria = cantidadATruncar - decimal.Truncate(cantidadATruncar);
                // Verifica si hay decimales en el Importe
                if (parteFraccionaria > 0)
                {
                    // Calcula la base para remover los decimales deseados
                    decimal base10 = ((decimal)Math.Pow(10, numeroMaximoDecimales));

                    cantidadATruncar = cantidadATruncar * base10;
                    cantidadATruncar = decimal.Truncate(cantidadATruncar);
                    cantidadATruncar = cantidadATruncar / base10;
                }
            }
            return cantidadATruncar;
        }

        #region Fichas Pago
        
        

        public async Task<bool> GenerarFichaPago(FichaPagoRiesgo fichaPagoRiesgo, int servicioProducto)
        {
            try
            {
                Parametro parametro = null;

                switch (servicioProducto)
                {

                    case (int)ServicioProducto.EvaluacionRiesgo:
                        parametro = await this._seguroCalidadDM.ObtenerParametroAsIsAsync(PARAMETRO_URL_EVALUACION);
                        break;
                    case (int)ServicioProducto.PagoDiferencias:
                        parametro = await this._seguroCalidadDM.ObtenerParametroAsIsAsync(PARAMETRO_URL_DIFERENCIAS);
                        break;
                    case (int)ServicioProducto.SolicitarPoliza:
                        parametro = await this._seguroCalidadDM.ObtenerParametroAsIsAsync(PARAMETRO_URL_POLIZA);
                        break;

                }

                JavaScriptSerializer jss = new JavaScriptSerializer();
                string fichaPagoRiesgo_ = jss.Serialize(fichaPagoRiesgo);

                HttpWebRequest request = (HttpWebRequest)WebRequest.Create(parametro.valor);
                request.Method = "POST";

                System.Text.UTF8Encoding encoding = new System.Text.UTF8Encoding();
                Byte[] byteArray = encoding.GetBytes(fichaPagoRiesgo_);

                request.ContentLength = byteArray.Length;
                request.ContentType = @"application/json";

                using (Stream dataStream = request.GetRequestStream())
                {
                    dataStream.Write(byteArray, 0, byteArray.Length);
                }
                long length = 0;
                try
                {
                    using (HttpWebResponse response = (HttpWebResponse)request.GetResponse())
                    {
                        length = response.ContentLength;
                    }
                }
                catch (WebException ex)
                {
                    // Log exception and throw as for GET example above
                }

            }
            catch (Exception exc)
            {
                return false;
            }
            return true;
        }

        #endregion

        #region Recalculo Poliza

        /// <summary>
        /// Metodo para la recalcular el costo de polizas cuando se tiene el monto del valor avaluo
        /// </summary>
        /// <returns></returns>
        public async Task<bool> RecalcularCostoPoliza()
        {
            List<CreditoVivienda> listaViviendas = await this._seguroCalidadDM.RecalcularCostoPolizaAsync();

            foreach (var creditoVivienda in listaViviendas) {

                DatosValorAvaluo datosValorAvaluo = new DatosValorAvaluo();
                datosValorAvaluo.cuv = creditoVivienda.cuv;
                datosValorAvaluo.fechaCierreAvaluo = creditoVivienda.fechaCierreAvaluo;
                datosValorAvaluo.fechaVigenciaAvaluo = creditoVivienda.fechaVigenciaAvaluo;
                datosValorAvaluo.numeroAvaluo = creditoVivienda.numeroAvaluo;
                datosValorAvaluo.valorAvaluo = creditoVivienda.montoAvaluo;
                await this.GuardarDatosValorAvaluo(datosValorAvaluo);
                
            }

            return true;
        }

        #endregion

        #region Servicio Infonavit

        /// <summary>
        /// 
        /// </summary>
        /// <param name="datosValorAvaluo"></param>
        /// <returns></returns>
        private async Task<ResultadoPeticion> ValidarDatosViviendaCredi(EstatusCreditoVivienda datosValorAvaluo)
        {
            ResultadoPeticion resultadoPeticion = null;
            try
            {


                if (datosValorAvaluo == null)
                {
                    resultadoPeticion = new ResultadoPeticion();
                    resultadoPeticion.resultado = "NOK";
                    resultadoPeticion.descripcion = "Los datos recibidos no son validos";
                    resultadoPeticion.codigo = "0007";
                    return resultadoPeticion;
                }


                if (datosValorAvaluo.usuario == null)
                {
                    resultadoPeticion = new ResultadoPeticion();
                    resultadoPeticion.resultado = "NOK";
                    resultadoPeticion.descripcion = "El usuario es necesario";
                    resultadoPeticion.codigo = "0008";
                    return resultadoPeticion;
                }

                if (datosValorAvaluo.password == null)
                {
                    resultadoPeticion = new ResultadoPeticion();
                    resultadoPeticion.resultado = "NOK";
                    resultadoPeticion.descripcion = "El password es necesario";
                    resultadoPeticion.codigo = "0009";
                    return resultadoPeticion;
                }

                if (datosValorAvaluo == null && datosValorAvaluo.EstatusCredito == null || datosValorAvaluo.EstatusCredito.cuv == null || datosValorAvaluo.EstatusCredito.cuv.Length != 16)
                {
                    resultadoPeticion = new ResultadoPeticion();
                    resultadoPeticion.resultado = "NOK";
                    resultadoPeticion.descripcion = "La Cuv no es válida";
                    resultadoPeticion.codigo = "0002";
                    return resultadoPeticion;
                }
                else
                {
                    DatosValorAvaluo exiteCuv = await this._seguroCalidadDM.BuscarCuv(datosValorAvaluo.EstatusCredito.cuv);

                    if (exiteCuv == null || exiteCuv.cuv == null)
                    {
                        resultadoPeticion = new ResultadoPeticion();
                        resultadoPeticion.resultado = "NOK";
                        resultadoPeticion.descripcion = "La Cuv no existe";
                        resultadoPeticion.codigo = "0002";
                        return resultadoPeticion;
                    }

                }

                if (datosValorAvaluo.EstatusCredito.fechaInicioVigencia == null)
                {


                    resultadoPeticion = new ResultadoPeticion();
                    resultadoPeticion.resultado = "NOK";
                    resultadoPeticion.descripcion = "La fecha de cierre del Avaluó no es valida(dd-mm-yyyy HH:mm)";
                    resultadoPeticion.codigo = "0003";
                    return resultadoPeticion;
                }
                else
                {

                    try
                    {
                        DateTime myDate = DateTime.ParseExact(datosValorAvaluo.EstatusCredito.fechaInicioVigencia, "dd-MM-yyyy HH:mm",
                                          System.Globalization.CultureInfo.InvariantCulture);

                        string fecha = myDate.ToString("yyyy/MM/dd HH:mm");

                        datosValorAvaluo.EstatusCredito.fechaInicioVigencia = fecha;

                        if (myDate == null)
                        {
                            resultadoPeticion = new ResultadoPeticion();
                            resultadoPeticion.resultado = "NOK";
                            resultadoPeticion.descripcion = "La fecha de cierre del Avaluó no es valida(dd-mm-yyyy HH:mm)";
                            resultadoPeticion.codigo = "0003";
                            return resultadoPeticion;
                        }
                    }
                    catch (Exception exc)
                    {
                        resultadoPeticion = new ResultadoPeticion();
                        resultadoPeticion.resultado = "NOK";
                        resultadoPeticion.descripcion = "La fecha de cierre del Avaluó no es valida(dd-mm-yyyy HH:mm)";
                        resultadoPeticion.codigo = "0003";
                        return resultadoPeticion;
                    }
                }

                if (datosValorAvaluo == null && datosValorAvaluo.EstatusCredito == null || datosValorAvaluo.EstatusCredito.claveEstatus == null 
                    || (!datosValorAvaluo.EstatusCredito.claveEstatus.Equals(eEstatusViviendaInfonavit.CancelacionCredito) 
                    && !datosValorAvaluo.EstatusCredito.claveEstatus.Equals(eEstatusViviendaInfonavit.EjercicioCredito) 
                    && !datosValorAvaluo.EstatusCredito.claveEstatus.Equals(eEstatusViviendaInfonavit.InscripcionCredito)))
                {
                    resultadoPeticion = new ResultadoPeticion();
                    resultadoPeticion.resultado = "NOK";
                    resultadoPeticion.descripcion = "El estatus enviado es invalido";
                    resultadoPeticion.codigo = "0004";
                    return resultadoPeticion;
                }

            }
            catch (Exception exc)
            {
                resultadoPeticion = new ResultadoPeticion();
                resultadoPeticion.resultado = "NOK";
                resultadoPeticion.descripcion = "Fuera de servicio/error interno";
                resultadoPeticion.codigo = "0005";
                return resultadoPeticion;
            }

            return resultadoPeticion;
        }

        #endregion

<<<<<<< Updated upstream
        #region Generacion Poliza

        /// <summary>
        /// Metodo para la recalcular el costo de polizas cuando se tiene el monto del valor avaluo
        /// </summary>
        /// <returns></returns>
        public async Task<bool> GenerarDocumentoPoliza(PeticionSolicitudPoliza peticionSolicitudPoliza)
        {
            DatosGeneracionPoliza datosGeneracionPoliza_ = await this._seguroCalidadDM.ObtenerDatosGeneralesPolizaAsync(peticionSolicitudPoliza.ordenVerificacion);
          
            RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.Viviendas vivienda = await this._seguroCalidadDM.ObtenerDatosGeneralesCuvPolizaAsync(peticionSolicitudPoliza.cuv);
            
            RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api.Direccion direccion = await this._seguroCalidadDM.ObtenerDireccionCuvPolizaAsync(peticionSolicitudPoliza.cuv);

            vivienda.direccion = direccion;

            datosGeneracionPoliza_.Viviendas = new List<Modelo.SeguroCalidad.Api.Viviendas>();
            datosGeneracionPoliza_.Viviendas.Add(vivienda);
            await SolicitarPolizaAseguradora(datosGeneracionPoliza_);

            return true;
        }
=======
        #region PagoDiferencias
>>>>>>> Stashed changes

        /// <summary>
        /// 
        /// </summary>
<<<<<<< Updated upstream
        /// <param name="datosGeneracionPoliza_"></param>
        /// <returns></returns>
        private async Task<bool> SolicitarPolizaAseguradora(DatosGeneracionPoliza datosGeneracionPoliza_)
        {

            Parametro parametro = null;

            parametro = await this._seguroCalidadDM.ObtenerParametroAsIsAsync(PARAMETRO_URL_EVALUACION);


            var authData = string.Format("{0}:{1}", "SERVICIORUVINFO", "SERVWEBGMXRUV*18");
            var authHeaderValue = Convert.ToBase64String(Encoding.UTF8.GetBytes(authData));

            JavaScriptSerializer jss = new JavaScriptSerializer();
            string fichaPagoRiesgo_ = jss.Serialize(datosGeneracionPoliza_);

            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(parametro.valor);
            
            //request.Headers["Authorization"] = "Basic " + authData;
            //request.Credentials = await GetCredential();
            //request.PreAuthenticate = true;
            request.Method = "POST";

            System.Text.UTF8Encoding encoding = new System.Text.UTF8Encoding();
            Byte[] byteArray = encoding.GetBytes(fichaPagoRiesgo_);

            request.ContentLength = byteArray.Length;
            request.ContentType = @"application/json";

            using (Stream dataStream = request.GetRequestStream())
            {
                dataStream.Write(byteArray, 0, byteArray.Length);
            }
            long length = 0;
            try
            {
                using (HttpWebResponse response = (HttpWebResponse)request.GetResponse())
                {
                    length = response.ContentLength;
                }
            }
            catch (Exception ex)
            {
                return false;
                // Log exception and throw as for GET example above
            }
            return true;

        }

        private async Task<CredentialCache> GetCredential()
        {

            Parametro parametro = null;

            parametro = await this._seguroCalidadDM.ObtenerParametroAsIsAsync(PARAMETRO_URL_EVALUACION);

            string url = @parametro.valor;
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Ssl3;
            CredentialCache credentialCache = new CredentialCache();
            credentialCache.Add(new System.Uri(url), "Basic", new NetworkCredential("SERVICIORUVINFO", "SERVWEBGMXRUV*18"));
            return credentialCache;
        }
=======
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="ordenVerificacion"></param>
        /// <returns></returns>
        public async Task<ResultadoPaginado<List<PagoDiferenciaOV>>> ObtenerPagoDiferenciaOV(int tamanioPagina, int pagina, string ordenVerificacion)
        {
            ResultadoPaginado<List<PagoDiferenciaOV>> resultado = new ResultadoPaginado<List<PagoDiferenciaOV>>()
            {
                TamanioPagina = tamanioPagina,
                PaginaActual = pagina
            };

            var data = await this._seguroCalidadDM.ObtenerPagoDiferenciaOV(tamanioPagina, pagina, ordenVerificacion);

            if (data.Item1 > 0)
            {
                resultado.TotalRegistros = data.Item1;
                resultado.TotalPaginas = (int)Math.Ceiling((double)(resultado.TotalRegistros / tamanioPagina));
            }

            resultado.Resultado =  data.Item2;

            if (resultado.Resultado != null && resultado.Resultado.Any())
            {
                foreach (var item in resultado.Resultado)
                {
                    if (item.fechaCreacion != null)
                        item.fechaCreacion = Convert.ToDateTime(item.fechaCreacion).ToString("dd/MM/yyyy");

                    if (item.fechaSolicitudPago != null)
                        item.fechaSolicitudPago = Convert.ToDateTime(item.fechaSolicitudPago).ToString("dd/MM/yyyy");

                    if (item.fechaPago != null)
                        item.fechaPago = Convert.ToDateTime(item.fechaPago).ToString("dd/MM/yyyy");
                }
            }

            return resultado;
        }

        public async Task<PagoDiferenciaOV> AgregarPagoDiferenciaOVAsync(PagoDiferenciaOV pagoDiferenciaOV)
        {
            var insertado = new PagoDiferenciaOV();

            using (var transaccion = new TransactionScope(TransactionScopeOption.RequiresNew, TransactionScopeAsyncFlowOption.Enabled))
            {
                insertado = await this._seguroCalidadDM.AgregarPagoDiferenciaOVAsync(pagoDiferenciaOV);

                transaccion.Complete();
            }

            return insertado;

        }

>>>>>>> Stashed changes
        #endregion
    }




}
