﻿using RUV.RUVPP.Oferta.Comun.Modelo;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api
{
    public class RelacionComercial
    {

        public int? idRelacionComercial { get; set; }

        public string costoRelacion { get; set; }

        public int? evaluacionRiesgo { get; set; }

        public DateTime? fechaRegistro { get; set; }

        public string fechaRegistroString { get; set; }

        public DateTime? fechaActualizacion { get; set; }

        public string fechaActualizacionString { get; set; }

        public bool? activo { get; set; }

        public DateTime? fechaAceptacion { get; set; }

        public string fechaAceptacionString { get; set; }

        public string numeroContrato { get; set; }

        public DateTime? fechaInicioContrato { get; set; }

        public DateTime? fechaTerminoContrato { get; set; }

        public DateTime? fechaRechazo { get; set; }

        public string fechaRechazoString { get; set; }

        public DateTime? fechaRechazoevaluacion { get; set; }

        public string fechaRechazoevaluacionString { get; set; }

        public string motivoRechazo { get; set; }

        public int? idOferente { get; set; }

        public string idEmpresaInstMasMas { get; set; }

        public int? idAseguradora { get; set; }

        public int? idTipoAsignacion { get; set; }

        public string tipoAsignacion { get; set; }

        public int? idEstatusRelacionComercial { get; set; }

        public string estatusRelacionComercial { get; set; }

        public int? idEstatusPagoEvaluacionRiesgo { get; set; }

        public string motivoRechazoEvaluacionRiesgo { get; set; }

        public string estatuspagoEvaluacionRiesgo { get; set; }

        public int? idDocumentoContrato { get; set; }

        public string nombreRazonSocial { get; set; }
        
        public DocumentoRuv contrato { get; set; }

        public string razonSocialAseguradora { get; set; }

        public string razonSocialVerificador { get; set; }

        public int? idUsuarioConfirmoCosto { get; set; }        

    }
}
