﻿using RUV.RUVPP.Oferta.Modelo.Comun;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Dominio.Prototipo
{
    /// <summary>
    /// 
    /// </summary>
    public interface IServicioPrototipo
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="prototipo"></param>
        /// <returns></returns>
        Task<bool> GuardarPrototipoAsync(Modelo.Prototipos.Api.Prototipo prototipo);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tamanioPagina"></param>
        /// <param name="pagina"></param>
        /// <param name="idEmpresa"></param>
        /// <param name="idPrototipo"></param>
        /// <param name="precioDesde"></param>
        /// <param name="precioHasta"></param>
        /// <param name="idTipologiaVivienda"></param>
        /// <param name="nombre"></param>
        /// <returns></returns>
        Task<ResultadoPaginado<List<Modelo.Prototipos.Api.Prototipo>>> ObtenerPrototiposFiltradoAsync(int tamanioPagina, int pagina, int? idEmpresa, int? idPrototipo, int? precioDesde, int? precioHasta, int? idTipologiaVivienda, string nombre);

<<<<<<< Updated upstream
        Task<bool> EliminarPrototipoAsync(int idPrototipo, int idEmpresa);
=======
        /// <summary>
        /// 
        /// </summary>
        /// <param name="idPrototipo"></param>
        /// <returns></returns>
        Task<Modelo.Prototipos.Api.Prototipo> ObtenerPrototipoPorIdAsync(int idPrototipo);
>>>>>>> Stashed changes
    }
}
