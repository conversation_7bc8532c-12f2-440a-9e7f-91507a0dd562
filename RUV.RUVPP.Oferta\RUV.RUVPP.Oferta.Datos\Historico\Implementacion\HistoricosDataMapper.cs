﻿using RUV.Comun.Datos.Mapper;
using RUV.RUVPP.Oferta.Modelo.Historico.Data;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.ApplicationInsights;
using Dapper;
using System.Data;
using System.Linq;

namespace RUV.RUVPP.Oferta.Datos.Historico.Implementacion
{
    public class HistoricosDataMapper : SqlDataMapperBase, IHistoricosDataMapper
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="clienteTelemetria"></param>
        /// <param name="gestorConexiones"></param>
        /// <param name="nombreCadenaConexion"></param>
        public HistoricosDataMapper(string nombreCadenaConexion, GestorConexiones gestorConexiones = null) : base(nombreCadenaConexion, gestorConexiones)
        {
        }

        public async Task<EstatusOfertaVivienda> GuardarHistoricoOfertaVivienda(EstatusOfertaVivienda estatusOfertaVivienda)
        {
            var parametros = new DynamicParameters();

            parametros.Add("@idCatalogoEvento", estatusOfertaVivienda.idEvento);
            parametros.Add("@claveOfertaVivienda", estatusOfertaVivienda.claveOfertaVivienda);
            parametros.Add("@estatusOferta", estatusOfertaVivienda.estatusOferta);
            parametros.Add("@idUsuario", estatusOfertaVivienda.idUsuario);
            parametros.Add("@nombreUsuario", estatusOfertaVivienda.nombreUsuario);
            parametros.Add("@idOferta", estatusOfertaVivienda.idOferta);
            var readerOferta = await this._conexion.ExecuteScalarAsync("historico.usp_InsertarEventoOfertaVivienda", parametros, commandType: CommandType.StoredProcedure);
            var resultOferta = readerOferta ?? 0;

            estatusOfertaVivienda.idEstatusOfertaVivienda = Convert.ToInt32(resultOferta);
            return estatusOfertaVivienda;
        }
        
        public async Task<int> GuardarDetalleHistoricoOfertaVivienda(EstatusOfertaVivienda estatusOfertaVivienda, List<CampoAdicional> listaDetalleEstatus)
        {
            //foreach (CampoAdicional campoAdicional in listaDetalleEstatus)
            //{
            //    var parametrosDetalle = new DynamicParameters();
            //    //parametrosDetalle.Add("@idDetalleEstatusOfertaVivienda", 1);
            //    //parametrosDetalle.Add("@idEstatusOfertaVivienda", estatusOfertaVivienda.idEstatusOfertaVivienda);
            //    parametrosDetalle.Add("@idCampoAdicional", campoAdicional.idCampoAdicional);
            //    parametrosDetalle.Add("@valorCampoAdicional", campoAdicional.valor);

            //    await this._conexion.ExecuteScalarAsync("historico.usp_InsertarDetalleEventoOfertaVivienda", parametrosDetalle, commandType: CommandType.StoredProcedure);
            //}
            return 1;
        }
        
        public async Task<EstatusProyectos> GuardarHistoricoProyecto(EstatusProyectos estatusProyecto)
        {
            var parametros = new DynamicParameters();

            parametros.Add("@idCatalogoEvento", estatusProyecto.idEvento);
            parametros.Add("@idProyecto", estatusProyecto.idProyecto);
            parametros.Add("@estatusProyecto", estatusProyecto.estatusProyecto);
            parametros.Add("@idUsuario", estatusProyecto.idUsuario);
            parametros.Add("@nombreUsuario", estatusProyecto.nombreUsuario);
            var readerProyecto = await this._conexion.ExecuteScalarAsync("historico.usp_InsertarEventoProyecto", parametros, commandType: CommandType.StoredProcedure);
            estatusProyecto.idEstatusProyecto = Convert.ToInt32(readerProyecto);
            return estatusProyecto;
        }
        
        public async Task<int> GuardarDetalleHistoricoProyecto(EstatusProyectos estatusProyecto, List<CampoAdicional> listaDetalleEstatus)
        {
            //foreach (CampoAdicional campoAdicional in listaDetalleEstatus)
            //{

            //    var parametrosDetalle = new DynamicParameters();
            //    //parametrosDetalle.Add("@idDetalleEstatusProyecto", 1);
            //    //parametrosDetalle.Add("@idEstatusProyecto", estatusProyecto.idEstatusProyecto);
            //    parametrosDetalle.Add("@idCampoAdicional", campoAdicional.idCampoAdicional);
            //    parametrosDetalle.Add("@valorCampoAdicional", campoAdicional.valor);

            //    await this._conexion.ExecuteScalarAsync("historico.usp_InsertarDetalleEventoProyecto", parametrosDetalle, commandType: CommandType.StoredProcedure);
            //}
            return 1;
        }
        
        public async Task<EstatusViviendas> GuardarHistoricoVivienda(EstatusViviendas estatusVivienda)
        {
            var parametros = new DynamicParameters();

            parametros.Add("@idCatalogoEvento", estatusVivienda.idEvento);
            parametros.Add("@identificadorVivienda", estatusVivienda.identificadorVivienda);
            parametros.Add("@estatusCUV", estatusVivienda.estatusCUV);
            parametros.Add("@idUsuario", estatusVivienda.idUsuario);
            parametros.Add("@nombreUsuario", estatusVivienda.nombreUsuario);
            parametros.Add("@cuv", estatusVivienda.cuv);
            var readerProyecto = await this._conexion.ExecuteScalarAsync("historico.usp_InsertarEventoVivienda", parametros, commandType: CommandType.StoredProcedure);
            estatusVivienda.idEstatusVivienda = Convert.ToInt32(readerProyecto);

            return estatusVivienda;
        }
        
        public async Task<int> GuardarDetalleHistoricoVivienda(EstatusViviendas estatusVivienda, List<DetalleEstatusVivienda> listaDetalleEstatus)
        {
            foreach (DetalleEstatusVivienda campoAdicional in listaDetalleEstatus)
            {
                var parametrosDetalle = new DynamicParameters();
                parametrosDetalle.Add("@idEventoVivienda", estatusVivienda.idEstatusVivienda);
                parametrosDetalle.Add("@idCampoAdicional", campoAdicional.idCampoAdicional);
                parametrosDetalle.Add("@valorCampoAdicional", campoAdicional.valorCampoAdicional);

                await this._conexion.ExecuteScalarAsync("historico.usp_InsertarDetalleEventoVivienda", parametrosDetalle, commandType: CommandType.StoredProcedure);
            }

            return 1;
        }
        
        public async Task<List<CampoAdicional>> GuardarAdicionalHistorico(List<CampoAdicional> listaDetalleEstatus)
        {
            foreach (CampoAdicional campoAdicional in listaDetalleEstatus)
            {
                var parametrosAdicional = new DynamicParameters();

                parametrosAdicional.Add("@nombreDelCampo", campoAdicional.nombreDelCampo);
                parametrosAdicional.Add("@descripcion", campoAdicional.descripcion);
                parametrosAdicional.Add("@tipoDato", campoAdicional.tipoDato);
                parametrosAdicional.Add("@activo", campoAdicional.activo);

                var reader = await _conexion.ExecuteScalarAsync("historico.usp_InsertarCampoAdicional", parametrosAdicional, commandType: CommandType.StoredProcedure);
                var result = reader ?? 0;
                campoAdicional.idCampoAdicional = Convert.ToInt32(result);
            }
            return listaDetalleEstatus;
        }
        
        public async Task<List<HistoricoCuv>> ObtenerHistoricoCuvPorIdViviendaAsync(int idVivienda)
        {
            var parametros = new DynamicParameters();
            parametros.Add("@idVivienda", idVivienda);

            var reader = await this._conexion.QueryMultipleAsync("historico.usp_ObtenerHistoricoCuvXidVivienda", parametros, commandType: CommandType.StoredProcedure);

            var historico = await reader.ReadAsync<HistoricoCuv>();

            return historico.ToList();
        }

        public async Task<bool> InsertarDocumentoPorEventoVivienda(int idEventoVivienda, int idDocumento)
        {
            var parametros = new DynamicParameters();
            parametros.Add("@idEventoVivienda", idEventoVivienda);
            parametros.Add("@idDocumento", idDocumento);

            var lector = await this._conexion.ExecuteScalarAsync("[historico].[usp_InsertarDocumentoPorEventoVivienda]", parametros, commandType: CommandType.StoredProcedure);

            return Convert.ToInt32(lector) > 0;
        }

        public async Task<List<int>> ObtenerDocumentosPorEventoVivienda(int idEventoVivienda)
        {
            var parametros = new DynamicParameters();
            parametros.Add("@idEventoVivienda", idEventoVivienda);

            var lector = await this._conexion.QueryMultipleAsync("[historico].[usp_ObtenerDocumentosEventoVivienda]", parametros, commandType: CommandType.StoredProcedure);

            var resultado = await lector.ReadAsync<int>();

            return resultado.ToList();
        }
    }
}