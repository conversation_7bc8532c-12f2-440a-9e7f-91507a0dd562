﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RUV.RUVPP.Oferta.Modelo.Avaluo;

namespace RUV.RUVPP.Oferta.Datos.Avaluo
{
    public interface IAvaluoDataMapper : IDisposable
    {
        Task<int> NotificacionDatosAvaluo(DatosAvaluoSolicitud entity);
        Task<int> ReasignacionAvaluo(DatosReasignacion entity);
        Task<int> CancelacionAvaluo(DatosCancelacion entity);
        Task<int> FotoAvaluo(DocumentoFotoAvaluo entity);
        Task<List <DatosAvaluoLogingUv>> DatosAvaluoLogingUv(DatosAvaluoSolicitud entity);
        Task<int> ConsultaCuv(string entitty);
        Task<string> ConsultaAsignacionAvaluo(string entitty);

        Task<DTOAvaluo> filtroAvaluoViviviendaMAI(string cuv, string cve_ent);
        Task<DTOAvaluo> filtroAvaluoVivivienda(string cuv, string cve_ent);
        Task<List<DTOAvaluo>> filtroAvaluo(FiltroAvaluo filtroAvaluo);
        Task<int> existeUnidadValuacion(int unidadValuacion,int opcion,string idvaluacion);
        Task<int> actualizacionDireccionAvaluoVivienda(DomicilioAvaluo entity);
        Task<int> fechaUpdateReporteAvaluo(DateTime fechaReporte,string idBitacoraAvaluo , string IdPeritoSHF);
        Task<int> existeReporteAvaluo(string cuv);

        Task<List<DTOAvaluo>> direccionViviendaAvaluo(int idUnidadValuacion);

        Task<List<DocumentoFotoAvaluo>> envioFotosInfonavit(string cuv);
    }
}
