//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace RUV.RUVPP.Oferta.MigracionProyectos.Modelo
{
    using System;
    using System.Collections.Generic;
    
    public partial class DirectorResponsableObra
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public DirectorResponsableObra()
        {
            this.Proyecto = new HashSet<Proyecto>();
        }
    
        public int idDRO { get; set; }
        public Nullable<int> idIdentificacionOficial { get; set; }
        public Nullable<int> idLicencia { get; set; }
        public string nombreDRO { get; set; }
        public string apellidoPaterno { get; set; }
        public string apellidoMaterno { get; set; }
        public string numeroPerito { get; set; }
        public System.DateTime fechaVigencia { get; set; }
        public System.DateTime fechaRegistro { get; set; }
        public Nullable<System.DateTime> fechaActualizacion { get; set; }
        public bool activo { get; set; }
    
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Proyecto> Proyecto { get; set; }
    }
}
