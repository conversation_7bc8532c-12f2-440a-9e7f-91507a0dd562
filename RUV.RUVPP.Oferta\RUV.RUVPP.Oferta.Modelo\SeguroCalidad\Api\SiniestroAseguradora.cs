﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Modelo.SeguroCalidad.Api
{
    public class SiniestroAseguradora
    {
        public string folioReporteRuv { get; set; }
        public string cuv { get; set; }

        public string domicilio { get; set; }

        public string fechaOcurrencia { get; set; }
        public string fechaReporte { get; set; }
        public string poliza { get; set; }
        public string numeroCredito { get; set; }
        public string fechaInicioPoliza { get; set; }
        public string fechaFinPoliza { get; set; }               
        public string descripcionSiniestro { get; set; }
        public string coberturaAfectada { get; set; }
        public string personaAfectada { get; set; }
        public string personaReporta { get; set; }
        public string telefonoContacto1 { get; set; }
        public string telefonoContacto2 { get; set; }
        public string correoContacto1 { get; set; }
        public string correoContacto2 { get; set; }
        public string estatusReporte { get; set; }
        public int? idAseguradora { get; set; }
        public int? idSiniestro { get; set; }

    }
}
