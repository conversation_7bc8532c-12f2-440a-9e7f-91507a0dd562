﻿using Dapper;
using Microsoft.ApplicationInsights;
using RUV.Comun.Datos.Mapper;
using System;
using System.Data;
using System.Linq;
using System.Threading.Tasks;

namespace RUV.RUVPP.Oferta.Datos.Dictaminacion.Implementacion
{
    public class DictaminacionDataMapper : SqlDataMapperBase, IDictaminacionDataMapper
    {
        public DictaminacionDataMapper(string nombreCadenaConexion, GestorConexiones gestorConexiones = null) : base(nombreCadenaConexion, gestorConexiones)
        {
        }

        /// <summary>
        /// Guarda el JSON de dictaminacion para un registro y orden de trabajo determinados
        /// </summary>
        /// <param name="idServicio">Identificador del servicio</param>
        /// <param name="idOrdenDeTrabajo">Identificador de la orden de trabajo</param>
        /// <param name="idRegistro">Identificador del registro</param>
        /// <param name="dictaminacionJSON">Cadena conteniendo el JSON de dictaminacion</param>
        /// <returns></returns>
        public async Task<bool> GuardarDictaminacionAsync(short idServicio, int idOrdenDeTrabajo, int idRegistro, string dictaminacionJSON)
        {
            var parameters = new DynamicParameters();
            
            parameters.Add("@idServicio", idServicio);
            parameters.Add("@idOrdenDeTrabajo", idOrdenDeTrabajo);
            parameters.Add("@idRegistro", idRegistro);
            parameters.Add("@dictaminacionJSON", dictaminacionJSON);

            var reader = await this._conexion.ExecuteScalarAsync("VyD.usp_DictaminacionFormulario_Insertar", parameters, commandType: CommandType.StoredProcedure);
            var renglonesAfectados = Convert.ToInt32(reader ?? 0);
            var result = renglonesAfectados > 0 ? true : false;

            return result;
        }

        /// <summary>
        /// Actualiza el JSON de dictaminacion de un registro para una ODT determinada
        /// </summary>
        /// <param name="idServicio">Identificador del servicio</param>
        /// <param name="idOrdenDeTrabajo">Identificador de la orden de trabajo</param>
        /// <param name="idRegistro">Identificador del registro</param>
        /// <param name="dictaminacionJSON">Cadena conteniendo el JSON de dictaminacion</param>
        /// <returns></returns>
        public async Task<bool> ActualizarDictaminacionAsync(short idServicio, int idOrdenDeTrabajo, int idRegistro, string dictaminacionJSON)
        {
            var parameters = new DynamicParameters();

            parameters.Add("@idServicio", idServicio);
            parameters.Add("@idOrdenDeTrabajo", idOrdenDeTrabajo);
            parameters.Add("@idRegistro", idRegistro);
            parameters.Add("@dictaminacionJSON", dictaminacionJSON);

            var reader = await this._conexion.ExecuteScalarAsync("VyD.usp_DictaminacionFormulario_Actualizar", parameters, commandType: CommandType.StoredProcedure);
            var renglonesAfectados = Convert.ToInt32(reader ?? 0);
            var result = renglonesAfectados > 0 ? true : false;

            return result;
        }

        /// <summary>
        /// Obtiene el ultimo JSON de dictaminacion para un registro determinado
        /// </summary>
        /// <param name="idServicio">Identificador del servicio</param>
        /// <param name="idRegistro">Identificador del registro</param>
        /// <returns></returns>
        public async Task<string> ObtenerDictaminacionAsync(short idServicio, int idRegistro)
        {
            var parameters = new DynamicParameters();

            parameters.Add("@idServicio", idServicio);            
            parameters.Add("@idRegistro", idRegistro);

            var reader = await this._conexion.QueryMultipleAsync("VyD.usp_DictaminacionFormulario_Obtener", parameters, commandType: CommandType.StoredProcedure);
            var dictaminacionJSON = await reader.ReadAsync<string>();

            return dictaminacionJSON.FirstOrDefault();
        }

        /// <summary>
        /// Obtiene el JSON de dictaminacion de un registro para una ODT determinada
        /// </summary>
        /// <param name="idServicio">Identificador del servicio</param>
        /// <param name="idRegistro">Identificador del registro</param>
        /// <param name="idOrdenTrabajo">Identificador de la orden de trabajo</param>
        /// <returns></returns>
        public async Task<string> ObtenerDictaminacionPorOrdenTrabajoAsync(short idServicio, int idRegistro, int idOrdenTrabajo)
        {
            var parameters = new DynamicParameters();

            parameters.Add("@idServicio", idServicio);
            parameters.Add("@idRegistro", idRegistro);
            parameters.Add("@idOrdenTrabajo", idOrdenTrabajo);

            var reader = await this._conexion.QueryMultipleAsync("VyD.usp_DictaminacionFormulario_ObtenerXOrdenTrabajo", parameters, commandType: CommandType.StoredProcedure);
            var dictaminacionJSON = await reader.ReadAsync<string>();

            return dictaminacionJSON.FirstOrDefault();
        }
        /// <summary>
        /// Obtiene el JSON de dictaminacion de la una oferta para una ODT determinada, si no la encuentra, regresa el JSON de la ultima ODT de ese registro.
        /// </summary>
        /// <param name="idServicio">Identificador del servicio</param>
        /// <param name="idOrdenDeTrabajo">Identificador de la ODT</param>
        /// <param name="idRegistro">Identificador del registro</param>
        /// <returns></returns>
        public async Task<string> ObtenerUltimaDictaminacionOAnteriorAsync(short idServicio, int idOrdenDeTrabajo, int idRegistro)
        {
            var parameters = new DynamicParameters();

            parameters.Add("@idServicio", idServicio);
            parameters.Add("@idOrdenDeTrabajo", idOrdenDeTrabajo);
            parameters.Add("@idRegistro", idRegistro);

            var reader = await this._conexion.QueryMultipleAsync("VyD.usp_DictaminacionFormulario_ObtenerUltimaOAnteriorOT", parameters, commandType: CommandType.StoredProcedure);
            var dictaminacionJSON = await reader.ReadAsync<string>();

            return dictaminacionJSON.FirstOrDefault();
        }

        public async Task<int> ObtenerCantidadDictaminaciones(short idServicio, int idRegistro)
        {
            DynamicParameters parametros = new DynamicParameters();

            parametros.Add("@idServicio", idServicio);
            parametros.Add("@idRegistro", idRegistro);

            SqlMapper.GridReader lector = await this._conexion.QueryMultipleAsync("VyD.usp_DictaminacionFormulario_ObtenerCantidadDictaminaciones", parametros, commandType: CommandType.StoredProcedure);

            System.Collections.Generic.IEnumerable<int> cantidad = await lector.ReadAsync<int>();

            return cantidad.FirstOrDefault();
        }
    }
}
